<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>找茬活动统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        getCurrent()
        var abol = false  //纪检身份
        abolFun()
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/findFaults/queryAllFaultsInfo?faultsType=" +  gps.faultsType, //table列表的查询命令
                    "queryParams": {},
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "归属单位", field: "companyName", width: 100, tooltip: true, align: "center" },
                            { title: "归属部门", field: "departmentName", width: 120, tooltip: true, align: "center" },
                            { title: "上报人姓名", field: "applyUser", width: 120, tooltip: true, align: "center" },
                            { title: "联系电话", field: "applyUserPhone", width: 50, tooltip: true, align: "center" },
                            { title: "意见或建议归属系统", field: "appName", width: 120, tooltip: true, align: "center"},
                            { title: "上报时间", field: "createdTime", width: 70, tooltip: true, align: "center" },
                            {
                                field: "opt", title: "操作", width: 60, rowspan: 1, align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = "<a class='check' index='" + index + "'>【查看】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                }
            };

            if (gps.companyCode) {
                pageparam.listtable.queryParams = {
                    belongDepartmentCode: gps.companyCode
                }
            }

            loadGrid(pageparam);
            $('#belongDepartmentCode').val(gps.companyCode)

            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir + "action/findFaults/exportParameter?faultsType=" +  gps.faultsType);
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });

            //选择组织
            $(".chooseOrgs").on("click", function () {
                var href = { "multi": "0", "name": "chooseOrgsVal", "pmInsId": 'C' };
                if ($("#taskTableQueryForm .chooseOrgs").val() != "") {
                    var datas = [];
                    var names = $("#taskTableQueryForm .chooseOrgs").val().split(",");
                    var codes = $("#taskTableQueryForm input[name=belongDepartmentCode]").val().split(",");
                    var comps = $("#taskTableQueryForm input[name=companyTypeDictValue]").val().split(",");
                    for (var i in comps) {
                        var datai = {};
                        datai.id = codes[i];
                        datai.name = names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseOrgsVal = { "data": datas };
                } else {
                    top.chooseWeb.chooseOrgsVal = { "data": [] };
                }
                var url = tourl('html/choose/chooseCompanyII.html', href);
                top.dialogP(url, window.name, '选择组织', 'chooseOrgs', false, '800');
            });
            if (web.currentUser.belongCompanyTypeDictValue == '01' ||abol) {
                $('.c02').show()
            } else {
                $('.c01').show()
            }
            //分公司人员新增接口做个判断，控制是否显示c03
            if(web.currentUser.belongCompanyTypeDictValue == '02' &&!abol){
                ajaxgeneral({
                    url: 'action/index/judgeUser',
                    contentType: 'application/json; charset=utf-8',
                    success: function (res) { 
                        if(!res.data.isAdmin){
                            $('.c03').hide()
                        }
                    }
                });
            }




        });

        function abolFun(){
            for(var i in web.currentUser.authRoles){
                if(web.currentUser.authRoles[i].authority == 'djfupt_001'){
                    return abol = true
                }
            }
        }

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };

        //详情查看
        $(document).on('click', 'a.check', function () {
            var index = $(this).attr('index');
            var item = $('#taskTable').datagrid('getRows')[index];
            var location = item.activityDefId ? item.activityDefId : 'djfupt.start'
            var param = {
                processInstId: item.processInstId,
                id: item.id,
                source: "PC",
                pmInsId: item.pmInsId,
                location: location,
            }
            var url = tourl('html/query/faultsApply.html?workFlag=join&type=join', param)
            top.dialogP(url, window.name, '问题上报', 'groupCheck', true, 'maximized', 'maximized')
        })
        //修改
        $(document).on('click', 'a.hadminUpdata', function () {
            var index = $(this).attr('index');
            var item = $('#taskTable').datagrid('getRows')[index];
            var location = item.activityDefId ? item.activityDefId : 'djfupt.start'
            var param = {
                processInstId: item.processInstId,
                id: item.id,
                source: "PC",
                pmInsId: item.pmInsId,
                location: location,
            }
            var url = tourl('html/change/changeQuestion.html?type=draft&otherType=hadminUpdata', param)
            top.dialogP(url, window.name, '问题上报', 'groupCheck', true, 'maximized', 'maximized')
        })

        //选择组织
        window.chooseOrgs = function (data) {
            var names = [], codes = [], styles = [], comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
                comps.push(data.data[0].companyTypeDictValue);
            }
            $("#taskTableQueryForm input[name=belongDepartmentCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $("#taskTableQueryForm input[name=pmInsId]").val('C');
            $("#taskTableQueryForm input[name=companyTypeDictValue]").val(comps.join(","));
        };
        function companyFun(data) {
            $("#taskTableQueryForm input[name=companyCode]").val(data.value);
        }
        function dialogClosed(){
            top.dialogClose("groupCheck")
        }
    </script>
    <style>
        .date{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .td_flex {
            display: flex;
            align-items: center;
        }

        .td_pr5{
            padding-right: 5px;
        }
    </style>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <input id="belongDepartmentCode" name="belongDepartmentCode" type="hidden" />
        <input name="companyCode" type="hidden"/>
        <input name="pmInsId" type="hidden" value="C" />
        <input name="companyTypeDictValue" type="hidden" />
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr class="td_flex">



                <!-- test wdy -->
                <td width="80" align="right" class="c03 td_pr5">上报单位</td>
                <td width="200" class="c01 c03 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" style="width:100%; height: 32px;" readonly/>
                </td>
                <td width="200" class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width:100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>
                <td width="80" align="right" class="td_pr5">归属系统</td>
                <td width="200"><input name="appName" type="text" /></td>
                <td width="80" align="right" class="td_pr5">网格名称</td>
                <td width="200"><input name="gridName" type="text" /></td>
                <td width="90" align="right" class="td_pr5">上报时间</td>
                <td width="400" class="date">
                    <input id="startDate" name="startDate" type="text" class="easyui-datebox"
                           style="width:calc(45% - 10px);height:32px;" validType="startDateCheck['endDate','startDate']"
                           data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="endDate" name="endDate" type="text" class="easyui-datebox" style="width:calc(45% - 10px);height:32px;"
                           validType="endDateCheck['startDate','endDate']"
                           data-options="panelHeight:'auto', editable:false" />
                </td>
                <td width="1%"></td>
            </tr>
            <tr>
                <td colspan="9">
                    <a class="btn fr searchtable">
                        <font>查询</font>
                    </a>
                    <a class="btn fr mr10 exporttable">
                        <font>导出</font>
                    </a>
                </td>
                <td></td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>