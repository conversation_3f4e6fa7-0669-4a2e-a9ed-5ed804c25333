package com.simbest.boot.djfupt.index.web;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Lists;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;
import com.simbest.boot.djfupt.index.model.EcportVp;
import com.simbest.boot.djfupt.index.service.IndexService;
import com.simbest.boot.djfupt.policy.model.UsPolicyExport;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.AppFileUtil;
import com.simbest.boot.util.encrypt.Md5Encryptor;
import com.simbest.boot.util.http.BrowserUtil;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 首页相关接口
 */
@Slf4j
@RestController
@RequestMapping(value = "/action/index")
public class HomeController {

    @Autowired
    private IndexService service;

    @Autowired
    private AppFileUtil appFileUtil;

    @Autowired
    private ISysFileService fileService;
    @Getter
    public Charset charset = Charset.forName("GBK");

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private PaginationHelps paginationHelp;
    /**
     * 待办事项通知
     * @return
     */
    @ApiOperation(value = "待办事项通知", notes = "待办事项通知")
    @PostMapping(value = {"/todoItems", "/api/todoItems", "/todoItems/sso","/anonymous/todoItems"})
    public JsonResponse  todoItems(     @RequestParam (required = false)String currentUserCode,
                                        @RequestParam (required = false )String source) {
        operateLogTool.operationSource(source, currentUserCode);
        return  service.todoItems();
    }

    /**
     * 党的政策要求
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/queryPolicyInfo", "/api/queryPolicyInfo", "/queryPolicyInfo/sso","/anonymous/queryPolicyInfo"})
    public JsonResponse  queryPolicyInfo( @RequestBody(required = false) Map<String, Object> resultMap,
                                          @RequestParam (required = false)String currentUserCode,
                                          @RequestParam (required = false )String source){
        operateLogTool.operationSource(source, currentUserCode);
        return  service.queryPolicyInfo(resultMap);

    }





    /**
     * 导出工作写实
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/importExcel", "/api/importExcel", "/importExcel/sso","/anonymous/importExcel"})
    public ResponseEntity<?> importExcel(
                                         HttpServletResponse response,
                                         HttpServletRequest request,
                                         @RequestParam (required = false)String currentUserCode,
                                         @RequestParam (required = false )String source
                                    ){
        String  startTime=request.getParameter("startTime");
        String  endTime=request.getParameter("endTime");
        operateLogTool.operationSource(source, currentUserCode);
        return  service.importExcel(startTime,endTime,response,request);

    }


    /**
     * 工单查询
     * @param page
     * @param size

     * @return
     */
    @PostMapping(value = {"/workOrder","/workOrder/api","/workOrder/sso"})
    public JsonResponse workOrder( @RequestParam int page,
                                   @RequestParam int size,
                                   @RequestParam (required = false)String currentUserCode,
                                   @RequestParam (required = false )String source,
                                   @RequestBody  Map<String, Object> resultMap){
        String title=MapUtil.getStr(resultMap,"title");
        String pmInsType=MapUtil.getStr(resultMap,"pmInsType");
        String state=MapUtil.getStr(resultMap,"currentState");
        String startTime=MapUtil.getStr(resultMap,"startTime");
        String endTime=MapUtil.getStr(resultMap,"endTime");
        operateLogTool.operationSource(source, currentUserCode);
        return  service.workOrder(page,size,title,pmInsType,state,startTime,endTime);

    }


    @ApiOperation(value = "打包下载附件", notes = "打包下载附件")
    @PostMapping(value = {"/downLoadZip","/downLoadZip/api","/downLoadZip/sso"})
    public ResponseEntity<?> downLoad(HttpServletRequest request,
                                      @RequestParam String fileIds) throws IOException {
        Resource resource = null;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = df.format(new Date()) + "政策宣讲清单-宣讲事项附件";
        String[] id= fileIds.split(",");
        List<File> fileList = Lists.newArrayList();
        for(String s:id){
            SysFile sysFile = fileExtendService.findById(s);
            if (StringUtils.isNotEmpty(sysFile.getId())) {
                File file = sysFileService.getRealFileById(sysFile.getId());//fastdfs
                fileList.add(file);
            }
        }


        try {
            File zipFile = appFileUtil.createTempFileWithName(fileName.concat(".zip"));
            ZipUtil.zip(zipFile, charset, true, fileList.toArray(new File[]{}));
            String fileName1 = URLEncoder.encode(fileName.concat(".zip"), ApplicationConstants.UTF_8);
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=\"" + fileName1 + "\"");
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            resource = new InputStreamResource(new FileInputStream(zipFile));
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return ResponseEntity.ok().headers(headers).body(resource);
    }

    @PostMapping(value = {"/exportParameter", "/api/exportParameter", "/sso/exportParameter"})
    public void exportParameter(
                                @RequestParam (required = false)String currentUserCode,
                                @RequestParam (required = false )String source,
                                @RequestParam (required = false )String title,
                                @RequestParam (required = false )String pmInsType,
                                @RequestParam (required = false )String currentState,
                                @RequestParam (required = false )String startTime,
                                @RequestParam (required = false )String endTime,
                                HttpServletRequest request,
                                HttpServletResponse response) {
        operateLogTool.operationSource(source, currentUserCode);
        service.exportParameter(title,pmInsType,currentState,startTime,endTime,request,response);
    }


    /**
     * 数据总览
     * @param page
     * @param size
     * @param currentUserCode
     * @param source
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/dataScreening", "/api/dataScreening", "/sso/dataScreening"})
    public JsonResponse  dataScreening(@RequestParam int page,
                                       @RequestParam int size,
                                       @RequestParam (required = false)String currentUserCode,
                                       @RequestParam (required = false )String source,
                                       @RequestBody  Map<String, Object> resultMap){
        operateLogTool.operationSource(source, currentUserCode);
        IUser user= SecurityUtils.getCurrentUser();
        List<DataScreeningVo> dataScreeningVos=new ArrayList<>();
        if(user.getBelongCompanyTypeDictValue().equals("01")){
            dataScreeningVos= service.dataScreening(resultMap);
        }else {
            dataScreeningVos= service.dataScreeningBranch(resultMap);
        }
        Pageable pageable = paginationHelp.getPageable(page, size, "", "");
        if (dataScreeningVos != null) {
            long totalRecords = dataScreeningVos.size();
            dataScreeningVos = PageTool.pagination(dataScreeningVos, page, size);
            Page pageInfo = new PageImpl<>(dataScreeningVos, pageable, totalRecords);

            return JsonResponse.success(pageInfo);
        }
            return  JsonResponse.success(dataScreeningVos);


    }



    @PostMapping(value = {"/exportDataScreening", "/api/exportDataScreening", "/exportDataScreening/soo"})
    public void exportRisk(@RequestParam(required = false) String currentUserCode,
                           @RequestParam (required = false )String source,
                           HttpServletRequest request,
                           HttpServletResponse response,
                           EcportVp ecportVp

    ) throws Exception {
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("companyName",ecportVp.getCompanyName());
        resultMap.put("time",ecportVp.getTime());
        resultMap.put("startTime",ecportVp.getStartTime());
        resultMap.put("endTime",ecportVp.getEndTime());
        operateLogTool.operationSource(source, currentUserCode);
        service.exportDataScreening( request, response, resultMap);
    }


    @PostMapping(value = {"/judgeUser", "/api/judgeUser", "/judgeUser/soo"})
    public JsonResponse judgeUser(@RequestParam(required = false) String currentUserCode,
                                  @RequestParam (required = false )String source){
        operateLogTool.operationSource(source, currentUserCode);
     return    service.judgeUser();
    }



}
