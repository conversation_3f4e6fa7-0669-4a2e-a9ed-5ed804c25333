package com.simbest.boot.djfupt.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public class CreatNumUtil {

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    /**
     * 流程数据
     * -- 业务流程id
     * create sequence DJFUPT_SEQUENCE_PROCESS_ID
     * minvalue 1
     * maxvalue 1499999
     * start with 1
     * increment by 1
     * cache 20;
     *
     * -- 流程实例id
     * create sequence DJFUPT_SEQUENCE_WORKITEM_ID
     * minvalue 1
     * maxvalue 4999999
     * start with 1
     * increment by 1
     * cache 20;
     *
     *
     */

    private static final String processSequenceUatSql = "select  DJFUPT_SEQUENCE_PROCESS_ID.nextval FROM dual";
    private static final String workItemSequenceUatSql = "select   DJFUPT_SEQUENCE_WORKITEM_ID.nextval FROM dual";

    private static final String PROCESS_TYPE = "1";

    /**
     * @param type
     * @return
     */
    public Long autoIncrementNum(String type ) {
        Long num = null;
        String sql = workItemSequenceUatSql;
        if (StrUtil.equals(type , PROCESS_TYPE )) {
            sql = processSequenceUatSql;
        }
        List<Map<String, Object>> mapList = customDynamicWhere.queryForList(sql, null);
        if (CollectionUtil.isNotEmpty(mapList)) {
            Map<String, Object> map = mapList.get(0);
            num = MapUtil.getLong(map , "NEXTVAL");
        }
        return num;
    }
}
