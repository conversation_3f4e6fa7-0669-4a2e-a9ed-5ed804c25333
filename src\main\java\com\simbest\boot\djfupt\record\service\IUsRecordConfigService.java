package com.simbest.boot.djfupt.record.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IUsRecordConfigService extends ILogicService<UsRecordConfig, String> {

    /**
     * 展示所有思政配置信息
     * @param resultMap
     * @return
     */
    List<Map<String,Object>> findAllInfo(Map<String, Object> resultMap);


    /**
     * 下载模板
     * @return
     */
    void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     *  导入思政配置数据
     * @param request
     * @param response
     */
    void importInfo(HttpServletRequest request, HttpServletResponse response);

}
