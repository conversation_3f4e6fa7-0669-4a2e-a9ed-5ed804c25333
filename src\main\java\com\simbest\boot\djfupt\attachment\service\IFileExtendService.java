package com.simbest.boot.djfupt.attachment.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.sys.model.SysFile;

import java.util.List;

/**
 * 作用：附件扩展类
 */
public interface IFileExtendService extends ILogicService<SysFile,String> {

    /**
     * 更新附件
     * @param pmInsId 主单据id
     * @param pmInsType 流程类型
     * @param id 附近id
     * @return
     */
    int updatePmInsId(String pmInsId, String pmInsType, String id);


    /**
     * 查询区域附件
     * @param pmInsId
     * @param filePart
     * @return
     */
    List<SysFile> getPartFile(String pmInsId, String filePart);

    List<SysFile> queryFile(String pmInsId);

    List<SysFile> getFileByPmInsId(String pmInsId);

    void updateAnnexFile(String pmInsId, List<SysFile> annex);
}
