package com.simbest.boot.djfupt.stat.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * <strong>Title : VisitorStat</strong><br>
 * <strong>Description : 访客统计分析实体类</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity(name = "us_visitor_stat")
@ApiModel(value = "访客统计分析")
public class VisitorStat extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "VSST")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(value = "用户名称")
    private String username;

    @Column(length = 100)
    @ApiModelProperty(value = "标题")
    private String title;

    @Column(length = 20)
    @ApiModelProperty(value = "每条数据消息条数")
    private Integer msgNum;

    @Column(length = 20)
    @ApiModelProperty(value = "总条数")
    private Integer sumCount;

    @Column(length = 200)
    @ApiModelProperty(value = "所属单位名称")
    private String belongCompanyName;

    @Column(length = 200)
    @ApiModelProperty(value = "上级单位名称")
    private String belongCompanyNameParent;

    @Column(length = 200)
    @ApiModelProperty(value = "所属部门名称")
    private String belongDepartmentName;

    @Column(length = 200)
    @ApiModelProperty(value = "所属组织名称")
    private String belongOrgName;

    @Column(length = 50)
    @ApiModelProperty(value = "会话ID")
    private String conversationId;

    @Column(length = 50)
    @ApiModelProperty(value = "消息ID")
    private String messageId;

    @Column
    @ApiModelProperty(value = "点赞数")
    private Integer likeNum;

    @Column(name = "dislike")
    @ApiModelProperty(value = "点踩数")
    private Integer dislike;

    @Column
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "业务创建时间")
    private LocalDateTime createdAt;

    @Column
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "业务修改时间")
    private LocalDateTime updatedAt;

}
