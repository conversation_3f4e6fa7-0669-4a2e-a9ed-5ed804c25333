package com.simbest.boot.djfupt.wfquey.service;

import com.simbest.boot.base.service.IGenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import org.springframework.data.domain.Page;

/**
 * 用途：查询待办已办 service层
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
public interface IQueryActBusinessStatusService extends IGenericService<ActBusinessStatus, Long> {

    /**
     * 查询我的待办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    JsonResponse myTaskToDo(Integer pageindex, Integer pagesize, String title, String workCode, String source, String userCode);





    /**
     * 查询我的待办党建大屏使用
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    JsonResponse myTaskToDoDj(Integer pageindex, Integer pagesize, String title, String workCode, String source, String userCode);

    /**
     * 查询我的已办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    JsonResponse queryMyJoin(Integer pageindex, Integer pagesize, String title,String workCode, String source, String userCode);

    /**
     * 查询我的申请
     *
     * @param pageindex
     * @param pagesize
     * @param title
     * @return
     */
    Page<ActBusinessStatus> queryMyApply(Integer pageindex, Integer pagesize, String title);

    /**
     * 查询我的待阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    JsonResponse queryMyPending(Integer pageindex, Integer pagesize, String title, String source, String userCode);

    /**
     * 查询我的已阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    JsonResponse queryMyRead(Integer pageindex, Integer pagesize, String title, String source, String userCode);

    /**
     * 查询草稿
     * @param page  页码
     * @param rows  数量
     * @param title 标题
     * @param source 来源
     * @param currentUserCode OA账号
     * @return
     */
    JsonResponse myDraftToDo(Integer page, Integer rows, String title, String source, String currentUserCode);

}
