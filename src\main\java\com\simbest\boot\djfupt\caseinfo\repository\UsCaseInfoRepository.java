package com.simbest.boot.djfupt.caseinfo.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface UsCaseInfoRepository extends LogicRepository<UsCaseInfo, String> {

    @Transactional
    @Query(
            value = "select a.* from US_CASE_INFO a,us_pm_instence p where a.enabled = 1 and a.pm_ins_id = p.pm_ins_id and p.id=:id",
            nativeQuery = true
    )
    UsCaseInfo getFromDetail(@Param("id") String id);

    /**
     * 根据pmInsId查询业务单据信息
     *
     * @param pmInsId
     * @return
     */
    @Transactional
    @Query(
            value = "select a.* from US_CASE_INFO a where a.enabled = 1 and a.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    UsCaseInfo getFormDetailByPmInsId(@Param("pmInsId") String pmInsId);

    @Query(
            value = "select t.belong_department_code" +
                    "  from uums.SYS_ORG_INFO_FULL t" +
                    " where (t.parent_org_code = :departmentCode and" +
                    "       t.company_type_dict_value = '03' and t.enabled = 1)" +
                    "    or t.org_code = :departmentCode ",
            nativeQuery = true
    )
    List<String> findALlBydepartMentCode(@Param("departmentCode") String departmentCode);

    @Query(
            value = "select t.*\n" +
                    "  from uums.SYS_ORG_INFO_FULL t" +
                    " where (t.style_dict_value = 02 and t.org_name like '%分公司%')" +
                    "    or (t.level_dict_value = 1 and t.company_type_dict_value = 01) order by t. level_dict_value asc",
            nativeQuery = true
    )
    List<Map<String,String>> findAllByOrg();

    //查询月份下当前人有没有起草工单
    @Query(
            value = " select t.* " +
                    "  from US_CASE_INFO t " +
                    " where t.enabled = 1 " +
                    "   and t.creator =:creator " +
                    "   and to_CHAR(t.created_time, 'YYYY') = :year   ",
            nativeQuery = true
    )
    List<UsCaseInfo> findAllByCreatorAndYear(@Param("year") int year,
                                                @Param("creator") String creator);





    @Query(
            value = "    select count(1)" +
                    "  from US_CASE_INFO t, act_business_status act" +
                    " where t.enabled = 1 and act.enabled = 1 and act.current_state = 7" +
                    "   and t.pm_ins_id = act.receipt_code" +
                    "   and to_char(t.created_time,'yyyy-MM-dd hh:mm:ss') >=:startTime" +
                    "   and to_char(t.created_time,'yyyy-MM-dd hh:mm:ss') <=:endTime" +
                    "   and t.question_mode = 0",
            nativeQuery = true
    )
    int findAllByEnabledAndQuestionMode(@Param("startTime") String startTime,
                                        @Param("endTime") String endTime);


    @Query(
            value = " select t.* " +
                    "  from US_CASE_INFO t  , act_business_status act" +
                    " where t.enabled = 1  and act.current_state = 7 " +
                    "   and t.pm_ins_id = act.receipt_code" +
                    "   and t.creator =:creator " +
                    "   and to_CHAR(t.created_time, 'YYYY') = :year   ",
            nativeQuery = true
    )
    List<UsCaseInfo> findAllByCreatorAndYearAndAct(@Param("year") int year,
                                             @Param("creator") String creator);

}
