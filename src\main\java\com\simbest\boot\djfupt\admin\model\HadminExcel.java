package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @data 2022/11/25 0025 17:24
 */
@Data
public class HadminExcel {



    @Column(length = 20)
    @ApiModelProperty(value = "OA账号")
    @ExcelVOAttribute(name = "OA账号", column = "A")
    private String userName;

    @Column(length = 20)
    @ApiModelProperty(value = "管理员姓名")
    @ExcelVOAttribute(name = "管理员姓名", column = "B")
    private String trueName;

    @ExcelVOAttribute(name ="省公司党办管理员/分公司党办管理员" , column = "C")
    private String HadminType;   //

}
