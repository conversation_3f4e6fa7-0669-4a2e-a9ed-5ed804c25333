package com.simbest.boot.djfupt.xtgj.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.ImmutableMap;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.service.impl.UsAdminManagerServiceImpl;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.xtgj.model.UsZwrgdyInfo;
import com.simbest.boot.djfupt.xtgj.model.XtgjDictValueVO;
import com.simbest.boot.djfupt.xtgj.repository.UsZwrgdyInfoRepository;
import com.simbest.boot.djfupt.xtgj.service.IUsZwrgdyInfoService;
import com.simbest.boot.security.IRole;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class UsZwrgdyInfoServiceImpl extends LogicService<UsZwrgdyInfo, String> implements IUsZwrgdyInfoService {

    private final SysDictValueService sysDictValueService;
    private final UsAdminManagerServiceImpl usAdminManagerService;
    private UsZwrgdyInfoRepository usZwrgdyInfoRepository;

    public UsZwrgdyInfoServiceImpl(UsZwrgdyInfoRepository usZwrgdyInfoRepository, SysDictValueService sysDictValueService, UsAdminManagerServiceImpl usAdminManagerService) {
        super(usZwrgdyInfoRepository);
        this.usZwrgdyInfoRepository = usZwrgdyInfoRepository;
        this.sysDictValueService = sysDictValueService;
        this.usAdminManagerService = usAdminManagerService;
    }

    @Value("${app.file.upload.path}")
    private String UPLOAD_PATH;

    @Override
    public UsZwrgdyInfo insertInfo(UsZwrgdyInfo o) {
        IUser user = SecurityUtils.getCurrentUser();
        o.setUsername(user.getUsername());
        o.setTruename(user.getTruename());
        o.setPhone(user.getPreferredMobile());
        BelongInfoTool.setBelongCompanyAndDepartment(o);

        o.setOperationInfo(this.list2StringByOperationInfo(o.getOperationList()));
        o.setRemarkInfo(this.list2StringByRemarkInfo(o.getOperationList()));

        return super.insert(o);
    }

    @Override
    public UsZwrgdyInfo updateInfo(UsZwrgdyInfo o) {
        IUser user = SecurityUtils.getCurrentUser();
        o.setUsername(user.getUsername());
        o.setTruename(user.getTruename());
        o.setPhone(user.getPreferredMobile());
        BelongInfoTool.setBelongCompanyAndDepartment(o);

        o.setOperationInfo(this.list2StringByOperationInfo(o.getOperationList()));
        o.setRemarkInfo(this.list2StringByRemarkInfo(o.getOperationList()));

        return super.update(o);
    }

    @Override
    public UsZwrgdyInfo findByIdInfo(String id) {
        UsZwrgdyInfo byId = super.findById(id);

        byId.setOperationList(this.string2List(byId.getOperationInfo(), byId.getRemarkInfo()));

        return byId;
    }

    /**
     * 通用查询列表
     */
    public List<UsZwrgdyInfo> findAllInfoList(UsZwrgdyInfo o) {
        o = Optional.ofNullable(o).orElse(new UsZwrgdyInfo());

        // 获取查询条件
        Specification<UsZwrgdyInfo> and = Specifications.<UsZwrgdyInfo>and()
                .eq(StringUtils.isNotBlank(o.getBelongCompanyCode()), "belongCompanyCode", o.getBelongCompanyCode())
                .eq(StringUtils.isNotBlank(o.getBelongDepartmentCode()), "belongDepartmentCode", o.getBelongDepartmentCode())
                .eq(StringUtils.isNotBlank(o.getBelongOrgCode()), "belongOrgCode", o.getBelongOrgCode())
                .like(StringUtils.isNotBlank(o.getGridName()), "gridName", String.format("%%%s%%", o.getGridName()))
                .like(StringUtils.isNotBlank(o.getBelongOrgName()), "belongOrgName", String.format("%%%s%%", o.getBelongOrgName()))
                .like(StringUtils.isNotBlank(o.getTruename()), "truename", String.format("%%%s%%", o.getTruename()))
                .ge(Objects.nonNull(o.getSdate()), "createdTime", o.getSdate())
                .le(Objects.nonNull(o.getEdate()), "createdTime", o.getEdate())
                .build();
        IUser user = SecurityUtils.getCurrentUser();
        Set<? extends IRole> roles = user.getAuthRoles();
        // 省公司党建管理员
        if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_PRO) || Objects.equals(v.getId(), Constants.FJFUPT_PRO))) {
        } else if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_City) || Objects.equals(v.getId(), Constants.FJFUPT_City))) {
            // 分公司党建管理员
            and = Specifications.<UsZwrgdyInfo>and()
                    .eq("belongCompanyCode", user.getBelongCompanyCode()).build()
                    .and(and);
        } else if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_BRO) || Objects.equals(v.getId(), Constants.FJFUPT_BRO))) {
            // 党建管理员
            and = Specifications.<UsZwrgdyInfo>and()
                    .eq("username", user.getUsername()).build()
                    .and(and);
        }

        return super.findAllNoPage(and, Sort.by(Sort.Direction.DESC, "createdTime"));
    }

    /**
     * 信息查询 通用分页
     * <br/>params [o, pageable]
     */
    @Override
    public Page<UsZwrgdyInfo> findAllInfo(UsZwrgdyInfo o, Pageable pageable) {
        List<UsZwrgdyInfo> list = this.findAllInfoList(o);

        return PageTool.getPage(list, pageable);
    }

    /**
     * 信息导出
     * <br/>params [o, pageable]
     */
    @Override
    public void exportInfo(HttpServletRequest request, HttpServletResponse response, UsZwrgdyInfo o) {
        List<UsZwrgdyInfo> list = this.findAllInfoList(o);
        if (CollectionUtils.isEmpty(list)) return;

        String modelPath = "model/装维入格调研台账导出模版.xls";
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(modelPath);
             Workbook workbook = new HSSFWorkbook(in);) {
            Sheet sheet = workbook.getSheetAt(0);

            //设置数据列数据格式
            CellStyle dataStyle = getCellStyle(workbook);
            // 相关数据写入
            int rowNum = 3;
            for (UsZwrgdyInfo info : list) {
                Row row = CellUtil.getRow(rowNum++, sheet);

                row.createCell(0).setCellValue(info.getBelongCompanyName());
                row.createCell(1).setCellValue(info.getBelongDepartmentName());
                row.createCell(2).setCellValue(info.getTruename());
                row.createCell(3).setCellValue(info.getPhone());
                row.createCell(4).setCellValue(info.getGridName());
                row.createCell(5).setCellValue(info.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                info.setOperationList(this.string2List(info.getOperationInfo(), info.getRemarkInfo()));

                row.createCell(6).setCellValue(info.getOperationList().get(0).getOperationInfo());
                row.createCell(7).setCellValue(StringUtils.defaultString(info.getOperationList().get(0).getRemarkInfo()));
                row.createCell(8).setCellValue(info.getOperationList().get(1).getOperationInfo());
                row.createCell(9).setCellValue(StringUtils.defaultString(info.getOperationList().get(1).getRemarkInfo()));
                row.createCell(10).setCellValue(info.getOperationList().get(2).getOperationInfo());
                row.createCell(11).setCellValue(StringUtils.defaultString(info.getOperationList().get(2).getRemarkInfo()));

                // 处理样式
                row.setHeightInPoints(60f);
                IntStream.range(0, 12).forEach(v -> Optional.ofNullable(row.getCell(v)).ifPresent(op -> op.setCellStyle(dataStyle)));
            }

            // # 输出到新文件，保持模板不变
            // 临时文件目录
            String dataDir = UPLOAD_PATH + "/export/";
            String filename = String.format("%s_%s.xls", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE), "装维入格调研台账导出信息");
            File file = new File(String.format("%s\\%s", dataDir, filename));
            FileUtils.touch(file);
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
            }
            FileTool.download(file.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 获取网格名称
     */
    @Override
    public JsonResponse getGridName() {
        String currentUserName = SecurityUtils.getCurrentUserName();
        String gridName = usAdminManagerService.findAllNoPage(Specifications.<UsAdminManager>and().eq("userName", currentUserName).build())
                .stream().findFirst().map(UsAdminManager::getGridName).orElse("");

        return JsonResponse.success(ImmutableMap.of(
                "userName", currentUserName,
                "gridName", gridName
        ));
    }

//    /**
//     * 获取字段间的分隔符与连接符
//     * <br/> params []
//     *
//     * @return {@link JsonResponse}
//     * <AUTHOR>
//     * @since 2024/10/8 11:52
//     */
//    @Override
//    public JsonResponse getSplitSign() {
//        ImmutableMap<Object, Object> map = ImmutableMap.of(
//                "connect", Constants.XTGJ_CONNECT_SIGN,
//                "separate", Constants.XTGJ_SEPARATE_SIGN,
//                "存储规则", " '字典name' + '连接符:' + '对应操作1/0' + '分隔符!@' ",
//                "操作字段存储示例", String.format("A%s1%sB%s0%sC%s1", Constants.XTGJ_CONNECT_SIGN, Constants.XTGJ_SEPARATE_SIGN, Constants.XTGJ_CONNECT_SIGN, Constants.XTGJ_SEPARATE_SIGN, Constants.XTGJ_CONNECT_SIGN),
//                "备注字段存储示例", String.format("A%sxxx%sB%sxxx%sC%sxxx", Constants.XTGJ_CONNECT_SIGN, Constants.XTGJ_SEPARATE_SIGN, Constants.XTGJ_CONNECT_SIGN, Constants.XTGJ_SEPARATE_SIGN, Constants.XTGJ_CONNECT_SIGN)
//        );
//        return JsonResponse.success(map);
//    }

    /**
     * 数据转换
     */
    private String list2StringByOperationInfo(List<XtgjDictValueVO> list) {
        return list.stream().map(v -> String.format("%s%s%s", v.getDictId(), Constants.XTGJ_CONNECT_SIGN, v.getOperationInfo()))
                .collect(Collectors.joining(Constants.XTGJ_SEPARATE_SIGN));
    }

    /**
     * 数据转换
     */
    private String list2StringByRemarkInfo(List<XtgjDictValueVO> list) {
        return list.stream().map(v -> String.format("%s%s%s", v.getDictId(), Constants.XTGJ_CONNECT_SIGN, StringUtils.defaultString(v.getRemarkInfo())))
                .collect(Collectors.joining(Constants.XTGJ_SEPARATE_SIGN));
    }

    /**
     * 数据转换
     */
    private List<XtgjDictValueVO> string2List(String operationInfo, String remarkInfo) {
        List<XtgjDictValueVO> collect = Arrays.stream(operationInfo.split(Constants.XTGJ_SEPARATE_SIGN)).map(v -> {
            String[] split = v.split(Constants.XTGJ_CONNECT_SIGN);
            XtgjDictValueVO vo = new XtgjDictValueVO();
            vo.setDictId(split[0]);
            vo.setOperationInfo(split[1]);
            return vo;
        }).collect(Collectors.toList());

        List<XtgjDictValueVO> collect1 = Arrays.stream(remarkInfo.split(Constants.XTGJ_SEPARATE_SIGN)).map(v -> {
            String[] split = v.split(Constants.XTGJ_CONNECT_SIGN);
            XtgjDictValueVO vo = new XtgjDictValueVO();
            vo.setDictId(split[0]);
            if (split.length == 1) {
                vo.setRemarkInfo("");
            } else {
                vo.setRemarkInfo(split[1]);
            }
            return vo;
        }).collect(Collectors.toList());

        List<SysDictValue> dictValues = sysDictValueService.findByDictType(Constants.XTGJ_ZWRGDY_TYPE);
        collect.forEach(v1 -> {
            collect1.forEach(v2 -> {
                if (v1.getDictId().equals(v2.getDictId())) {
                    v1.setRemarkInfo(v2.getRemarkInfo());
                }
            });
            dictValues.forEach(v2 -> {
                if (v1.getDictId().equals(v2.getId())) {
                    v1.setDictName(v2.getName());
                    v1.setDictValue(v2.getValue());
                }
            });
        });

        return collect;
    }

    /**
     * 获取导出单元格样式
     *
     * @param workbook 工作薄
     * @return 单元格样式
     */
    public static CellStyle getCellStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SKY_BLUE.getIndex());
        dataStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.GREY_40_PERCENT.getIndex());
        dataStyle.setBorderBottom(BorderStyle.THIN); //下边框
        dataStyle.setBorderLeft(BorderStyle.THIN);//左边框
        dataStyle.setBorderTop(BorderStyle.THIN);//上边框
        dataStyle.setBorderRight(BorderStyle.THIN);//右边框
        dataStyle.setAlignment(HorizontalAlignment.CENTER); // 左右居中
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        dataStyle.setWrapText(true); // 自动换行
        Font dataFont = workbook.createFont();
        dataFont.setFontName("华文中宋");
        dataFont.setFontHeightInPoints((short) 10); //字体大小
        dataStyle.setFont(dataFont);
        return dataStyle;
    }

}
