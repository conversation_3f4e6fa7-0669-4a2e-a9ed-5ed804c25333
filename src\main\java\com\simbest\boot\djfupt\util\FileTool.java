package com.simbest.boot.djfupt.util;

import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Description 文件操作工具类
 * ClassName: FileTool.java
 * <AUTHOR>
 * @date 上午10:23:41
 */
public final class FileTool {

    private static final int MAXBYTES_ONETIME = 100;// 下载缓存大小

    private FileTool(){}

    /**
     * @Description: 保存文件到本地
     * @param  @param file
     * @param  @param request
     * @param  @throws Exception
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2016年6月2日
     */
    static public void saveFile(MultipartFile file,File targetFile) throws Exception{
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        try {
            // 保存
            file.transferTo(targetFile);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * @Description 判断文件是否存在，存在的话删除
     * @param path  文件路径
     * @throws Exception
     */
    static public boolean delFile(String path)throws Exception{
        try {
            // path是指欲下载的文件的路径。
            File file = new File(path);
            if(file.isFile()){
                if (file.exists()){
                    //file.delete();
                    return true;
                }
            }
        }catch ( Exception e ){
            throw e;
        }
        return false;
    }

    /**
     * @Description: 用io流的形式从服务器上下载文件
     * @param  @param path
     * @param  @param response
     * @return void
     * @throws IOException
     * @throws
     * <AUTHOR>
     * @date 2016年6月12日
     */
    static public void download(String path, HttpServletResponse response) throws IOException {
        OutputStream toClient = null;
        InputStream fis = null;
        try {
            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();
            // 取得文件的后缀名。
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(path));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            //response.addHeader("Content-Disposition","attachment;filename="+ new String(filename.getBytes("utf-8"), "ISO-8859-1"));
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setBufferSize(100 * 1024);
            //response.addHeader("Content-Disposition","attachment;filename="+ Encodes.urlEncode(filename));
            response.addHeader("Content-Disposition","attachment;filename="+ new String(filename.getBytes("GBK"), "ISO-8859-1"));
            response.addHeader("Content-Length", "" + file.length());
            toClient = new BufferedOutputStream(response.getOutputStream());
            toClient.write(buffer);
            toClient.flush();
        } catch (IOException ex) {
            //ex.printStackTrace();
            throw ex;
        }finally {
            try  {
                toClient.close();
                fis.close();
            } catch (IOException e) {}
        }
    }

    /**
     * @Description: 用io流的形式从服务器上下载文件
     * @param  @param path
     * @param  @param response
     * @return void
     * @throws IOException
     * @throws
     * <AUTHOR>
     * @date 2016年6月12日
     */
    static public void downloadHelp(String path,String filename,HttpServletResponse response) throws IOException {
        response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setBufferSize(100 * 1024);
        response.setHeader("Cache-Control", "public");
        response.addHeader("Content-Disposition","attachment;filename="+ new String(filename.getBytes("utf-8"), "ISO-8859-1"));
        OutputStream os = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(path);
            os = response.getOutputStream();
            byte[] buf = new byte[MAXBYTES_ONETIME];
            int len = 0;
            while ((len = fis.read(buf)) > 0) {
                os.write(buf, 0, len);
            }
        } catch (Exception e) {
            // 如果遇到客户端中断的异常则不进行处理(否则下载中途客户取消会抛出异常),否则才打印信息
            e.printStackTrace();
            if (!"org.apache.catalina.connector.ClientAbortException".equals(e.getClass().getName())) {
                e.printStackTrace();
            }
        } finally {
            try {
                os.close();
                fis.close();
            } catch (IOException e) {
            }
        }
    }

    static public void downloadIn(InputStream in,String filename,HttpServletResponse response) throws IOException {
        response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setBufferSize(100 * 1024);
        response.setHeader("Cache-Control", "public");
        response.addHeader("Content-Disposition","attachment;filename="+ new String(filename.getBytes("GBK"), "ISO-8859-1"));
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            byte[] buf = new byte[MAXBYTES_ONETIME];
            int len = 0;
            while ((len = in.read(buf)) > 0) {
                os.write(buf, 0, len);
            }
        } catch (Exception e) {
            // 如果遇到客户端中断的异常则不进行处理(否则下载中途客户取消会抛出异常),否则才打印信息
            e.printStackTrace();
            if (!"org.apache.catalina.connector.ClientAbortException".equals(e.getClass().getName())) {
                e.printStackTrace();
            }
        } finally {
            try {
                os.close();
                in.close();
            } catch (IOException e) {
            }
        }
    }

    public static ResponseEntity<?> download(File sysFile) throws FileNotFoundException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        headers.setContentType( MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition cd = ContentDisposition.builder("attachment").filename(sysFile.getName(), StandardCharsets.UTF_8).size(102400L).build();
        headers.setContentDisposition(cd);
        Resource resource = new InputStreamResource(new FileInputStream(new File(sysFile.getPath())));
        return ((ResponseEntity.BodyBuilder )ResponseEntity.ok().headers(headers)).body(resource);
    }

    /**
     * 把图片的id取出来,放入字段中
     * @param sysFileList 文件列表
     * @return
     */
    public static String findFileIds(List<SysFile> sysFileList){
        StringBuffer fileId = new StringBuffer(  );
        int num = 0;
        for(SysFile sysFile:sysFileList){
            fileId.append( sysFile.getId() );
            if(num<sysFileList.size()-1){
                fileId.append( ApplicationConstants.COMMA );
            }
        }
        return fileId.toString();
    }




    /**
     * @Description: 用io流的形式从服务器上下载文件
     * @param  @param path
     * @param  @param response
     * @return void
     * @throws IOException
     * @throws
     * <AUTHOR>
     * @date 2016年6月12日
     */
    static public void download(String path, HttpServletRequest request , HttpServletResponse response) throws IOException {
        OutputStream toClient = null;
        InputStream fis = null;
        try {
            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();
            // 取得文件的后缀名。
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(path));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            //response.addHeader("Content-Disposition","attachment;filename="+ new String(filename.getBytes("utf-8"), "ISO-8859-1"));
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setBufferSize(100 * 1024);
            //response.addHeader("Content-Disposition","attachment;filename="+ Encodes.urlEncode(filename));
            String newFileName = new String(filename.getBytes("utf-8"), "ISO-8859-1");
            String userAgent = request.getHeader("user-agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
                // win10 ie edge 浏览器 和其他系统的ie
                newFileName = URLEncoder.encode(filename, "UTF-8");
            }
            response.addHeader("Content-Disposition","attachment;filename="+ newFileName );
            response.addHeader("Content-Length", "" + file.length());
            toClient = new BufferedOutputStream(response.getOutputStream());
            toClient.write(buffer);
            toClient.flush();
        } catch (IOException ex) {
            //ex.printStackTrace();
            throw ex;
        }finally {
            try  {
                toClient.close();
                fis.close();
            } catch (IOException e) {}
        }
    }
}
