package com.simbest.boot.djfupt.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <strong>Title : ExternalApiConfig</strong><br>
 * <strong>Description : 外部API配置类</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "app.external.api")
public class ExternalApiConfig {

    /**
     * 智能对话API基础地址
     */
    @Value("${api.conversationBaseUrl}")
    private String conversationBaseUrl ;
//    private String conversationBaseUrl = " http://*************:4000";
//    private String conversationBaseUrl = "http://************:8088";

    /**
     * 获取消息历史接口路径
     */
    private String messagesPath = "/v1/messages";



    /**
     * API超时时间（毫秒）
     */
    private int timeout = 30000;

    /**
     * 默认查询消息数量限制
     */
    private int defaultLimit = 100;

    /**
     * 获取完整的消息API地址
     * @return 完整的API地址
     */
    public String getMessagesUrl() {
        return conversationBaseUrl + messagesPath;
    }
}
