package com.simbest.boot.djfupt.process.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @projectName dyytdb
 * @className: ApplicationForm
 * @description: 流程流转表单控制层
 * <AUTHOR>
 * @date 2019/7/18 8:55s
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "sys_process_info")
@Table(appliesTo = "sys_process_info" , comment = "流程信息表")
@ApiModel(description = "流程信息表")
public class SysProcessInfo extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "Y") //主键前缀
    private  String id;

    @ApiModelProperty(value = "流程表id")
    @Column(length = 100)
    private String processDefId;

    @ApiModelProperty(value = "流程表名称")
    @Column(length = 200)
    private String processDefName;

    @ApiModelProperty(value = "来源环节编码")
    @Column(length = 100)
    private String fromActivityId;

    @ApiModelProperty(value = "来源环节名称")
    @Column(length = 200)
    private String fromActivityName;

    @ApiModelProperty(value = "连线条件")
    @Column(length = 100)
    private String outcome;

    @ApiModelProperty(value = "连线规则名称")
    @Column(length = 200)
    private String outcomeShowName;

    @ApiModelProperty(value = "目标环节编码")
    @Column(length = 100)
    private String targetActivityId;

    @ApiModelProperty(value = "目标环节名称")
    private String targetActivityName;

}
