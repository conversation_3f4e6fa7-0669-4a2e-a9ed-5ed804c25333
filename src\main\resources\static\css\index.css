/**index**/
html{overflow-x:hidden;overflow-y:auto;width:100%;height:100%;}
a, a:link, a:visited ,a:hover{text-decoration: none;}

/**top**/
.top{width:100%;height:60px;}
.top_left{width:auto;height:50px;padding:11px 0 0;#height:39px;*height:39px;float:left;position:relative;z-index:6;}
.top_left_top{width:100%;height:39px;float:left;padding:0px 0px 0px 9px;}
.top_left_top img{width:auto;height:39px;line-height:39px;}
.top_left_top h1{height:39px;line-height:35px;font-size:30px;font-weight:bold;color:#9ea6b9;margin:0px 0px 0px 12px;padding:0px 0px 0px 12px;border-left:1px solid #9ea6b9;overflow:hidden;}
.top_right{min-width:270px;height:48px;float:right;padding:6px 18px 0px 0px;#height:62px;*height:62px;}
.top_right img{width:47px;height:47px;float:left;#width:43px;*width:43px;#height:43px;*height:43px;-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;border:2px solid #9ea6b9;}
.top_right strong{min-width:150px;height:46px;#height:18px;*height:18px;float:left;line-height:18px;padding:14px 3px 14px 8px;font-weight:normal;color:#9ea6b9;}
.top_right i{width:18px;height:18px;float:left;color:#9ea6b9;font-size:16px;margin:14px 0px 0px;cursor:pointer;}
.top_right span{width:1px;height:18px;float:left;border-left:1px dotted #9ea6b9;margin:14px 8px 0px;}
/**center**/
.center{width:100%;position:relative;z-index:6;background:#39aef5;}
.left{width:218px;#width:216px;*width:216px;float:left;position:absolute;z-index:8;top:0px;left:0px;overflow:hidden;padding:0px 2px 0 0;}
.left_menu{width:100%;height:auto;float:left;position:relative;z-index:6;background:#2f4050;}
ul.nav1{width:100%;float:left;}
ul.nav1 li a{width:100%;float:left;line-height:45px;min-height:45px;padding:0px 0px 0px 18px;overflow:hidden;#width:180px;*width:180px;}
ul.nav1 li a i{width:20px;text-align:center;margin-right:10px;line-height:45px;overflow:hidden;color:#9ea6b9;font-size:18px;}
ul.nav1 li a font{color:#9ea6b9;font-size:14px;}
ul.nav1 li a:hover{background:#4e5c65;color:#fff;text-decoration:none;border-left:5px solid #39aef5;padding:0px 0px 0px 13px;}
ul.nav1 li a.a_hover{background:#2b3337;color:#fff;text-decoration:none;}
ul.nav1 li a:hover font,ul.nav1 li a.a_hover font{color:#fff;}
ul.nav1 li a:hover i,ul.nav1 li a.a_hover i{color:#fff;}
ul.nav1 li ul.nav2{background:#314455;}
ul.nav1 li ul.nav2,ul.nav1 li ul.nav2 li ul.nav3{float:left;width:100%;display:none;padding:0px;}
ul.nav1 li ul.nav2 li a{padding:0px 0px 0px 28px;#width:168px;*width:168px;}
ul.nav1 li ul.nav2 li a i{font-size:15px;width:15px;}
ul.nav1 li ul.nav2 li a:hover{border:0 none;background:none;padding:0px 0px 0px 28px;}
ul.nav1 li ul.nav2 li a.a_hover{border:0 none;background:#39aef5;}
ul.nav1 li ul.nav2 li ul.nav3{background:rgb(255,253,251);}
ul.nav1 li ul.nav2 li ul.nav3 li a{padding:0px 0px 0px 38px;#width:160px;*width:160px;}
ul.nav1 li ul.nav2 li ul.nav3 li a i{font-size:10px;width:10px;}
/*ul.nav1 li ul.nav2 li ul.nav3 li a:hover,ul.nav1 li ul.nav2 li ul.nav3 li a.a_hover{border:0 none;background:none;}*/
ul.nav1 li ul.nav2 li ul.nav3 li a:hover,ul.nav1 li ul.nav2 li ul.nav3 li a.a_hover{border:0 none;}
ul.nav1 li a i.down{margin-right:12px;font-size:14px;}
ul.nav1 li ul.nav2 li a i.down{margin-right:16px;font-size:14px;}

.left_menu span .todo{width:8px;height:8px;position:absolute;z-index:9;top:6px;right:58px;background:#0080FF;border-radius:4px;}
.left_menu li .todo{width:12px;height:12px;line-height:11px;position:absolute;z-index:9;top:4px;right:58px;background:#0080FF;border-radius:6px;text-align:center;color:#ffffff;font-weight:normal;font-size:10px;}

.right{float:left;font-size:12px;position:absolute;top:0px;left:218px;z-index:8;min-width:calc(100% - 218px);}
.right_tab{width:100%;height:47px;#height:41px;*height:41px;border-top:5px solid #39aef5;float:left;background:#fff;border-bottom:1px solid #39aef5;overflow:hidden;}
.right_tab a{line-height:40px;padding:0 10px;float:left;color:#2f4050;}
.right_tab a font{font-size:14px;}
.right_tab a.a_hover{background:#39aef5;color:#fff;}
.right_tab a i.fl{margin-right:5px;}
.right_tab a i.fr{margin-left:10px;color:#c2c2c2;}
.right iframe{width:100%;float:left;background:#fff;padding:0px;}
.right iframe.iframe_first{-moz-border-top-left-radius:0px;-webkit-border-top-left-radius:0px;border-top-left-radius:0px;}

.datagrid-header{background: #F9E1E1!important;}
.datagrid-row{background: #FAFAFA!important;}
.datagrid-row-alt{background: #FAEFEF!important;}
.datagrid-header-row td{color: #873A19}
a.a_primary{background-color:#F4902D;}
a.a_primary:hover{background-color:#F4902D;}
.top{background:#dd4c3b url(../images/backindex.png) no-repeat;background-size: auto 100%;}
a.a_success{background-color:#EF6354;}
a.a_success:hover{background-color:#EF6354 ;}
table.ctable td{padding: 5px;}
/*a.btn{background-color:#E2841FFF}*/
/*table.ctable thead tr{background:rgb(255, 243, 241)}*/
.datagrid-row-over,
.datagrid-header td.datagrid-header-over{background:#F9E1E1;}



/*重置样式*/
.p4_0{padding: 4px 0;}
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}
.clearfix {
    *zoom:1;
}

.container {width: 95%;margin: 0 auto;margin-top: 50px;}
.wrapperhead{width: 100%;height: 50px;}
.wrapperhead ul{width: 100%;height: 100%;background-color: rgb(255, 243, 241);}
.wrapperhead ul li{width: 100%;color: #747474;height: 50px;float:left;font-size: 16px;line-height:50px;text-align: center;background: rgb(255,243,241);}
.wrapper{ width: 1000px;margin: 0 auto;border: 1px solid #e6e6e6;}
.container{width: 95%;margin: 0 auto;margin-top: 10px;}
.ctable img{height: 20px;width: 20px;}
table.ctable thead tr{font-weight:normal}
a.btn1{width: auto;height: 32px;float: left;cursor: pointer;line-height: 30px;padding: 0 18px;color: #fff;background-color: #39aef5;text-align: center;-moz-border-radius: 2px;
    -webkit-border-radius: 2px;border-radius: 2px;}
.datagrid-htable tbody .datagrid-header-row td{color: #333}

.pageInfoD {
    border-left: 5px solid #d31805 !important;
}
a.btn {
    background-color: #d31805 !important;
}
a{ color: #d31805 }
a:link{
    color: #d31805;
}
.dialog-button .l-btn{
    background-color: #d31805;
}

 .pagination-first {
    background: url('../images/i2.png') no-repeat 0 center;
  }
  .pagination-prev {
    background: url('../images/i2.png') no-repeat -16px center;
  }
  .pagination-next {
    background: url('../images/i2.png') no-repeat -32px center;
  }
  .pagination-last {
    background: url('../images/i2.png') no-repeat -47px center;
  }
  .pagination-load {
    background: url('../images/i2.png') no-repeat -63px center;
  }

  /* .tooltip {
    background-color: #f9e1e1;
    border-color: #d31805;
    color: #000000;
  } */