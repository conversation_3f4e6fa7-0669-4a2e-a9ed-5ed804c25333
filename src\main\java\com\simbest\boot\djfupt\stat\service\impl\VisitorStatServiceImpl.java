package com.simbest.boot.djfupt.stat.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.config.ExternalApiConfig;
import com.simbest.boot.djfupt.stat.model.VisitorStat;
import com.simbest.boot.djfupt.stat.model.VisitorStatExport;
import com.simbest.boot.djfupt.stat.repository.VisitorStatRepository;
import com.simbest.boot.djfupt.stat.service.IVisitorStatService;
import com.simbest.boot.djfupt.util.DateUtil;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <strong>Title : VisitorStatServiceImpl</strong><br>
 * <strong>Description : 访客统计分析服务实现类</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class VisitorStatServiceImpl extends LogicService<VisitorStat, String> implements IVisitorStatService {

    private VisitorStatRepository repository;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ExternalApiConfig externalApiConfig;

    @Autowired
    public VisitorStatServiceImpl(VisitorStatRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public JsonResponse saveVisitorStat(String source, String currentUserCode, VisitorStat visitorStat) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 获取当前用户信息
            IUser iuser = SecurityUtils.getCurrentUser();
            if (iuser != null) {
                // 设置组织相关信息
                visitorStat.setBelongCompanyName(iuser.getBelongCompanyName());
                visitorStat.setBelongCompanyNameParent(iuser.getBelongCompanyNameParent());
                visitorStat.setBelongDepartmentName(iuser.getBelongDepartmentName());
                visitorStat.setBelongOrgName(iuser.getBelongOrgName());
            }

            // 保存访客统计数据
            VisitorStat savedVisitorStat = this.insert(visitorStat);
            if (savedVisitorStat != null) {
                return JsonResponse.success("保存成功");
            } else {
                return JsonResponse.fail("保存失败");
            }
        } catch (Exception e) {
            log.error("保存访客统计数据失败", e);
            return JsonResponse.fail("保存失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse updateVisitorStat(String source, String currentUserCode, VisitorStat visitorStat) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 检查ID是否存在
            if (StrUtil.isEmpty(visitorStat.getId())) {
                return JsonResponse.fail("ID不能为空");
            }

            // 查询原有数据
            VisitorStat existingVisitorStat = this.findById(visitorStat.getId());
            if (existingVisitorStat == null) {
                return JsonResponse.fail("未找到对应的访客统计数据");
            }

            // 更新访客统计数据
            VisitorStat updatedVisitorStat = this.update(visitorStat);
            if (updatedVisitorStat != null) {
                return JsonResponse.success("更新成功");
            } else {
                return JsonResponse.fail("更新失败");
            }
        } catch (Exception e) {
            log.error("更新访客统计数据失败", e);
            return JsonResponse.fail("更新失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse findVisitorStatById(String source, String currentUserCode, String id) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 查询访客统计数据
            VisitorStat visitorStat = this.findById(id);
            if (visitorStat != null) {
                return JsonResponse.success(visitorStat);
            } else {
                return JsonResponse.fail("未找到对应的访客统计数据");
            }
        } catch (Exception e) {
            log.error("查询访客统计数据失败", e);
            return JsonResponse.fail("查询失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse deleteVisitorStat(String source, String currentUserCode, String id) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 查询访客统计数据
            VisitorStat visitorStat = this.findById(id);
            if (visitorStat == null) {
                return JsonResponse.fail("未找到对应的访客统计数据");
            }

            // 逻辑删除
            this.delete(visitorStat);
            return JsonResponse.success("删除成功");
        } catch (Exception e) {
            log.error("删除访客统计数据失败", e);
            return JsonResponse.fail("删除失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse findAllVisitorStat(VisitorStat visitorStat, Pageable pageable) {
        try {
            // 构建查询条件
            Specification<VisitorStat> specification = buildSpecification(visitorStat);

            // 分页查询
            Page<VisitorStat> page = this.findAll(specification, pageable);
            return JsonResponse.success(page);
        } catch (Exception e) {
            log.error("查询访客统计数据列表失败", e);
            return JsonResponse.fail("查询失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse countVisitsByCompany(String source, String currentUserCode) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 统计各单位的访问量
            List<Object[]> results = repository.countVisitsByCompany();
            List<Map<String, Object>> statistics = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(results)) {
                for (Object[] result : results) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("companyName", result[0]);
                    item.put("visitCount", result[1]);
                    statistics.add(item);
                }
            }

            return JsonResponse.success(statistics);
        } catch (Exception e) {
            log.error("统计各单位访问量失败", e);
            return JsonResponse.fail("统计失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse queryVisitorStatByCondition(int page, int size, String direction, String properties, Map<String, Object> params) {
        try {
            log.info("开始条件查询访客统计数据，参数：page={}, size={}, direction={}, properties={}, params={}",
                    page, size, direction, properties, params);

            // 构建查询条件
//            Specification<VisitorStat> specification = buildSpecificationFromParams(params);

            String beginDate = MapUtil.getStr(params, "beginDate");
            String endDate = MapUtil.getStr(params, "endDate");

            Specification<VisitorStat> build = Specifications.<VisitorStat>and()
                    .eq("enabled", Boolean.TRUE)
                    .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "username")), "username", "%" + MapUtil.getStr(params, "username") + "%")
                    .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "title")), "title", "%" + MapUtil.getStr(params, "title") + "%")
                    .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "belongDepartmentName")), "belongDepartmentName", "%" + MapUtil.getStr(params, "belongDepartmentName") + "%")
                    .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "belongCompanyName")), "belongCompanyName", "%" + MapUtil.getStr(params, "belongCompanyName") + "%")
//                    .between(null != beginDate && null != endDate, "createdAt", LocalDateTime.parse(DateUtil.getDate(beginDate , DateUtil.timestampPattern1) , dateTimeFormatter ),LocalDateTime.parse( DateUtil.getDate(info.getEeDate() , DateUtil.timestampPattern1) , dateTimeFormatter))
                    .build();

            // 处理时间范围查询


            if (StrUtil.isNotEmpty(beginDate)) {
                try {
                    LocalDateTime beginDateTime = LocalDateTime.parse(beginDate + "T00:00:00");
                    build = build.and(Specifications.<VisitorStat>and()
                            .ge("createdAt", beginDateTime)
                            .build());
                    log.info("添加开始时间查询条件：{}", beginDateTime);
                } catch (Exception e) {
                    log.error("解析开始时间失败：{}", beginDate, e);
                }
            }

            if (StrUtil.isNotEmpty(endDate)) {
                try {
                    LocalDateTime endDateTime = LocalDateTime.parse(endDate + "T23:59:59");
                    build = build.and(Specifications.<VisitorStat>and()
                            .le("createdAt", endDateTime)
                            .build());
                    log.info("添加结束时间查询条件：{}", endDateTime);
                } catch (Exception e) {
                    log.error("解析结束时间失败：{}", endDate, e);
                }
            }
            // 获取分页参数
            Pageable pageable = this.getPageable(page, size, direction, properties);

            // 分页查询
            Page<VisitorStat> result = this.findAll(build, pageable);

            log.info("条件查询访客统计数据成功，返回{}条记录，总计{}条", result.getContent().size(), result.getTotalElements());
            return JsonResponse.success(result);
        } catch (Exception e) {
            log.error("条件查询访客统计数据失败", e);
            return JsonResponse.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据实体构建查询条件
     *
     * @param visitorStat 查询条件实体
     * @return 查询条件
     */
    private Specification<VisitorStat> buildSpecification(VisitorStat visitorStat) {
        Specification<VisitorStat> specification = Specifications.<VisitorStat>and()
                .eq("enabled", Boolean.TRUE)
                .build();

        if (visitorStat != null) {
            // 用户名模糊查询
            if (StrUtil.isNotEmpty(visitorStat.getUsername())) {
                specification = specification.and(Specifications.<VisitorStat>and()
                        .like("name", "%" + visitorStat.getUsername() + "%")
                        .build());
            }

            // 会话标题模糊查询
            if (StrUtil.isNotEmpty(visitorStat.getTitle())) {
                specification = specification.and(Specifications.<VisitorStat>and()
                        .like("title", "%" + visitorStat.getTitle() + "%")
                        .build());
            }

            // 所属单位模糊查询
            if (StrUtil.isNotEmpty(visitorStat.getBelongCompanyName())) {
                specification = specification.and(Specifications.<VisitorStat>and()
                        .like("belongCompanyName", "%" + visitorStat.getBelongCompanyName() + "%")
                        .build());
            }

            // 所属部门模糊查询
            if (StrUtil.isNotEmpty(visitorStat.getBelongDepartmentName())) {
                specification = specification.and(Specifications.<VisitorStat>and()
                        .like("belongDepartmentName", "%" + visitorStat.getBelongDepartmentName() + "%")
                        .build());
            }
        }

        return specification;
    }

    /**
     * 根据参数构建查询条件
     *
     * @param params 查询参数
     * @return 查询条件
     */
    private Specification<VisitorStat> buildSpecificationFromParams(Map<String, Object> params) {
        log.info("构建查询条件，输入参数：{}", params);

        String beginDate = MapUtil.getStr(params, "beginDate");
        String endDate = MapUtil.getStr(params, "endDate");

        Specification<VisitorStat> build = Specifications.<VisitorStat>and()
                .eq("enabled", Boolean.TRUE)
                .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "username")), "username", "%" + MapUtil.getStr(params, "username") + "%")
                .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "title")), "title", "%" + MapUtil.getStr(params, "title") + "%")
                .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "belongDepartmentName")), "belongDepartmentName", "%" + MapUtil.getStr(params, "belongDepartmentName") + "%")
                .like(StrUtil.isNotEmpty(MapUtil.getStr(params, "belongCompanyName")), "belongCompanyName", "%" + MapUtil.getStr(params, "belongCompanyName") + "%")
//                    .between(null != beginDate && null != endDate, "createdAt", LocalDateTime.parse(DateUtil.getDate(beginDate , DateUtil.timestampPattern1) , dateTimeFormatter ),LocalDateTime.parse( DateUtil.getDate(info.getEeDate() , DateUtil.timestampPattern1) , dateTimeFormatter))
                .build();

        // 处理时间范围查询


        if (StrUtil.isNotEmpty(beginDate)) {
            try {
                LocalDateTime beginDateTime = LocalDateTime.parse(beginDate + "T00:00:00");
                build = build.and(Specifications.<VisitorStat>and()
                        .ge("createdAt", beginDateTime)
                        .build());
                log.info("添加开始时间查询条件：{}", beginDateTime);
            } catch (Exception e) {
                log.error("解析开始时间失败：{}", beginDate, e);
            }
        }

        if (StrUtil.isNotEmpty(endDate)) {
            try {
                LocalDateTime endDateTime = LocalDateTime.parse(endDate + "T23:59:59");
                build = build.and(Specifications.<VisitorStat>and()
                        .le("createdAt", endDateTime)
                        .build());
                log.info("添加结束时间查询条件：{}", endDateTime);
            } catch (Exception e) {
                log.error("解析结束时间失败：{}", endDate, e);
            }
        }

        log.info("查询条件构建完成");
        return build;
    }

    @Override
    public JsonResponse getCompanyHierarchyTree(String source, String currentUserCode) {
        try {
            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 查询所有启用的访客统计数据
            Specification<VisitorStat> specification = Specifications.<VisitorStat>and()
                    .eq("enabled", Boolean.TRUE)
                    .build();
            List<VisitorStat> allVisitorStats = this.findAllNoPage(specification);

            // 构建公司层级树
            List<Map<String, Object>> treeNodes = buildCompanyHierarchyTree(allVisitorStats);

            return JsonResponse.success(treeNodes);
        } catch (Exception e) {
            log.error("查询公司层级树失败", e);
            return JsonResponse.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 构建公司层级树
     *
     * @param visitorStats 访客统计数据列表
     * @return 公司层级树结构
     */
    private List<Map<String, Object>> buildCompanyHierarchyTree(List<VisitorStat> visitorStats) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Map<String, Object>> companyMap = new HashMap<>();
        Map<String, Map<String, Map<String, Object>>> departmentMap = new HashMap<>();

        // 统计数据
        Map<String, Integer> companyVisitCount = new HashMap<>();
        Map<String, Integer> companyQuestionCount = new HashMap<>();
        Map<String, Integer> companyLikeCount = new HashMap<>();
        Map<String, Integer> departmentVisitCount = new HashMap<>();
        Map<String, Integer> departmentQuestionCount = new HashMap<>();
        Map<String, Integer> departmentLikeCount = new HashMap<>();

        // 遍历所有访客统计数据，按公司和部门分组
        for (VisitorStat stat : visitorStats) {
            String parentCompany = stat.getBelongCompanyNameParent();
            String company = stat.getBelongCompanyName();
            String department = stat.getBelongDepartmentName();

            // 统计公司级别数据
            if (parentCompany != null && company != null) {
                String companyKey = parentCompany + "-" + company;
                companyVisitCount.put(companyKey, companyVisitCount.getOrDefault(companyKey, 0) + 1);
                companyQuestionCount.put(companyKey, companyQuestionCount.getOrDefault(companyKey, 0) + 1);
                companyLikeCount.put(companyKey, companyLikeCount.getOrDefault(companyKey, 0) + (stat.getLikeNum() != null ? stat.getLikeNum() : 0));
            }

            // 统计部门级别数据
            if (parentCompany != null && company != null && department != null) {
                String departmentKey = parentCompany + "-" + company + "-" + department;
                departmentVisitCount.put(departmentKey, departmentVisitCount.getOrDefault(departmentKey, 0) + 1);
                departmentQuestionCount.put(departmentKey, departmentQuestionCount.getOrDefault(departmentKey, 0) + 1);
                departmentLikeCount.put(departmentKey, departmentLikeCount.getOrDefault(departmentKey, 0) + (stat.getLikeNum() != null ? stat.getLikeNum() : 0));
            }
        }

        // 构建树结构
        for (VisitorStat stat : visitorStats) {
            String parentCompany = stat.getBelongCompanyNameParent();
            String company = stat.getBelongCompanyName();
            String department = stat.getBelongDepartmentName();

            // 跳过无效数据
            if (parentCompany == null || company == null) {
                continue;
            }

            // 处理父公司节点
            if (!companyMap.containsKey(parentCompany)) {
                Map<String, Object> parentNode = new HashMap<>();
                parentNode.put("name", parentCompany);
                parentNode.put("visitCount", 0);
                parentNode.put("questionCount", 0);
                parentNode.put("likeCount", 0);
                parentNode.put("children", new ArrayList<Map<String, Object>>());
                companyMap.put(parentCompany, parentNode);
                result.add(parentNode);
            }

            // 处理子公司节点
            String companyKey = parentCompany + "-" + company;
            if (!departmentMap.containsKey(companyKey)) {
                departmentMap.put(companyKey, new HashMap<>());

                Map<String, Object> companyNode = new HashMap<>();
                companyNode.put("name", company);
                companyNode.put("visitCount", companyVisitCount.getOrDefault(companyKey, 0));
                companyNode.put("questionCount", companyQuestionCount.getOrDefault(companyKey, 0));
                companyNode.put("likeCount", companyLikeCount.getOrDefault(companyKey, 0));
                companyNode.put("children", new ArrayList<Map<String, Object>>());

                // 添加到父节点的子节点列表
                ((List<Map<String, Object>>) companyMap.get(parentCompany).get("children")).add(companyNode);
                departmentMap.put(companyKey, new HashMap<>());
                departmentMap.get(companyKey).put(company, companyNode);

                // 更新父节点的统计数据
                companyMap.get(parentCompany).put("visitCount",
                        (Integer) companyMap.get(parentCompany).get("visitCount") + companyVisitCount.getOrDefault(companyKey, 0));
                companyMap.get(parentCompany).put("questionCount",
                        (Integer) companyMap.get(parentCompany).get("questionCount") + companyQuestionCount.getOrDefault(companyKey, 0));
                companyMap.get(parentCompany).put("likeCount",
                        (Integer) companyMap.get(parentCompany).get("likeCount") + companyLikeCount.getOrDefault(companyKey, 0));
            }

            // 处理部门节点
            if (department != null && !departmentMap.get(companyKey).containsKey(department)) {
                String departmentKey = parentCompany + "-" + company + "-" + department;

                Map<String, Object> departmentNode = new HashMap<>();
                departmentNode.put("name", department);
                departmentNode.put("visitCount", departmentVisitCount.getOrDefault(departmentKey, 0));
                departmentNode.put("questionCount", departmentQuestionCount.getOrDefault(departmentKey, 0));
                departmentNode.put("likeCount", departmentLikeCount.getOrDefault(departmentKey, 0));

                // 添加到公司节点的子节点列表
                ((List<Map<String, Object>>) departmentMap.get(companyKey).get(company).get("children")).add(departmentNode);
                departmentMap.get(companyKey).put(department, departmentNode);
            }
        }

        return result;
    }

    @Override
    public JsonResponse syncConversationData(String username, String conversationId, String apiKey) {
        try {
            log.info("开始同步会话数据，用户：{}，会话ID：{}", username, conversationId);

            // 调用外部API获取会话数据
            String encodedUser = URLEncoder.encode(username, "UTF-8");
            if (username.length()>30){
                username = "hadmin";
            }
            String encodedConversationId = URLEncoder.encode(conversationId, "UTF-8");
            String apiUrl = externalApiConfig.getMessagesUrl() +
                    "?user=" + encodedUser +
                    "&conversation_id=" + encodedConversationId +
                    "&limit=" + externalApiConfig.getDefaultLimit();

            log.debug("调用API地址：{}", apiUrl);

            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Authorization", "Bearer " + apiKey)
                    .timeout(externalApiConfig.getTimeout())
                    .execute();

            if (!response.isOk()) {
                log.error("调用外部API失败，状态码：{}，响应内容：{}", response.getStatus(), response.body());
                return JsonResponse.fail("调用外部API失败，状态码：" + response.getStatus());
            }

            String responseBody = response.body();


            if (StrUtil.isEmpty(responseBody)) {
                log.warn("API返回数据为空");
                return JsonResponse.fail("API返回数据为空");
            }

            log.debug("API响应内容：{}", responseBody);

            // 解析API响应数据
            JSONObject apiResult = JSONUtil.parseObj(responseBody);

            // 检查API响应结构
            if (!apiResult.containsKey("data")) {
                log.error("API响应格式错误，缺少data字段：{}", responseBody);
                return JsonResponse.fail("API响应格式错误");
            }

            JSONArray dataArray = apiResult.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.warn("没有找到会话数据，会话ID：{}", conversationId);
                return JsonResponse.fail("没有找到会话数据");
            }

            // 统计数据
            int totalMessages = dataArray.size();
            int likeCount = 0;
            int dislikeCount = 0;
            String lastMessageId = "";
            String firstQuery = ""; // 用于存储第一条消息的查询内容作为标题
            LocalDateTime firstMessageCreatedTime = null; // 用于存储第一条消息的创建时间
            LocalDateTime lastMessageCreatedTime = null; //用于存储最后一条消息的时间 也就是更新时间

            log.info("找到{}条消息记录", totalMessages);

            // 遍历消息数据，统计点赞数等信息
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject message = dataArray.getJSONObject(i);

                // 获取消息ID
                String messageId = message.getStr("id");
                if (i == dataArray.size() - 1) {
                    lastMessageId = messageId;

                    // 解析最后一条消息的创建时间
                    String createdAtStr1 = message.getStr("created_at");
                    if (StrUtil.isNotEmpty(createdAtStr1)) {
                        try {
                            // 尝试解析时间戳（秒级或毫秒级）
                            if (createdAtStr1.matches("\\d+")) {
                                long timestamp = Long.parseLong(createdAtStr1);
                                // 判断是秒级还是毫秒级时间戳
                                if (timestamp < 10000000000L) {
                                    // 秒级时间戳，转换为毫秒
                                    timestamp = timestamp * 1000;
                                }
                                lastMessageCreatedTime = LocalDateTime.ofInstant(
                                        java.time.Instant.ofEpochMilli(timestamp),
                                        ZoneId.systemDefault()
                                );
                            } else {
                                // 尝试解析 ISO 格式时间字符串
                                lastMessageCreatedTime = LocalDateTime.parse(createdAtStr1.replace("Z", ""));
                            }
                            log.debug("解析第一条消息创建时间：{} -> {}", createdAtStr1, lastMessageCreatedTime);
                        } catch (Exception e) {
                            log.warn("解析消息创建时间失败，使用当前时间：{}", createdAtStr1, e);
                            lastMessageCreatedTime = LocalDateTime.now();
                        }
                    } else {
                        lastMessageCreatedTime = LocalDateTime.now();
                    }
                }

                // 获取第一条消息的查询内容作为标题和创建时间
                if (i == 0) {
                    firstQuery = message.getStr("query");
                    if (firstQuery == null) {
                        firstQuery = "会话数据同步";
                    }

                    // 解析第一条消息的创建时间
                    String createdAtStr = message.getStr("created_at");
                    if (StrUtil.isNotEmpty(createdAtStr)) {
                        try {
                            // 尝试解析时间戳（秒级或毫秒级）
                            if (createdAtStr.matches("\\d+")) {
                                long timestamp = Long.parseLong(createdAtStr);
                                // 判断是秒级还是毫秒级时间戳
                                if (timestamp < 10000000000L) {
                                    // 秒级时间戳，转换为毫秒
                                    timestamp = timestamp * 1000;
                                }
                                firstMessageCreatedTime = LocalDateTime.ofInstant(
                                        java.time.Instant.ofEpochMilli(timestamp),
                                        ZoneId.systemDefault()
                                );
                            } else {
                                // 尝试解析 ISO 格式时间字符串
                                firstMessageCreatedTime = LocalDateTime.parse(createdAtStr.replace("Z", ""));
                            }
                            log.debug("解析第一条消息创建时间：{} -> {}", createdAtStr, firstMessageCreatedTime);
                        } catch (Exception e) {
                            log.warn("解析消息创建时间失败，使用当前时间：{}", createdAtStr, e);
                            firstMessageCreatedTime = LocalDateTime.now();
                        }
                    } else {
                        firstMessageCreatedTime = LocalDateTime.now();
                    }
                }

                // 统计点赞数 - 检查feedback字段
                JSONObject feedback = message.getJSONObject("feedback");
                if (feedback != null) {
                    String rating = feedback.getStr("rating");
                    if ("like".equals(rating)) {
                        likeCount++;
                    }
                    if ("dislike".equals(rating)){
                        dislikeCount++;
                    }
                }



                log.debug("处理消息 {}/{}，ID：{}，点赞状态：{}",
                        i + 1, totalMessages, messageId,
                        feedback != null ? feedback.getStr("rating") : "无");
            }

            log.info("统计完成 - 总消息数：{}，点赞数：{}，最后消息ID：{}", totalMessages, likeCount, lastMessageId);

            // 查询是否已存在该会话的统计数据
            VisitorStat existingStat = repository.findByConversationIdAndEnabled(conversationId, Boolean.TRUE);

            VisitorStat visitorStat;
            if (existingStat != null) {
                log.info("更新现有统计数据，记录ID：{}", existingStat.getId());

                // 更新现有数据
                visitorStat = existingStat;
                visitorStat.setSumCount(totalMessages);
                visitorStat.setLikeNum(likeCount);
                visitorStat.setMessageId(lastMessageId);
                visitorStat.setTitle(firstQuery); // 更新标题
                visitorStat.setLikeNum(likeCount);
                visitorStat.setDislike(dislikeCount);

                // 设置修改时间为当前时间，保持创建时间不变
                visitorStat.setModifiedTime(lastMessageCreatedTime);
                visitorStat.setModifier(username);
                visitorStat.setCreatedAt(firstMessageCreatedTime);
                visitorStat.setUpdatedAt(lastMessageCreatedTime);

                // 获取当前用户信息更新组织信息
                IUser iuser = SecurityUtils.getCurrentUser();

                if (iuser != null) {
                    visitorStat.setBelongCompanyName(iuser.getBelongCompanyName());
                    visitorStat.setBelongCompanyNameParent(iuser.getBelongCompanyNameParent());
                    visitorStat.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    visitorStat.setBelongOrgName(iuser.getBelongOrgName());
                    log.debug("更新用户组织信息：{}", iuser.getBelongCompanyName());
                }

                visitorStat = this.update(visitorStat);
                log.info("更新会话统计数据成功，会话ID：{}，记录ID：{}", conversationId, visitorStat.getId());
            } else {
                log.info("创建新的统计数据");

                // 创建新的统计数据
                visitorStat = new VisitorStat();
                // 不手动设置ID，让SnowflakeId生成器自动生成
                visitorStat.setUsername(username);
                visitorStat.setTitle(firstQuery); // 使用第一条消息的查询内容作为标题
                visitorStat.setConversationId(conversationId);
                visitorStat.setMessageId(lastMessageId);
                visitorStat.setSumCount(totalMessages);
                visitorStat.setLikeNum(likeCount);
                visitorStat.setDislike(dislikeCount);

                // 获取当前用户信息设置组织信息
                IUser iuser = SecurityUtils.getCurrentUser();
                visitorStat.setCreator(username);

                // 使用 API 返回的第一条消息的创建时间，如果解析失败则使用当前时间
                if (firstMessageCreatedTime != null) {
                    visitorStat.setCreatedTime(firstMessageCreatedTime);
                    log.info("使用API返回的创建时间：{}", firstMessageCreatedTime);
                } else {
                    visitorStat.setCreatedTime(LocalDateTime.now());
                    log.warn("API创建时间解析失败，使用当前时间");
                }

                visitorStat.setModifiedTime(lastMessageCreatedTime);
                visitorStat.setModifier(username);
                visitorStat.setCreatedAt(firstMessageCreatedTime);
                visitorStat.setUpdatedAt(lastMessageCreatedTime);

                if (iuser != null) {
                    visitorStat.setBelongCompanyName(iuser.getBelongCompanyName());
                    visitorStat.setBelongCompanyNameParent(iuser.getBelongCompanyNameParent());
                    visitorStat.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    visitorStat.setBelongOrgName(iuser.getBelongOrgName());
                    log.debug("设置用户组织信息：{}", iuser.getBelongCompanyName());
                }


                this.insert(visitorStat);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("visitorStat", visitorStat);
            result.put("totalMessages", totalMessages);
            result.put("likeCount", likeCount);
            result.put("conversationId", conversationId);
            result.put("username", username);

            return JsonResponse.success(result, "同步会话数据成功");

        } catch (Exception e) {
            log.error("同步会话数据失败，会话ID：{}，错误信息：{}", conversationId, e.getMessage(), e);
            return JsonResponse.fail("同步会话数据失败：" + e.getMessage());
        }
    }

    @Override
    public JsonResponse newFindAll(String source, String currentUserCode, int page, int size, String direction, String properties, String keyword) {
        try {
            log.info("开始新查询接口，参数：source={}, currentUserCode={}, page={}, size={}, keyword={}",
                    source, currentUserCode, page, size, keyword);

            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 参数验证
            if (page < 1) page = 1;
            if (size < 1) size = 10;
            if (size > 100) size = 100; // 限制最大页面大小

            // 构建查询条件
            Specification<VisitorStat> specification = buildSimpleSpecification(keyword);

            // 获取分页参数
            Pageable pageable = this.getPageable(page, size, direction, properties);

            // 执行分页查询
            Page<VisitorStat> pageResult = this.findAll(specification, pageable);

            log.info("新查询接口执行成功，返回{}条记录，总计{}条", pageResult.getContent().size(), pageResult.getTotalElements());
            return JsonResponse.success(pageResult, "查询成功");

        } catch (Exception e) {
            log.error("新查询接口执行失败，错误信息：{}", e.getMessage(), e);
            return JsonResponse.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 构建简单查询条件
     *
     * @param keyword 关键词（模糊搜索）
     * @return 查询条件
     */
    private Specification<VisitorStat> buildSimpleSpecification(String keyword) {
        Specification<VisitorStat> specification = Specifications.<VisitorStat>and()
                .eq("enabled", Boolean.TRUE)
                .build();

        // 关键词模糊搜索（搜索name、title、belongCompanyName、belongDepartmentName字段）
        if (StrUtil.isNotEmpty(keyword)) {
            Specification<VisitorStat> keywordSpec = Specifications.<VisitorStat>or()
                    .like("name", "%" + keyword + "%")
                    .like("title", "%" + keyword + "%")
                    .like("belongCompanyName", "%" + keyword + "%")
                    .like("belongDepartmentName", "%" + keyword + "%")
                    .like("belongOrgName", "%" + keyword + "%")
                    .build();
            specification = specification.and(keywordSpec);
        }

        return specification;
    }

    @Override
    public JsonResponse findCount(String source, String currentUserCode, Map<String, Object> params) {
        try {
            log.info("开始按公司分类统计访客数据（含条件过滤），参数：source={}, currentUserCode={}, params={}", source, currentUserCode, params);

            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 构建查询条件 - 结合 enabled=TRUE 和 params 中的条件
            Specification<VisitorStat> specification = buildSpecificationFromParams(params);
            // buildSpecificationFromParams already includes enabled=TRUE

            // 使用 Specification 查询符合条件的所有数据
            List<VisitorStat> visitorStats = this.findAllNoPage(specification);

            // 按belongCompanyName分组统计
            Map<String, List<VisitorStat>> companyGroupMap = visitorStats.stream()
                    .filter(stat -> StrUtil.isNotEmpty(stat.getBelongCompanyName()))
                    .collect(Collectors.groupingBy(VisitorStat::getBelongCompanyName));

            // 统计每个公司的总数 (人员请求数量) 和 sum 字段总和 (总请求数)
            Map<String, Map<String, Integer>> companyStatsMap = new HashMap<>();
            for (Map.Entry<String, List<VisitorStat>> entry : companyGroupMap.entrySet()) {
                String companyName = entry.getKey();
                List<VisitorStat> companyStats = entry.getValue();

                int personCount = companyStats.size(); // 人员请求数量
                int totalCount = companyStats.stream()
                        .filter(stat -> stat.getSumCount() != null)
                        .mapToInt(VisitorStat::getSumCount)
                        .sum(); // 总请求数

                Map<String, Integer> stats = new HashMap<>();
                stats.put("personCount", personCount);
                stats.put("totalCount", totalCount);
                companyStatsMap.put(companyName, stats);
            }

            // 定义期望的排序顺序 (保留原有逻辑)
            List<String> desiredOrder = Arrays.asList(
                    "省公司",
                    "郑州分公司",
                    "南阳分公司",
                    "周口分公司",
                    "洛阳分公司",
                    "商丘分公司",
                    "信阳分公司",
                    "新乡分公司",
                    "驻马店分公司",
                    "安阳分公司",
                    "开封分公司",
                    "平顶山分公司",
                    "许昌分公司",
                    "濮阳分公司",
                    "焦作分公司",
                    "三门峡分公司",
                    "漯河分公司",
                    "鹤壁分公司",
                    "济源分公司"
                    // ... 其他可能的分公司名称
            );

            // 构建按期望顺序排列的结果列表，并填充缺失单位数据 (保留原有逻辑)
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (String companyName : desiredOrder) {
                Map<String, Object> companyStat = new HashMap<>();
                companyStat.put("companyName", companyName);

                Map<String, Integer> stats = companyStatsMap.get(companyName);
                if (stats != null) {
                    companyStat.put("personCount", stats.get("personCount"));
                    companyStat.put("totalCount", stats.get("totalCount"));
                } else {
                    // 填充缺失单位数据，数量为0
                    companyStat.put("personCount", 0);
                    companyStat.put("totalCount", 0);
                }
                resultList.add(companyStat);
            }

            // 构建最终返回结果 (保留原有逻辑)
            Map<String, Object> result = new HashMap<>();
            result.put("companyStats", resultList);        // 各公司的统计数据 (按期望顺序排列)
            result.put("totalCompanies", resultList.size()); // 公司总数 (按期望列表大小)
            result.put("totalRecords", visitorStats.size()); // 总记录数 (符合条件的所有记录)
            // 计算所有公司的人员总数和总请求数
            int totalPersonCount = companyStatsMap.values().stream()
                    .mapToInt(stats -> stats.get("personCount"))
                    .sum();
            int totalSumCount = companyStatsMap.values().stream()
                    .mapToInt(stats -> stats.get("totalCount"))
                    .sum();
            result.put("totalPersonCount", totalPersonCount);
            result.put("totalSumCount", totalSumCount);

            log.info("按公司分类统计完成 - 公司数：{}，总记录数：{}，总人员请求数：{}，总请求数：{}",
                    resultList.size(), visitorStats.size(), totalPersonCount, totalSumCount);

            return JsonResponse.success(result, "按公司分类统计成功");

        } catch (Exception e) {
            log.error("按公司分类统计访客数据失败，错误信息：{}", e.getMessage(), e);
            return JsonResponse.fail("统计失败：" + e.getMessage());
        }
    }

    @Override
    public void exportVisitorStat(String source, String currentUserCode, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("开始导出访客统计数据，参数：source={}, currentUserCode={}", source, currentUserCode);

            // 处理移动端登录
            operateLogTool.operationSource(source, currentUserCode);

            // 获取所有访客统计数据（与页面显示的列表数据一致）
            JsonResponse listResponse = this.newFindAll(source, currentUserCode, 1, Integer.MAX_VALUE, "desc", "createdAt", null);
            if (listResponse.getData() == null) {
                log.error("获取访客统计数据失败");
                return;
            }

            @SuppressWarnings("unchecked")
            Page<VisitorStat> pageData = (Page<VisitorStat>) listResponse.getData();
            List<VisitorStat> visitorStats = pageData.getContent();

            // 构建导出数据
            List<VisitorStatExport> exportList = new ArrayList<>();

            // 转换数据格式
            if (visitorStats != null && !visitorStats.isEmpty()) {
                for (VisitorStat stat : visitorStats) {
                    VisitorStatExport exportItem = new VisitorStatExport();
                    exportItem.setTitle(stat.getTitle() != null ? stat.getTitle() : "");
                    exportItem.setUserName(stat.getUsername() != null ? stat.getUsername() : "");
                    exportItem.setBelongCompanyName(stat.getBelongCompanyName() != null ? stat.getBelongCompanyName() : "");
                    exportItem.setBelongDepartmentName(stat.getBelongDepartmentName() != null ? stat.getBelongDepartmentName() : "");
                    exportItem.setMessageCount(stat.getSumCount() != null ? String.valueOf(stat.getSumCount()) : "0");

                    // 格式化创建时间为 yyyy-MM-dd HH:mm:ss 格式
                    String formattedTime = "";
                    if (stat.getCreatedAt() != null) {
                        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        formattedTime = stat.getCreatedAt().format(timeFormatter);
                    }
                    exportItem.setCreated_at(formattedTime);
                    if (stat.getUpdatedAt() != null) {
                        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        formattedTime = stat.getUpdatedAt().format(timeFormatter);
                    }
                    exportItem.setUpdated_at(formattedTime);
                    exportList.add(exportItem);
                }
            }

            // 生成文件名
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = "访客统计列表_" + dateStr + ".csv";

            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "text/csv");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 导出CSV文件
            try {
                // 创建CSV文件并下载
                String path = request.getServletContext().getRealPath("down");
                if (path == null) {
                    path = System.getProperty("java.io.tmpdir");
                }
                File targetFile = new File(path, fileName);

                // 确保目录存在
                targetFile.getParentFile().mkdirs();

                log.info("导出文件路径：{}", targetFile.getAbsolutePath());
                log.info("导出数据条数：{}", exportList.size());

                // CSV格式导出
                try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                    StringBuilder csvContent = new StringBuilder();
                    csvContent.append("会话标题,用户名,所属单位,所属部门,消息数,创建时间,更新时间\n");

                    for (VisitorStatExport item : exportList) {
                        csvContent.append(item.getTitle() != null ? item.getTitle() : "").append(",")
                                .append(item.getUserName() != null ? item.getUserName() : "").append(",")
                                .append(item.getBelongCompanyName() != null ? item.getBelongCompanyName() : "").append(",")
                                .append(item.getBelongDepartmentName() != null ? item.getBelongDepartmentName() : "").append(",")
                                .append(item.getMessageCount() != null ? item.getMessageCount() : "").append(",")
                                .append(item.getCreated_at() != null ? item.getCreated_at() : "").append(",")
                                .append(item.getUpdated_at() != null ? item.getUpdated_at() : "").append("\n");
                    }

                    fos.write(csvContent.toString().getBytes("UTF-8"));
                }

                // 下载文件
                response.setContentType("application/octet-stream");
                java.nio.file.Files.copy(targetFile.toPath(), response.getOutputStream());
                response.getOutputStream().flush();

                log.info("访客统计列表导出成功，文件：{}", fileName);

            } catch (Exception e) {
                log.error("导出CSV文件失败", e);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }

        } catch (Exception e) {
            log.error("导出访客统计列表失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

}
