package com.simbest.boot.djfupt.attachment.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.attachment.repository.FileExtendRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.sys.model.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 作用：附件扩展类
 */
@Slf4j
@Service
public class FileExtendServiceImpl extends LogicService<SysFile,String> implements IFileExtendService {
    private FileExtendRepository fileExtendRepository;

    @Autowired
    public FileExtendServiceImpl(FileExtendRepository repository) {
        super(repository);
        this.fileExtendRepository = repository;
    }

    /**
     * 更新附件
     * @param pmInsId 主单据id
     * @param pmInsType 流程类型
     * @param id 附近id
     * @return
     */
    @Override
    public int updatePmInsId( String pmInsId, String pmInsType, String id ) {
        return fileExtendRepository.updatePmInsId(pmInsId,pmInsType,id);
    }

    /**
     * 查询区域附件
     * @param pmInsId 流程实例id
     * @param filePart 区域标识
     * @return
     */
    @Override
    public List<SysFile> getPartFile(String pmInsId, String filePart) {
        return fileExtendRepository.getPartFile(pmInsId,filePart);
    }


    /**
     * 查询区域附件
     * @param pmInsId 流程实例id
     * @return
     */
    @Override
    public List<SysFile> queryFile(String pmInsId) {
        return fileExtendRepository.getPminsId(pmInsId);
    }

    /**
     * 根据pmInsId查询附件
     *
     * @param pmInsId 流程实例id
     * @return list
     */
    @Override
    public List<SysFile> getFileByPmInsId(String pmInsId) {
        return super.findAllNoPage(Specifications.<SysFile>and().eq("pmInsId", pmInsId).build());
    }

    /**
     * 如果annex不为null 根据pmInsId更新附件
     * 1. 根据pmInsId删除原有附件
     * 2. 更新附件pmInsId
     *
     * @param pmInsId pmInsId
     * @param annex   新附件列表
     */
    @Override
    public void updateAnnexFile(String pmInsId, List<SysFile> annex) {
        Optional.ofNullable(annex).ifPresent(v -> {
            // 根据pmInsId删除原有附件
            List<SysFile> list = this.getFileByPmInsId(pmInsId);
            list = list.stream().filter(f -> v.stream().noneMatch(f1 -> Objects.equals(f1.getId(), f.getId()))).collect(Collectors.toList());
            super.deleteAll(list);
            // 更新附件pmInsId
            v.forEach(annexItem -> this.updatePmInsId(pmInsId, null, annexItem.getId()));
        });
    }
}
