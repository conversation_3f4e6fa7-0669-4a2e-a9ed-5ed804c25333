package com.simbest.boot.djfupt.wfquey.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.wfquey.service.IQueryDictValueService;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用途：数据字段查询
 * 作者：zsf
 * 时间：2018/07/06
 */
@Slf4j
@Service
public class QueryDictValueImpl extends LogicService<SysDictValue, String> implements IQueryDictValueService {

    @Autowired
    private SysDictValueRepository sysDictValueRepository;

    private OperateLogTool operateLogTool;
    private UumsSysOrgApi uumsSysOrgApi;


    public QueryDictValueImpl(SysDictValueRepository sysDictValueRepositorySuper, OperateLogTool operateLogTool, UumsSysOrgApi uumsSysOrgApi) {
        super(sysDictValueRepositorySuper);
        this.sysDictValueRepository = sysDictValueRepositorySuper;
        this.operateLogTool = operateLogTool;
        this.uumsSysOrgApi = uumsSysOrgApi;
    }

    /**
     * 数据字典查询
     *
     * @param dictType
     * @return
     */
    @Override
    public List<SysDictValue> queryByType(String dictType) {
        log.debug("数据字典查询>>>>>>>>>>>>>>dictType=" + dictType);
        return sysDictValueRepository.findDictValue(dictType);
    }

    /**
     * 查询18个分公司和省公司
     */
    @Override
    public List<SimpleOrg> find18CityOrgAndPOrg(String source, String currentUserCode) {
        /**准备操作参数**/
        List<SimpleOrg> simpleOrgList = new ArrayList<>();
        try {
            if (Constants.MOBILE.equals(source)) {
                operateLogTool.operationSource(source, currentUserCode);
            }

//           List<SimpleOrg> uumsSysGroupApi.findAllSonGroup(Constants.APP_CODE,"G0227");
            simpleOrgList = uumsSysOrgApi.findPOrgAnd18CityOrg(Constants.APP_CODE);
            simpleOrgList.remove(0);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return simpleOrgList;
    }

}
