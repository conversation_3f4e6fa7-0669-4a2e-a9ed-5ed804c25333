package com.simbest.boot.djfupt.policy.model;


import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

@Data
public class UsPolicyExport {


    @ExcelVOAttribute(name = "单位", column = "A")
    private String parentCompanyName;


    @ExcelVOAttribute(name = "应宣讲次数", column = "B")
    private Object preach;


    @ExcelVOAttribute(name = "已完成宣讲次数", column = "C")
    private Object dopreach;


    @ExcelVOAttribute(name = "完成率", column = "D")
    private String rate;


//    @ExcelVOAttribute(name = "宣讲内容数量", column = "E")
//    private String preachContent;


    @ExcelVOAttribute(name = "宣讲内容数量", column = "E")
    private Object contentcount;

    private String city;


    private String startDate;


    private String endDate;

    private String startTime;


    private String endTime;

    private String year;

    private String month;

}
