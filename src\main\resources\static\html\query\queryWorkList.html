<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>工单查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent();
        var myinfo = {};
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/index/workOrder?isCurrentGeneration=" + web.currentUser.username + "&source=PC", //table列表的查询命令
                    "queryParams": { pmInsType: 'C' },
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "searchvalidate": function () {
                        var data = getFormValue('taskTableQueryForm')
                        if (data.startTime && !data.endTime) {
                            top.mesShow("温馨提示", "请选择结束时间", 1500, 'red');
                            return false
                        }
                        if (!data.startTime && data.endTime) {
                            top.mesShow("温馨提示", "请选择开始时间", 1500, 'red');
                            return false
                        }
                        return true
                    },
                    "columns": [
                        [ 
                            { title: "工单标题", field: "title", width: 120, tooltip: true, align: "center" },
                            { title: "创建部门", field: "belongCompanyName", width: 100, tooltip: true, align: "center" },
                            { title: "创建人", field: "applyUser", width: 80, tooltip: true, align: "center" },
                            { title: "创建时间", field: "createdTime", width: 80, tooltip: true, align: "center", 
                                formatter: function (value, row, index) {
                                    var g = value.slice(0,11)
                                    return g
                                }
                            },
                            { title: "当前办理环节", field: "currentState", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {
                                    if (value != 7) {
                                        return row.handlingLink
                                    } else {
                                        return '已归档'
                                    }
                                }
                            },
                            { field: "opt", title: "操作", width: 80, rowspan: 1, align: "center",//align：对齐此列的数据，可以用left、right、center
                                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = "";
                                    var location = row.activityDefId ? row.activityDefId : 'djfupt.start'
                                    if ("A" == row.type) {
                                        g += "<a class='audit' index='" + index + "' title='查看' ptitle='优秀案例上报' pmInsId='" + row.pmInsId + "' " +
                                            "path='html/change/changeExperience.html?workFlag=join&type=join&location=" + location + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.pmInsId + "' >【查看】</a>";
                                    } else if ("B" == row.type) {
                                        g += "<a class='audit' index='" + index + "' title='查看' ptitle='问题上报' pmInsId='" + row.pmInsId + "' " +
                                            "path='html/change/changeQuestion.html?workFlag=join&location=" + location + "&type=join" + "&source=PC&processInstId=" + row.processInstId + "' >【查看】</a>";
                                    } else {
                                        g += "<a class='audit' index='" + index + "' title='查看' ptitle='政策宣讲' pmInsId='" + row.pmInsId + "' " +
                                            "path='html/change/changePolicy.html?workFlag=join&type=join&location=" + location + "&source=PC&processInstId=" + row.processinstid + "&pmInsId=" + row.pmInsId + "' >【查看】</a>";
                                    }
                                    return g
                                }
                            }
                        ]
                    ]
                },
            };
            $("#pmInsType").combobox("setValue", "C");
            loadGrid(pageparam);
            //导出
            $(".exporttable").on("click", function () {
                var data = getFormValue('taskTableQueryForm')
                if (data.startTime && !data.endTime) {
                    top.mesShow("温馨提示", "请选择结束时间", 1500, 'red');
                    return false
                }
                if (!data.startTime && data.endTime) {
                    top.mesShow("温馨提示", "请选择开始时间", 1500, 'red');
                    return false
                }
                $("#taskTableQueryForm").attr("action", web.rootdir + "action/index/exportParameter?isCurrentGeneration=" + web.currentUser.username + "&source=PC");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });

            $('#taskTableQueryForm').resize()
        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
            $('#taskTableQueryForm').resize()
        };

        //详情查看
        $(document).on("click", ".audit", function () {
            var item = $(this);
            var url = item.attr("path");
            top.dialogP(url, window.name, item.attr("ptitle"), 'getCheck', true, 'maximized', 'maximized');
        });
    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td align="right">工单类型</td>
                <td>
                    <input id="pmInsType" name="pmInsType" class="easyui-combobox" style="width: 100%; height: 32px;"
                        data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        textField: 'text',
                        editable:false,
                        data:[{value:'A',text:'优秀案例上报'},
                        {value:'B',text:'问题上报'},
                        {value:'C',text:'政策宣讲'}]" />
                </td>
                <td align="right">工单标题</td>
                <td><input id="title" name="title" type="text" /></td>
                <td align="right">工单状态</td>
                <td>
                    <input id="currentState" name="currentState" class="easyui-combobox"
                        style="width: 100%; height: 32px;" data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        ischooseall:true,
                        textField: 'text',
                        editable:false,
                        data:[{value:'2',text:'流转中'},
                        {value:'7',text:'已归档'}]" />
                </td>

                <td width="20%"></td>
            </tr>
            <tr>
                <td align="right">开始时间</td>
                <td>
                    <input id="startTime" name="startTime" type="text" class="easyui-datebox"
                        style="width:100%;height:32px;" validType="startDateCheck['endTime','startTime']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>
                <td align="right">结束时间</td>
                <td>
                    <input id="endTime" name="endTime" type="text" class="easyui-datebox"
                        style="width:100%;height:32px;" validType="endDateCheck['startTime','endTime']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>
                <td></td>
                <td colspan="2">
                    <a class="btn fr ml10 exporttable"><span>导出</span></a>
                    <a class="btn fr searchtable"><span>查询</span></a>

                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>