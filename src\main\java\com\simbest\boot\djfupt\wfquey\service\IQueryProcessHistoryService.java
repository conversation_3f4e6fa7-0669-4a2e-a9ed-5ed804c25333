package com.simbest.boot.djfupt.wfquey.service;

import java.util.List;
import java.util.Map;

/**
 * 用途：查询审批流程 service层
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
public interface IQueryProcessHistoryService  {

    /**
     * 查询流转过的工作项
     * @param processInstId 流程实例id
     * @param currentUserCode 当前人
     * @return
     */
    List<Map<String,Object>> getWorkItems (Long processInstId,String currentUserCode );
}
