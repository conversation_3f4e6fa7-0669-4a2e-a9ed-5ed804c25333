package com.simbest.boot.djfupt.admin.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.service.IUsAdminManagerService;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 思政纪实配置相关接口
 */
@Api(description = "管理员配置相关接口")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/usAdminManager")
public class UsAdminManagerController extends LogicController<UsAdminManager, String> {

    private IUsAdminManagerService usAdminManagerService;

    @Autowired
    public UsAdminManagerController(IUsAdminManagerService usAdminManagerService) {
        super(usAdminManagerService);
        this.usAdminManagerService = usAdminManagerService;
    }

    @Autowired
    private PaginationHelps paginationHelp;

    /**
     * 查询管理员
     *
     * @return
     */
    @PostMapping(value = {"/queryHadmin", "/api/queryHadmin", "/queryHadmin/sso"})
    public JsonResponse queryHadmin(
            @RequestBody UsAdminManager usAdminManager,
            @RequestParam(required = false) String type,
            Integer page,
            Integer size) {
        String trueName = usAdminManager.getTrueName();
        String userName = usAdminManager.getUserName();
        String roleUserId = usAdminManager.getRoleUserId();
        String belongComCode = usAdminManager.getBelongComCode();
        String gridName = usAdminManager.getGridName();
        return usAdminManagerService.queryHadmin(trueName, userName, roleUserId, belongComCode, page, size, type, gridName);

    }

    /**
     * 查询管理员
     *
     * @return
     */
    @PostMapping(value = {"/findAllAdmin", "/api/findAllAdmin", "/findAllAdmin/sso"})
    public JsonResponse findAllAdmin(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                     @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                     @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                     @RequestParam(required = false) String properties, //排序规则（属性名称）
                                     @RequestParam(required = false) String roleUserId, //排序规则（属性名称）
                                     @RequestParam(required = false) String type, //排序规则（属性名称）,
                                     @RequestBody(required = false) Map<String, Object> resultMap) {
        String gridName=null;
        if(resultMap.get("gridName")!=null){
             gridName=resultMap.get("gridName").toString();
        }


        List<Map<String, Object>> resultList = usAdminManagerService.findAllAdmin(resultMap, roleUserId, type,gridName);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");
    }


    /**
     * 导出管理员
     * @param direction
     * @param properties
     * @param roleUserId
     * @param type
     * @param resultMap
     */
    @PostMapping(value = {"/exportExcel", "/api/exportExcel", "/exportExcel/sso"})
    public void exportExcel(@RequestParam(required = false) String direction, //排序规则（asc/desc）
                            @RequestParam(required = false) String properties, //排序规则（属性名称）
                            @RequestParam(required = false) String roleUserId, //排序规则（属性名称）
                            @RequestParam(required = false) String type, //排序规则（属性名称）
                            @RequestParam(required = false) String trueName,
                            @RequestParam(required = false) String userName,
                            HttpServletResponse response,
                            HttpServletRequest request){
        Map<String, Object> resultMap=new HashMap<>();
        resultMap.put("trueName",trueName);
        resultMap.put("userName",userName);
        String gridName=null;
        if(resultMap.get("gridName")!=null){
            gridName=resultMap.get("gridName").toString();
        }

        usAdminManagerService.exportExcel(direction,properties, roleUserId, type,resultMap,response,request,gridName);
    }


    @ApiOperation(value = "添加管理员信息")
    @PostMapping(value = {"/insert", "/insert/sso", "/insert/api"})
    public JsonResponse insert(@RequestBody UsAdminManager usAdminManager) {
        return usAdminManagerService.insertInfo(usAdminManager);
    }

    @ApiOperation(value = "修改管理员信息")
    @PostMapping(value = {"/updateInfo", "/updateInfo/sso", "/updateInfo/api"})
    public JsonResponse updateInfo(@RequestBody UsAdminManager usAdminManager) {
        return usAdminManagerService.updateInfo(usAdminManager);
    }


    @ApiOperation(value = "删除管理员信息")
    @PostMapping(value = {"/delInfo", "/delInfo/sso", "/delInfo/api"})
    public JsonResponse delInfo(@RequestParam String id) {
        return usAdminManagerService.delInfo(id);
    }

    /**
     * 根据oa账号查询用户个人信息
     *
     * @param userName
     * @return
     */
    @PostMapping(value = {"/queryUserOrgInfo", "/queryUserOrgInfo/sso", "/queryUserOrgInfo/api", "/anonymous/queryUserOrgInfo"})
    public JsonResponse queryUserOrgInfo(@RequestParam String userName) {
        return usAdminManagerService.queryUserOrgInfo(userName);
    }

    /**
     * 导出模板
     */
    @GetMapping(value = {"/exportHadmin", "/api/exportHadmin", "/exportHadmin/sso", "/anonymous/exportHadmin"})
    public JsonResponse exportHadmin(HttpServletRequest request, HttpServletResponse response) {
        usAdminManagerService.exportHadmin(request, response);
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 导入模板
     *
     * @param request
     * @param response
     */
    @PostMapping(value = {"/importInfoHadmin", "/api/importInfoHadmin", "/importInfoHadmin/sso"})
    public void importInfoHadmin(HttpServletRequest request, HttpServletResponse response) {
        usAdminManagerService.importInfoHadmin(request, response);
    }

    /**
     * 导出模板
     */
    @GetMapping(value = {"/exportPartyBuilding", "/api/exportPartyBuilding", "/exportPartyBuilding/sso", "/anonymous/exportPartyBuilding"})
    public JsonResponse exportPartyBuilding(HttpServletRequest request, HttpServletResponse response) {
        usAdminManagerService.exportPartyBuilding(request, response);
        return JsonResponse.defaultSuccessResponse();
    }


    /**
     * 导入模板
     *
     * @param request
     * @param response
     */
    @PostMapping(value = {"/importInfoPartyBuilding", "/api/importInfoPartyBuilding", "/importInfoPartyBuilding/sso"})
    public void importInfoPartyBuilding(HttpServletRequest request, HttpServletResponse response) {
        usAdminManagerService.importInfoPartyBuilding(request, response);
    }


    /**
     * 问题数量
     */
    @ApiOperation(value = "网格和管理员", notes = "问题数量")
    @PostMapping(value = {"/findAllGridCount", "/api/findAllGridCount", "/findAllGridCount/sso"})
    public JsonResponse problemAllCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usAdminManagerService.findAllGridCount(resultMap);
        return JsonResponse.success(resultList);
    }

    @PostMapping(value = {"/jiJian", "/api/jiJian", "/jiJian/sso"})
    public Boolean jiJian() throws ParseException {
        return   usAdminManagerService.jiJian();
    }


}
