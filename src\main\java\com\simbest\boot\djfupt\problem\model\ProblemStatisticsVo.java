package com.simbest.boot.djfupt.problem.model;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

@Data
public class ProblemStatisticsVo {

    @ExcelVOAttribute(name = "编号", column = "A")
    @Excel(name = "编号")
    private int num;//编号

    @ExcelVOAttribute(name = "公司名字", column = "B")
    @Excel(name = "公司名字")
    private String companyName;//公司名字


    private String companyCode;//公司code

    @ExcelVOAttribute(name = "问题数量", column = "C")
    @Excel(name = "问题数量")
    private int problemNums;//问题数量

    @ExcelVOAttribute(name = "解决数量", column = "D")
    @Excel(name = "解决数量")
    private int solveNums;//解决数量

    @ExcelVOAttribute(name = "完成率", column = "E")
    @Excel(name = "完成率")
    private String completionRate;//完成率
}
