﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    
    $(function(){
        $("#orgTree").tree({
            url:web.rootdir+"action/commom/queryOrgTreeBySearch?pmInsId="+gps.pmInsId,
            checkbox:true,//是否在每一个借点之前都显示复选框
            lines:true,//是否显示树控件上的虚线
            treeId:'orgCode',
            treePid:'parentOrgCode',
            cascadeCheck:false,
            onlyone:gps.multi==0?true:false,//不要乱配
            fileds:'orgCode|id,orgName|text,belongCompanyCode,parentOrgCode,displayName',
            animate:true,//节点在展开或折叠的时候是否显示动画效果
            onLoadSuccess:function(node, data){
                treeLoadSuccess();
            },
            onClick:function(node){
                if(node.children){
                    if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
                }else{
                    ajaxgeneral({
                        url:"uums/sys/org/findSonByParentOrgId?appcode="+web.appCode+"&orgCode="+node.id,
                        data:{"appCode":web.appCode,"orgCode":node.id},
                        contentType: "application/json; charset=utf-8",
                        success:function(data){
                            if(data.data.length==0){
                                top.mesShow("温馨提示","该组织无下级数据！", 2000);
                            }else{
                                for(var i in data.data){
                                    data.data[i].text=data.data[i].orgName;
                                    data.data[i].id=data.data[i].orgCode;
                                }
                                $("#orgTree").tree("append", {
                                    parent : node.target,
                                    data : data.data
                                });
                                treeLoadSuccess();
                            }
                        }
                    });
                }
            },
            onBeforeCheck:function(node,checked,a){
                if(gps.multi==0){
                    var nodes=$("#orgTree").tree("getChecked");
                    for(var i in nodes){
                        //var nodei=$("#orgTree").tree("find",nodes[i].id);
                        $("#orgTree").tree("uncheck",nodes[i].target);
                    }
                    $(".role").html("");
                }
            },
            onCheck:function(node,checked){
                if(checked){
                    if($(".role a#"+node.id).length==0) $(".role").append("<a id='"+node.orgCode+"'   belongCompanyCode='" + node.belongCompanyCode + "'><font>"+node.text+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                }else{
                    $(".role a#"+node.orgCode).remove();
                }
            }
        });
        //删除已选
        $(document).on("click",".role a i",function(){
            var id=$(this).parent().attr("id");
            $(this).parent().remove();
            var nodei=$("#orgTree").tree("find",id);
            if(nodei) $("#orgTree").tree("uncheck",nodei.target);
        });
    });
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
        for(var i in chooseRow){
            if ($(".role a#" + chooseRow[i].id).length == 0) $(".role").append("<a id='" + chooseRow[i].id + "'  belongCompanyCode='" + chooseRow[i].belongCompanyCode + "'  ><font>" + chooseRow[i].name + "</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            var nodei=$("#orgTree").tree("find",chooseRow[i].id);
            if(nodei) {
                $("#orgTree").tree("check", nodei.target);
            }
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};
            data.orgCode=$(v).attr("id");
            data.belongCompanyCode=$(v).attr("belongCompanyCode");
            data.text=$(v).children("font").html();
            datas.push(data);
        });
        return {"data":datas,"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
