<!-- 反馈报告新增 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>新增/编辑思政纪实分类</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
    </script>

    <script type="text/javascript">
        var gps = getQueryString()
        console.log(gps)
        getCurrent()
        $(function () {
            loadForm('queryIdeaEdit')

            if (gps.id) {
                formval({
                    id: gps.id,
                    name: gps.name_other
                }, "queryIdeaEdit");
            }

            if (gps.type == 'read') {
                $('.body_page').css('margin-top', '-50px')
                formReadonly("queryIdeaEdit")
            } else if (gps.type == 'edit') {
                $('.reSet').show()
                $('.saveEdit').show()
                $('.confirm').hide()
                $('.pageInfo').show()
            } else if (gps.type == 'add') {
                $('.reSet').show()
                $('.saveEdit').hide()
                $('.confirm').show()
                $('.pageInfo').show()
            }

            $('#addTable').resize()
        })
        $(document).on("click", ".Close", function () {
            top.window.queryIdeaType.window.dialogClosed()
        });

        $(document).on("click", ".reSet", function () {
            formreset('queryIdeaEdit')
        });

        //确认保存
        $(document).on("click", ".confirm", function () {
            if (gps.type == 'add') {
                if (formValidate('queryIdeaEdit')) {
                    var data = getFormValue('queryIdeaEdit')
                    ajaxgeneral({
                        url: "action/usRecordType/create",
                        data: data,
                        contentType: "application/json; charset=utf-8",
                        success: function () {
                            top.window.queryIdeaType.window.dialogClosed()
                        }
                    });
                }
            }
        });
        //保存修改
        $(document).on("click", ".saveEdit", function () {
            if (formValidate('queryIdeaEdit')) {
                var data = getFormValue('queryIdeaEdit')
                ajaxgeneral({
                    url: "action/usRecordType/update",
                    data: data,
                    contentType: "application/json; charset=utf-8",
                    success: function () {
                        top.window.queryIdeaType.window.dialogClosed()
                    }
                });
            }
        });
    </script>
    <style>
        .formTable {
            width: 100%;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
            table-layout: fixed;
        }

        .formTable > tbody > tr:not(:first-child) > td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            font-weight: bold;
            height: 32px;
        }

        .formTable > tbody > tr > td input,
        .formTable > tbody > tr > td span,
        .formTable > tbody > tr > td textarea,
        .formTable > tbody > tr > td .textbox .textbox-text {
            border: none;
            font-size: 13px;
            background-color: #fff;
        }

        .formTable td.lable {
            background-color: #F9E1E1;
            padding: 5px;
            text-align: left;
        }

        .formTable td .textAndInput_, .formTable td .textAndInput_ .validatebox- {
            background-color: #f7f7f7;
        }

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: 3px;
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        .tit {
            padding-left: 10px;
            background-color: #def1ff;
        }

        .titTxt {
            text-align: center;
            padding: 12px 0;
            border: 1px solid #e8e8e8;
            margin: 10px 0;
            color: #38adf9;
            font-size: 18px;
            font-weight: bold;
        }

        table.tabForm {
            border-color: #e8e8e8;
        }

        #queryIdeaEdit {
            margin-top: 75px;
        }

        .titBox {
            text-align: center;
            width: 100%;
            border: 1px solid #e8e8e8;
            margin: 20px 0;
        }

        .textAndInput_readonly, .textAndInput_readonly .validatebox-readonly {
            background-color: #fff;
        }
    </style>
</head>

<body class="body_page">
<form id="queryIdeaEdit">
    <div class="pageInfo hide">
        <div class="pageInfoD">
            <a class="btn small fl mr15 confirm"><i class="iconfont">&#xe688;</i>
                <font>确认</font>
            </a>
            <a class="btn small fl mr15 saveEdit"><i class="iconfont">&#xe688;</i>
                <font>保存修改</font>
            </a>
            <a class="btn small fl mr15 reSet hide"><i class="iconfont">&#xe63a;</i>
                <font>重置</font>
            </a>
            <a class="btn small fl mr15 Close"><i class="iconfont">&#xe690;</i>
                <font>关闭</font>
            </a>
        </div>
    </div>

    <table id="addTable" border="0" cellpadding="0" cellspacing="10" class="w100 tabForm formTable">
        <input type="hidden" name="id" id="id"/>
        <tr>
            <td width="25%"></td>
            <td width="75%"></td>
        </tr>
        <tr>
            <td align="left" class="lable">名称<font class="col_r">*</font></td>
            <td>
                <input id="name" name="name" style="width: 99%; height: 32px;" type="text" class="easyui-validatebox" required/>
            </td>
        </tr>
    </table>
</form>
</body>

</html>