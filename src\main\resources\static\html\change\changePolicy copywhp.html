<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>政策宣讲下达</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent();
        var webId = getUUID(32, 16)

        $(function () {
            var param = {
                "htmlName": "changePolicy",
                "formId": "glyTableQueryForm",
                "processNextCmd": "action/usPolicyInfo/startSubmitProcess",
                "processNextBeforeSubmit": function (data) {
                    return true;
                }
            };
            if (gps.type == 'check' || gps.type == 'edit') {
                $('.pageInfo').hide();
                $('.body_page').attr('style', 'padding-top: 20px')
            } else {
                $('.body_page').attr('style', 'padding-top: 85px')
            }

            var data = {
                currentUserCode: web.currentUser.username,
                source: 'PC'
            }
            if (web.currentUser.belongCompanyTypeDictValue == '01') {
                data.webId = webId
            } else if (web.currentUser.belongCompanyTypeDictValue == '02') {
                data.webId = webId
            }
            $('#applyUser').val(web.currentUser.truename)
            $('#belongOrgName').val(web.currentUser.belongCompanyName + '/' + web.currentUser.belongDepartmentName)
            $('#applyUserPhone').val(web.currentUser.preferredMobile)
            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/usPantchDetail/mattersList", //table列表的查询命令
                    "queryParams": data,
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {
                                title: "宣讲事项", field: "lectureItems", width: 300, tooltip: true, align: "center"
                            },
                            {
                                title: "类型", field: "pantchType", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引    
                                    var g = "";
                                    if (value == '1') {
                                        g = '必选项'
                                    } else if (value == '2') {
                                        g = '推荐项'
                                    }
                                    return g
                                }
                            },
                            {
                                title: "支撑材料附件", field: "fileIds", width: 100, tooltip: true, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的
                                    var g = '无'
                                    if (value) {
                                        g = "<a href='#' class='downloadFile' fileId=" + row.id + ">【批量下载】</a>"
                                    }
                                    return g
                                }
                            },
                            {
                                field: "opt", title: "操作", width: 60, rowspan: 1, align: "center", hidden: false, formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引    
                                    var g = '';
                                    if ((web.currentUser.belongCompanyTypeDictValue == '02' && row.isFlag == 1 && row.pantchType == 1)) {
                                        g += "<a href='#' class='readDialog'  readDialogindex='" + index + "'>【查看】</a>"
                                    } else {
                                        g += "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                            + "<a href='#' class='delete' id=" + row.id + ">【删除】</a>"
                                    }
                                    return g
                                }
                            }
                        ]
                    ]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/usPantchDetail/insertUsPantchDetail",//新增命令
                    "updatacmd": "action/usPantchDetail/updateUsPantchDetail",//修改命令
                    "onSubmit": function (data) {
                        data.webId = webId
                        if (gps.type == 'draft') {
                            data.pmInsId = $('#pmInsId').val()
                            data.workCode = $('#workCode').val()
                        }
                        data.source = 'PC'
                        data.currentUserCode = web.currentUser.username
                        if (web.currentUser.belongCompanyTypeDictValue == '01') {
                            data.isFlag = 1
                        }
                        if (web.currentUser.belongCompanyTypeDictValue == '02') {
                            data.isFlag = 2
                        }
                        return data
                    },
                },
                "readDialog": {//查看
                    "dialogid": "#buttons",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#glyTableAddForm"
                },
            };
            if (web.currentUser.belongCompanyTypeDictValue == '03') {
                pageparam.listtable.columns[0].pop()
            }

            loadProcess(param, pageparam);
            $("#policyStartTime").datetimebox('calendar').calendar({
                styler: function (date) {
                    var now = new Date();
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    return date < d1 ? "color:#eee" : "";
                },
                validator: function (date) {
                    var now = new Date();
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    return date >= d1;
                }
            });
        });

        var now = new Date()
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        var yearArr = []
        for (var i = 0; i < 50; i++) {
            yearArr.push({ value: year + i, text: year + i + '年' })
        }
        var monthArr = []
        for (var i = 1; i < 13; i++) {
            if (i >= month)
                monthArr.push({ value: i, text: i + '月' })
        }
        var quarterArr = [
            { value: '一季度', text: '一季度' },
            { value: '二季度', text: '二季度' },
            { value: '三季度', text: '三季度' },
            { value: '四季度', text: '四季度' },
        ]
        if (month > 3) {
            quarterArr.shift()
        }
        if (month > 6) {
            quarterArr.shift()
        }
        if (month > 9) {
            quarterArr.shift()
        }
        yearArr.unshift({ value: '', text: '--请输入--' })
        monthArr.unshift({ value: '', text: '--请输入--' })
        quarterArr.unshift({ value: '', text: '--请输入--' })
        function changeSelected(n, o) {
            $('#emptyTd').hide()
            $('#date').hide()
            $('#yearTd').hide()
            $('#monthTd').hide()
            $('#quarterlyTd').hide()
            $('#year,#month,#quarterly').combobox({ required: false })
            $('#policyStartTime,#policyEndTime').datebox({ required: false })
            if (n == 1) {
                $('#date').show()
                $('#policyStartTime,#policyEndTime').datebox({ required: true })
            } else if (n == 2) {
                $('#yearTd').show()
                $('#monthTd').show()
                $('#year,#month').combobox({
                    required: true
                })
            } else if (n == 3) {
                $('#yearTd').show()
                $('#quarterlyTd').show()
                $('#year,#quarterly').combobox({
                    required: true
                })
            } else if (n == 4) {
                $('#yearTd').show()
                $('#year').combobox({
                    required: true
                })
            }
            if (gps.location) {
                $('#policyStartTime,#policyEndTime').datebox({ readonly: true })
                $('#year,#month,#quarterly').combobox({ readonly: true })
            }
        };

        function changeStart(data) {
            $("#policyEndTime").combo('setText', '');
            $("#policyEndTime").datetimebox('calendar').calendar({
                styler: function (date) {
                    var now = new Date(data);
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    return date < d1 ? "color:#eee" : "";
                },
                validator: function (date) {
                    var now = new Date(data);
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    return date >= d1;
                }
            });
        }

        // 新增
        $(document).on('click', '.addPolicy', function () {
            var param = {
            }
            var url = tourl('html/change/changeMatter.html', param)
            top.dialogP(url, window.name, '新增宣讲事项', 'matterAdd', false, 'maximized', 'maximized')
        })

        window.matterAdd = function () {

        }

        // 编辑
        $(document).on('click', 'a.edit', function () {
            var param = {
                type: 'edit',
            }
            var url = tourl('html/change/changeMatter.html', param)
            top.dialogP(url, window.name, '修改宣讲事项', 'matterEdit', false, 'maximized', 'maximized')
        })


        // 删除
        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('id');
            top.mesConfirm("温馨提示", "请确认是否删除该宣讲事项，删除后不可恢复！", function () {
                ajaxgeneral({
                    url: 'action/usPantchDetail/deleteUsPantchDetail?id=' + id,
                    success: function (data) {
                        $("#glyTable").datagrid("reload");
                    }
                });
            });
        })

        // 下载
        $(document).on('click', 'a.downloadFile', function () {
            var fileId = $(this).attr('fileId');
            $("#applicationForm").attr("action", web.rootdir + "action/usPantchDetail/usPantchDetailExport?usPantchId=" + fileId);
            $("#applicationForm").attr("method", "post");
            $("#applicationForm").submit();
        })

        // 获取uuid
        function getUUID(len, radix) {
            var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
            var uuid = []
            var i
            radix = radix || chars.length
            if (len) {
                for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
            } else {
                var r
                uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
                uuid[14] = '4'
                for (i = 0; i < 36; i++) {
                    if (!uuid[i]) {
                        r = 0 | Math.random() * 16
                        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
                    }
                }
            }
            return uuid.join('')
        }

        function groupNameSelect(data) {
            if (!data) return;
            $('#groupRegistDate').val(data.groupRegistDate || '')
            $('#groupApplyDate').val(data.groupApplyDate || '')
            $('#groupPhone').val(data.groupPhone || '')
            $('#groupApplyUser').val(data.groupApplyUser || '')
            $('#groupBelongDepartmentName').val(data.groupBelongDepartmentName || '')
            $('#groupBelongCompanyName').val(data.groupBelongCompanyName || '')
            $('#groupNumber').val(data.groupNumber || '')
        }

        function getcallback(res) {

            getSubjectList(res)
            pageData.listtable.queryParams.pmInsId = res.pmInsId


            // if(res.feedBcakFile.length>0){
            //     $('#feedBcakFile').hide()
            // }
            // if(res.policyOutline.length>0){
            //     $('#policyOutline').hide()
            // }

            loadGrid(pageData);
        }

        window.getchoosedata = function () {
            if (!formValidate('glyTableQueryForm')) {
                return false
            }
            var datas = getFormValue('glyTableQueryForm');

            return { 'data': datas, 'state': 1 };
        };

        var pageData
        var processNext_flag = true;//防止接口慢时用户再次流转
        function loadProcess(param, pageparam) {
            if (gps.from) {
                $("#" + param.formId).attr("cmd-select", $("#" + param.formId).attr("cmd-select") + "/sso");
            }
            if (!gps.location) {
                getCurrent(loadF);
                $(".nextBtn,.saveDraft").show();
                loadGrid(pageparam);
                $('.djAdmin').remove()
            } else {
                pageData = pageparam
                if (gps.type) gps.workFlag = gps.type;
                gps.source = "PC";
                gps.currentUserCode = web.currentUser.username;
                loadForm(param.formId, gps);
                if (!gps.modify) {
                    if (gps.location != $("#" + param.formId).attr("formLocation") || (gps.type != "task" && gps.type != "draft")) {
                        formReadonly(param.formId);
                        if (web.currentUser.belongCompanyTypeDictValue != '03') {
                            $('.addMatter').show()
                        }
                    }
                } else {
                    $("#" + param.formId + " input").removeAttr("readonly");
                }

                if (!gps.modify && (gps.type == "task" || gps.type == "toRead")) {
                    $(".nextBtn,.flowTrack,.viewComments,.processImg").show()
                }

                if (gps.type == "join" || gps.type == "doRead") {
                    $(".flowTrack,.viewComments,.processImg").show()
                }

                if (($("#" + param.formId).attr("archiveLocation") && $("#" + param.formId).attr("archiveLocation").indexOf(gps.location) > -1) || (gps.currentState && gps.currentState == "7")) $(".printOut").show();
                if (gps.modify) $(".wfmgModifyBtn").show();
                // 草稿
                if (gps.type == "draft") {
                    $(".nextBtn,.saveDraft").show();
                    if (gps.location) {
                        $(".reprealDraft").show();
                    }
                } else {
                    $('#subjectTable').show()
                }
                if (web.currentUser.belongCompanyTypeDictValue != '03') {
                    $('.djAdmin').remove()
                } else {
                    if (gps.type != 'join') {
                        $('#policyTime').datebox({ readonly: false })
                        $('#participants,#policyAddress').validatebox({ readonly: false })
                        $('#participants,#policyAddress').css('backgroundColor', '#fff')
                    } else if (gps.location == "djfupt.djAdminCheck" && gps.type == 'join') {
                        $('#feedBcakFile').show();
                        $('#policyOutline').show();
                    } else {
                        $('#feedBcakFile').hide()
                        $('#policyOutline').hide()
                    }
                }
            }

            //废除草稿
            function abolishSure() {
                var href = { "pmInsId": gps.pmInsId, "source": "PC" };
                var url = tourl(param.processDraftDeleteCmd, href);//(+gps.from?"/sso":"")
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.from)
                                window.colse();
                            else
                                top.dialogClose("audit");
                        }
                    });
                }
            };
            //流程下一步
            window.processNext = function (data) {
                var href = {};
                href.outcome = data.data.outcome;
                href.outcomeName = encodeURI(data.data.outcomeName);
                href.copyLocation = data.data.copyLocation;
                if (gps.workItemId) href.workItemId = gps.workItemId;
                if (gps.processInstId) href.processInstId = gps.processInstId;
                if (gps.location) href.location = gps.location;
                if (gps.type == "toRead") href.notificationId = gps.notificationId;
                href.source = "PC";
                var url = tourl(param.processNextCmd, href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                formD.type = "C"
                var table = $('#glyTable').datagrid('getData');
                var list = JSON.parse(JSON.stringify(table.data.content));
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]
                    item.createdTime = getNow('yyyy-MM-dd hh:mm:ss', false, item.createdTime)
                    item.modifiedTime = getNow('yyyy-MM-dd hh:mm:ss', false, item.modifiedTime)
                }
                formD.usPantchDetailList = list
                var fData = { "formData": formD };

                if (data.data.nextUserName) fData.nextUserName = data.data.nextUserName;
                if (data.data.message) fData.message = data.data.message;
                fData.copyNextUserNames = data.data.copyNextUserNames;
                fData.copyMessage = data.data.copyMessage;
                fData.decisionId = data.data.outcome;
                fData.outcomeName = data.data.outcomeName;
                if (formD) {
                    /**
                    编辑者：申振楠
                    修改时间：2022/5/24 - 2022/9/15
                    修改内容：点击确定按钮置灰
                     */
                    $(window.parent.document).find("#processNextConfirm").linkbutton({ disabled: true })
                    processNext_flag = false;
                    if (param.processNextBeforeSubmit) {
                        var pnbs = eval(param.processNextBeforeSubmit(fData));
                        if (!pnbs) return false;
                    }
                    top.dialogClose("processNext");
                    ajaxgeneral({
                        url: url,
                        data: { "flowParam": fData },
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        loading: param.processNextLoading && param.processNextLoading == true ? true : false,
                        success: function (data) {
                            /**
                            编辑者：申振楠
                            修改时间：2022/5/24 - 2022/9/15
                            修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({ disabled: false })
                            processNext_flag = true;
                            if (gps.dialogClose) {
                                top.dialogClose(gps.dialogClose);
                            }
                            if (gps.location || gps.initForm) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                } else if (gps.formToTab) {
                                    top.tabClose("li_" + param.htmlName);
                                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                                    var iframs = gps.initForm.split("@");
                                    setTimeout(function () {
                                        if (iframs[0].indexOf("_") > -1) {
                                            var ifi = iframs[0].split("_");
                                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                                        } else {
                                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                                            }
                                        }
                                    }, 1500);
                                } else if (gps.fromIfr) {
                                    top.dialogClose(gps.fromIfr);
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                if (gps.fromIfr) {//从列表打开的工单详情
                                    top.dialogClose(gps.fromIfr);
                                } else if (gps.newPage) {
                                    top.tabClick("li_processTask");
                                } else if (gps.openMenu) {//打开指定菜单
                                    top.tabClick(gps.openMenu);
                                } else {
                                    //window.location.href=window.location.href;
                                    setTimeout(function () {
                                        top.tabOpen("html/process/processTask.html", "我的待办");
                                        top.tabClose("li_" + param.htmlName);
                                    }, 1500);
                                }
                            }
                        }, sError: function () {
                            processNext_flag = true;
                            /**
                            编辑者：申振楠
                            修改时间：2022/5/24 - 2022/9/15
                            修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({ disabled: false })
                        }, error: function () {
                            processNext_flag = true;
                            /**
                            编辑者：申振楠
                            修改时间：2022/5/24 - 2022/9/15
                            修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({ disabled: false })
                        }
                    });
                }
            };
            //流程下一步
            $(document).on("click", ".nextBtn", function () {
                if (gps.location == 'djfupt.djAdminCheck') {
                    if (!getFormValue(param.formId).policyOutline) {
                        $.messager.alert('提示:', '请上传宣讲提纲!', 'error');
                    }
                }
                if (!processNext_flag) return false;
                $(".nextBtn").removeClass("activeNextBtn");
                if (!$(this).hasClass("activeNextBtn")) $(this).addClass(" activeNextBtn");//标识用
                var $form = $(this).parents("form");
                if (formValidate($form.attr("id"))) {
                    var href = {};
                    if (gps.location) {
                        href.location = gps.location;
                        if (gps.processInstId) href.processInstId = gps.processInstId;
                        if (gps.processDefName) href.processDefName = gps.processDefName;
                    } else {
                        href.location = $(this).parents("form").attr("formLocation");
                    }
                    if (gps.from) href.from = gps.from;
                    if (gps.type) href.type = gps.type;
                    var otherhref = {};
                    if ($form.attr("nextBtnOther")) {
                        if ($form.attr("nextBtnOther").indexOf("()") > -1)
                            otherhref = eval($form.attr("nextBtnOther"));
                        else
                            otherhref = eval($form.attr("nextBtnOther") + "()");
                    }
                    for (var i in otherhref) {
                        href[i] = otherhref[i];
                    }

                    href.source = "PC";
                    href.processType = 'policy'
                    href.questionMode = $('#questionMode').val()
                    var url = tourl('html/process/processNext.html', href);
                    var s = getBrowserInfo();
                    var w = 1000;
                    if ((s.browser == "msie" && parseInt(s.ver) < 9) || $(window.parent.window).height() < 550) {
                        w = 1017;
                    }
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流转下一步', 'processNext', false, w);
                }
            });
            //废除草稿确认
            $(document).on("click", ".abolish ", function () {
                top.mesConfirm("温馨提示", "确认废除吗？", abolishSure);
            });
            //流程跟踪
            $(document).on("click", ".flowTrack", function () {
                var href = { "location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation") };
                var flowTrackText = $(".flowTrack font").text();
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.flowTrackBefore) {
                    var ft = eval(param.flowTrackBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processTrack.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), flowTrackText, 'processTrack', true);
            });
            //查看意见
            $(document).on("click", ".viewComments", function () {
                var href = { "location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation") };
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.viewCommentsBefore) {
                    var ft = eval(param.viewCommentsBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processComments.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '审批意见', 'processComments', true);
            });
            //打印
            $(document).on("click", ".printOut", function () {
                var url = tourl(param.printHtml || 'html/process/processPrint.html', gps);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '打印预览', 'processPrint', false, 960, $(window).height() - 80);
            });
            //流程图
            $(document).on("click", ".processImg", function () {
                var href = { "processinstid": gps.processInstId, "tenantId": web.appCode };
                if (param.processImgBefore) {
                    var ft = eval(param.processImgBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }

                if (web.appCode == "dict") {
                    // dict流程图
                    var url = tourl('html/process/flowImage.html', href);
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流程图', 'flowImage', true, 'maximized', 'maximized');
                } else {
                    var url = 'http://10.92.82.44:8888/nbps/processGraphic.jsp';
                    if (window.location.href.indexOf("10.92.81.163") > -1 || window.location.href.indexOf("10.92.82.140") > -1 || window.location.href.indexOf("10.92.82.141") > -1) url = 'http://10.92.81.163:8088/nbps/processGraphic.jsp';
                    url = tourl(url, href);//正式:http://10.92.81.163:8088/nbps/processGraphic.jsp  测试:http://10.92.82.44:8888/nbps/processGraphic.jsp
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流程图', 'processImg', true, $(window).width() - 50, $(window).height() - 20);
                }
            });
            //修改提交用于wfmg
            $(document).on("click", ".wfmgModifyBtn", function () {
                var href = {};
                href.businessKey = gps.businessKey;
                var url = tourl("action/workOrderMg/updateWorkDetail", href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.location) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                //window.location.href=window.location.href;
                                setTimeout(function () { top.tabClose("li_" + param.htmlName); }, 1500);
                            }
                        }
                    });
                }
            });
        };

        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            if (!processNext_flag) return false;
            processNext_flag = false
            if (formValidate('glyTableQueryForm')) {
                var data = getFormValue('glyTableQueryForm');
                var table = $('#glyTable').datagrid('getData');
                var list = JSON.parse(JSON.stringify(table.data.content));
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]
                    item.createdTime = getNow('yyyy-MM-dd hh:mm:ss', false, item.createdTime)
                    item.modifiedTime = getNow('yyyy-MM-dd hh:mm:ss', false, item.modifiedTime)
                }
                data.usPantchDetailList = list
                data.source = 'PC'
                data.currentUserCode = web.currentUser.username
                var ajaxopts = {
                    url: "action/usPolicyInfo/saveDraft",
                    contentType: "application/json; charset=utf-8",
                    data: data,
                    success: function (data) {
                        processNext_flag = true
                        if (gps.type == "draft") {
                            top.dialogClose("audit");
                        } else {
                            top.tabClick("processDraft");
                            top.tabClose("li_changePolicy");
                        }
                    },
                    error: function (error) {
                        processNext_flag = true
                    }
                };
                ajaxgeneral(ajaxopts);
            }
        });

        //废除草稿
        $(document).on("click", ".reprealDraft", function () {
            var data = getFormValue('glyTableQueryForm');
            data.source = 'PC'
            data.currentUserCode = web.currentUser.username
            var ajaxopts = {
                url: "action/usPolicyInfo/deleteDraft",
                contentType: "application/json; charset=utf-8",
                data: data,
                success: function (data) {
                    if (gps.type == "draft") {
                        top.dialogClose("audit");
                    } else {
                        top.tabClick("processDraft");
                        top.tabClose("li_applicationFrom");
                    }
                }
            };
            ajaxgeneral(ajaxopts);
        });

        function beforerender(data) {
            if (!data.id) return
            var ajaxopts = {
                url: "action/usPantchDetail/checkUsPantchDetail?id=" + data.id,
                success: function (res) {
                    formval(res.data, 'glyTableAddForm')
                }
            };
            ajaxgeneral(ajaxopts);
        }
        // 年份变更 更改月份与季度
        function changeYear(data) {
            var quarterArr = [
                { value: '一季度', text: '一季度' },
                { value: '二季度', text: '二季度' },
                { value: '三季度', text: '三季度' },
                { value: '四季度', text: '四季度' },
            ]
            monthArr = []

            var now = new Date()
            var year = now.getFullYear();
            var month = now.getMonth() + 1;

            if (data > year) {
                for (var i = 1; i < 13; i++) {
                    monthArr.push({ value: i, text: i + '月' })
                }
            } else {
                for (var i = 1; i < 13; i++) {
                    if (i >= month)
                        monthArr.push({ value: i, text: i + '月' })
                }
                if (month > 3) {
                    quarterArr.shift()
                }
                if (month > 6) {
                    quarterArr.shift()
                }
                if (month > 9) {
                    quarterArr.shift()
                }
            }
            monthArr.unshift({ value: '', text: '--请输入--' })
            quarterArr.unshift({ value: '', text: '--请输入--' })
            $("#month").combobox({ data: monthArr })
            $("#quarterly").combobox({ data: quarterArr })
        }
        function beforeGetForm(res) {
            changeYear(res.year)
            $('#webId').val(webId)
        }
        // 获取审批意见
        function getSubjectList(res) {
            var list = ''
            ajaxgeneral({
                url: 'action/usPolicyInfo/findAllMsg?pmInsId=' + res.pmInsId + '&currentUserCode=' + web.currentUser.username,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    var data = res.data
                    var tr = ''
                    for (var i = 0; i < data.length; i++) {
                        var item = data[i]
                        tr += [
                            "<tr>",
                            "<td style='text-aligin:center:heigth:40px;line-height:40px'>" + item.producerName + "</td>",
                            "<td style='text-aligin:center:heigth:40px;line-height:40px'>" + item.content + "</td>",
                            "<td style='text-aligin:center:heigth:40px;line-height:40px'>" + item.createdTime + "</td>",
                            "<td style='text-aligin:center:heigth:40px;line-height:40px'>" + item.modifiedTime + "</td>",
                            "</tr>",
                        ].join('')
                    }
                    $('#subjectTable').append(tr)
                }
            });
        }
    </script>
    <style>
        .ctableT {
            border: none;
            text-align: left;
        }

        textarea {
            white-space: normal !important;
        }

        a.mybtn {
            width: auto;
            height: 32px;
            float: left;
            cursor: pointer;
            line-height: 30px;
            padding: 0 18px;
            color: #fff;
            background-color: #39aef5;
            text-align: center;
            -moz-border-radius: 2px;
            -webkit-border-radius: 2px;
            border-radius: 2px;
        }

        a.mybtn:hover {
            text-decoration: none;
            background: #38a8ec;
        }

        .tips p {
            font-size: 12px;
            color: #333;
        }

        .tips {
            display: none;
            margin-bottom: 30px;
        }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }

        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: center;
            max-width: 100px;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #f7f7f7;
        }

        input:read-only {
            background-color: #f7f7f7;
        }

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: 3px;
            width: 54px
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        .uploadImage {
            left: 0;
            width: 90%;
        }
    </style>
</head>

<body class="body_page">
    <!--noNextUserDecisionId无下一审批人的决策id比如归档，可以为多个节点中间用|隔开；mulitNextUserDecisionId审批人多选的决策项-->
    <!---->
    <form id="glyTableQueryForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
        cmd-select="action/usPolicyInfo/getFormDetail" getcallback="getcallback()" beforerender="beforeGetForm()">
        <input id="pmInsId" name="pmInsId" value="" hidden>
        <input id="id" name="id" value="" hidden>
        <input id="type" name="type" value="" hidden>
        <input id="webId" name="webId" value="" hidden>
        <input id="workCode" name="workCode" value="" hidden>
        <input id="workItemId" name="workItemId" value="" hidden>
        <div class="pageInfo" style="min-width: 0">
            <div class="pageInfoD">
                <a class="btn small fl mr15 nextBtn hide"><i class="iconfont">&#xe688;</i>
                    <font>流转下一步</font>
                </a>
                <a class="btn small fl mr15 saveDraft hide"><i class="iconfont">&#xe6bd;</i>
                    <font>保存</font>
                </a>
                <a class="btn small fl mr15 reprealDraft hide"><i class="iconfont">&#xe6bd;</i>
                    <font>废除草稿</font>
                </a>
                <a class="btn small fl mr15 flowTrack hide"><i class="iconfont">&#xe68c;</i>
                    <font>流程跟踪</font>
                </a>
                <a class="btn small fl mr15 viewComments hide"><i class="iconfont">&#xe629;</i>
                    <font>查看意见</font>
                </a>
                <a class="btn small fl mr15 processImg hide"><i class="iconfont">&#xe6bd;</i>
                    <font>流程图</font>
                </a>
            </div>
        </div>

        <div style="width: 100%;">
            <table class="formTable" id="baseForm" border="0" cellpadding="0" th:colspan="6" cellspacing="10"
                width="90%" style="white-space: nowrap">
                <tr>
                    <td colspan="6"
                        style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding: 5px;">政策宣讲
                    </td>
                </tr>
                <tr>
                    <td align="center" class="lable" width="10%">
                        政策宣讲时限类型<font class="col_r">*</font>
                    </td>
                    <td width="23.3%">
                        <input id="policyType" name="policyType" class="easyui-combobox"
                            style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            panelHeight:'auto',
                            ischooseall:true,
                            textField: 'text',
                            editable:false,
                            required:true,
                            data:[{value:'1',text:'周'},
                            {value:'2',text:'月'},
                            {value:'3',text:'季度'},
                            {value:'4',text:'年'}],
                            onChange:changeSelected" />
                    </td>
                    <td align="center" class="lable" width="10%">
                        发起人
                    </td>
                    <td width="23.3%"><input id="applyUser" name="applyUser" type="text" readonly="readonly"
                            noReset="true" style="width:100%; height: 32px;" />
                    </td>
                    <td align="center" class="lable" width="10%">发起人组织</td>
                    <td width="23.3%"><input id="belongOrgName" name="belongOrgName" type="text" readonly="readonly"
                            noReset="true" style="width:100%; height: 32px;" />
                    </td>
                </tr>

                <tr>
                    <td align="center" class="lable" width="10%">政策宣讲时间<font class="col_r">*</font>
                    </td>
                    <td colspan="5" id="emptyTd"></td>
                    <td colspan="5" width="56.6%" id="date" style="display:none">
                        <input id="policyStartTime" name="policyStartTime" type="text" class="easyui-datebox"
                            style="width:40%;height:32px;" validType="startDateCheck['policyEndTime','policyStartTime']"
                            data-options="panelHeight:'auto', editable:false,onChange:changeStart, " />
                        <input id="policyEndTime" name="policyEndTime" type="text" class="easyui-datebox"
                            style="width:40%;height:32px;" validType="endDateCheck['policyStartTime','policyEndTime']"
                            data-options="panelHeight:'auto', editable:false" />
                    </td>
                    <td style="display:none">
                        年份
                    </td>
                    <td id="yearTd" width="23.3%" style="display:none">
                        <input id="year" name="year" class="easyui-combobox" style="width: 100%; height: 32px;"
                            data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            onChange:changeYear,
                            data:yearArr" />
                    </td>
                    <td style="display:none">
                        月份
                    </td>
                    <td id="monthTd" width="66.6%" style="display:none" colspan="4">
                        <input id="month" name="month" class="easyui-combobox" style="width: 33.3%; height: 32px; "
                            data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            data:monthArr" />
                    </td>
                    <td style="display:none">
                        季度
                    </td>
                    <td id="quarterlyTd" width="66.6%" style="display:none" colspan="4">
                        <input id="quarterly" name="quarterly" class="easyui-combobox"
                            style="width: 33.3%; height: 32px;" data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            data:quarterArr" />
                    </td>
                </tr>
                <tr class="djAdmin">
                    <td class="lable" align="center">宣讲提纲<font class="col_r">*</font>
                    </td>
                    <td colspan="5">
                        <input id="policyOutline" name="policyOutline" type="text" file="true"
                            class="cselectorImageUpload" mulaccept="true"
                            btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                            href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
                    </td>
                </tr>
                <tr>
                    <td colspan=" 6" style="font-weight: 700;font-size: 16px;padding: 5px;line-height:32px;">
                        政策宣讲清单
                        <a class="btn fr ml10 addMatter showDialog"><span>新增宣讲事项</span></a>
                    </td>
                </tr>
            </table>

            <div class="glyTable">
                <table id="glyTable"></table>
            </div>
            <table class="formTable djAdmin" border="0" cellpadding="0" th:colspan="6" cellspacing="10" width="90%">
                <tr>
                    <td colspan=" 6" style="font-weight: 700;font-size: 16px;padding: 5px;line-height:32px;">
                        政策宣讲反馈
                    </td>
                </tr>
                <tr>
                    <td align="center" class="lable" width="10%">
                        宣讲时间<font class="col_r">*</font>
                    </td>
                    <td width="23.3%">
                        <input id="policyTime" name="policyTime" type="text" class="easyui-datebox"
                            style="width:100%;height:32px;"
                            data-options="panelHeight:'auto', editable:false,requied:true " />
                    </td>
                    <td align="center" class="lable" width="10%">
                        宣讲地点
                    </td>
                    <td width="23.3%"><input id="policyAddress" name="policyAddress" type="text"
                            class="easyui-validatebox" style="width:100%; height: 32px;" />
                    </td>
                    <td align="center" class="lable" width="10%">
                        参与人员
                    </td>
                    <td width="23.3%"><input id="participants" class="easyui-validatebox" name="participants"
                            type="text" style="width:100%; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td align="center" class="lable" width="10%">宣讲反馈附件</td>
                    <td width="23.3%" colspan="5">
                        <input id="feedBcakFile" name="feedBcakFile" type="text" file="true"
                            class="cselectorImageUpload" mulaccept="true"
                            btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                            href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
                    </td>
                </tr>
            </table>
            <table class="formTable" id="subjectTable" border="0" cellpadding="0" th:colspan="6" cellspacing="10"
                width="90%" style="display:none">
                <tr>
                    <td colspan=" 6" style="font-weight: 700;font-size: 16px;padding: 5px;line-height:32px;">
                        意见列表
                    </td>
                </tr>
                <tr>
                    <td class="lable" width="30%">
                        审批意见人
                    </td>
                    <td class="lable" width="30%">
                        审批意见
                    </td>
                    <td class="lable" width="20%">
                        到达时间
                    </td>
                    <td class="lable" width="20%">
                        审批时间
                    </td>
                </tr>
            </table>
        </div>
    </form>
    <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:950px;height:320px;">
        <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()"
            beforerender="beforerender()">
            <input type="hidden" id="id" name="id" />
            <table border="0" cellpadding="0" cellspacing="10" class="formTable">
                <tr>
                    <td class="lable" align="center" width="20%">
                        宣讲事项<font class="col_r">*</font>
                    </td>
                    <td width="80%">
                        <textarea id="lectureItems" name="lectureItems" class="easyui-validatebox" required="true"
                            validType="maxLength[100]" style="min-height:120px;"></textarea>
                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="20%">
                        类别<font class="col_r">*</font>
                    </td>
                    <td width="80%">
                        <input id="pantchType" name="pantchType" class="easyui-combobox"
                            style="width: 100%; height: 32px;" data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        ischooseall:true,
                        textField: 'text',
                        editable:false,
                        required:true,
                        data:[
                        {value:'1',text:'必选项'},
                        {value:'2',text:'推荐项'}]" />
                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="10%">支撑材料附件</td>
                    <td width="90%">
                        <input id="drawFiles" name="drawFiles" type="text" file="true" class="cselectorImageUpload"
                            mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                            href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
                    </td>
                </tr>
            </table>
        </form>
        <form id="applicationForm"></form>
    </div>
</body>

</html>