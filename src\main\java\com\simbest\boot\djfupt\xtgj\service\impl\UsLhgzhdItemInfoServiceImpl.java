package com.simbest.boot.djfupt.xtgj.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.attachment.service.impl.FileExtendServiceImpl;
import com.simbest.boot.djfupt.xtgj.model.UsLhgzhdItemInfo;
import com.simbest.boot.djfupt.xtgj.repository.UsLhgzhdItemInfoRepository;
import com.simbest.boot.djfupt.xtgj.service.IUsLhgzhdItemInfoService;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.distribution.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UsLhgzhdItemInfoServiceImpl extends LogicService<UsLhgzhdItemInfo, String> implements IUsLhgzhdItemInfoService {

    private final FileExtendServiceImpl fileExtendServiceImpl;
    private UsLhgzhdItemInfoRepository usLhgzhdItemInfoRepository;

    public UsLhgzhdItemInfoServiceImpl(UsLhgzhdItemInfoRepository usLhgzhdItemInfoRepository, FileExtendServiceImpl fileExtendServiceImpl, SysDictValueService sysDictValueService) {
        super(usLhgzhdItemInfoRepository);
        this.usLhgzhdItemInfoRepository = usLhgzhdItemInfoRepository;
        this.fileExtendServiceImpl = fileExtendServiceImpl;
    }

    @Override
    public UsLhgzhdItemInfo insertInfo(UsLhgzhdItemInfo o) {
        String pmInsId = getPmInsId("ULIIF");
        o.setFilePmInsId(pmInsId);

        // 保存附件
        fileExtendServiceImpl.updateAnnexFile(o.getFilePmInsId(), o.getOpinionAnnex());

        return super.insert(o);
    }

    /**
     * 根据 pid 删除数据
     *
     * @param pid 主单据id
     */
    @Override
    public void deleteAllByPid(String pid) {
        List<UsLhgzhdItemInfo> list = super.findAllNoPage(Specifications.<UsLhgzhdItemInfo>and()
                .eq("pid", pid).build());
        super.deleteAll(list);
    }

    /**
     * 使用全删全加逻辑保存数据
     *
     * @param pid  主单据id
     * @param list 待保存数据
     * @return 保存后的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UsLhgzhdItemInfo> deleteAndAddInfo(String pid, List<UsLhgzhdItemInfo> list) {

        this.deleteAllByPid(pid);

        return list.stream().map(v -> {
            v.setPid(pid);
            return this.insertInfo(v);
        }).collect(Collectors.toList());

    }

    /**
     * 根据 pid 查询列表数据
     *
     * @param pid 主单据id
     * @return 列表数据
     */
    @Override
    public List<UsLhgzhdItemInfo> findAllByPid(String pid) {
        List<UsLhgzhdItemInfo> list = super.findAllNoPage(Specifications.<UsLhgzhdItemInfo>and()
                .eq("pid", pid).build(), Sort.by(Sort.Direction.ASC, "createdTime"));
        // 获取附件
        list.forEach(v -> {
            v.setOpinionAnnex(fileExtendServiceImpl.getFileByPmInsId(v.getFilePmInsId()));
        });
        return list;
    }

    /**
     * 获取pmInsId
     *
     * @param type 流程类型
     * @return pmInsId
     */
    public static String getPmInsId(String type) {
        return type.concat(String.valueOf(IdGenerator.idWorker.nextId()));
    }

}
