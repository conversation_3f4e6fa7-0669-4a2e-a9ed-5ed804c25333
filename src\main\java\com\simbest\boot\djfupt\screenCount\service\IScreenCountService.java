package com.simbest.boot.djfupt.screenCount.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.screenCount.model.ScreenCount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IScreenCountService extends ILogicService<ScreenCount, String> {


    Page<Map<String, Object>> findAllInfo(ScreenCount o, Pageable pageable);

    void record(String name);

    void export(HttpServletResponse response, HttpServletRequest request, ScreenCount o);
}
