package com.simbest.boot.djfupt.xtgj.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_lhgzhd_item_info")
@ApiModel(value = "联合跟装活动-子项")
public class UsLhgzhdItemInfo extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ULII")
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "pid")
    private String pid;

    @Column(length = 20)
    @ApiModelProperty(value = "意见建议类别")
    private String opinionType;

    @Column(length = 2000)
    @ApiModelProperty(value = "意见建议填报")
    private String opinionRemark;

    @Column(length = 40)
    @ApiModelProperty(value = "意见建议附件pmInsId")
    private String filePmInsId;

    @Transient
    @ApiModelProperty(value = "意见建议附件")
    private List<SysFile> opinionAnnex;

}
