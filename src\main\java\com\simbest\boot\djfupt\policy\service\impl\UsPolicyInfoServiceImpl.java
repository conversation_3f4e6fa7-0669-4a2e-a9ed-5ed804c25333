package com.simbest.boot.djfupt.policy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.admin.repository.WfWorkItemRepository;
import com.simbest.boot.djfupt.admin.service.IUsAdminManagerService;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import com.simbest.boot.djfupt.policy.model.UsPolicyExport;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.repository.UsPolicyInfoRepository;
import com.simbest.boot.djfupt.policy.service.IUsPantchDetailService;
import com.simbest.boot.djfupt.policy.service.IUsPolicyInfoService;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.djfupt.process.repository.WfWorkItemMsgRepository;
import com.simbest.boot.djfupt.process.service.IprocessService;
import com.simbest.boot.djfupt.record.service.IUsRecordFillService;
import com.simbest.boot.djfupt.util.*;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.CustomBeanUtil;
import com.simbest.boot.util.SpringContextUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service(value = "usPolicyInfoService")
@SuppressWarnings("ALL")
public class UsPolicyInfoServiceImpl extends LogicService<UsPolicyInfo, String> implements IUsPolicyInfoService {

    private UsPolicyInfoRepository usPolicyInfoRepository;

    @Autowired
    public UsPolicyInfoServiceImpl(UsPolicyInfoRepository repository) {
        super(repository);
        this.usPolicyInfoRepository = repository;
    }

    @Autowired
    private LoginUtils loginUtils;
    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private WfWorkItemMsgRepository wfWorkItemMsgRepository;


    @Autowired
    private IUsProblemInfoService usProblemInfoService;
    @Autowired
    private IUsPolicyInfoService usPolicyInfoService;
    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private IUsRecordFillService usRecordFillService;
    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private IUsAdminManagerService usAdminManagerService;

    @Autowired
    private IprocessService processService;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;


    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private IUsCaseInfoService caseInfoService;
    @Autowired
    private IUsPmInstenceService usPmInstenceService;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;


    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;
    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IActBusinessStatusService statusService;
    @Autowired
    private OperateLogTool operateLogTool;
    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IUsPantchDetailService usPantchDetailService;


    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;

    @Resource
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    private SimpleConfig hlgj_Config;

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private SpringContextUtil springContextUtil;

    String param1 = "/action/usPolicyInfo";

    @PostConstruct
    private void init() {
        hlgj_Config = uumsSysAppConfigApi.findAppConfigByStyle("hlgj", "hadmin", Constants.APP_CODE);
    }

    /**
     * 更新附件
     *
     * @param usPolicyInfo
     */

    @Override
    public void updateFileInfoById(UsPolicyInfo usPolicyInfo) {
        List<SysFile> drawFiles = usPolicyInfo.getFeedBcakFile();
        List<String> drawFileIds = new ArrayList<>();
        String fileid = "";

        List<SysFile> policyOutFiles = usPolicyInfo.getPolicyOutline();
        List<String> policyOutFileIds = new ArrayList<>();
        String policyOutfileid = "";

        if (CollectionUtil.isNotEmpty(drawFiles)) {
            if (drawFiles != null || drawFiles.size() > 0) {
                for (SysFile drawFile : drawFiles) {
                    drawFileIds.add(drawFile.getId());
                }
            }
            fileid = Joiner.on(",").join(drawFileIds);
            usPolicyInfo.setFeedBcakFileIds(fileid);
        }


        if (CollectionUtil.isNotEmpty(policyOutFiles)) {
            if (policyOutFiles != null || policyOutFiles.size() > 0) {
                for (SysFile policyOutFile : policyOutFiles) {
                    policyOutFileIds.add(policyOutFile.getId());
                }
            }
            policyOutfileid = Joiner.on(",").join(policyOutFileIds);
            usPolicyInfo.setPolicyOutlineIds(policyOutfileid);
        }
        usPolicyInfoService.update(usPolicyInfo);
    }

    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(UsPolicyInfo usPolicyInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "usPolicyInfo=" + usPolicyInfo.toString() + ",source=" + usPolicyInfo.getSource() + ",userCode=" + usPolicyInfo.getCurrentUserCode();
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(usPolicyInfo.getSource(), usPolicyInfo.getCurrentUserCode(), param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 保存草稿
            if (StringUtils.isEmpty(usPolicyInfo.getId())) {
                // 保存业务单据信息
                this.saveBusinessData(usPolicyInfo);
            }
            // 更新草稿
            else {
                //更新业务单据信息
                this.updateBusinessData(usPolicyInfo);
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usPolicyInfo, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(UsPolicyInfo usPolicyInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + usPolicyInfo.getPmInsId() + ",source=" + usPolicyInfo.getSource() + ",userCode=" + usPolicyInfo.getCurrentUserCode();
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(usPolicyInfo.getSource(), usPolicyInfo.getCurrentUserCode(), param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(usPolicyInfo.getPmInsId());
            usPmInstenceService.delete(pmInstence);
            // 删除业务单据数据
            usPolicyInfoService.deleteById(usPolicyInfo.getId());
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }


    /**
     * 保存业务单据信息
     *
     * @param usPolicyInfo
     * @return
     */
    private Map<String, Object> saveBusinessData(UsPolicyInfo usPolicyInfo) {
        Map<String, Object> result = Maps.newHashMap();
        IUser iuser = SecurityUtils.getCurrentUser();
        UsPmInstence usPmInstence = new UsPmInstence();
        String workCode = "";
        String title = "";
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StringUtils.isEmpty(usPolicyInfo.getId())) {
                String processName = "com.djglpt.flow.policyPropaganda";
                String processType = "C";
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    String currentStr = DateUtil.getCurrentStr();
                    String count = usPmInstenceService.getCounts("C", currentStr);
                    workCode = OrderNumberCreate.generateNumber("C", count);//编号生成
                    String pmInsId = processType + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                    if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyType())) {
                        if ("1".equals(usPolicyInfo.getPolicyType())) {
                            title = usPolicyInfo.getPolicyStartTime() + "-" + usPolicyInfo.getPolicyEndTime() + "政策宣讲内容";
                        }
                        if ("2".equals(usPolicyInfo.getPolicyType())) {
                            title = usPolicyInfo.getYear() + usPolicyInfo.getMonth() + "政策宣讲内容";
                        }
                        if ("3".equals(usPolicyInfo.getPolicyType())) {
                            title = usPolicyInfo.getYear() + usPolicyInfo.getQuarterly() + "政策宣讲内容";
                        }
                        if ("4".equals(usPolicyInfo.getPolicyType())) {
                            title = usPolicyInfo.getYear() + "政策宣讲内容";
                        }

                    }
                    usPmInstence.setPmInsId(pmInsId);
                    usPmInstence.setWorkCode(workCode);
                    usPmInstence.setProcessName(processName);
                    usPmInstence.setPmInsTitle(title);
                    usPmInstence.setPmInsType(processType);
                    if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                        usPmInstence.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                        usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                        usPmInstence.setBelongDepartmentName(iuser.getBelongCompanyName());
                        usPmInstence.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    }
                    if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                        usPmInstence.setBelongCompanyName(iuser.getBelongCompanyName());
                        usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                        usPmInstence.setBelongDepartmentName(iuser.getBelongDepartmentName());
                        usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    }
                    usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                    usPmInstence.setBelongOrgName(iuser.getBelongOrgName());
                    usPmInstenceService.insert(usPmInstence);
                }
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (StringUtils.isNotEmpty(usPmId)) {

                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usPolicyInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usPolicyInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                    usPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPolicyInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usPolicyInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                }
                usPolicyInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPolicyInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usPolicyInfo.setBelongOrgName(iuser.getBelongOrgName());
                usPolicyInfo.setPmInsId(usPmInstence.getPmInsId());
                usPolicyInfo.setWorkCode(workCode);
                usPolicyInfo.setTitle(title);
                UsPolicyInfo newUsPolicyInfo = usPolicyInfoService.insert(usPolicyInfo);
                newUsPolicyInfo.setId(usPolicyInfo.getId());
                if (StringUtils.isNotEmpty(usPolicyInfo.getId())) {
                    updateFileInfoById(usPolicyInfo);//更新附件
                }

            }
            if (CollectionUtil.isNotEmpty(usPolicyInfo.getUsPantchDetailList())) {
                for (UsPantchDetail usPantchDetail : usPolicyInfo.getUsPantchDetailList()) {
                    UsPantchDetail newUsPantchDetail = usPantchDetailService.findById(usPantchDetail.getId());
                    newUsPantchDetail.setWorkCode(usPmInstence.getWorkCode());
                    newUsPantchDetail.setIsFlag("1");
                    newUsPantchDetail.setPolicyId(usPolicyInfo.getId());
                    newUsPantchDetail.setPmInsId(usPolicyInfo.getPmInsId());
                    if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                        newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                        newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyNameParent());
                        newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                        newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                        newUsPantchDetail.setBelongDepartmentName(iuser.getBelongCompanyName());
                        newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    }
                    if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                        newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyName());
                        newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCode());
                        newUsPantchDetail.setBelongDepartmentName(iuser.getBelongDepartmentName());
                        newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                        newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCode());
                        newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyName());
                    }
                    newUsPantchDetail.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPantchDetailService.update(newUsPantchDetail);
                }
            }


        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return result;
        }
        return result;
    }

    /**
     * 修改业务单据信息
     *
     * @param usPolicyInfo
     * @return
     */
    private void updateBusinessData(UsPolicyInfo usPolicyInfo) {

        String title = "";
        UsPolicyInfo repair = this.findById(usPolicyInfo.getId());
        CustomBeanUtil.copyPropertiesIgnoreNull(usPolicyInfo, repair);
        // 更新表单基础数据
        this.update(repair);
        if (CollectionUtil.isNotEmpty(usPolicyInfo.getUsPantchDetailList())) {
            for (UsPantchDetail usPantchDetail : usPolicyInfo.getUsPantchDetailList()) {
                UsPantchDetail newUsPantchDetail = usPantchDetailService.findById(usPantchDetail.getId());
                newUsPantchDetail.setWorkCode(usPolicyInfo.getWorkCode());
                newUsPantchDetail.setIsFlag("1");
                newUsPantchDetail.setPolicyId(usPolicyInfo.getId());
                newUsPantchDetail.setPmInsId(usPolicyInfo.getPmInsId());
                usPantchDetailService.update(newUsPantchDetail);
            }
        }
        UsPmInstence usPmInstence = usPmInstenceService.findByPmInsId(usPolicyInfo.getPmInsId());
        if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyType())) {
            if ("1".equals(usPolicyInfo.getPolicyType())) {
                title = usPolicyInfo.getPolicyStartTime() + "-" + usPolicyInfo.getPolicyEndTime() + "政策宣讲内容";
            }
            if ("2".equals(usPolicyInfo.getPolicyType())) {
                title = usPolicyInfo.getYear() + usPolicyInfo.getMonth() + "政策宣讲内容";
            }
            if ("3".equals(usPolicyInfo.getPolicyType())) {
                title = usPolicyInfo.getYear() + usPolicyInfo.getQuarterly() + "政策宣讲内容";
            }
            if ("4".equals(usPolicyInfo.getPolicyType())) {
                title = usPolicyInfo.getYear() + "政策宣讲内容";
            }

        }
        usPmInstence.setPmInsTitle(title);
        usPmInstenceService.update(usPmInstence);

    }


    /**
     * 根据登录人所在公司获取流程
     *
     * @return
     */
    private Map<String, String> getProcessMap() {

        Map<String, String> map = Maps.newHashMap();
        String processName = null;
        String processType = null;
        map.put("processName", "com.djglpt.flow.policyPropaganda");
        map.put("processType", "C");
        return map;
    }

    /**
     * 手机端 模拟登录
     *
     * @param source          手机端还是PC端
     * @param currentUserCode 当前用户code
     */
    public void mobileLogin(String source, String currentUserCode) {
        if (!Objects.equals(Constants.MOBILE, source)) {
            return;
        }
        Assert.state(StringUtils.isNotBlank(currentUserCode), "未登录状态,OA账户不能为空!");
        loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
    }

    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param bodyParam
     * @param formId          表单id
     * @return
     */
    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String processDefId, Map<String, Object> bodyParam, String formId, String notificationId) {
        JsonResponse jsonResponse = new JsonResponse();
        this.mobileLogin(source, currentUserCode);
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                UsPolicyInfo usPolicyInfo = null;
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    if ("MOBILE".equals(source)) {
                        List<Object> personAssessList = (List<Object>) formData1.get("usPantchDetailList");
                        for (Object obj : personAssessList) {
                            ((LinkedHashMap) obj).put("createdTime", LocalDateTime.now());
                            ((LinkedHashMap) obj).put("modifiedTime", LocalDateTime.now());
                        }
                    }
                    usPolicyInfo = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), UsPolicyInfo.class);
                } else {
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(formId) && "MOBILE".equals(source)) {
                        usPolicyInfo = this.findById(formId);
                    }
                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }
                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;

                if (usPolicyInfo != null && usPolicyInfo.getId() != null && org.apache.commons.lang3.StringUtils.isNotEmpty(workItemId)) {
                    String key = SecurityUtils.getCurrentUserName() + "-" + workItemId ;
                    try {
                        String s = RedisUtil.get(key);
                        if (StrUtil.isNotEmpty(s)) {
                            return JsonResponse.fail(-1 , "当前工单正在办理中，请勿重复办理！");
                        } else {
                            // todo 查询办理状态是否为已办理
                            WfWorkItemModel wfWorkItemModel=wfWorkItemRepository.findAllByEnabledAndReceiptCodeAndParticipant(Boolean.TRUE,usPolicyInfo.getPmInsId(),SecurityUtils.getCurrentUserName());
                            //todo 如果未办理
                            if(wfWorkItemModel!=null&& wfWorkItemModel.getCurrentState().intValue()==10){
                                RedisUtil.set(key , "true" , 600);
                            }else {
                                return JsonResponse.fail(-1 , "当前工单正在办理中，请勿重复办理！");
                            }


                        }
                        jsonResponse = saveSubmitTask(usPolicyInfo, workItemId, outcome, message, nextUserName, location, processDefId, notificationId, source, currentUserCode);
                    } catch (Exception e ) {
                        jsonResponse = JsonResponse.fail(-1 , "流转失败!");
                    } finally {
                        RedisUtil.delete(key);
                    }

                    } else {
                    jsonResponse = startProcess(usPolicyInfo, nextUserName, outcome, message, source, currentUserCode, processDefId);//创建提交
                }
            }
        }
        return jsonResponse;
    }

    @Override
    public JsonResponse startProcess(UsPolicyInfo usPolicyInfo, String nextUserName, String outcome, String message, String source, String currentUserCode, String processDefId) {
        log.debug("起草接口----------startProcess---------->" + usPolicyInfo.toString());
        long ret = 0;
        //工单编号
        String workCode = "";
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",currentUserCode=" + currentUserCode + ",usPolicyInfo=" + usPolicyInfo.toString() + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser iuser = SecurityUtils.getCurrentUser();
            List<UsPolicyInfo> usList = new ArrayList<>();
            Map<String, Object> hashMap = new HashMap<>();
            List<String> idList = new ArrayList<>();
            Map<String, Object> newMapS = new HashMap<>();
            /**校验表单和下一步审批人是否为空**/
            if (StringUtils.isNotEmpty(nextUserName)) {
                List<String> nextUserNameList = Arrays.asList(nextUserName.split(","));

                /**获取登录人所在公司应启动的流程**/
                Map<String, String> map = this.getProcessMap();
                Map<String, Object> newMap = new HashMap<>();
                String processType = map.get("processType");
                String processName = Constants.POLICY_PROPAGANDA;
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    boolean flag = false;
                    boolean statue = false;
                    UsPmInstence usPmInstence = new UsPmInstence();
                    if (StringUtils.isNotEmpty(processType)) {
                        usPmInstence.setPmInsType(processType);
                        usPmInstence.setProcessName(processName);
                    } else {
                        usPmInstence.setPmInsType("C");
                        usPmInstence.setProcessName(processName);
                    }
                    /**保存业务数据**/
                    if (StringUtils.isEmpty(usPolicyInfo.getId())) {
                        flag = this.saveUsPmInstence(usPolicyInfo, usPmInstence);

                    } else {
                        UsPolicyInfo newusPolicyInfo = this.findById(usPolicyInfo.getId());
                        String currentStr = DateUtil.getCurrentStr();
                        workCode = usPmInstenceService.getCounts("C", currentStr);
                        newusPolicyInfo.setWorkCode(workCode);
                        this.update(newusPolicyInfo);
                        //补充主单据数据
                        usPmInstence = usPmInstenceService.findByPmInsId(newusPolicyInfo.getPmInsId());
                        usPmInstence.setWorkCode(workCode);
                        usPmInstenceService.update(usPmInstence);
                        flag = true;
                    }
                    if (flag) {
                        for (String userName : nextUserNameList) {
                            UsPolicyInfo usPolicyInfo1 = saveUsPolicyInfo(usPolicyInfo, userName);
                            if (ObjectUtil.isNotEmpty(usPolicyInfo1)) {
                                usList.add(usPolicyInfo1);
                                if (StringUtils.isNotEmpty(usPolicyInfo1.getId())) {
                                    idList.add(usPolicyInfo1.getId());
                                }
                                statue = true;
                            }
                        }

                    }
                    /**启动发起流程**/
                    if (statue) {
                        newMap.put("title", usPmInstence.getPmInsTitle());
                        newMap.put("code", usPmInstence.getPmInsId());
                        newMap.put("receiptId", usPmInstence.getId());
                        newMap.put("outcome", outcome);
                        newMap.put("message", message);
                        newMap.put("inputUserIds", nextUserName);
                        newMap.put("processDefId", processName);
                        newMap.put("processType", processType);
                        newMap.put("nextUserName", nextUserName);
                        //第一个参数为流程定义名称
                        Long workItemId = processService.startProcess(processName, newMap);
                        if (workItemId != 0) {
                            if (CollectionUtil.isNotEmpty(idList)) {
                                String ids = Joiner.on(",").join(idList);
                                hashMap.put("ids", ids);
                            }
                            /**提交表单审批处理**/
                            if (StringUtils.isNotEmpty(nextUserName)) {
                                ret = this.processApproval(workItemId, currentUserCode, currentUserCode, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence, hashMap);
                                if (ret > 0) {
                                    if (CollectionUtil.isNotEmpty(usList)) {
                                        for (UsPolicyInfo policyInfo : usList) {
                                            policyInfo.setWorkItemId(String.valueOf(workItemId));
                                            usPolicyInfoService.update(policyInfo);
                                        }
                                    }
                                }
                            } else {
                                operateLog.setErrorMsg("获取审批人失败");
                                JsonResponse.fail(null, "获取审批人失败");
                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存割接计划失败");
                        JsonResponse.fail(null, "保存割接计划失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
    }

    /**
     * 流转下一步
     *
     * @param workItemID      活动实例id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人姓名
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param location        当前所处环节
     * @param message         审批意见
     * @param pmInstence      主单据
     * @return
     */
    private long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence, Map<String, Object> Hsmap) {
        long ret;
        String ids = MapUtil.getStr(Hsmap, "ids");
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserIds", nextUserName);//指定下一审批人
        }
        if (StringUtils.isNotEmpty(ids)) {
            map.put("ids", ids);
        }
        map.put("outcome", outcome);
        map.put("message", message);
        map.put("inputUserIds", nextUserName);
        map.put("nextUserName", nextUserName);
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        map.put("processDefId", "com.djglpt.flow.policyPropaganda");
        map.put("processType", "C");
        try {
            //添加流程审批意见
            processService.saveMsg(workItemID, message);
            ret = processService.completeWorkItem(workItemID, map);
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }


    @Override
    public JsonResponse saveSubmitTask(UsPolicyInfo usPolicyInfo, String workItemId, String outcome, String message, String nextUserName, String location, String processDefId, String notificationId, String source, String currentUserCode) {
        log.debug("起草接口----------saveSubmitTask---------->" + usPolicyInfo.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "applicationForm=" + usPolicyInfo.toString() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",notificationId=" + notificationId + ",source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            String pmInsId = usPolicyInfo.getPmInsId();
            operateLog.setBussinessKey(pmInsId);
            Map<String, Object> hashMap = new HashMap<>();
            List<String> idList = new ArrayList<>();
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            List<String> nextUserNameList = new ArrayList<>();
            if (StringUtils.isNotEmpty(nextUserName)) {
                nextUserNameList = Arrays.asList(nextUserName.split(","));
            }

            /**相关流转审批操作**/
            if (pmInstence != null) {
                /**归档操作，保存表单数据**/
                if ("end".equals(outcome)) {//归档操作，保存表单数据
                    if (StringUtils.isNotEmpty(usPolicyInfo.getId())) {
                        updateFileInfoById(usPolicyInfo);//更新附件
                    }
                    UsPolicyInfo newUsPolicyInfo = this.update(usPolicyInfo);//更新表单
                    if (newUsPolicyInfo == null) {
                        return JsonResponse.fail(newUsPolicyInfo, "表单更新失败");
                    }
                }

                /**获取用户**/
                IUser user = SecurityUtils.getCurrentUser();
                /**审批流转**/
                if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                    if ((!"end".equals(outcome) && StringUtils.isNotEmpty(nextUserName)) || "end".equals(outcome)) {
                        if (CollectionUtil.isNotEmpty(nextUserNameList)) {
                            for (String userName : nextUserNameList) {
                                if (StringUtils.isNotEmpty(userName)) {
                                    UsPolicyInfo usPolicyInfo1 = saveUsPolicyInfo(usPolicyInfo, userName);
                                    if (location.equals("djfupt.braAdminCheck")) {
                                        usPolicyInfo1.setPolicyStatue("1");
                                        usPolicyInfoService.update(usPolicyInfo1);
                                    }
                                    if ("end".equals(outcome)) {
                                        usPolicyInfo1.setPolicyStatue("2");
                                        usPolicyInfoService.update(usPolicyInfo1);
                                    }
                                    if (ObjectUtil.isNotEmpty(usPolicyInfo1)) {
                                        if (StringUtils.isNotEmpty(usPolicyInfo1.getId())) {
                                            idList.add(usPolicyInfo1.getId());
                                        }

                                    }
                                }

                            }
                        }
                        if (CollectionUtil.isNotEmpty(idList)) {
                            String ids = Joiner.on(",").join(idList);
                            hashMap.put("ids", ids);

                        }
                        ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence, hashMap);
                    } else {
                        ret = 0;
                        operateLog.setErrorMsg("审批人不能为空");
                        return JsonResponse.fail("审批人不能为空");
                    }
                }
            } else {
                operateLog.setErrorMsg("请联系管理员，主数据查找异常！pmInsId = " + pmInsId);
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
            /**提醒流转下一步信息**/
            String showMessage = "下发成功";
            return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        }
    }

    @Override
    public UsPolicyInfo findUsPolicyInfoInfo(String creator, String pmInsId) {
        return usPolicyInfoRepository.findUsPolicyInfoInfo(pmInsId, creator);
    }


    /**
     * 生成一个主单据信息
     *
     * @return
     */
    public boolean saveUsPmInstence(UsPolicyInfo usPolicyInfo, UsPmInstence usPmInstence) {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        String title = "";
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            String planId = usPolicyInfo.getId();
            if (org.apache.commons.lang3.StringUtils.isEmpty(planId) || Constants.STR_NULL.equals(planId)) {
                String currentStr = DateUtil.getCurrentStr();
                String count = usPmInstenceService.getCounts("C", currentStr);
                String workCode = OrderNumberCreate.generateNumber("C", count);//编号生成
                String pmInsId = usPmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                String processName = this.getProcessName(usPmInstence.getPmInsType());
                if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyType())) {
                    if ("1".equals(usPolicyInfo.getPolicyType())) {
                        title = usPolicyInfo.getPolicyStartTime() + "-" + usPolicyInfo.getPolicyEndTime() + "政策宣讲内容";
                    }
                    if ("2".equals(usPolicyInfo.getPolicyType())) {
                        title = usPolicyInfo.getYear() + usPolicyInfo.getMonth() + "政策宣讲内容";
                    }
                    if ("3".equals(usPolicyInfo.getPolicyType())) {
                        title = usPolicyInfo.getYear() + usPolicyInfo.getQuarterly() + "政策宣讲内容";
                    }
                    if ("4".equals(usPolicyInfo.getPolicyType())) {
                        title = usPolicyInfo.getYear() + "政策宣讲内容";
                    }
                }

                usPmInstence.setPmInsId(pmInsId);
                usPmInstence.setPmInsTitle(title);
                usPmInstence.setWorkCode(workCode);
                usPmInstence.setProcessName(processName);
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usPmInstence.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usPmInstence.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usPmInstence.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usPmInstence.setBelongCompanyName(iuser.getBelongCompanyName());
                    usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPmInstence.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                }
                usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                usPmInstence.setBelongOrgName(iuser.getBelongOrgName());
                usPmInstenceService.insert(usPmInstence);
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(usPmId)) {
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usPolicyInfo.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                    usPolicyInfo.setParentCompanyName(iuser.getBelongCompanyNameParent());
                    usPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usPolicyInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usPolicyInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                    usPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPolicyInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usPolicyInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    usPolicyInfo.setParentCompanyCode(iuser.getBelongCompanyCode());
                    usPolicyInfo.setParentCompanyName(iuser.getBelongCompanyName());
                }
                usPolicyInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPolicyInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usPolicyInfo.setBelongOrgName(iuser.getBelongOrgName());
                usPolicyInfo.setPmInsId(usPmInstence.getPmInsId());
                usPolicyInfo.setTitle(usPmInstence.getPmInsTitle());
                usPolicyInfo.setWorkCode(usPmInstence.getWorkCode());
                usPolicyInfo.setPolicyStatue("0");//初始状态

                UsPolicyInfo newUsPolicyInfo = usPolicyInfoService.insert(usPolicyInfo);
                newUsPolicyInfo.setTrueName(uumsSysUserinfoApi.findByUsername(newUsPolicyInfo.getCreator(), Constants.APP_CODE).getTruename());
                usPolicyInfoService.updatePolicyInfo(newUsPolicyInfo);
                updateFileInfoById(usPolicyInfo);//更新附件
                if (CollectionUtil.isNotEmpty(usPolicyInfo.getUsPantchDetailList())) {
                    for (UsPantchDetail usPantchDetail : usPolicyInfo.getUsPantchDetailList()) {
                        UsPantchDetail newUsPantchDetail = usPantchDetailService.findById(usPantchDetail.getId());
                        newUsPantchDetail.setWorkCode(usPmInstence.getWorkCode());
                        newUsPantchDetail.setIsFlag("1");
                        newUsPantchDetail.setPolicyId(usPolicyInfo.getId());
                        newUsPantchDetail.setPmInsId(usPolicyInfo.getPmInsId());
                        if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                            newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                            newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyNameParent());
                            newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                            newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                            newUsPantchDetail.setBelongDepartmentName(iuser.getBelongCompanyName());
                            newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                        }
                        if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                            newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyName());
                            newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCode());
                            newUsPantchDetail.setBelongDepartmentName(iuser.getBelongDepartmentName());
                            newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                            newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCode());
                            newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyName());
                        }
                        newUsPantchDetail.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                        usPantchDetailService.update(newUsPantchDetail);
                    }
                }
                flag = true;
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }

    /**
     * 启动流程时候有多少个流转下一步的待办接收人，这块就循环多少次存储
     *
     * @param usPolicyInfo
     * @return
     */

    public UsPolicyInfo saveUsPolicyInfo(UsPolicyInfo usPolicyInfo, String nextUserName) {
        boolean flag = false;
        UsPolicyInfo newUsPolicyInfo = null;
        if (StringUtils.isNotEmpty(nextUserName)) {
            newUsPolicyInfo = new UsPolicyInfo();
            BeanUtils.copyProperties(usPolicyInfo, newUsPolicyInfo);//将 a拷贝到b
            newUsPolicyInfo.setId("");
            SimpleUser iuser = uumsSysUserinfoApi.findByUsername(nextUserName, Constants.APP_CODE);
            if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                List<UsAdminManager> usAdminManagerList = usAdminManagerService.findByUserNameAndEnabled(nextUserName);
                if (CollectionUtil.isNotEmpty(usAdminManagerList)) {
                    if (StringUtils.isNotEmpty(usAdminManagerList.get(0).getGridName())) {
                        newUsPolicyInfo.setGridName(usAdminManagerList.get(0).getGridName());
                        newUsPolicyInfo.setTrueName(iuser.getTruename());
                    }
                }
                newUsPolicyInfo.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                newUsPolicyInfo.setParentCompanyName(iuser.getBelongCompanyNameParent());
                newUsPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                newUsPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                newUsPolicyInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                newUsPolicyInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
            }
            if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                newUsPolicyInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                newUsPolicyInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                newUsPolicyInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                newUsPolicyInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                newUsPolicyInfo.setParentCompanyCode(iuser.getBelongCompanyCode());
                newUsPolicyInfo.setParentCompanyName(iuser.getBelongCompanyName());
            }
            newUsPolicyInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
            newUsPolicyInfo.setBelongOrgCode(iuser.getBelongOrgCode());
            newUsPolicyInfo.setBelongOrgName(iuser.getBelongOrgName());
            newUsPolicyInfo.setCreator(iuser.getUsername());
            newUsPolicyInfo.setModifier(iuser.getUsername());
            newUsPolicyInfo.setModifiedTime(LocalDateTime.now());
            newUsPolicyInfo.setCreatedTime(LocalDateTime.now());
            UsPolicyInfo usPolicyInfo1 = usPolicyInfoService.insert(newUsPolicyInfo);
            newUsPolicyInfo.setId(usPolicyInfo1.getId());
            if (StringUtils.isNotEmpty(usPolicyInfo1.getId())) {
                updateFileInfoById(usPolicyInfo1);//更新附件
            }

            if (CollectionUtil.isNotEmpty(usPolicyInfo.getUsPantchDetailList())) {
                for (UsPantchDetail usPantchDetail : usPolicyInfo.getUsPantchDetailList()) {
                    UsPantchDetail newUsPantchDetail = new UsPantchDetail();
                    BeanUtils.copyProperties(usPantchDetail, newUsPantchDetail);//将 a拷贝到b
                    if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                        newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                        newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyNameParent());
                        newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                        newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                        newUsPantchDetail.setBelongDepartmentName(iuser.getBelongCompanyName());
                        newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    }
                    if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                        newUsPantchDetail.setBelongCompanyName(iuser.getBelongCompanyName());
                        newUsPantchDetail.setBelongCompanyCode(iuser.getBelongCompanyCode());
                        newUsPantchDetail.setBelongDepartmentName(iuser.getBelongDepartmentName());
                        newUsPantchDetail.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                        newUsPantchDetail.setParentCompanyCode(iuser.getBelongCompanyCode());
                        newUsPantchDetail.setParentCompanyName(iuser.getBelongCompanyName());
                    }
                    newUsPantchDetail.setCreator(iuser.getUsername());
                    newUsPantchDetail.setModifier(iuser.getUsername());
                    newUsPantchDetail.setModifiedTime(LocalDateTime.now());
                    newUsPantchDetail.setCreatedTime(LocalDateTime.now());
                    newUsPantchDetail.setId("");
                    newUsPantchDetail.setWorkCode(usPolicyInfo.getWorkCode());
                    newUsPantchDetail.setIsFlag("1");
                    newUsPantchDetail.setPolicyId(usPolicyInfo.getId());
                    newUsPantchDetail.setPmInsId(usPolicyInfo.getPmInsId());
                    usPantchDetailService.insert(newUsPantchDetail);
                }
            }
        }
        return newUsPolicyInfo;
    }

    /**
     * 获取使用的流程名称
     *
     * @return
     */
    public String getProcessName(String type) {
        String processName = null;
        switch (type) {
            case Constants.PM_INS_TYPE_A:
                processName = Constants.CASE_REPORT;
                break;
            case Constants.PM_INS_TYPE_B:
                processName = Constants.PROBLEM_REPOR;
                break;
            case Constants.PM_INS_TYPE_C:
                processName = Constants.POLICY_PROPAGANDA;
                break;
            default:
                break;
        }
        return processName;
    }

    /**
     * 获取使用的流程名称
     *
     * @return
     */
    public Map<String, String> getProcessMap(String type) {
        Map<String, String> map = Maps.newHashMap();
        String processType = null;
        String processName = null;
        switch (type) {
            case Constants.PM_INS_TYPE_A:
                processType = "A";
                processName = Constants.CASE_REPORT;
                break;
            case Constants.PM_INS_TYPE_B:
                processType = "B";
                processName = Constants.PROBLEM_REPOR;
                break;
            case Constants.PM_INS_TYPE_C:
                processType = "B";
                processName = Constants.POLICY_PROPAGANDA;
                break;
            default:
                break;
        }
        map.put("processName", processName);
        map.put("processType", processType);
        return map;
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(nextUserName)) {
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                }
            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }

        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }


    @Override
    public JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location) {
        UsPolicyInfo usPolicyInfo = new UsPolicyInfo();
        SysOperateLog operateLog = new SysOperateLog();
        List<SysFile> feedBcakFile = new ArrayList<>();
        List<SysFile> policyOutlineile = new ArrayList<>();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source +
                ",userCode=" + userCode + ",location=" + location + ",pmInsId=" + pmInsId;
        operateLog.setInterfaceParam(params);
        try {
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
                userCode = rsaEncryptor.decrypt(userCode);
            }
            /**点击办理查看详情**/
            if (null != processInstId) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    usPolicyInfo = usPolicyInfoService.findUsPolicyInfoInfo(userCode, pmInsId);
                    if (usPolicyInfo != null) {
                        List<UsPantchDetail> usPantchDetailInfo = usPantchDetailService.findUsPantchDetailInfo(pmInsId, userCode);
                        if (CollectionUtil.isNotEmpty(usPantchDetailInfo)) {
                            usPolicyInfo.setUsPantchDetailList(usPantchDetailInfo);
                        }
                        if (StringUtils.isNotEmpty(usPolicyInfo.getFeedBcakFileIds())) {
                            List<String> fileIdList = Arrays.asList(usPolicyInfo.getFeedBcakFileIds().split(","));
                            if (CollectionUtil.isNotEmpty(fileIdList)) {
                                for (String fileIds : fileIdList) {
                                    SysFile sysFile = fileExtendService.findById(fileIds);
                                    if (ObjectUtil.isNotEmpty(sysFile)) {
                                        feedBcakFile.add(sysFile);
                                    }

                                }
                            }
                            usPolicyInfo.setFeedBcakFile(feedBcakFile);
                        }
                        if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyOutlineIds())) {
                            List<String> fileIdList = Arrays.asList(usPolicyInfo.getPolicyOutlineIds().split(","));
                            if (CollectionUtil.isNotEmpty(fileIdList)) {
                                for (String fileIds : fileIdList) {
                                    SysFile sysFile = fileExtendService.findById(fileIds);
                                    if (ObjectUtil.isNotEmpty(sysFile)) {
                                        policyOutlineile.add(sysFile);
                                    }

                                }
                            }
                            usPolicyInfo.setPolicyOutline(policyOutlineile);
                        }
                        operateLog.setBussinessKey(usPolicyInfo.getPmInsId());
                    }
                }
            } else {
                //草稿箱详情查询
                usPolicyInfo = usPolicyInfoService.findUsPolicyInfoInfo(userCode, pmInsId);
                if (usPolicyInfo != null) {
                    List<UsPantchDetail> usPantchDetailInfo = usPantchDetailService.findUsPantchDetailInfo(pmInsId, userCode);
                    if (CollectionUtil.isNotEmpty(usPantchDetailInfo)) {
                        usPolicyInfo.setUsPantchDetailList(usPantchDetailInfo);
                    }
                    operateLog.setBussinessKey(usPolicyInfo.getPmInsId());
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usPolicyInfo);
    }


    @Override
    public JsonResponse findFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location, String workItemId) {
        UsPolicyInfo usPolicyInfo = new UsPolicyInfo();
        List<UsPolicyInfo> usPolicyInfoList = new ArrayList<>();
        SysOperateLog operateLog = new SysOperateLog();
        List<SysFile> feedBcakFile = new ArrayList<>();
        List<SysFile> policyOutlineile = new ArrayList<>();
        IUser user = SecurityUtils.getCurrentUser();
        try {
            /**点击办理查看详情**/
            if (null != processInstId) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    //    WfWorkItemModel workItemId1 = wfWorkItemRepository.findByWorkItemId(workItemId);
                    usPolicyInfoList = usPolicyInfoRepository.findUsPolicyInfoInfoAndCompanyCode(pmInsId);
                    if (usPolicyInfoList.size() > 0) {
                        usPolicyInfo = usPolicyInfoList.get(0);
                        List<UsPantchDetail> usPantchDetailInfo = usPantchDetailService.findUsPantchDetailInfo(pmInsId, user.getUsername());
                        if (CollectionUtil.isNotEmpty(usPantchDetailInfo)) {
                            usPolicyInfo.setUsPantchDetailList(usPantchDetailInfo);
                        }
                        if (StringUtils.isNotEmpty(usPolicyInfo.getFeedBcakFileIds())) {
                            List<String> fileIdList = Arrays.asList(usPolicyInfo.getFeedBcakFileIds().split(","));
                            if (CollectionUtil.isNotEmpty(fileIdList)) {
                                for (String fileIds : fileIdList) {
                                    SysFile sysFile = fileExtendService.findById(fileIds);
                                    if (ObjectUtil.isNotEmpty(sysFile)) {
                                        feedBcakFile.add(sysFile);
                                    }

                                }
                            }
                            usPolicyInfo.setFeedBcakFile(feedBcakFile);
                        }
                        if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyOutlineIds())) {
                            List<String> fileIdList = Arrays.asList(usPolicyInfo.getPolicyOutlineIds().split(","));
                            if (CollectionUtil.isNotEmpty(fileIdList)) {
                                for (String fileIds : fileIdList) {
                                    SysFile sysFile = fileExtendService.findById(fileIds);
                                    if (ObjectUtil.isNotEmpty(sysFile)) {
                                        policyOutlineile.add(sysFile);
                                    }

                                }
                            }
                            usPolicyInfo.setPolicyOutline(policyOutlineile);
                        }
                        operateLog.setBussinessKey(usPolicyInfo.getPmInsId());
                    }
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        }
        return JsonResponse.success(usPolicyInfo);
    }


    /**
     * 政策宣讲台账信息
     */
    // @Override
    public List<Map<String, Object>> findPolicyLederold(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer(" select wf.receipt_code, t.belong_company_name," +
                " COUNT(distinct wf.receipt_code) as allcount," +
                " COUNT(CASE WHEN wf.current_state = '10' THEN '01'END) as lectures ," +
                " COUNT(CASE WHEN wf.current_state = '12' THEN '02'END) as alLectures," +
                " 100 * ROUND(COUNT(CASE WHEN wf.current_state = '12' THEN '02'END) / COUNT(distinct wf.receipt_code) OVER(), 4) || '%' PERCENT" +
                "  from US_POLICY_INFO t, wf_workitem_model wf" +
                " where t.enabled = 1" +
                "   and wf.enabled = 1" +
                "   and t.pm_ins_id=wf.receipt_code" +
                "   and wf.activity_def_id='djfupt.djAdminCheck'" +
                "    ");
        String sql2 = " group by wf.receipt_code, t.belong_company_name ";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));

        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    /**
     * 政策宣讲台账信息
     * findPolicyLeder
     * 这才是老的
     */
    @Override
    public List<Map<String, Object>> newFindPolicyLeder(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.belong_company_name," +
                "      COUNT(CASE WHEN t.policy_statue = '1' THEN '01' END) as preach," +
                "      COUNT(CASE WHEN t.policy_statue = '2' THEN '02' END) as dopreach," +
                "      COUNT(CASE WHEN t.belong_company_type_dict_value = '03' THEN '03' END) as contentCount" +
                "      from US_POLICY_INFO t, wf_workitem_model wf" +
                "      where t.enabled = 1" +
                "       and wf.enabled = 1" +

                "       and t.belong_company_type_dict_value='03'" +

                "       and t.pm_ins_id = wf.receipt_code");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and t.policy_time >= :startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append("  and t.policy_time <=:endDate ");
            param.put("endDate", endDate);
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : list) {
            Map<String, Object> newMap = new HashMap<>(stringObjectMap);
            if (null != stringObjectMap.get("preach")) {
                preach = stringObjectMap.get("preach").toString();
            }
            if (null != stringObjectMap.get("dopreach")) {
                dopreach = stringObjectMap.get("dopreach").toString();
            }
            String rate = "";
            float f1 = Float.parseFloat(preach);
            float f2 = Float.parseFloat(dopreach);
            rate = ((f2 / (f1 + f2)) * 100) + "%";
            newMap.put("rate", rate);
            resultList.add(newMap);
            stringObjectMap.put("contentCount", f1 + f2);

        }
        resultList = FormatTool.formatConversion(resultList);//驼峰转换
        return resultList;
    }

    /**
     * 政策宣讲台账信息
     * newFindPolicyLeder
     * 这是新写的注意注意，千万注意
     */

    @Override
    public List<Map<String, Object>> findPolicyLeder(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
            policyTime = year + "-" + month;
        }

        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select   t.belong_company_name , " +
                "  (select count(t.id) from US_ADMIN_MANAGER asd " +
                "                     where asd.enabled=1 and asd.belong_company_name=t.belong_company_name" +
                "                       and asd.role_user_id= 'djfupt_002') as preach," +
                "                        COUNT(CASE WHEN t.policy_time is not null THEN '01' END) as dopreach " +
                "                                      from US_POLICY_INFO t, act_business_status ac  " +
                "                                      where t.enabled = 1  " +
                "                                      and ac.enabled = 1  " +
                "                                      and t.pm_ins_id = ac.receipt_code " +
                "                                      and t.policy_statue >0 " +
                "                                      and t.belong_company_name not like '%省公司%' ");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyTime)) {
            sql.append(" and  SUBSTR(nvl(t.policy_time,'1950-01'),0,7) = :policyTime");
            param.put("policyTime", policyTime);
        }

        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    sql.append(" and   t.grid_name   =:gridName  ");
                    List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                    if (adminManagers.size() > 0) {
                        param.put("gridName", adminManagers.get(0).getGridName());
                    }

                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList = FormatTool.formatConversion(list);//驼峰转换
        if (resultList.size() > 0) {
            //计算宣讲内容数量
            for (Map<String, Object> map : resultList) {
                Map<String, Object> params = Maps.newHashMap();
                StringBuffer sqls = new StringBuffer("select distinct (d.id)" +
                        "  from us_pantch_detail d" +
                        "  left join us_policy_info t" +
                        "    on d.pm_ins_id = t.pm_ins_id" +
                        " where " +
                        "    t.belong_company_name = :companyName" +
                        "   and t.policy_statue = 0" +
                        "   and t.creator = d.creator" +
                        "   and t.enabled = 1" +
                        "   and d.enabled = 1" +
                        "   and t.pm_ins_id in" +
                        "       (select a.pm_ins_id" +
                        "          from us_policy_info a" +
                        "         where " +
                        "            a.belong_company_name = :companyName" +
                        " and a.enabled=1  and SUBSTR(nvl(a.policy_time, '1950-01'), 0, 7) = :policyTime  )");
                params.put("companyName", map.get("belongCompanyName"));
                params.put("policyTime", policyTime);
                List<Map<String, Object>> lists = customDynamicWhere.queryNamedParameterForList(sqls.toString(), params);
                map.put("contentcount", lists.size());

                if (null != map.get("preach")) {
                    preach = map.get("preach").toString();
                }
                if (null != map.get("dopreach")) {
                    dopreach = map.get("dopreach").toString();
                }
                String rate = "";
                float f1 = Float.parseFloat(preach);
                float f2 = Float.parseFloat(dopreach);
                if (preach.equals("0") || dopreach.equals("0")) {
                    rate = "0";
                } else {
                    float f3 = (f2 / f1) * 100;
                    BigDecimal b = new BigDecimal(f3);
                    f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                    if(Float.compare(f3, 100.0f)>0){
                        f3=100;
                    }
                    rate = f3 + "%";
                }

                map.put("rate", rate);
            }
        }
        return resultList;
    }


    @Override
    public List<Map<String, Object>> findPolicyLederOther(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//4度
        String companyCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");
        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
            policyTime = year + "-" + month;
        }

        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));


        List<Map<String, Object>> mapList = new ArrayList<>();
        if(isRoleAdmin){

            Map<String, Object> map = new HashMap<>();
            Map<String, Object> params = Maps.newHashMap();
            List<UsPolicyInfo>  dopreach = usPolicyInfoRepository.findAllByCountDep(SecurityUtils.getCurrentUser().getBelongDepartmentCode(), policyTime);


            if (dopreach.size() > 0) {
                StringBuffer sqls = new StringBuffer("select count(d.id) from us_pantch_detail d " +
                        "where" +
                        " d.pm_ins_id =:pmInsId " +
                        "and d.creator=:creator     ");
                params.put("pmInsId", dopreach.get(0).getPmInsId());
                params.put("creator", dopreach.get(0).getCreator());
                List<Map<String, Object>> lists = customDynamicWhere.queryNamedParameterForList(sqls.toString(), params);
                map.put("contentcount", lists.size());
            } else {
                map.put("contentcount", 0);
            }
            int preach  = adminManagerRepository.findByDepCodeAndEnabledAndRoleUserIdss(user.getBelongDepartmentCode(), Constants.FJFUPT_BRO);




            map.put("preach", preach);
            map.put("dopreach", dopreach.size());
            map.put("belongCompanyName", user.getBelongDepartmentName());
            String rate = "";
            float f1 = Float.parseFloat(String.valueOf(preach));
            float f2 = Float.parseFloat(String.valueOf(dopreach.size()));
            if (preach == 0 || dopreach.size() == 0) {
                rate = "0";
            } else {
                float f3 = (f2 / f1) * 100;
                BigDecimal b = new BigDecimal(f3);
                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                if(Float.compare(f3, 100.0f)>0){
                    f3=100;
                }

                rate = f3 + "%";
            }

            map.put("rate", rate);
            mapList.add(map);

        }else {

            List<SysDictValue> sysDictValues = new ArrayList<>();
            //查出当前下的组织
            List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
            int i = 0;
            if (simpleOrgList.size() > 0) {
                for (SimpleOrg simpleOrg : simpleOrgList) {
                    if (i == 0) {
                        simpleOrg.setOrgName(simpleOrg.getDisplayName());

                    }
                    if (StringUtils.isNotEmpty(companyCode)) {
                        if (!simpleOrg.getOrgCode().equals(companyCode)) {
                            continue;
                        }
                    }
                    if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                        continue;
                    }
                    SysDictValue sysDictValue = new SysDictValue();
                    sysDictValue.setValue(simpleOrg.getOrgCode());
                    sysDictValue.setName(simpleOrg.getOrgName());
                    sysDictValue.setFlag(simpleOrgList.get(0).getParentOrgCode());
                    sysDictValues.add(sysDictValue);
                    i++;
                }
            }

            if (sysDictValues.size() > 0) {
                int is = 0;
                //计算宣讲内容数量
                for (SysDictValue sysDictValue : sysDictValues) {

                    Map<String, Object> map = new HashMap<>();
                    Map<String, Object> params = Maps.newHashMap();
                    List<UsPolicyInfo> dopreach = new ArrayList<>();
                    if (is == 0) {
                        dopreach = usPolicyInfoRepository.findAllByCountComCodes(sysDictValue.getValue(), policyTime);
                    } else {
                        dopreach = usPolicyInfoRepository.findAllByCountComCode(sysDictValue.getValue(), policyTime);
                    }

                    if (dopreach.size() > 0) {
                        StringBuffer sqls = new StringBuffer("select count(d.id) from us_pantch_detail d " +
                                "where" +
                                " d.pm_ins_id =:pmInsId " +
                                "and d.creator=:creator     ");
                        params.put("pmInsId", dopreach.get(0).getPmInsId());
                        params.put("creator", dopreach.get(0).getCreator());
                        List<Map<String, Object>> lists = customDynamicWhere.queryNamedParameterForList(sqls.toString(), params);
                        map.put("contentcount", lists.size());
                    } else {
                        map.put("contentcount", 0);
                    }
                    int preach = 0;
                    if (is == 0) {

                        preach = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(sysDictValue.getValue(), Constants.FJFUPT_BRO);

                    } else {
                        preach = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIds(sysDictValue.getValue(), Constants.FJFUPT_BRO);

                    }


                    map.put("preach", preach);
                    map.put("dopreach", dopreach.size());
                    map.put("belongCompanyName", sysDictValue.getName());
                    String rate = "";
                    float f1 = Float.parseFloat(String.valueOf(preach));
                    float f2 = Float.parseFloat(String.valueOf(dopreach.size()));
                    if (preach == 0 || dopreach.size() == 0) {
                        rate = "0";
                    } else {
                        float f3 = (f2 / f1) * 100;
                        BigDecimal b = new BigDecimal(f3);
                        f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                        rate = f3 + "%";
                    }

                    map.put("rate", rate);
                    mapList.add(map);
                    i++;

                    is++;
                }
            }
        }




        return mapList;
    }

    @Override
    public List<Map<String, Object>> findDpPolicyLeder(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
//        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
//            policyTime = "2023" + "-" + "02";
//        }
//        policyTime = "2023" + "-" + "02";
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数


        StringBuffer sql1 = new StringBuffer("select dict.name as belong_company_name ,(");
        StringBuffer sql2 = new StringBuffer("select count(CASE WHEN t.policy_time is not null THEN 1 else 0 END) as dopreach" +
                " from US_POLICY_INFO t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and ac.enabled = 1" +
                "   and t.belong_company_name=dict.name" +
                "   and t.pm_ins_id = ac.receipt_code" +
                "   and t.policy_statue > 0" +
                "   and t.belong_company_name not like '%省公司%'" +
                "   and  SUBSTR(nvl(t.policy_time,'1950-01'),0,7) =TO_CHAR(SYSDATE-1,'YYYY-MM')");

        StringBuffer sql3 = new StringBuffer(") as dopreach ," +
                "   (select count(ad.id) from us_admin_manager ad " +
                "   where ad.enabled=1 and " +
                "   ad.belong_company_name=dict.name ) as preach" +
                "   from sys_dict_value dict  where dict.enabled=1 " +
                "   and dict_type='company' " +
                "   order by dict.display_order asc");
//        String sql3 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql2.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyTime)) {
            sql2.append(" and  SUBSTR(nvl(t.policy_time,'1950-01'),0,7) = TO_CHAR(SYSDATE-1,'YYYY-MM')");
            param.put("policyTime", policyTime);
        }

        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            List<UsAdminManager> adminManagers = null;
            if (StrUtil.equals(DataPermissionConstants.QUERY_LEVEL_FOUR , queryLevel)  ) {
                adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                if (CollectionUtil.isEmpty(adminManagers)) {
                    queryLevel = DataPermissionConstants.QUERY_LEVEL_DEFAULT;
                }
            }
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql2, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql2, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    sql2.append(" and   t.grid_name   =:gridName  ");
                    param.put("gridName", adminManagers.get(0).getGridName());
                    break;
                default:
                    sql2.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql1.append(sql2).append(sql3).toString(), param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList = FormatTool.formatConversion(list);//驼峰转换
        if (resultList.size() > 0) {
            //计算宣讲内容数量
            for (Map<String, Object> map : resultList) {
                Map<String, Object> params = Maps.newHashMap();
                StringBuffer sqls = new StringBuffer("select distinct (d.id)" +
                        "  from us_pantch_detail d" +
                        "  left join us_policy_info t" +
                        "    on d.pm_ins_id = t.pm_ins_id" +
                        " where " +
                        "    t.belong_company_name = :companyName" +
                        "   and t.policy_statue = 0" +
                        "   and t.creator = d.creator" +
                        "   and t.enabled = 1" +
                        "   and d.enabled = 1" +
                        "   and t.pm_ins_id in" +
                        "       (select a.pm_ins_id" +
                        "          from us_policy_info a" +
                        "         where " +
                        "            a.belong_company_name = :companyName" +
                        " and a.enabled=1  and SUBSTR(nvl(a.policy_time, '1950-01'), 0, 7) = TO_CHAR(SYSDATE-1,'YYYY-MM')  )");
                params.put("companyName", map.get("belongCompanyName"));
                params.put("policyTime", policyTime);
                List<Map<String, Object>> lists = customDynamicWhere.queryNamedParameterForList(sqls.toString(), params);
                map.put("contentcount", lists.size());
//                int preachs = usAdminManagerService.findByCompanyCodeAndEnabledAndRoleUserId(map.get("belongCompanyName").toString(), Constants.FJFUPT_BRO);

//                map.put("preach", preachs);
                if (null != map.get("preach")) {
                    preach = map.get("preach").toString();
                }
                if (null != map.get("dopreach")) {
                    dopreach = map.get("dopreach").toString();
                }
                String rate = "";
                float f1 = Float.parseFloat(preach);//网格数  应宣讲次数
                float f2 = Float.parseFloat(dopreach);// 已宣讲次数
                if (preach.equals("0") || dopreach.equals("0")) {
                    rate = "0";
                } else {
                    rate = (f2 / f1 * 100) + "%";
                }
                map.put("rate", rate);
            }
        }
        return resultList;
    }

    /**
     * 政策宣讲台账信息
     * newFindPolicyLeder
     * 这是新写的注意注意，千万注意
     */
    @Override
    public List<Map<String, Object>> findPolicyLederOlds(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.belong_company_name," +
                "      COUNT(CASE WHEN t.policy_statue = '0' THEN '01' END) as preach,      " +
                "      COUNT(CASE WHEN ac.current_state = '7' THEN '02' END) as dopreach    " +
                "      from US_POLICY_INFO t, act_business_status ac" +
                "      where t.enabled = 1" +
                "      and ac.enabled = 1" +
                "      and t.pm_ins_id = ac.receipt_code" +
                "      and t.belong_company_name not like '%省公司%'");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and t.policy_time >= :startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append("  and t.policy_time <=:endDate ");
            param.put("endDate", endDate);
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        List<Map<String, Object>> mapList = usPolicyInfoService.findPolicyCount(param);
        //合并两个List
        List<Map<String, Object>> mererList = MergeUtil.merge(list, mapList, "BELONG_COMPANY_NAME");
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : mererList) {
            Map<String, Object> newMap = new HashMap<>(stringObjectMap);
            if (null != stringObjectMap.get("preach")) {//应宣讲次数
                preach = stringObjectMap.get("preach").toString();
            }
            if (null != stringObjectMap.get("dopreach")) {//以宣讲次数
                dopreach = stringObjectMap.get("dopreach").toString();
            }
            String rate = "";
            float f1 = Float.parseFloat(preach);//应宣讲次数
            float f2 = Float.parseFloat(dopreach);//以宣讲次数
            if (f1 == 0.0 || f2 == 0.0) {
                rate = "0%";
            } else {
                rate = ((f2 / f1) * 100) + "%";
            }
            newMap.put("rate", rate);
            resultList.add(newMap);
        }
        resultList = FormatTool.formatConversion(resultList);//驼峰转换
        return resultList;
    }

    /**
     * 政策宣讲台账信息
     * 宣讲内容数量信息
     * 查询的时间段内的各单位的宣讲事项的总数
     * 只统计第二部时候的数据
     */
    @Override
    public List<Map<String, Object>> findPolicyCount(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.belong_company_name, " +
                "       count(t.belong_company_name) as contentCount" +
                "  from us_pantch_detail t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and ac.enabled = 1" +
                "   and t.pm_ins_id = ac.receipt_code" +
                "   and t.belong_company_type_dict_value = '02'");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and t.policy_time >= :startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append("  and t.policy_time <=:endDate ");
            param.put("endDate", endDate);
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        return list;
    }


    /**
     * 政策宣讲台账--点击百分号
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findPolicyDeatilLeder(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//宣讲结束时间
        String gridName = cn.hutool.core.map.MapUtil.getStr(resultMap, "gridName");//网格名称
        String title = cn.hutool.core.map.MapUtil.getStr(resultMap, "title");//标题
        String policyStatue = cn.hutool.core.map.MapUtil.getStr(resultMap, "policyStatue");//是否已完成\
        String companyCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");//4度

        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
            policyTime = year + "-" + month;
        }
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.*," +
                "       ac.receipt_title as titles," +
                "       ac.current_state," +
                "       (select ad.grid_name" +
                "          from djfupt.us_admin_manager ad" +
                "         where ad.enabled = 1" +
                "           and ad.user_name = t.creator) as names, " +
                "                          (select ad.true_name " +
                "                          from djfupt.us_admin_manager ad " +
                "                         where ad.enabled = 1 " +
                "                           and ad.user_name = t.creator) as true_names " +
                "  from djfupt.US_POLICY_INFO t" +
                "       " +
                "      ," +
                "       djfupt.act_business_status ac" +
                "" +
                " where t.enabled = 1" +
                "   and ac.enabled = 1" +
                "      " +
                "   and t.policy_statue > 0" +
                "   and t.pm_ins_id = ac.receipt_code");
        Map<String, Object> param = Maps.newHashMap();

        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and t.policy_time >= :startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and t.policy_time <=:endDate ");
            param.put("endDate", endDate);
        }

        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(gridName)) {
            sql.append(" and  (t.grid_name  like concat(concat('%', :gridName), '%')  or  t.policy_address  like concat(concat('%', :gridName), '%'))");
            param.put("gridName", gridName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(title)) {
            sql.append(" and ac.receipt_title  like concat(concat('%', :title), '%') ");
            param.put("title", title);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyStatue) && policyStatue.equals("7")) {
            sql.append(" and t.policy_time is not null ");

        } else if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyStatue)) {
            sql.append(" and t.policy_time is  null ");
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));

        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));


        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(companyCode)) {
            sql.append("    and (t.belong_company_code = :companyCode or" +
                    "       t.belong_department_code =:companyCode) ");
            param.put("companyCode", companyCode);
        } else if (isRoleAdmin) {
            sql.append("    and    t.belong_department_code =:companyCode ");
            param.put("companyCode", SecurityUtils.getCurrentUser().getBelongDepartmentCode());

        } else if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    //网格五级权限

                    List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                    if (adminManagers.size() > 0) {
                        sql.append(" and   t.grid_name   =:gridName  ");
                        param.put("gridName", adminManagers.get(0).getGridName());
                    } else {
                        sql.append(" and  t.creator =:username  ");
                        param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    }

                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        sql.append(" order by t.created_time desc ");
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        if (list.size() > 0) {
            for (Map<String, Object> map : list) {
                map.put("title", map.get("titles"));
                map.put("title", map.get("titles"));
                if (map.get("policyTime") != null) {
                    map.put("currentState", "7");
                } else {
                    map.put("currentState", "2");
                }


                if (null == map.get("gridName")) {
                    if (null != map.get("names")) {
                        map.put("gridName", map.get("names"));
                    } else {
                        map.put("names", map.get("policyAddress"));
                        map.put("gridName", map.get("policyAddress"));
                    }
                }
                if (null == map.get("trueNames")) {
                    map.put("trueNames", map.get("trueName"));
                }


            }

        }

        return list;
    }


    /**
     * 政策宣讲台账--点击百分号
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findPolicyDeatilLederOther(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//宣讲结束时间
        String gridName = cn.hutool.core.map.MapUtil.getStr(resultMap, "gridName");//网格名称
        String title = cn.hutool.core.map.MapUtil.getStr(resultMap, "title");//标题
        String policyStatue = cn.hutool.core.map.MapUtil.getStr(resultMap, "policyStatue");//是否已完成

        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
            policyTime = year + "-" + month;
        }
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.*, ac.receipt_title as titles, ac.current_state " +
                "  from US_POLICY_INFO t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and ac.enabled = 1" +
                "   and ac.enabled = 1" +
                "   and t.policy_statue > 0 " +
                "   and t.pm_ins_id = ac.receipt_code");
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }

        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and t.policy_time >= :startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and t.policy_time <=:endDate ");
            param.put("endDate", endDate);
        }

        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(gridName)) {
            sql.append(" and t.grid_name  like concat(concat('%', :gridName), '%') ");
            param.put("gridName", gridName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(title)) {
            sql.append(" and ac.receipt_title  like concat(concat('%', :title), '%') ");
            param.put("title", title);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyStatue) && policyStatue.equals("7")) {
            sql.append(" and t.policy_time is not null ");

        } else if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyStatue)) {
            sql.append(" and t.policy_time is  null ");
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    //网格五级权限
                    sql.append(" and   t.grid_name   =:gridName  ");
                    List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                    if (adminManagers.size() > 0) {
                        param.put("gridName", adminManagers.get(0).getGridName());
                    }

                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        sql.append(" order by t.created_time desc ");
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        if (list.size() > 0) {
            for (Map<String, Object> map : list) {
                map.put("title", map.get("titles"));
                map.put("title", map.get("titles"));
                if (map.get("policyTime") != null) {
                    map.put("currentState", "7");
                } else {
                    map.put("currentState", "2");
                }
            }
        }

        return list;
    }

    /**
     * 政策宣讲台账导出
     *
     * @param currentUserCode
     * @param request
     * @param response
     * @param resultMap
     */
    @Override
    public void exportPolicyLeder(String currentUserCode, HttpServletRequest request, HttpServletResponse response, UsPolicyExport usPolicyExpor) {
        String policyTime = null;
        try {
            Map<String, Object> paramMap = new HashMap<>();
            if (StringUtils.isNotEmpty(usPolicyExpor.getCity())) {
                paramMap.put("city", usPolicyExpor.getCity());
            }

            String year = usPolicyExpor.getYear();//
            String month = usPolicyExpor.getMonth();//
            if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
                paramMap.put("year", year);
                paramMap.put("month", month);
            }

            String path = request.getServletContext().getRealPath("down");
            List<Map<String, Object>> resultData;
            IUser user = SecurityUtils.getCurrentUser();
            if (user.getBelongCompanyTypeDictValue().equals("01")) {
                resultData = usPolicyInfoService.findPolicyLeder(paramMap);
            } else {
                resultData = usPolicyInfoService.findPolicyLederOther(paramMap);
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = "政策宣讲数据统计" + dateStr + ".xls";
            List<UsPolicyExport> askBillList = new ArrayList<>();
            for (Map<String, Object> resultDatum : resultData) {
                UsPolicyExport usPolicyExport = creatModelRiskDate(resultDatum);
                askBillList.add(usPolicyExport);
            }
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Type", "application/msexcel");
            response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String targetFileName = path + "" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<UsPolicyExport> exportUtil = new ExcelUtil<>(UsPolicyExport.class);
            exportUtil.exportExcel(askBillList, "政策宣讲台账列表", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    /**
     * 政策宣讲台账导出
     * 政策宣讲台账--点击百分号
     *
     * @param currentUserCode
     * @param request
     * @param response
     * @param resultMap
     */
    @Override
    public void exportPolicyDeatilLeder(String currentUserCode, HttpServletRequest request, HttpServletResponse response, UsPolicyInfo policyInfo) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("city", policyInfo.getCity());
            paramMap.put("gridName", policyInfo.getGridName());
            paramMap.put("title", policyInfo.getTitle());
            paramMap.put("policyStatue", policyInfo.getPolicyStatue());
            paramMap.put("year", policyInfo.getYear());
            paramMap.put("month", policyInfo.getMonth());
            paramMap.put("startTime", policyInfo.getStartTime());
            paramMap.put("endTime", policyInfo.getEndTime());
            //  paramMap.put("endTime", policyInfo.getEndTime());

            String path = request.getServletContext().getRealPath("down");
            List<Map<String, Object>> resultData = this.findPolicyDeatilLeder(paramMap);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = "政策宣讲台账" + dateStr + ".xls";
            List<UsPolicyInfo> askBillList = new ArrayList<>();
            for (Map<String, Object> resultDatum : resultData) {
                UsPolicyInfo usPolicyInfo = creatUsPolicy(resultDatum);
                if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyTime())) {
                    usPolicyInfo.setPolicyStatue("已完成");
                } else {
                    usPolicyInfo.setPolicyStatue("未完成");
                }
                usPolicyInfo.setCompanyName(usPolicyInfo.getBelongCompanyName());
                usPolicyInfo.setDepName(usPolicyInfo.getBelongDepartmentName());

                askBillList.add(usPolicyInfo);
            }
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Type", "application/msexcel");
            response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String targetFileName = path + "" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<UsPolicyInfo> exportUtil = new ExcelUtil<>(UsPolicyInfo.class);
            exportUtil.exportExcel(askBillList, "政策宣讲台账列表", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    private UsPolicyExport creatModelRiskDate(Map<String, Object> map) {
        UsPolicyExport usPolicyExport = new UsPolicyExport();
        usPolicyExport.setParentCompanyName((String) map.get("belongCompanyName"));
        usPolicyExport.setPreach(map.get("preach"));
        usPolicyExport.setDopreach(map.get("dopreach"));
        usPolicyExport.setRate((String) map.get("rate"));
        usPolicyExport.setContentcount(map.get("contentcount"));
        return usPolicyExport;
    }


    private UsPolicyInfo creatUsPolicy(Map<String, Object> map) {
        UsPolicyInfo usPolicyInfo = new UsPolicyInfo();
        usPolicyInfo.setParentCompanyName((String) map.get("parentCompanyName"));
        usPolicyInfo.setGridName((String) map.get("gridName"));
        usPolicyInfo.setTitle((String) map.get("title"));
        usPolicyInfo.setPolicyTime((String) map.get("policyTime"));
        usPolicyInfo.setTrueName((String) map.get("trueNames"));

        usPolicyInfo.setPolicyAddress((String) map.get("policyAddress"));
        usPolicyInfo.setParticipants((String) map.get("participants"));
        usPolicyInfo.setPolicyStatue((String) map.get("policyStatue"));
        usPolicyInfo.setBelongCompanyName((String) map.get("belongCompanyName"));//谈话或走访对象
        usPolicyInfo.setBelongDepartmentName((String) map.get("belongDepartmentName"));//谈话或走访对象
        return usPolicyInfo;
    }


    /**
     * 获取决策项
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @param processType    流程类型
     * @return
     */
    @Override
    public JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String processType) {
        /**处理操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getDecisions";
        String params = "processInstId=" + processInstId + ",processDefName=" + processDefName + ",location=" + location + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        processDefName = Constants.POLICY_PROPAGANDA;
        List<SimpleAppDecision> decisions = new ArrayList<>();
        if (null != processDefName && processDefName.equals("undefined")) {
            processDefName = null;
        }
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**开始阶段没有流程定义id**/
            if (Constants.ACTIVITY_START.equals(location) && StringUtils.isEmpty(processDefName)) {
                if (StringUtils.isEmpty(processDefName)) {
                    return JsonResponse.fail(null, "获取流程失败");
                }
            }
            /**当前环节下所有决策**/
            Map<String, String> map = new HashMap<>();
            map.put("appCode", Constants.APP_CODE);
            map.put("processDefId", processDefName);
            map.put("activityDefId", location);
            decisions = uumsSysAppDecisionApi.findDecisions(Constants.APP_CODE, map);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(decisions);
    }

    /**
     * 获取决策项审批人员
     *
     * @param processInstId  流程实例id
     * @param source         来源
     * @param userCode       用户OA账户
     * @param sysAppDecision 决策对象
     * @return
     */
    @Override
    public JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision) {
        JsonResponse userList = new JsonResponse();
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getOrgAndUser";
        String params = "processInstId=" + processInstId + ",source=" + source + ",userCode=" + userCode + ",SimpleAppDecision=" + sysAppDecision.toString();
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        try {
            /**判断来源记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**根据根据flowType处理查询组织人员**/
            IUser currentUser = SecurityUtils.getCurrentUser();
            String defaultValue = sysAppDecision.getDecisionConfig();
            if (defaultValue != null) {
                String newDefault = JacksonUtils.unescapeString(defaultValue).replace(("\'"), "\"");
                List<HashMap<String, String>> mapLists = JacksonUtils.json2Type(newDefault, new TypeReference<List<HashMap<String, String>>>() {
                });
                if (mapLists != null && mapLists.size() > 0) {
                    Map<String, String> hashMap = mapLists.get(0);
                    String isNextStep = null;// hashMap.get("typeValue");
                    String nextStepValue = null;// hashMap.get("includeValue");
                    String filterValue = null;// hashMap.get("includeValue");
                    String groupType = null;// hashMap.get("includeValue");
                    for (Map<String, String> map : mapLists) {
                        String type = map.get("type");
                        if ("flowType".equals(type)) {
                            isNextStep = map.get("typeValue");
                            nextStepValue = map.get("includeValue");
                        }
                        if ("filterUserType".equals(type)) {
                            filterValue = map.get("typeValue");
                        }
                        if ("pageType".equals(type)) {
                            groupType = map.get("groupType");
                        }
                    }
                    if ("normalStep".equals(isNextStep)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("appCode", Constants.APP_CODE);
                        map.put("processDefId", sysAppDecision.getProcessDefId());
                        map.put("activityDefId", sysAppDecision.getActivityDefId());
                        map.put("decisionId", sysAppDecision.getDecisionId());
                        map.put("groupId", sysAppDecision.getGroupId());
                        map.put("decisionConfig", sysAppDecision.getDecisionConfig());
                        // 如果有过滤规则 orgRule
                        if (("orgRule".equals(filterValue))) {
                            // 获取当前登录人
                            // 如果当前登录人所属公司为省公司或分公司,orgCode传递departmentCode
                            if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {
                                map.put("orgCode", currentUser.getBelongDepartmentCode());
                            }
                            // 如果当前登录人所属公司为县公司，orgCode传递companyCode
                            else {
                                map.put("orgCode", currentUser.getBelongCompanyCode());
                            }
                        }
                        //如果有过滤规则 companyCodeRule
                        if (("companyCodeRule".equals(filterValue))) {
                            // 获取当前登录人
                            // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                            if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {

                                map.put("orgCode", currentUser.getBelongCompanyCode());
                            }
                            // 如果当前登录人所属公司为县公司，orgCode传递companyCodeParent
                            else {
                                map.put("orgCode", currentUser.getBelongCompanyCodeParent());
                            }
                        }
                        if (("departmentCodeRule".equals(filterValue))) {
                            // 获取当前登录人
                            // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                            if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH)) {

                                map.put("orgCode", currentUser.getBelongDepartmentCode());
                            }
                            // 如果当前登录人所属公司为县公司，orgCode传递companyCodeParent
                            else {
                                map.put("orgCode", currentUser.getBelongDepartmentCode());
                            }
                        }
                        // 如果有过滤规则 companyCodeRule
                        if (!StringUtils.isEmpty(filterValue) && ("companyCodeRule".equals(filterValue))) {
                            // 获取当前登录人

                            // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                            if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {
                                map.put("orgCode", currentUser.getBelongCompanyCode());
                            }
                            // 如果当前登录人所属公司为县公司，orgCode传递上级公司组织code
                            else {
                                SimpleOrg simpleOrg = uumsSysOrgApi.findParentBySon(Constants.APP_CODE, currentUser.getBelongCompanyCode());
                                map.put("orgCode", simpleOrg.getOrgCode());
                            }
                        }
                        userList = uumsSysUserinfoApi.findUserByDecisionNoPage(Constants.APP_CODE, map);
                    }
                    if ("previousStep".equals(isNextStep) && !StringUtils.isEmpty(nextStepValue)) {//退回上一步处理,获取上一步审批人
                        if (processInstId != null) {
                            WfWorkItemModel wfWorkItemModel = (WfWorkItemModel) workItemManager.getByProInstIdAAndAInstId(Long.parseLong(processInstId), nextStepValue);
                            if (wfWorkItemModel != null) {
                                String userName = wfWorkItemModel.getParticipant();
                                userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                            }
                        }
                    }


                    if (sysAppDecision.getDecisionId().equals("djfypt.braAdminToDjAdmin")) {
                        List<String> userListInfo = usAdminManagerService.findDjAdminByCode(currentUser.getBelongCompanyCode(), "0", "djfupt_002");
                        String userName = Joiner.on(",").join(userListInfo);
                        userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                    }


                }
            }
            if (ObjectUtil.isNotEmpty(userList)) {
                dealWithUserList(userList, false, true, false);
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return userList;
    }


    //对出人结果做处理，isSingle是否单选；idDefaultSelect是否默认选中；isCancelSelect是否有X
    private void dealWithUserList(JsonResponse userList, boolean isSingle, boolean idDefaultSelect, boolean isCancelSelect) {
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) userList.getData();
        Set<String> userIdSet = new HashSet<>();
        for (Map<String, Object> map : dataList) {
            map.put("singleSel", isSingle);
            List<Map<String, Object>> userDataList = (List<Map<String, Object>>) map.get("user");
            for (Map<String, Object> userMap : userDataList) {
                String treeType = (String) userMap.get("treeType");
                String userId = (String) userMap.get("id");
                if ("user".equals(treeType)) {
                    userIdSet.add(userId);
                    //defaultSelectUser 默认选中 true  反之false
                    userMap.put("defaultSelectUser", idDefaultSelect);
                    //cancelSelectUser 默认选中状态下没有X true 有X false
                    userMap.put("cancelSelectUser", isCancelSelect);
                }
            }
        }
    }


    /**
     * 获取意见
     *
     * @param processInstId
     * @param workItemList
     * @return
     */
    @Override
    public List<WfOptMsgModel> findAllMsg(String pmInsId, List<String> workItemList) {
        return wfWorkItemMsgRepository.findAllMsg(pmInsId, workItemList);
    }


    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    //党建智慧大屏  数据

    /**
     * 政策宣讲数量
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> policyCount(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.belong_company_name," +
                "       COUNT(CASE WHEN t.policy_statue = '0' THEN '01' END) as preach,      " +
                "       COUNT(CASE WHEN ac.current_state = '7' THEN '02' END) as dopreach    " +
                "       from US_POLICY_INFO t, act_business_status ac" +
                "       where t.enabled = 1" +
                "       and ac.enabled = 1" +
                "       and t.pm_ins_id = ac.receipt_code" +
                "       and t.belong_company_name not like '%省公司%'");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and to_date(t.policy_time,'yyyy-MM-dd')>=:startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and to_date(t.policy_time,'yyyy-MM-dd')<=:endDate ");
            param.put("endDate", endDate);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : list) {
            Map<String, Object> newMap = new HashMap<>(stringObjectMap);

            if (null != stringObjectMap.get("preach")) {
                preach = stringObjectMap.get("preach").toString();
                if ("0".equals(preach)) {
                    preach = "1";
                }
            }
            if (null != stringObjectMap.get("dopreach")) {
                dopreach = stringObjectMap.get("dopreach").toString();
            }
            float f1 = Float.parseFloat(preach);
            float f2 = Float.parseFloat(dopreach);
            String rate = (f2 / f1) * 100 + "%";
            newMap.put("rate", rate);
            resultList.add(newMap);
        }
        resultList = FormatTool.formatConversion(resultList);//驼峰转换
        return resultList;
    }


    /**
     * 政策宣讲数量
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> policyAllCount(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select COUNT(CASE" +
                "               WHEN t.policy_time is not null THEN" +
                "                '01'" +
                "             END) as dopreach," +
                "       COUNT(CASE" +
                "               WHEN t.belong_company_type_dict_value = '03' THEN" +
                "                '03'" +
                "             END) as contentCount," +
                "       COUNT(CASE" +
                "               WHEN t.policy_statue = '0' THEN" +
                "                '01'" +
                "             END) as preach" +
                "  from US_POLICY_INFO t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and ac.enabled = 1" +
                "   and t.pm_ins_id = ac.receipt_code" +
                "   and t.policy_statue > 0" +
                "   and t.belong_company_name not like '%省公司%'");
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')>=:startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')<=:endDate ");
            param.put("endDate", endDate);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 获取当前登录人的相关信息
     *
     * @return
     */
    @Override
    public JsonResponse getUser() {
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************";
        } else {
            url = "http://************:8088";
        }

        return HttpClient.textBody(url+"/iportal/common/sso/add/header?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                //测试环境地址
//                 return HttpClient.textBody("http://************/iportal/common/sso/add/header?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt" )
                .param("loginuser",rsaEncryptor.encrypt(currentUser.getUsername()) )
                .param("appcode", Constants.APP_CODE)
                .asBean(JsonResponse.class);

    }


    /**
     * 获取第一议题数据
     *
     * @return
     */
    @Override
    public JsonResponse selectAllPro(String source, String currentUserCode) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Map<String, Object> map = new HashMap<>();

        //正式环境地址
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************:8088";
        } else {
            url = "http://************:8088";
        }

        return HttpClient.textBody(url+"/dwdyytxx/action/UsStudy/selectAllPro/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                //测试环境地址
                //return HttpClient.textBody("http://************:8088/dwdyytxx/action/UsStudy/selectAllPro/sso?page=1&size=4&loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .json(JSONUtil.toJsonStr(map))
                .asBean(JsonResponse.class);

    }


    /**
     * @return
     */
    @Override
    public JsonResponse selectAll(String source, String currentUserCode) {
        if (source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Map<String, Object> map = new HashMap<>();

        //正式环境地址
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************:8088";
        } else {
            url = "http://************:8088";
        }
        return HttpClient.textBody(url+"/zbjqxxzd/action/UsStudyMain/selectAll/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                //测试环境地址
                // return HttpClient.textBody("http://************:8088/zbjqxxzd/action/UsStudyMain/selectAll/sso?page=1&size=4&loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .json(JSONUtil.toJsonStr(map))
                .asBean(JsonResponse.class);

    }


    /**
     * 第一议题
     *
     * @return
     */
    @Override
    public JsonResponse selectCountYt(String source, String currentUserCode) {
        if (source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Map<String, Object> map = new HashMap<>();

        //正式环境地址
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************:8088";
        } else {
            url = "http://************:8088";
        }
        return HttpClient.textBody(url+"/dwdyytxx/action/UsStudy/selectCount/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                //测试环境地址
                //return HttpClient.textBody("http://************:8088/dwdyytxx/action/UsStudy/selectCount/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }

    /**
     * 支部近期
     *
     * @return
     */
    @Override
    public JsonResponse selectCountZb(String source, String currentUserCode) {
        if (source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Map<String, Object> map = new HashMap<>();

        //正式环境地址
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************:8088";
        } else {
            url = "http://************:8088";
        }
        return HttpClient.textBody(url+"/zbjqxxzd/action/UsStudyMain/selectCount/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                //测试环境地址
                //return HttpClient.textBody("http://************:8088/zbjqxxzd/action/UsStudyMain/selectCount/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }


    public Page<List<Map<String, Object>>> policyOrder(Map<String, Object> resultMap) {
        Page<List<Map<String, Object>>> applicationFormPage = null;
        IUser iuser = SecurityUtils.getCurrentUser();
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");
        String title = cn.hutool.core.map.MapUtil.getStr(resultMap, "title");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");
        String state = cn.hutool.core.map.MapUtil.getStr(resultMap, "state");

        try {
            //获取当前当前月份
            DateFormat df = new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            String treatTime = df.format(calendar.getTime());
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer("select t.* from US_POLICY_INFO t ,  act_business_status act  where t.enabled = 1  and  t.pm_ins_id = act.receipt_code");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            } else {
                sql.append(" and  to_char(t.created_time,'yyyy-mm' )=:treatTime ");
                map.put("treatTime", treatTime);
            }
            if (StringUtils.isNotEmpty(state)) {
                sql.append(" and   act.current_state   =:state  ");
                map.put("state", state);
            }
            if (StringUtils.isNotEmpty(title)) {
                sql.append(" and   t.title    like concat( concat('%',:title),'%')  ");
                map.put("title", title);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        return null;
    }


    //党建智慧大屏  地图数据
    @Override
    public List<Map<String, Object>> findMapDetail(Map<String, Object> resultMap) {
        // List<Map<String, Object>> policyCount = usPolicyInfoService.policyCount(resultMap);//政策宣讲数量
        List<Map<String, Object>> policyCount = usPolicyInfoService.findDpPolicyLeder(resultMap);//政策宣讲数量
        List<Map<String, Object>> gridCount = usAdminManagerService.findGridCount(resultMap);//网格和党建指导员数量
        List<Map<String, Object>> recordCount = usRecordFillService.findRecordCount(resultMap);//思政纪实数量
        List<Map<String, Object>> problemCount = usProblemInfoService.problemCount(resultMap);//问题数量
        List<Map<String, Object>> caseCount = caseInfoService.caseCount(resultMap);//优秀案例数量

        List<Map<String, Object>> mapList = MergeUtil.merge(policyCount, gridCount, "belongCompanyName");
        List<Map<String, Object>> mapList2 = MergeUtil.merge(mapList, recordCount, "belongCompanyName");
        List<Map<String, Object>> mapList3 = MergeUtil.merge(mapList, problemCount, "belongCompanyName");
        List<Map<String, Object>> mapList4 = MergeUtil.merge(mapList, caseCount, "belongCompanyName");
        return mapList4;
    }


    @Override
    public List<Map<String, Object>> findLatest(Map<String, Object> resultMap) {

        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        String gridName = cn.hutool.core.map.MapUtil.getStr(resultMap, "gridName");//网格名称
        String title = cn.hutool.core.map.MapUtil.getStr(resultMap, "title");//标题
        String policyStatue = cn.hutool.core.map.MapUtil.getStr(resultMap, "policyStatue");//是否已完成
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = null;
        IUser user = SecurityUtils.getCurrentUser();

        if (user.getBelongCompanyTypeDictValue().equals("01")) {
            sql = new StringBuffer("select distinct t.*," +
                    "                wf.receipt_title   as title," +
                    "                wf.process_inst_id as processInstId," +
                    "                wf.activity_def_id as location," +
                    "                wf.work_item_id as workItemId," +
                    "                wf.participant as participant" +
                    "  from US_POLICY_INFO t, wf_workitem_model wf" +
                    " where t.enabled = 1" +
                    "   and wf.enabled = 1" +
                    "   and t.pm_ins_id = wf.receipt_code" +
                    "   and t.policy_statue = '0'" +
                    "   and t.work_item_id is  null" +
                    "   and t.creator = wf.participant");
        } else {
            sql = new StringBuffer("select distinct t.*," +
                    "                wf.receipt_title   as title," +
                    "                wf.process_inst_id as processInstId," +
                    "                wf.activity_def_id as location," +
                    "                wf.work_item_id as workItemId," +
                    "                wf.participant as participant" +
                    "  from US_POLICY_INFO t, wf_workitem_model wf" +
                    " where t.enabled = 1" +
                    "   and wf.enabled = 1" +
                    "   and t.pm_ins_id = wf.receipt_code" +
                    "   and t.policy_statue = '0'" +
                    "   and t.work_item_id is not null" +
                    "   and t.creator = wf.participant");
        }
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')>=:startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')<=:endDate ");
            param.put("endDate", endDate);
        }

        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(gridName)) {
            sql.append(" and t.grid_name  like concat(concat('%', :gridName), '%') ");
            param.put("gridName", gridName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(title)) {
            sql.append(" and t.title  like concat(concat('%', :title), '%') ");
            param.put("title", title);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyStatue)) {
            sql.append(" and t.policy_statue  like concat(concat('%', :policyStatue), '%') ");
            param.put("policyStatue", policyStatue);
        }
        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    @Override
    public List<Map<String, Object>> findLatestOld(Map<String, Object> resultMap) {
        IUser user = SecurityUtils.getCurrentUser();
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String policyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (StringUtils.isNotEmpty(year) || StringUtils.isNotEmpty(month)) {
            policyTime = year + "-" + month;
        }
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select   t.belong_company_name ," +
                "        COUNT(CASE WHEN t.policy_time is not null THEN '01' END) as dopreach,  " +
                "      COUNT(CASE WHEN t.belong_company_type_dict_value = '03' THEN '03' END) as contentCount, " +
                "      COUNT(CASE WHEN t.policy_statue = '0' THEN '01' END) as preach      " +
                "                      from US_POLICY_INFO t, act_business_status ac " +
                "                      where t.enabled = 1 " +
                "                      and ac.enabled = 1 " +
                "                      and t.pm_ins_id = ac.receipt_code" +
                "                      and t.policy_statue >0" +
                "                      and t.belong_company_name not like '%省公司%' ");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyTime)) {
            sql.append(" and  SUBSTR(nvl(t.policy_time,'1950-01'),0,7) = :policyTime");
            param.put("policyTime", policyTime);
        }

        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    sql.append(" and   t.grid_name   =:gridName  ");
                    List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                    if (adminManagers.size() > 0) {
                        param.put("gridName", adminManagers.get(0).getGridName());
                    }

                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList = FormatTool.formatConversion(list);//驼峰转换
        if (resultList.size() > 0) {
            //计算宣讲内容数量
            for (Map<String, Object> map : resultList) {
                Map<String, Object> params = Maps.newHashMap();
                StringBuffer sqls = new StringBuffer("select distinct(d.id)" +
                        " from us_pantch_detail d " +
                        "  left join  us_policy_info t on d.pm_ins_id=t.pm_ins_id " +
                        "   where " +
                        " d.belong_company_name = :companyName" +
                        " and t.policy_statue>0 " +
                        " and t.enabled=1 and d.enabled=1 ");
                params.put("companyName", map.get("belongCompanyName"));
                if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(policyTime)) {
                    sqls.append(" and  SUBSTR(nvl(t.policy_time,'1950-01'),0,7) = :policyTime");
                    params.put("policyTime", policyTime);
                }

                //如果不是省公司管理员执行五级查询，不是的话默认查看全部
                if (!isAdmin) {
                    String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                    switch (queryLevel) {
                        case DataPermissionConstants.QUERY_LEVEL_FIRST:
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_SECOND:
                            DataPermissionTool.handleSql(sqls, params, DataPermissionConstants.QUERY_LEVEL_SECOND);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_THIRD:
                            DataPermissionTool.handleSql(sqls, params, DataPermissionConstants.QUERY_LEVEL_THIRD);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_FOUR:
                            sqls.append(" and   t.grid_name   =:gridName  ");
                            List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(user.getUsername(), Constants.FJFUPT_BRO);
                            if (adminManagers.size() > 0) {
                                params.put("gridName", adminManagers.get(0).getGridName());
                            }
                            break;
                        default:
                            sqls.append(" and  t.creator =:username  ");
                            sqls.append(" and  d.creator =:username  ");
                            params.put("username", SecurityUtils.getCurrentUser().getUsername());
                            break;
                    }
                }
                List<Map<String, Object>> lists = customDynamicWhere.queryNamedParameterForList(sqls.toString(), params);
                map.put("contentcount", lists.size());
                int preachs = usAdminManagerService.findByCompanyCodeAndEnabledAndRoleUserId(map.get("belongCompanyName").toString(), Constants.FJFUPT_BRO);
                map.put("preach", preachs);
                if (null != map.get("preach")) {
                    preach = map.get("preach").toString();
                }
                if (null != map.get("dopreach")) {
                    dopreach = map.get("dopreach").toString();
                }
                String rate = "";
                float f1 = Float.parseFloat(preach);
                float f2 = Float.parseFloat(dopreach);
                if (preach.equals("0") || dopreach.equals("0")) {
                    rate = "0";
                } else {
                    rate = (f2 / f1 * 100) + "%";
                }

                map.put("rate", rate);
            }
        }
        return resultList;
    }

    @Override
    public JsonResponse workDynamicsList(String source, String currentUserCode) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        return HttpClient.post(hlgj_Config.getAddress() + "/action/home/<USER>/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }

    @Override
    public JsonResponse overallProgress(String source, String currentUserCode) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        return HttpClient.post(hlgj_Config.getAddress() + "/action/home/<USER>/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }

    @Override
    public JsonResponse companyCompletionRate(String source, String currentUserCode) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        return HttpClient.post(hlgj_Config.getAddress() + "/action/home/<USER>/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }

    @Override
    public JsonResponse deptCompletionRate(String source, String currentUserCode) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        return HttpClient.post(hlgj_Config.getAddress() + "/action/home/<USER>/sso?loginuser=" + rsaEncryptor.encrypt(currentUser.getUsername()) + "&appcode=djfupt")
                .asBean(JsonResponse.class);

    }


    public JsonResponse updatePolicyInfo(UsPolicyInfo policyInfo) {
        if (StringUtils.isEmpty(policyInfo.getId())) {
            return JsonResponse.fail("参数有误");
        }

        this.update(policyInfo);
        updateFileInfoById(policyInfo);//更新附件
        return JsonResponse.success("修改成功");
    }


    public JsonResponse test1() {
        List<UsPolicyInfo> policyInfos = usPolicyInfoRepository.findAll();
        if (policyInfos.size() > 0) {
            for (UsPolicyInfo policyInfo : policyInfos) {
                IUser user = uumsSysUserinfoApi.findByUsername(policyInfo.getCreator(), Constants.APP_CODE);
                policyInfo.setTrueName(user.getTruename());
                this.update(policyInfo);
            }
        }
        return null;
    }


}
