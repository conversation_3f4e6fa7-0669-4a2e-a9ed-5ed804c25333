package com.simbest.boot.djfupt.record.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.record.model.UsTalkContent;
import com.simbest.boot.djfupt.record.repository.UsTalkContentRepository;
import com.simbest.boot.djfupt.record.service.IUsTalkContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service
@SuppressWarnings("ALL")
public class UsTalkContentServiceImpl extends LogicService<UsTalkContent, String> implements IUsTalkContentService {

    private UsTalkContentRepository repository;

    @Autowired
    public UsTalkContentServiceImpl(UsTalkContentRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public void dealInfo(String mainId, List<UsTalkContent> talkContentList) {
        repository.deleteByMainId(mainId);
        if (CollectionUtil.isNotEmpty(talkContentList) ) {
            for (UsTalkContent item : talkContentList) {
                item.setId(null);
                item.setMainId(mainId);
                this.insert(item);
            }
        }
    }
}
