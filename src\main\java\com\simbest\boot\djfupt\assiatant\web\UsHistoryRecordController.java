package com.simbest.boot.djfupt.assiatant.web;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.assiatant.model.UsHistoryRecord;
import com.simbest.boot.djfupt.assiatant.service.IUsHistoryRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.spring.web.json.Json;

import java.util.Map;

@Api(description = "智能助手-历史记录查询")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/UsHistoryRecord")
public class UsHistoryRecordController extends LogicController<UsHistoryRecord, String> {

    private IUsHistoryRecordService service;

    @Autowired
    public UsHistoryRecordController(IUsHistoryRecordService service) {
        super(service);
        this.service = service;
    }
    @Autowired
    private IUsHistoryRecordService iUsHistoryRecordService;

    @ApiOperation(value = "历史记录-查询", notes = "历史记录-查询")
    @PostMapping(value = {"/getAllNoPage", "/getAllNoPage/api", "/getAllNoPage/sso"})
    public JsonResponse getAllNoPage(@RequestParam(defaultValue = "PC") String source,
                                     @RequestParam(required = false) String currentUserCode,
                                     @RequestBody(required = false) Map<String, String> map) {
        return JsonResponse.success(service.getAllNoPage(source, currentUserCode,map));
    }

    @ApiOperation(value = "历史记录-详情查询", notes = "历史记录-详情查询")
    @PostMapping(value = {"/getAllDetailNoPage", "/getAllDetailNoPage/api", "/getAllDetailNoPage/sso"})
    public JsonResponse getAllDetailNoPage(@RequestParam(defaultValue = "PC") String source,
                               @RequestParam(required = false) String currentUserCode,
                               @RequestParam(required = false) String pmInsId) {
        return JsonResponse.success(service.getAllDetailNoPage(source, currentUserCode,pmInsId));
    }

    @ApiOperation(value = "单个记录删除", notes = "单个记录删除")
    @PostMapping(value = {"/clearByPmInsId", "/clearByPmInsId/api", "/clearByPmInsId/sso"})
    public JsonResponse clearByPmInsId(String pmInsId) {
        return service.clearByPmInsId(pmInsId);
    }
    @ApiOperation(value = "删除全部", notes = "删除全部")
    @PostMapping(value = {"/clearAll", "/clearAll/api", "/clearAll/sso"})
    public JsonResponse clearByPmInsId() {
        return service.clearAll();
    }
}
