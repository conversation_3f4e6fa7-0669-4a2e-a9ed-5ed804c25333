package com.simbest.boot.djfupt.xtgj.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.xtgj.model.UsZwrgdyInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IUsZwrgdyInfoService extends ILogicService<UsZwrgdyInfo, String> {

    UsZwrgdyInfo insertInfo(UsZwrgdyInfo o);

    UsZwrgdyInfo updateInfo(UsZwrgdyInfo o);

    UsZwrgdyInfo findByIdInfo(String id);

    Page<UsZwrgdyInfo> findAllInfo(UsZwrgdyInfo o, Pageable pageable);

    void exportInfo(HttpServletRequest request, HttpServletResponse response, UsZwrgdyInfo o);

    JsonResponse getGridName();
}
