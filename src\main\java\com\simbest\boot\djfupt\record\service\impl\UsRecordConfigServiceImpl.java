package com.simbest.boot.djfupt.record.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.common.BpsRedisUtil;
import com.simbest.boot.bps.process.bussiness.mapper.ActBusinessStatusMapper;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.mapper.WfProcessInstModelMapper;
import com.simbest.boot.bps.process.listener.mapper.WfWorkItemModelMapper;
import com.simbest.boot.bps.process.listener.service.IWfProcessInstModelService;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;
import com.simbest.boot.djfupt.record.repository.UsRecordConfigRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordConfigService;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service(value = "usRecordConfigService")
@SuppressWarnings("ALL")
public class UsRecordConfigServiceImpl extends LogicService<UsRecordConfig, String> implements IUsRecordConfigService {

    private UsRecordConfigRepository usRecordConfigRepository;

    @Autowired
    public UsRecordConfigServiceImpl(UsRecordConfigRepository repository) {
        super(repository);
        this.usRecordConfigRepository = repository;
    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;


    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private WfWorkItemModelMapper workItemModelMapper;

    @Autowired
    private BpsRedisUtil bpsRedisUtil;

    @Autowired
    private ActBusinessStatusMapper actBusinessStatusMapper;

    @Autowired
    private IWfProcessInstModelService wfProcessInstModelService;

    @Autowired
    private WfProcessInstModelMapper wfProcessInstModelMapper;

    @Autowired
    private ISysFileService fileService;


    String param1 = "/action/usRecordConfig";

    /**
     * 展示所有思政配置信息
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllInfo(Map<String, Object> resultMap) {
        String frequencyType = cn.hutool.core.map.MapUtil.getStr(resultMap, "frequencyType");//思政工作频次
        String workSettings = cn.hutool.core.map.MapUtil.getStr(resultMap, "workSettings");//工作设置
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select t.* from US_RECORD_CONFIG t where t.enabled=1 and t.removed_time is null ");
        Map<String, Object> param = Maps.newHashMap();
        if (StringUtils.isNotEmpty(frequencyType)) {
            sql.append(" and t.frequency_type  like concat(concat('%', :frequencyType), '%') ");
            param.put("frequencyType", frequencyType);
        }
        if (StringUtils.isNotEmpty(workSettings)) {
            sql.append(" and t.work_settings  like concat(concat('%', :workSettings), '%') ");
            param.put("workSettings", workSettings);
        }
        sql.append(" order by t.created_time desc");
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "导入模板.xls";

        List<UsRecordConfig> list = Lists.newArrayList();
        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<UsRecordConfig> exportUtil = new ExcelUtil<UsRecordConfig>(UsRecordConfig.class);
            exportUtil.exportExcel(list, "思政纪实配置导入模板", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), request, response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 导入数据
     *
     * @param request
     * @param response
     */
    @Override
    public void importInfo(HttpServletRequest request, HttpServletResponse response) {
        PrintWriter out = null;
        boolean flag = true;
        JsonResponse jsonResponse = null;
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            for (MultipartFile uploadfile : multipartFiles.values()) {
                // 先上传至sys_file表,注意sheetName名要与excel保持一致
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, "B", uuid, "1", UsRecordConfig.class, "思政纪实配置导入模板");
                /** 获取excel表格:整理数据添加编号,匹配数据字典value值**/
                List<UsRecordConfig> list = uploadFileResponse.getListData();
                if (CollectionUtil.isNotEmpty(list)) {
                    this.saveAll(list);
                    jsonResponse = JsonResponse.success(list);
                    jsonResponse.setData(uploadFileResponse);
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1, "数据格式异常，请检查导入数据信息。");
        } finally {
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        }
    }


}
