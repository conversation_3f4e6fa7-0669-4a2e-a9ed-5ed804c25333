package com.simbest.boot.djfupt.admin.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UsAdminManagerRepository extends LogicRepository<UsAdminManager, String> {

    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where t.enabled=1 and t.user_name=:userName",
            nativeQuery = true
    )
    List<UsAdminManager> findByUserNameAndEnabled(@Param("userName") String userName);



    List<UsAdminManager> findByUserNameAndEnabledAndRoleUserId(String userName, Boolean enabled, String roleUserId);

    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where t.enabled=1 and t.user_name=:userName and t.id != :id  and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    List<UsAdminManager> findByUserNameAndEnabledIsNotIdAndroleUserId(@Param("userName") String userName,
                                                                      @Param("id") String id,
                                                                      @Param("roleUserId") String roleUserId);


    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where t.enabled=1 and t.id = :id ",
            nativeQuery = true
    )
    UsAdminManager findByIdAndEnabled(@Param("id") String id);


    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where t.enabled=1 and t.user_name=:userName   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    List<UsAdminManager> findByUserNameAndEnabledAndroleUserId(@Param("userName") String userName,
                                                               @Param("roleUserId") String roleUserId);

    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where   t.user_name=:userName   and t.role_user_id= :roleUserId and t.enabled=1 ",
            nativeQuery = true
    )
    List<UsAdminManager> findByUserNameAndroleUserId(@Param("userName") String userName,
                                                               @Param("roleUserId") String roleUserId);


    @Query(
            value = " select t.user_name" +
                    "  from US_ADMIN_MANAGER t" +
                    " where t.enabled = 1" +
                    "   and t.parnet_belong_com_code=:belongComCode " +
                    "   and t.type = :type " +
                    "   and t.role_user_id=:roleUserId",
            nativeQuery = true
    )
    List<String> findDjAdminByCode(@Param("belongComCode") String belongComCode, @Param("type") String type, @Param("roleUserId") String roleUserId);


    @Query(
            value = " select count(t.id) from US_ADMIN_MANAGER t " +
                    " where t.enabled=1 and t.belong_company_name=:belongCompanyName" +
                    "   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    int  findByCompanyCodeAndEnabledAndRoleUserId(@Param("belongCompanyName") String belongCompanyName,
                                                               @Param("roleUserId") String roleUserId);

    @Query(
            value = " select count(t.id) from US_ADMIN_MANAGER t " +
                    " where t.enabled=1 and ( t.belong_company_code=:compangCode or t.belong_department_code=:compangCode  or t. belong_org_code=:compangCode )" +
                    "   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    int  findByCompanyCodeAndEnabledAndRoleUserIds(@Param("compangCode") String compangCode,
                                                  @Param("roleUserId") String roleUserId);

    @Query(
            value = " select count(t.id) from US_ADMIN_MANAGER t " +
                   " where t.enabled=1 and t. belong_org_code=:compangCode " +

                    // " where t.enabled=1 and ( t.belong_company_code=:compangCode or t.belong_department_code=:compangCode  or t. belong_org_code=:compangCode )" +
                    "   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    int  findByCompanyCodeAndEnabledAndRoleUserIdss(@Param("compangCode") String compangCode,
                                                   @Param("roleUserId") String roleUserId);


    @Query(
            value = " select t.* from US_ADMIN_MANAGER t  where  t.user_name=:userName",
            nativeQuery = true
    )
    List<UsAdminManager> findByUserName(@Param("userName") String userName);




    @Query(
            value = " select count(t.id) from US_ADMIN_MANAGER t " +
                    " where t.enabled=1 and t. belong_department_code=:compangCode " +
                         "   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    int  findByDepCodeAndEnabledAndRoleUserIdss(@Param("compangCode") String compangCode,
                                                    @Param("roleUserId") String roleUserId);

    @Query(
            value = " select count(t.id) from US_ADMIN_MANAGER t " +
                    " where t.enabled=1   " +
                    "   and t.role_user_id= :roleUserId ",
            nativeQuery = true
    )
    int findByEnabledAndRoleUserId( @Param("roleUserId") String roleUserId);



}
