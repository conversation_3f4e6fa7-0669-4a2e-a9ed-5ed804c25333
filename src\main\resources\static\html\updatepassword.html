﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
xmlns:th="http://www.thymeleaf.org">
<head>
    <title>修改密码</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var encrypt = new JSEncrypt();
        encrypt.setPublicKey(web.key);
        window.getchoosedata = function () {
            if(formValidate("passwordForm")){
                var data={};
                var oval=encrypt.encrypt($("#password").val());
                var opassW=encodeURI(oval);
                var nval=encrypt.encrypt($("#rsaPassword").val());
                var npassW=encodeURI(nval);
                data.oldRsaPassword=opassW;
                data.newRsaPassword=npassW;
                data.appCode=web.appCode;
                ajaxgeneral({
                    url:"sys/uums/userinfo/changeMyPassword",
                    data:data,
                    //currentUser:true,
                    //contentType:"application/json; charset=utf-8",
                    success:function(data){
                        top.dialogClose("password");
                    }
                });
            }
            return {"status":"0"};
        };
    </script>
</head>
<body>
<form id="passwordForm" method="post">
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="80" align="right">原密码：</td><td width="160">
            <input id="password" name="password" type="password" class="easyui-validatebox" required /></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td width="80" align="right">新密码：</td><td width="160">
            <input id="rsaPassword" name="rsaPassword" type="password" class="easyui-validatebox" required validType="password"/></td>
            <td width="100" align="right">重复密码：</td><td width="160"><input id="password1" name="password1" type="password" class="easyui-validatebox" required validType="equals['#rsaPassword']"/></td>
            <td></td>
        </tr>
    </table>
</form>
</body>
</html>
