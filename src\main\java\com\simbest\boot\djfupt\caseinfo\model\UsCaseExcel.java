package com.simbest.boot.djfupt.caseinfo.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @data 2022/11/17 0017 11:44
 */
@Data
public class UsCaseExcel {


    @ExcelVOAttribute(name = "编号", column = "A")
    @Excel(name = "编号")
    private int rownum;

    @ExcelVOAttribute(name = "优秀案例名称", column = "B")
    @Excel(name = "优秀案例名称")
    private String problemName;

    @ExcelVOAttribute(name = "优秀案例简述", column = "C")
    @Excel(name = "优秀案例简述")
    private String problemDescribe;

    @ExcelVOAttribute(name = "上报人", column = "D")
    @Excel(name = "上报人")
    private String applyUser;                   //发起人


    @ExcelVOAttribute(name = "上报人组织", column = "E")
    @Excel(name = "上报人组织")
    private String belongOrgName;                    //发起人组织


    @ExcelVOAttribute(name = "上报时间", column = "F")
    @Excel(name = "上报时间")
    private String createdTime;                   //发起人联系方式








}