package com.simbest.boot.djfupt.util;

public class OrderNumberCreate {

    /**
     * （3）工单编号在工单发起成功后生成。工单编号规则：
     * 政策宣讲下达：ZCXJ-yyyymmdd-4位序号
     * 问题上报：WT-yyyymmdd-4位序号
     * 优秀案例上报：YXAL-yyyymmdd-4位序号
     * （4）待办列表的工单标题生成规则：
     * 【政策宣讲下达】政策宣讲时间+政策宣讲
     * 【问题上报】问题名称
     * 【优秀案例上报】优秀案例名称
     *
     * @param pmInsType
     * @return
     */
    public static String generateNumber(String pmInsType, String countInfo) {
        String currentStr = DateUtil.getCurrentStr();
        String work = "";
        String result = "";
        int i = Integer.parseInt(countInfo);
        if (i >= 0 && i < 9) {
            work = "000" + (i + 1);
        } else if (i >= 9 && i < 99) {
            work = "00" + (i + 1);
        } else if (i >= 99 && i < 999) {
            work = "0" + (i + 1);
        } else if (i >= 999 && i < 9999) {
            work = "" + (i + 1);
        }
        switch (pmInsType) {
            case "C":
                result = "ZCXJ-" + currentStr + "-" + work;
                break;
        }
        return result;
    }
}
