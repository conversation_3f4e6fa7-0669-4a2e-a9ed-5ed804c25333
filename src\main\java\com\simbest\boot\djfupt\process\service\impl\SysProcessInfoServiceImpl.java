package com.simbest.boot.djfupt.process.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.process.model.SysProcessInfo;
import com.simbest.boot.djfupt.process.repository.SysProcessInfoRepository;
import com.simbest.boot.djfupt.process.service.ISysProcessInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <strong>Title :SysProcessInfoServiceImpl </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/22</strong><br>
 * <strong>Modify on : 2022/6/22</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

@Service
@Slf4j
public class SysProcessInfoServiceImpl extends LogicService<SysProcessInfo, String> implements ISysProcessInfoService {

    private SysProcessInfoRepository repository;

    public SysProcessInfoServiceImpl(SysProcessInfoRepository repository) {
        super(repository);
        this.repository = repository;
    }


    /**
     * 根据连线获取目标环节信息
     * @return
     */
    @Override
    public SysProcessInfo findByOutcome(String processDefId , String fromActivityId, String outcome) {
        SysProcessInfo processInfo = null;
        try {
            Specification<SysProcessInfo> build = Specifications.<SysProcessInfo>and()
                    .eq("enabled", Boolean.TRUE)
                    .eq("processDefId", processDefId)
                    .eq("fromActivityId", fromActivityId)
                    .eq(StringUtils.isNotEmpty(outcome) , "outcome", outcome)
                    .build();
            List<SysProcessInfo> processInfos = this.findAllNoPage(build);
            if (CollectionUtil.isNotEmpty(processInfos)) {
                processInfo = processInfos.get(0);
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
        }
        return processInfo;
    }

    @Override
    public SysProcessInfo findSysProcessInfo(String processDefId, String fromActivityId, String outcome) {
        return repository.findSysProcessInfo(processDefId,fromActivityId,outcome);
    }
}
