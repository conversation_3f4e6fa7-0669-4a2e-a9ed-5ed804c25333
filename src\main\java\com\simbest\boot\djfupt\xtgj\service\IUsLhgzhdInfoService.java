package com.simbest.boot.djfupt.xtgj.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.xtgj.model.UsLhgzhdInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IUsLhgzhdInfoService extends ILogicService<UsLhgzhdInfo, String> {

    UsLhgzhdInfo insertInfo(UsLhgzhdInfo o);

    UsLhgzhdInfo updateInfo(UsLhgzhdInfo o);

    UsLhgzhdInfo findByIdInfo(String id);

    Page<UsLhgzhdInfo> findAllInfo(UsLhgzhdInfo o, Pageable pageable);

    void exportInfo(HttpServletRequest request, HttpServletResponse response, UsLhgzhdInfo o);

}
