package com.simbest.boot.djfupt.common.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IQueryDictValueRepository extends LogicRepository<SysDictValue,String> {


    /**
     * 根据ordId查询组织
     * @param orgId
     * @param dictType
     * @return
     */
    @Query(
            value = "select t.*  from SYS_DICT_VALUE t where  t.spare1=:companyCode and t.enabled=1 and t.dict_type=:dictType ",
            nativeQuery = true
    )
    SysDictValue findAllByOrgIdAndEnable(@Param("companyCode") String companyCode,
                                         @Param("dictType") String dictType);

    /**
     * 查询所有分公司
     * @return
     */
    @Query(
            value = " select t.*  from SYS_DICT_VALUE t where  t.enabled=1 and t.dict_type='orgType' and t.value_type='02'",
            nativeQuery = true
    )
    List<SysDictValue> findAllByDictType();


    /**
     * 查询短信催办所有流程
     */

    /**
     * 查询所有分公司
     * @return
     */
    @Query(
            value = "select t.* from SYS_DICT_VALUE t  where t.dict_type='processType'  and  t.enabled=1 order by t.display_order  " ,
            nativeQuery = true
    )
    List<SysDictValue> findAllByDictTypeProcessType();
}
