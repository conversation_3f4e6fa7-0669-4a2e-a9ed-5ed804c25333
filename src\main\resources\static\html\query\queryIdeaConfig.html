<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>思政纪实配置</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/new.js?v=86648" th:src="@{/js/new.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
    </script>
    <script type="text/javascript">
        getCurrent()
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/usRecordConfig/findAllInfo", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {title: "工作设置", field: "workSettings", width: 120, tooltip: true, align: "center"},
                            {title: "思政工作频次类型", field: "frequency", width: 60, tooltip: true, align: "center"},
                            {
                                title: "思政工作时间", field: "startTime", width: 100, tooltip: true, align: "center",
                                formatter: function (value, row, index) {
                                    if (row.frequencyType == '周') {
                                        return row.startTime + '至' + row.endTime
                                    }
                                    if (row.frequencyType == '月度') {
                                        return row.year + '年' + row.month + '月'
                                    }
                                    if (row.frequencyType == '季度') {
                                        return row.year + '年' + row.quarterly
                                    }
                                    if (row.frequencyType == '年度') {
                                        return row.year + '年'
                                    }
                                }
                            },
                            {title: "配置人", field: "configuration", width: 60, tooltip: true, align: "center"},
                            {title: "配置时间", field: "configTime", width: 80, tooltip: true, align: "center"},
                            {
                                field: "opt", title: "操作", width: 60, rowspan: 1, align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引    
                                    var g = ""
                                    g += "<a href='#' class='delete' id=" + row.id + ">【删除】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/usRecordConfig/insertUsRecordConfig",//新增命令
                    "onSubmit": function (data) {
                        var files = data.taskDescriptionFiles
                        var fileIds = []
                        for (var i = 0; i < files.length; i++) {
                            fileIds.push(files[i].id)
                        }
                        data.taskDescriptionFileId = fileIds.join(',')

                        var nameArr = $('#recordType').combobox('getValues').map(function (item) {
                            return item
                        })
                        if (nameArr.length > 0) {
                            data.recordType = nameArr.join(',')
                        }

                        return true;
                    }
                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#glyTableReadForm"
                }
            };
            loadGrid(pageparam);

            $("#startTime").datetimebox('calendar').calendar({
                styler: function (date) {
                    var now = new Date();
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    return date < d1 ? "color:#eee" : "";
                },
                validator: function (date) {
                    var now = new Date();
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    return date >= d1;
                }
            });

            initMultipleCombobox("#recordType",
                tourl("action/usRecordType/findInfoNoPage",{state:1}),
                { valueField:'name',textField:'name'});//多选下拉框变形
        });

        var timeArr = [
            {value: '1次', text: '1次'},
            {value: '2次', text: '2次'},
            {value: '3次', text: '3次'},
            {value: '4次', text: '4次'},
            {value: '5次', text: '5次'},
            {value: '6次', text: '6次'},
            {value: '7次', text: '7次'},
            {value: '8次', text: '8次'},
            {value: '9次', text: '9次'},
            {value: '10次', text: '10次'},
        ]

        var now = new Date()
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        var yearArr = []
        for (var i = 0; i < 50; i++) {
            yearArr.push({value: year + i, text: year + i + '年'})
        }
        var monthArr = []
        for (var i = 1; i < 13; i++) {
            if (i >= month)
                monthArr.push({value: i, text: i + '月'})
        }
        var quarterArr = [
            {value: '一季度', text: '一季度'},
            {value: '二季度', text: '二季度'},
            {value: '三季度', text: '三季度'},
            {value: '四季度', text: '四季度'},
        ]
        if (month > 3) {
            quarterArr.shift()
        }
        if (month > 6) {
            quarterArr.shift()
        }
        if (month > 9) {
            quarterArr.shift()
        }
        yearArr.unshift({value: '', text: '--请输入--'})
        monthArr.unshift({value: '', text: '--请输入--'})
        quarterArr.unshift({value: '', text: '--请输入--'})

        function changeSelected(n, o) {
            $('.date').hide()
            $('#year').hide()
            $('#month').hide()
            $('#quarterly').hide()
            $('#year input,#month input,#quarterly input').combobox({required: false})
            $('#startTime,#endTime').datebox({required: false})
            $('#year').width('15%')
            $('#year').attr('colspan', '1')
            $('#workSettings').val('')
            if (n == '周') {
                $('.date').show()
                $('#startTime,#endTime').datebox({required: true})
            } else if (n == '月度') {
                $('#year').show()
                $('#month').show()
                $('#year input,#month input').combobox({
                    required: true
                })
            } else if (n == '季度') {
                $('#year').show()
                $('#quarterly').show()
                $('#year input,#quarterly input').combobox({
                    required: true
                })
            } else if (n == '年度') {
                $('#year').width('30%')
                $('#year').attr('colspan', '2')
                $('#year').show()
                $('#year input').combobox({
                    required: true
                })
            }
        };

        function changeStart(data) {
            var $ws = $('#workSettings')
            if ($ws.val()) {
                if ($ws.val().indexOf('至') == 0) {
                    $ws.val(data + $ws.val())
                } else {
                    $ws.val(data + $ws.val().slice(10))
                }
            } else {
                $ws.val(data + '思政纪实工作设置')
            }
            $("#endTime").combo('setText', '');
            $("#endTime").datetimebox('calendar').calendar({
                styler: function (date) {
                    var now = new Date(data);
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    return date < d1 ? "color:#eee" : "";
                },
                validator: function (date) {
                    var now = new Date(data);
                    var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    return date >= d1;
                }
            });
        }

        function changeEnd(data) {
            var $ws = $('#workSettings')
            if ($ws.val()) {
                if ($ws.val().indexOf('至') == 0) {
                    $ws.val('至' + data + '思政纪实工作设置')
                } else {
                    $ws.val($ws.val().slice(0, 10) + '至' + data + '思政纪实工作设置')
                }
            } else {
                $ws.val('至' + data + '思政纪实工作设置')
            }
        }

        function changeYear(data) {
            var $ws = $('#workSettings')
            if ($ws.val()) {
                if ($ws.val().indexOf('年') > -1) {
                    $ws.val(data + $ws.val().slice(4))
                } else {
                    $ws.val(data + '年' + $ws.val())
                }
            } else {
                $ws.val(data + '年思政纪实工作设置')
            }
            // 年份变更 更改月份与季度
            var quarterArr = [
                {value: '一季度', text: '一季度'},
                {value: '二季度', text: '二季度'},
                {value: '三季度', text: '三季度'},
                {value: '四季度', text: '四季度'},
            ]
            monthArr = []

            var now = new Date()
            var year = now.getFullYear();
            var month = now.getMonth() + 1;

            if (data > year) {
                for (var i = 1; i < 13; i++) {
                    monthArr.push({value: i, text: i + '月'})
                }
            } else {
                for (var i = 1; i < 13; i++) {
                    if (i >= month)
                        monthArr.push({value: i, text: i + '月'})
                }
                if (month > 3) {
                    quarterArr.shift()
                }
                if (month > 6) {
                    quarterArr.shift()
                }
                if (month > 9) {
                    quarterArr.shift()
                }
            }
            monthArr.unshift({value: '', text: '--请输入--'})
            quarterArr.unshift({value: '', text: '--请输入--'})
            $("#month input").combobox({data: monthArr})
            $("#quarterly input").combobox({data: quarterArr})
        }

        function changeMonth(data) {
            var $ws = $('#workSettings')
            if ($ws.val()) {
                if ($ws.val().indexOf('年') > -1 && $ws.val().indexOf('月') > -1) {
                    $ws.val($ws.val().slice(0, 5) + data + $ws.val().slice(7))
                } else if ($ws.val().indexOf('年') > -1 && $ws.val().indexOf('月') == -1) {
                    $ws.val($ws.val().slice(0, 5) + data + '月' + $ws.val().slice(5))
                } else {
                    $ws.val(data + '月' + $ws.val())
                }
            } else {
                $ws.val(data + '月思政纪实工作设置')
            }
        }

        function changeQuarter(data) {
            var $ws = $('#workSettings')
            if ($ws.val()) {
                if ($ws.val().indexOf('年') > -1 && $ws.val().indexOf('季度') > -1) {
                    $ws.val($ws.val().slice(0, 5) + data + $ws.val().slice(8))
                } else if ($ws.val().indexOf('年') > -1 && $ws.val().indexOf('季度') == -1) {
                    $ws.val($ws.val().slice(0, 5) + data + $ws.val().slice(5))
                } else {
                    $ws.val(data + $ws.val())
                }
            } else {
                $ws.val(data + '思政纪实工作设置')
            }
        }

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('id');
            top.mesConfirm("温馨提示", "请确认是否删除该思政纪实工作配置，删除后不可恢复！", function () {
                ajaxgeneral({
                    url: 'action/usRecordConfig/deleteUsRecordConfig',
                    data: {id: id},
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        $("#glyTable").datagrid("reload");
                    }
                });
            });
        })

        //下载模板
        function downloadFile() {
            window.open(web.rootdir + "action/usRecordConfig/downloadTemplate");
        }

        //导入excel
        function uploadExcel() {
            $('#uploadFiles').trigger('click');
        }

        //导入excel后
        function OtherInfo(data) {
            // $('#glyTable').datagrid('load');
            $("#glyTable").datagrid("reload");
            $("#glyTableAddForm").datagrid("reload");

        }

        function initsystem() {
            $('#configuration').val(web.currentUser.truename)
            $('#configDeptName').val(web.currentUser.belongOrgName)
            $('#configTime').val(getNow())
        };
    </script>
    <style>
        textarea {
            white-space: normal !important;
        }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }

        .formTable > tbody > tr > td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable > tbody > tr > td input,
        .formTable > tbody > tr > td span,
        .formTable > tbody > tr > td textarea,
        .formTable > tbody > tr > td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: center;
            max-width: 100px;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #fff;
        }

        /* input:read-only { background-color: #f7f7f7; } */

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: -15px;
        }

        .cselectorImageUL input[type='file'] {
            display: inline-block;
            width: 60px !important;
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        .cselectorImageUL {
            width: 100%;
        }

        .dialog-button {
            text-align: center;
        }

        .dialog-button .l-btn {
            margin-left: 30px;
        }

        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }

        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</head>

<body class="body_page">
<form id="glyTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="8%"></td>
            <td width="16%"></td>
            <td width="8%"></td>
            <td width="16%"></td>
            <td width="52%"></td>
        </tr>
        <tr>
            <td align="right">思政工作频次类型</td>
            <td>
                <input id="frequencyType" name="frequencyType" class="easyui-combobox"
                       style="width: 100%; height: 32px;" data-options="
                    valueField: 'value',
                    panelHeight:'auto',
                    ischooseall:true,
                    textField: 'text',
                    editable:false,
                    data:[{value:'周',text:'周'}, {value:'月度',text:'月度'}, {value:'季度',text:'季度'}, {value:'年度',text:'年度'}]"/>
            </td>
            <td align="right">工作设置</td>
            <td><input name="workSettings" type="text"/></td>
            <td>
                <!--                    <a class="btn fr a_primary ml10" onclick="downloadFile()"><span>模板下载</span></a>-->
                <a class="btn fr ml10 showDialog "><span>新增</span></a>
                <a class="btn fr searchtable"><span>查询</span></a>
            </td>
            <!--                <td>-->
            <!--                    <input id="uploadFiles" name="uploadFiles" type="text" file="list" mulaccept="true"-->
            <!--                        class="cselectorImageUpload" btnmsg="<span class='iconfont' title='添加' style='font-size:14px'>导入</span>"-->
            <!--                        href="action/usRecordConfig/importInfo" OtherInfo="OtherInfo" />-->
            <!--                </td>-->
            <!-- <td width="200"></td> -->
        </tr>
    </table>
</form>
<!--table-->
<div class="glyTable">
    <table id="glyTable"></table>
</div>
<!--新增修改的dialog页面-->
<div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true"
     style="width:1340px;height:480px; margin-top: 15px">
    <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()"
          initsystem='initsystem()'>
        <table border="0" cellpadding="0" cellspacing="10" class="formTable">
            <tr>
                <td width="20%" align="center" class="lable">
                    工作设置
                </td>
                <td width="80%" colspan="4">
                    <input id="workSettings" name="workSettings" type="text" class="easyui-validatebox"
                           readonly="true"/>
                </td>
            </tr>
            <tr>
                <td class="lable"><span>任务描述</span></td>
                <td colspan="5">
                        <textarea id="taskDescription" name="taskDescription" class="easyui-validatebox"
                                  validType="maxLength[1000,'talkContentTips']" style="min-height:120px;"></textarea>
                    <p class="talkContentTips"></p>
                </td>
            </tr>
            <tr style="height: 40px">
                <td class="lable">附件</td>
                <td colspan="5" style="padding-left: 7px">
                    <input id="taskDescriptionFiles" name="taskDescriptionFiles" type="text" file="true"
                           mulaccept="true" OtherInfo="funfun"
                           class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
                </td>
            </tr>
            <tr style="position: relative;">
                <td class="lable">
                    类别
                </td>
                <td colspan="5">
                    <input id="recordType" name="recordType" type="text" class="easyui-combobox"
                           style="width: 100%; height: 32px;" data-options="
                            panelHeight:'auto',
                            ischooseall:false,
                            multiple:true,
                            editable:false,
                            contentType:'application/json; charset=utf-8',"/>
                </td>
            </tr>
            <tr>
                <td width="20%" align="center" class="lable">
                    思政工作频次类型<font class="col_r">*</font>
                </td>
                <td width="30%">
                    <input name="frequencyType" class="easyui-combobox" style="width: 100%; height: 32px;"
                           data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        ischooseall:true,
                        textField: 'text',
                        editable:false,
                        required:true,
                        data:[{value:'周',text:'周'},
                        {value:'月度',text:'月度'},
                        {value:'季度',text:'季度'},
                        {value:'年度',text:'年度'}],
                        onChange:changeSelected"/>
                </td>
                <td width="20%" align="center" class="lable">
                    思政工作时间<font class="col_r">*</font>
                </td>
                <td style="display: none">开始时间</td>
                <td width="15%" class="date" style="display:none">
                    <input id="startTime" name="startTime" type="text" class="easyui-datebox fl"
                           style="width:100%;height:32px;" validType="startDateCheck['endTime','startTime']"
                           data-options="panelHeight:'auto', editable:false,onChange:changeStart, "/>
                </td>
                <td style="display: none">结束时间</td>
                <td width="15%" class="date" style="display:none">
                    <input id="endTime" name="endTime" type="text" class="easyui-datebox"
                           style="width:100%;height:32px;" validType="endDateCheck['startTime','endTime']"
                           data-options="panelHeight:'auto', editable:false,onChange:changeEnd"/>
                </td>
                <td id="year" style="display:none" colspan="1">
                    <input name="year" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            data:yearArr,
                            onChange: changeYear
                            "/>
                </td>
                <td id="month" style="display:none">
                    <input name="month" class="easyui-combobox" style="width: 100%; height: 32px; " data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            data:monthArr,
                            onChange: changeMonth
                            "/>
                </td>
                <td id="quarterly" style="display:none" colspan="1">
                    <input name="quarterly" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            textField: 'text',
                            editable:false,
                            data:quarterArr,
                            onChange: changeQuarter
                            "/>
                </td>

                <td></td>

            </tr>
            <tr>
                <td width="20%" align="center" class="lable">
                    思政工作频次<font class="col_r">*</font>
                </td>
                <td width="30%">
                    <input name="frequency" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                        valueField: 'value',
                        ischooseall:true,
                        treeprompt:'--请选择--',
                        textField: 'text',
                        editable:false,
                        required:true,
                        data:timeArr"/>
                </td>
                <td width="20%" align="center" class="lable">
                    配置人
                </td>
                <td width="30%" colspan="2">
                    <input id="configuration" name="configuration" type="text" readonly="readonly" noReset="true"/>
                </td>
            </tr>
            <tr>
                <td width="20%" align="center" class="lable">
                    配置部门
                </td>
                <td width="30%">
                    <input id="configDeptName" name="configDeptName" type="text" readonly="readonly"
                           noReset="true"/>
                </td>
                <td width="20%" align="center" class="lable">
                    配置时间
                </td>
                <td width="30%" colspan="2">
                    <input id="configTime" name="configTime" type="text" readonly="readonly" noReset="true"/>
                </td>
            </tr>
        </table>


        <!-- <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:1340px;height:400px;">
               <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()"
                   initsystem='initsystem()'>
                   <table border="0" cellpadding="0" cellspacing="10" class="formTable">
                       <tr>
                           <td  width="100" align="center" class="lable">
                               工作设置
                           </td>
                           <td  colspan="4">
                               <input id="workSettings" name="workSettings" type="text" class="easyui-validatebox"
                                   readonly="true" />
                           </td>
                       </tr>
                       <tr>
                           <td width="100" align="center" class="lable">
                               思政工作频次类型<font class="col_r">*</font>
                           </td>
                           <td width="220">
                               <input name="frequencyType" class="easyui-combobox" style="width: 100%; height: 32px;"
                                   data-options="
                               valueField: 'value',
                               panelHeight:'auto',
                               ischooseall:true,
                               textField: 'text',
                               editable:false,
                               required:true,
                               data:[{value:'周',text:'周'},
                               {value:'月度',text:'月度'},
                               {value:'季度',text:'季度'},
                               {value:'年度',text:'年度'}],
                               onChange:changeSelected" />
                           </td>
                           <td width="100" align="center" class="lable">
                               思政工作时间<font class="col_r">*</font>
                           </td>
                           <td style="display: none">开始时间</td>
                           <td width="110" class="date" style="display:none">
                               <input id="startTime" name="startTime" type="text" class="easyui-datebox fl"
                                   style="width:100%;height:32px;" validType="startDateCheck['endTime','startTime']"
                                   data-options="panelHeight:'auto', editable:false,onChange:changeStart, " />
                           </td>
                           <td style="display: none">结束时间</td>
                           <td width="110" class="date" style="display:none">
                               <input id="endTime" name="endTime" type="text" class="easyui-datebox"
                                   style="width:100%;height:32px;" validType="endDateCheck['startTime','endTime']"
                                   data-options="panelHeight:'auto', editable:false,onChange:changeEnd" />
                           </td>
                           <td id="year" width="110" style="display:none">
                               <input name="year" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                                   valueField: 'value',
                                   textField: 'text',
                                   editable:false,
                                   data:yearArr,
                                   onChange: changeYear
                                   " />
                           </td>
                           <td id="month" width="110" style="display:none">
                               <input name="month" class="easyui-combobox" style="width: 100%; height: 32px; " data-options="
                                   valueField: 'value',
                                   textField: 'text',
                                   editable:false,
                                   data:monthArr,
                                   onChange: changeMonth
                                   " />
                           </td>
                           <td id="quarterly" width="110" style="display:none" >
                               <input name="quarterly" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                                   valueField: 'value',
                                   textField: 'text',
                                   editable:false,
                                   data:quarterArr,
                                   onChange: changeQuarter
                                   " />
                           </td>
                       </tr>
                       <tr>
                           <td width="100" align="center" class="lable">
                               思政工作频次<font class="col_r">*</font>
                           </td>
                           <td width="220">
                               <input name="frequency" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                               valueField: 'value',
                               ischooseall:true,
                               treeprompt:'--请选择--',
                               textField: 'text',
                               editable:false,
                               required:true,
                               data:timeArr" />
                           </td>
                           <td width="100" align="center" class="lable">
                               配置人
                           </td>
                           <td width="220" colspan="2">
                               <input id="configuration" name="configuration" type="text" readonly="readonly" noReset="true" />
                           </td>
                       </tr>
                       <tr>
                           <td width="100" align="center" class="lable">
                               配置部门
                           </td>
                           <td width="220">
                               <input id="configDeptName" name="configDeptName" type="text" readonly="readonly"
                                   noReset="true" />
                           </td>
                           <td width="100" align="center" class="lable">
                               配置时间
                           </td>
                           <td width="20" colspan="2">
                               <input id="configTime" name="configTime" type="text" readonly="readonly" noReset="true" />
                           </td>
                       </tr>
                   </table>  -->


    </form>
</div>
</body>

</html>