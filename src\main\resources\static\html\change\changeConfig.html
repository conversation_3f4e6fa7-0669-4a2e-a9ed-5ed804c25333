<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>新增政策宣讲配置</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent();

        $(function () {
        })
    </script>
    <style>
    </style>
</head>

<body class="body_page">
    <form id="experienceForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
        cmd-select="/action/usProblemInfo/getFormDetail" beforeSubmit="beforeSubmit()" submitcallback="submitcallback()"
        getcallback="getcallback()">
        <div style="width: 100%;">
            <table class="formTable" border="0" cellpadding="0" th:colspan="6" cellspacing="10" width="90%"
                style="white-space: nowrap">
                <tr>
                    <td class="lable" align="center" width="20%">
                        工作设置<font class="col_r">*</font>
                    </td>
                    <td colspan="3" width="80%">

                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="20%">
                        思政工作频次类型<font class="col_r">*</font>
                    </td>
                    <td width="30%">
                        <select class="easyui-combobox" id="questionMode" name="questionMode"
                            style="width:100%; height: 32px;"
                            data-options="editable:false,onChange:gradChange,panelHeight:'auto'" noReset="true">
                            <option selected value="">--请选择--</option>
                            <option value="0">周</option>
                            <option value="1">月度</option>
                            <option value="2">季度</option>
                            <option value="3">年度</option>
                        </select>
                    </td>
                    <td class="lable" align="center" width="20%">
                        思政工作时间<font class="col_r">*</font>
                    </td>
                    <td width="30%">

                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="20%">
                        思政工作频次<font class="col_r">*</font>
                    </td>
                    <td width="30%">
                        <select class="easyui-combobox" id="questionMode" name="questionMode"
                            style="width:90%; height: 32px;" data-options="editable:false,onChange:gradChange"
                            noReset="true">
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select>
                        <span>次</span>
                    </td>
                    <td class="lable" align="center" width="20%">
                        配置人
                    </td>
                    <td width="30%">
                        <input id="problemName" name="problemName" type="text" style="width:100%; height: 32px;"
                            noReset="true" />
                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="20%">
                        配置部门
                    </td>
                    <td width="30%">
                        <input id="problemName" name="problemName" type="text" style="width:100%; height: 32px;"
                            noReset="true" />
                    </td>
                    <td class="lable" align="center" width="20%">
                        配置时间
                    </td>
                    <td width="30%">
                        <input id="problemName" name="problemName" type="text" style="width:100%; height: 32px;"
                            noReset="true" />
                    </td>
                </tr>
            </table>
        </div>
    </form>
</body>

</html>