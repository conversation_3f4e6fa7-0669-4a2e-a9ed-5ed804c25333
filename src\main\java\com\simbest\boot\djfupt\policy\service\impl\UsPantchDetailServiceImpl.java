package com.simbest.boot.djfupt.policy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import com.simbest.boot.djfupt.policy.repository.UsPantchDetailRepository;
import com.simbest.boot.djfupt.policy.service.IUsPantchDetailService;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.AppFileUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service(value = "usPantchDetailService")
@SuppressWarnings("ALL")
public class UsPantchDetailServiceImpl extends LogicService<UsPantchDetail, String> implements IUsPantchDetailService {

    private UsPantchDetailRepository usPantchDetailRepository;

    @Autowired
    public UsPantchDetailServiceImpl(UsPantchDetailRepository repository) {
        super(repository);
        this.usPantchDetailRepository = repository;
    }

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private ISysOperateLogService operateLogService;
    @Autowired
    private CustomDynamicWhere customDynamicWhere;
    @Autowired
    private IUsPantchDetailService usPantchDetailService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private AppFileUtil appFileUtil;


    String param1 = "/action/usPantchDetail";


    @Getter
    public Charset charset = Charset.forName("GBK");


    /**
     * 更新附件
     *
     * @param usPantchDetail
     */
    @Override
    public void updateFileInfoById(UsPantchDetail usPantchDetail) {
        List<SysFile> drawFiles = usPantchDetail.getDrawFiles();
        List<String> drawFileIds = new ArrayList<>();
        String fileid = "";

        if (CollectionUtil.isNotEmpty(drawFiles)) {
            if (drawFiles != null || drawFiles.size() > 0) {
                for (SysFile drawFile : drawFiles) {
                    drawFileIds.add(drawFile.getId());
                }
            }

        }
        fileid = Joiner.on(",").join(drawFileIds);
        usPantchDetail.setFileIds(fileid);
        usPantchDetailService.update(usPantchDetail);
    }

    /**
     * 宣讲清单列表
     *
     * @param source
     * @param currentUserCode
     * @param webId           页面UUID
     * @param policyId        宣讲清单页面ID
     * @return
     */
    @Override
    public List<Map<String, Object>> mattersList(String source, String currentUserCode, String pmInsId, String webId) {
        if(source.equals("MOBILE")){
            currentUserCode = rsaEncryptor.decrypt(currentUserCode);
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> param = Maps.newHashMap();
        StringBuffer sql = new StringBuffer("select * from US_PANTCH_DETAIL t where t.enabled=1 and t.removed_time is null ");
        if (StringUtils.isNotEmpty(webId) && StringUtils.isEmpty(pmInsId)) {//页面生成的UUID不为空，但宣讲清单为空 (省公司人创建)
            sql.append(" and t.web_id=:webId ");
            param.put("webId", webId);
        } else if (StringUtils.isNotEmpty(pmInsId) && StringUtils.isNotEmpty(webId)) {//页面生成的UUID为空，但宣讲清单不为空（分公司人创建）
            sql.append(" and (t.pm_ins_id = :pmInsId or t.web_id = :webId) and t.creator=:creator ");
            param.put("pmInsId", pmInsId);
            param.put("webId", webId);
            param.put("creator", currentUserCode);
        }else{
            sql.append(" and t.pm_ins_id = :pmInsId  and t.creator=:creator ");
            param.put("pmInsId", pmInsId);
            param.put("creator", currentUserCode);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    /**
     * 政策宣讲清单-宣讲事项一键下载
     *
     * @param usPantchId 宣讲事项列表ID
     * @param response
     * @param request
     * @return
     */
    @Override
    public ResponseEntity<?> usPantchDetailExport(String usPantchId, HttpServletResponse response, HttpServletRequest request) {
        Resource resource = null;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = df.format(new Date()) + "政策宣讲清单-宣讲事项附件";
        List<File> fileList = Lists.newArrayList();
        try {
            IUser iUser = SecurityUtils.getCurrentUser();
            if (StringUtils.isNotEmpty(usPantchId)) {
                UsPantchDetail usPantchDetail = usPantchDetailService.findById(usPantchId);
                if (StringUtils.isNotEmpty(usPantchDetail.getFileIds())) {
                    List<String> fileIdList = Arrays.asList(usPantchDetail.getFileIds().split(","));
                    for (String fileId : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileId);
                        if (StringUtils.isNotEmpty(sysFile.getId())) {
                            File file = sysFileService.getRealFileById(sysFile.getId());//fastdfs
                            fileList.add(file);
                        }

                    }

                }
            }
            File zipFile = appFileUtil.createTempFileWithName(fileName.concat(".zip"));
            ZipUtil.zip(zipFile, charset, true, fileList.toArray(new File[]{}));
            String fileName1 = URLEncoder.encode(fileName.concat(".zip"), ApplicationConstants.UTF_8);
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=\"" + fileName1 + "\"");
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            resource = new InputStreamResource(new FileInputStream(zipFile));
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return ResponseEntity.ok().headers(headers).body(resource);
    }

    @Override
    public List<UsPantchDetail> findUsPantchDetailInfo(String pmInsId, String creator) {
        return usPantchDetailRepository.findUsPantchDetailInfo(pmInsId,creator);
    }



}
