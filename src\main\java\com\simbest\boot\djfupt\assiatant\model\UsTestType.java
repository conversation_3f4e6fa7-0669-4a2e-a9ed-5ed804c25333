package com.simbest.boot.djfupt.assiatant.model;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_test_type")
@ApiModel(value = "智能助手问题类型")
public class UsTestType extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 1000)
    @ApiModelProperty(value = "事件名称")
    private String name;

    @Column(length = 1000)
    @ApiModelProperty(value = "事件操作")
    private String operation;

    @Column(length = 1000)
    @ApiModelProperty(value = "1大类2小类")
    private String testType;

    @Column(length = 1000)
    @ApiModelProperty(value = "父类id")
    private String parentId;
}

