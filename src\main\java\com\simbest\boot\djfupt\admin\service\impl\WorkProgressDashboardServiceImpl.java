package com.simbest.boot.djfupt.admin.service.impl;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.WorkProgressVo;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.admin.service.WorkProgressDashboardService;
import com.simbest.boot.djfupt.caseinfo.model.CaseStatisticsVo;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;
import com.simbest.boot.djfupt.index.service.IndexService;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.repository.UsPolicyInfoRepository;
import com.simbest.boot.djfupt.policy.service.IUsPolicyInfoService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.repository.UsProblemInfoRepository;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.djfupt.record.model.RecordVo;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.record.repository.UsRecordFillRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordFillService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service(value = "workProgressDashboardService")
@SuppressWarnings("ALL")
public class WorkProgressDashboardServiceImpl implements WorkProgressDashboardService {

    @Autowired
    private IUsPolicyInfoService usPolicyInfoService;

    @Autowired
    IUsRecordFillService usRecordFillService;

    @Autowired
    IUsProblemInfoService usProblemInfoService;

    @Autowired
    IUsCaseInfoService usCaseInfoService;

    @Autowired
    private IndexService indexService;

    @Autowired
    LoginUtils loginUtils;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;
    @Autowired
    UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private UsPolicyInfoRepository policyInfoRepository;

    @Autowired
    private UsRecordFillRepository fillRepository;


    @Autowired
    private UsProblemInfoRepository problemInfoRepository;

    @Autowired
    private UsCaseInfoRepository caseInfoRepository;

    @Override
    public Map<String, Map<String, Object>> findPolicyLederTotal(String currentUserCode, String source) {
        Map<String, Map<String, Object>> resultList = new HashMap<>();
        if (Constants.MOBILE.equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser user = SecurityUtils.getCurrentUser();

        //查询当前分公司本月数据参数预处理
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM");
        String formattedMonth = currentDate.format(formatter);
        Map<String, Object> paramMap = new HashMap<>();
        //  paramMap.put("city", user.getBelongCompanyName());
        paramMap.put("month", formattedMonth);
        paramMap.put("year", DateUtil.getCurrYear());
        //   paramMap.put("companyName", DateUtil.getCurrYear());

        //获取宣讲统计
        List<Map<String, Object>> lectureList;
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));
        if (user.getBelongCompanyTypeDictValue().equals("01") || isAdmin) {
            lectureList = usPolicyInfoService.findPolicyLeder(paramMap);
        } else {
            lectureList = usPolicyInfoService.findPolicyLederOther(paramMap);
        }
        Map<String, Object> policyMap = new HashMap<>();
        if (lectureList.size() > 0) {
            Integer dopreach = lectureList.stream()
                    .mapToInt(map -> MapUtil.getInt(map, "dopreach"))
                    .sum();
            Integer preach = lectureList.stream()
                    .mapToInt(map -> MapUtil.getInt(map, "preach"))
                    .sum();
            String rate = "";

            if (dopreach == 0 || preach == 0) {
                rate = "0%";
            } else {
              //  float f3 = (dopreach / preach) * 100;
                BigDecimal b = new BigDecimal(dopreach).divide(new BigDecimal(preach),2, BigDecimal.ROUND_HALF_UP);
//                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
//                if (Float.compare(f3, 100.0f) > 0) {
//                    f3 = 100;
//                }

                rate = b + "%";
            }

            policyMap.put("dopreach", dopreach);//已完成宣讲次数
            policyMap.put("preach", preach);//应宣讲次数
            policyMap.put("rate", rate);//完成率
        } else {
            policyMap.put("dopreach", 0);//已完成宣讲次数
            policyMap.put("preach", 0);//应宣讲次数
            policyMap.put("rate", "0%");//完成率
        }
        resultList.put("lecture", policyMap);

        //获取思政工作统计
        List<RecordVo> recordStatisticsList;
        paramMap.put("applyTime", DateUtil.getCurrMonth());

        if (user.getBelongCompanyTypeDictValue().equals("01") || isAdmin) {
            recordStatisticsList = usRecordFillService.recordStatistics(0, 99999999, paramMap);
        } else {
            recordStatisticsList = usRecordFillService.recordStatisticsOther(0, 99999999, paramMap);
        }

        Map<String, Object> recordStatisticsMap = new HashMap<>();
        if (recordStatisticsList.size() > 0) {
            Integer dopreach = recordStatisticsList.stream()
                    .mapToInt(RecordVo::getNums)
                    .sum();
            Integer preach = recordStatisticsList.stream()
                    .mapToInt(RecordVo::getGrids)
                    .sum();
            String rate = "";

            if (dopreach == 0 || preach == 0) {
                rate = "0%";
            } else {
             //   float f3 = (dopreach / preach) * 100;
                BigDecimal b = new BigDecimal(dopreach).divide(new BigDecimal(preach),2, BigDecimal.ROUND_HALF_UP);
//                BigDecimal b = new BigDecimal(f3);
//                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
//                if (Float.compare(f3, 100.0f) > 0) {
//                    f3 = 100;
//                }
                rate = b + "%";
            }
            recordStatisticsMap.put("dopreach", dopreach);//已完成宣讲次数
            recordStatisticsMap.put("preach", preach);//应宣讲次数
            recordStatisticsMap.put("rate", rate);//完成率
        } else {


            recordStatisticsMap.put("dopreach", 0);//已完成次数
            recordStatisticsMap.put("preach", 0);//应开展次数
            recordStatisticsMap.put("rate", "0%");//完成率
        }
        resultList.put("recordStatistics", recordStatisticsMap);


        //解决问题
        List<ProblemStatisticsVo> problemStatisticsList = null;
        String startTime = DateUtil.getCurrMonthFirstDay();
        String endTime = DateUtil.getCurrMonthLastDay();
        paramMap.put("startTime", startTime);
        paramMap.put("endTime", endTime);
        if (user.getBelongCompanyTypeDictValue().equals("01") || isAdmin) {
            problemStatisticsList = usProblemInfoService.problemStatistics(0, 99999999, paramMap);     //省公司
        } else {
            problemStatisticsList = usProblemInfoService.problemStatisticsOther(0, 99999999, paramMap);
        }
        Map<String, Object> problemStatisticsMap = new HashMap<>();
        if (problemStatisticsList.size() > 0) {
            Integer dopreach = problemStatisticsList.stream()
                    .mapToInt(ProblemStatisticsVo::getSolveNums)
                    .sum();
            Integer preach = problemStatisticsList.stream()
                    .mapToInt(ProblemStatisticsVo::getProblemNums)
                    .sum();
            String rate = "";

            if (dopreach == 0 || preach == 0) {
                rate = "0%";
            } else {
                BigDecimal b = new BigDecimal(dopreach).divide(new BigDecimal(preach),2, BigDecimal.ROUND_HALF_UP);
//                float f3 = (dopreach / preach) * 100;
//                BigDecimal b = new BigDecimal(f3);
//                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
//                if (Float.compare(f3, 100.0f) > 0) {
//                    f3 = 100;
//                }
                rate = b + "%";
            }
            problemStatisticsMap.put("dopreach", dopreach);//已完成宣讲次数
            problemStatisticsMap.put("preach", preach);//应宣讲次数
            problemStatisticsMap.put("rate", rate);//完成率
        } else {

            problemStatisticsMap.put("dopreach", 0);//已完成次数
            problemStatisticsMap.put("preach", 0);//应开展次数
            problemStatisticsMap.put("rate", "0%");//完成率
        }
        resultList.put("problemStatistics", problemStatisticsMap);


        //推广经验
        List<CaseStatisticsVo> caseStatisticsList = null;
        Map<String, Object> caseStatisticsMap = new HashMap<>();
        if (user.getBelongCompanyTypeDictValue().equals("01") || isAdmin) {//省公司
            caseStatisticsList = usCaseInfoService.caseStatistics(0, 99999999, paramMap);
        } else {
            caseStatisticsList = usCaseInfoService.caseStatisticsOther(0, 99999999, paramMap);
        }
        if (caseStatisticsList.size() > 0) {
            Integer dopreach = caseStatisticsList.stream()
                    .mapToInt(CaseStatisticsVo::getNums)
                    .sum();
            int gridNum = 0;
            boolean isBranchAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_City, simpleRoless.getRoleCode()));
            if (isAdmin) {
                gridNum = adminManagerRepository.findByEnabledAndRoleUserId(Constants.FJFUPT_BRO);
            } else if (isBranchAdmin) {
                gridNum = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(user.getBelongDepartmentCode(), Constants.FJFUPT_BRO);
            }
            String rate = "";
            if (dopreach == 0 || gridNum == 0) {
                rate = "0%";
            } else {
                BigDecimal b = new BigDecimal(dopreach).divide(new BigDecimal(gridNum),2, BigDecimal.ROUND_HALF_UP);
//                float f3 = (dopreach / gridNum) * 100;
//                BigDecimal b = new BigDecimal(f3);
//                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
//                if (Float.compare(f3, 100.0f) > 0) {
//                    f3 = 100;
//                }
                rate = b + "%";
            }
            caseStatisticsMap.put("dopreach", dopreach);//已完成次数
            caseStatisticsMap.put("preach", gridNum);//应开展次数
            caseStatisticsMap.put("rate", "0%");//完成率
        } else {

            caseStatisticsMap.put("dopreach", 0);//已完成次数
            caseStatisticsMap.put("preach", 0);//应开展次数
            caseStatisticsMap.put("rate", "0%");//完成率
        }
        resultList.put("caseStatistics", caseStatisticsMap);
        return resultList;
    }

    @Override
    public DataScreeningVo dataScreening(String currentUserCode, String source, Map<String, Object> resultMap) {
        if (Constants.MOBILE.equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser user = SecurityUtils.getCurrentUser();
        DataScreeningVo dataScreeningVo = new DataScreeningVo();
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formattedMonth = currentDate.format(formatter);
        String startTime = formattedMonth + "-01 00:00:00";
        String endTime = DateUtil.getCurrMonthLastDay() + " 23:59:59";
        resultMap.put("startTime", startTime);
        resultMap.put("endTime", endTime);
        resultMap.put("time", formattedMonth);
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));
        if (user.getBelongCompanyTypeDictValue().equals("01") || isAdmin) {
            dataScreeningVo = indexService.dataScreeningTotal(resultMap);
        } else {
            resultMap.put("companyName", user.getBelongCompanyName());
            dataScreeningVo = indexService.dataScreeningBranchTotal(resultMap);
        }
        return dataScreeningVo;
    }

    @Override
    public JsonResponse workProgress() {
        WorkProgressVo workProgressVo = new WorkProgressVo();
        IUser user = SecurityUtils.getCurrentUser();
        String time = DateUtil.getCurrMonth();
        String year = DateUtil.getCurrYear();
        String month = DateUtil.getCurrMonth();
        List<UsPolicyInfo> policyInfos = policyInfoRepository.findAllByTimeAndCreate(time, user.getUsername());
        workProgressVo.setPolicyNum(policyInfos.size());
        if (policyInfos.size() > 0) {
            workProgressVo.setPolicyRate("100%");
        } else {
            workProgressVo.setPolicyRate("0%");
        }

        int recordFills = fillRepository.countByName(time, user.getUsername());
        workProgressVo.setFillNums(recordFills);
        if (recordFills > 0) {
            workProgressVo.setFillRate("100%");
        } else {
            workProgressVo.setFillRate("0%");
        }

        List<UsProblemInfo> problemInfos = problemInfoRepository.findAllByCreatorAndYearAndIsDraft(Integer.parseInt(year), user.getUsername());
        workProgressVo.setProblemNums(problemInfos.size());
        List<UsProblemInfo> problemInfoList = problemInfoRepository.findAllByCreatorAndMonthAndIsDraft(month, user.getUsername());
        if (problemInfoList.size() > 0) {
            workProgressVo.setProblemRate("100%");
        }else {
            workProgressVo.setProblemRate("0%");
        }
        List<UsCaseInfo> caseInfos=caseInfoRepository.findAllByCreatorAndYearAndAct(Integer.parseInt(year),user.getUsername());
        workProgressVo.setCaseNums(caseInfos.size());
        if(caseInfos.size()>0){
            workProgressVo.setCaseRate("100%");
        }else {
            workProgressVo.setCaseRate("0%");
        }
        return JsonResponse.success(workProgressVo);
    }
}

