package com.simbest.boot.djfupt.mainbills.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import org.springframework.data.repository.query.Param;

/**
 * <strong>Title : IPmInstenceService</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IUsPmInstenceService extends ILogicService<UsPmInstence,String> {

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    int deleteByPmId(String id);

    /**
     * 根据pmInsId查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    UsPmInstence findByPmInsId(String pmInsId);

    /**
     * 保存主单据
     * @param usPmInstence 主数据
     * @return
     */
    UsPmInstence saveData( UsPmInstence usPmInstence);

    /**
     * 根据business_key查找主单据
     * @param businessKey
     * @return
     */
    UsPmInstence findByBusinessKey(String businessKey);

    /**
     * 获取个数
     * @param pmInsType
     * @param createdTime
     * @return
     */

    String getCounts(@Param("pmInsType") String pmInsType, @Param("createdTime") String createdTime);

}
