package com.simbest.boot.djfupt.policy.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UsPantchDetailRepository extends LogicRepository<UsPantchDetail, String> {
    /**
     * 根据主单据ID和政策宣讲ID查询
     * @param pmInsId
     * @param policyId
     * @return
     */
    @Query(
            value = "select *" +
                    "  from US_PANTCH_DETAIL t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.pm_ins_id = :pmInsId" +
                    "   and t.creator=:creator",
            nativeQuery = true
    )
    List<UsPantchDetail> findUsPantchDetailInfo(@Param("pmInsId") String pmInsId,  @Param("creator") String creator);



    @Modifying
    @Query(
            value = "update" +
                    "   US_PANTCH_DETAIL t  set t.creator= :newCreator" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.pm_ins_id = :pmInsId" +
                    "   and t.creator=:creator",
            nativeQuery = true
    )
    int updateCaterByPmInsID(@Param("pmInsId") String pmInsId,
                             @Param("creator") String creator,
                             @Param("newCreator") String newCreator);




}
