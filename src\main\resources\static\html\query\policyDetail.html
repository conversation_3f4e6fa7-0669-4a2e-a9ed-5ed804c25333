<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>政策宣讲详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usPolicyInfo/findPolicyDeatir?id=" + gps.id, //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {
                                title: "单位", field: "belongCompanyName", width: 80, tooltip: true, align: "center"
                            },
                            {
                                title: "网格", field: "gridName", width: 80, tooltip: true, align: "center"
                            },
                            {
                                title: "工单标题", field: "title", width: 120, tooltip: true, align: "center"
                            },
                            {
                                title: "宣讲事项", field: "problemDescribe", width: 150, tooltip: true, align: "center"
                            },
                        ]
                    ]
                }
            };
            loadGrid(pageparam);

            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir +
                    "action/statisticalReport/exportGroupInfo");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });
        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };

    </script>
</head>

<body class="body_page" style="padding-top: 85px">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <div class="pageInfo" style="min-width: 0">
            <table border="0" cellpadding="0" cellspacing="6" width="100%">
                <tr>
                    <td width="90" align="right">单位</td>
                    <td width="200"><input id="city" name="city" class="easyui-combobox"
                            style="width: 100%; height: 32px;" data-options="
                            valueField: 'ORG_NAME',
                            panelHeight:'200px',
                            ischooseall:true,
                            textField: 'ORG_NAME',
                            editable:false,
                            url: 'action/commom/queryOrg'" />
                    </td>
                    <td width="90" align="right">网格</td>
                    <td width="200">
                        <input id="gridName" name="gridName" type="text" />
                    </td>
                    <td width="90" align="right">工单标题</td>
                    <td width="200"><input id="title" name="title" type="text" /></td>
                    <td width="90" align="right">宣讲事项</td>
                    <td width="200"><input name="lectureItems" type="text" /></td>
                    <td>
                        <div class=" w200">
                            <a class="btn fl searchtable">
                                <font>查询</font>
                            </a>
                            <a class="btn fl ml10 a_success exporttable ">
                                <font>导出</font>
                            </a>
                        </div>
                    </td>
                </tr>
            </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>