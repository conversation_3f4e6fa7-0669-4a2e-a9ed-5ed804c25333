package com.simbest.boot.djfupt.admin.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.task.todo.TodoCloseInterface;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.datapermission.common.model.QueryLevelConfig;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.model.ExportExcel;
import com.simbest.boot.djfupt.admin.model.HadminExcel;
import com.simbest.boot.djfupt.admin.model.PartyBuildingExcel;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.admin.repository.WfWorkItemRepository;
import com.simbest.boot.djfupt.admin.service.IUsAdminManagerService;
import com.simbest.boot.djfupt.common.repository.ActBusinessStatusRepository;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.policy.repository.UsPolicyInfoRepository;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.util.SMSTool;
import com.simbest.boot.djfupt.wfquey.repository.DictValueRepository;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(value = "usAdminManagerService")
@SuppressWarnings("ALL")
public class UsAdminManagerServiceImpl extends LogicService<UsAdminManager, String> implements IUsAdminManagerService {

    private UsAdminManagerRepository usAdminManagerRepository;

    @Autowired
    public UsAdminManagerServiceImpl(UsAdminManagerRepository usAdminManagerRepository) {
        super(usAdminManagerRepository);
        this.usAdminManagerRepository = usAdminManagerRepository;
    }

    @Autowired
    private IUsAdminManagerService usAdminManagerService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;


    @Autowired
    private AppConfig config;


    @Autowired
    public RsaEncryptor rsaEncryptor;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;

    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;

    @Autowired
    private TodoCloseInterface todoCloseInterface;

    @Autowired
    private ActBusinessStatusRepository actRep;


    @Autowired
    private SMSTool smsTool;

    @Autowired
    private ICommonService commonService;


    @Autowired
    private UsPolicyInfoRepository usPolicyInfoRepository;

    String param1 = "/action/usAdminManager";

    @Autowired
    private DictValueRepository dictValueRepository;


    /**
     * 查询管理员
     *
     * @param trueName
     * @param userName
     * @param roleUserId
     * @param belongComCode
     * @return
     */
    public JsonResponse queryHadmin(String trueName, String userName, String roleUserId, String belongComCode, Integer page, Integer size, String type, String gridName) {
        Specification<UsAdminManager> build = Specifications.<UsAdminManager>and()
                .eq("enabled", Boolean.TRUE)
                .eq(null != trueName, "trueName", trueName)
                .eq(null != roleUserId, "roleUserId", roleUserId)
                .eq(null != belongComCode, "belongComCode", belongComCode)
                .eq(null != userName, "userName", userName)
                .eq("type", type)
                .eq(type.equals("0") && null != gridName, "gridName", gridName)
                .build();
        Pageable pageable = this.getPageable(page, size);
        Page<UsAdminManager> usAdminManagers = this.findAll(build, pageable);
        return JsonResponse.success(usAdminManagers);

    }

    /**
     * 查询所有管理员信息
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllAdmin(Map<String, Object> resultMap, String roleUserId, String type, String gridName) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        String trueName = cn.hutool.core.map.MapUtil.getStr(resultMap, "trueName");
        String userName = cn.hutool.core.map.MapUtil.getStr(resultMap, "userName");
        if (StringUtils.isEmpty(roleUserId)) {
            roleUserId = cn.hutool.core.map.MapUtil.getStr(resultMap, "roleUserId");
        }

        String belongComCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "belongComCode");
        StringBuffer sql = new StringBuffer("select t.*  from US_ADMIN_MANAGER t where t.enabled=1 and t.removed_time is null");
        Map<String, Object> param = Maps.newHashMap();

        if (StringUtils.isNotEmpty(roleUserId)) {
            sql.append(" and t.role_user_id=:roleUserId  ");
            param.put("roleUserId", roleUserId);
        }

        if (StringUtils.isNotEmpty(gridName)) {
            sql.append(" and t.grid_name like concat(concat('%', :gridName), '%') ");
            param.put("gridName", gridName);
        }
        if (StringUtils.isNotEmpty(type)) {
            sql.append(" and t.type=:type ");
            param.put("type", type);
        }
        if (StringUtils.isNotEmpty(trueName)) {
            sql.append(" and t.true_name  like concat(concat('%', :trueName), '%') ");
            param.put("trueName", trueName);
        }
        if (StringUtils.isNotEmpty(userName)) {
            sql.append(" and t.user_name  like concat(concat('%', :userName), '%') ");
            param.put("userName", userName);
        }
        if (StringUtils.isNotEmpty(belongComCode)) {
            sql.append(" and (t.belong_com_code  like concat(concat('%', :belongComCode), '%')   or t.belong_org_code  like concat(concat('%', :belongComCode), '%')  ) ");
            param.put("belongComCode", belongComCode);
        }
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        String sql2 = " order by t.created_time desc";
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    public void exportExcel(String direction, //排序规则（asc/desc）
                            String properties, //排序规则（属性名称）
                            String roleUserId, //排序规则（属性名称）
                            String type, //排序规则（属性名称）
                            Map<String, Object> resultMap,
                            HttpServletResponse response,
                            HttpServletRequest request,
                            String gridName) {
        List<Map<String, Object>> maps = findAllAdmin(resultMap, roleUserId, type, gridName);
        String fileName = "";

        try {

            if (type.equals("1")) {
                fileName = "管理员信息.xls";

                //获取项目动态绝对路径
                String path = request.getServletContext().getRealPath("down");

                List<UsAdminManager> usAdminManagers = new ArrayList<>();

                for (Map<String, Object> systemCompilationMap : maps) {
                    systemCompilationMap.put("modifiedTime", "2022-12-06 00:00:00");
                    systemCompilationMap.put("createdTime", "2022-12-06 00:00:00");

                    UsAdminManager usAdminManager = null;
                    usAdminManager = JacksonUtils.json2obj(JacksonUtils.obj2json(systemCompilationMap), UsAdminManager.class);
                    if (usAdminManager.getRoleUserId().equals("djfupt_001")) {
                        usAdminManager.setType("省公司管理员");
                    } else {
                        usAdminManager.setType("分公司管理员");
                    }
                    if(StringUtils.isEmpty(usAdminManager.getPhone())){
                        SimpleUser iuser = uumsSysUserinfoApi.findByUsername(usAdminManager.getUserName(), Constants.APP_CODE);
                        usAdminManager.setPhone(iuser.getPreferredMobile());
                        this.update(usAdminManager);
                    }
                    usAdminManager.setOrgName(usAdminManager.getBelongCom()+"-"+usAdminManager.getBelongDept());
                    usAdminManagers.add(usAdminManager);


                }
                File targetFile = new File(fileName);
                ExcelUtil<UsAdminManager> exportUtil = new ExcelUtil<UsAdminManager>(UsAdminManager.class);
                exportUtil.exportExcel(usAdminManagers, "sheet1", new FileOutputStream(targetFile), null);
                FileTool.download(targetFile.getPath(), response);
            } else {
                fileName = "党建指导员信息.xls";

                //获取项目动态绝对路径
                String path = request.getServletContext().getRealPath("down");

                List<ExportExcel> exportExcels = new ArrayList<>();

                for (Map<String, Object> systemCompilationMap : maps) {
                    ExportExcel exportExcel = null;
                    exportExcel = JacksonUtils.json2obj(JacksonUtils.obj2json(systemCompilationMap), ExportExcel.class);
                    if(StringUtils.isEmpty(exportExcel.getPhone())){
                        SimpleUser iuser = uumsSysUserinfoApi.findByUsername(exportExcel.getUserName(), Constants.APP_CODE);
                        UsAdminManager adminManager=this.findById(exportExcel.getId());
                        adminManager.setPhone(iuser.getPreferredMobile());
                        exportExcel.setPhone(adminManager.getPhone());
                        this.update(adminManager);
                    }
                    exportExcel.setOrgName(exportExcel.getBelongCom()+"-"+exportExcel.getBelongDept());

                    exportExcels.add(exportExcel);



                }
                File targetFile = new File(fileName);
                ExcelUtil<ExportExcel> exportUtil = new ExcelUtil<ExportExcel>(ExportExcel.class);
                exportUtil.exportExcel(exportExcels, "sheet1", new FileOutputStream(targetFile), null);
                FileTool.download(targetFile.getPath(), response);
            }


        } catch (Exception e) {
            Exceptions.printException(e);

        }

    }

    /**
     * 添加管理员
     *
     * @param usAdminManager
     * @return
     */
    public JsonResponse insertInfo(UsAdminManager usAdminManager) {
        IUser user = SecurityUtils.getCurrentUser();
        List<UsAdminManager> usAdminManagers = usAdminManagerRepository.findByUserNameAndEnabled(usAdminManager.getUserName());
        if (CollectionUtil.isNotEmpty(usAdminManagers)) {
            return JsonResponse.fail("该管理员信息已经存在，请勿重复添加！");
        }
//        if (StringUtils.isNotEmpty(usAdminManager.getBelongComCode())) {
//            if (!user.getBelongCompanyCode().equals("4772338661636601428")) {
//                if (!usAdminManager.getBelongComCode().equals(user.getBelongCompanyCode())) {
//                    return JsonResponse.fail("您无权添加其他公司管理员,请您核查后再进行添加");
//                }
//            }
//        }

        if (usAdminManager.getType().equals("0")) {
            usAdminManager.setRoleUserId("djfupt_002");
        }
        if (usAdminManager.getRoleUserId().equals("djfupt_010")) {
            usAdminManager.setType("2");
        }
        //添加 userRol表数据.
        addRoleUser(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
        SimpleUser iuser = uumsSysUserinfoApi.findByUsername(usAdminManager.getUserName(), Constants.APP_CODE);
        usAdminManager.setPhone(iuser.getPreferredMobile());
        if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyNameParent());
            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyNameParent());
            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setBelongDepartmentName(iuser.getBelongCompanyName());
            usAdminManager.setBelongDepartmentCode(iuser.getBelongCompanyCode());
            usAdminManager.setBelongCom(iuser.getBelongCompanyNameParent());
            usAdminManager.setBelongComCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setBelongDept(iuser.getBelongCompanyName());
            usAdminManager.setBelongDeptCode(iuser.getBelongCompanyCode());
        }
        if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyName());
            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCode());
            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyName());
            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCode());
            usAdminManager.setBelongDepartmentName(iuser.getBelongDepartmentName());
            usAdminManager.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
            usAdminManager.setBelongCom(iuser.getBelongCompanyName());
            usAdminManager.setBelongComCode(iuser.getBelongCompanyCode());
            usAdminManager.setBelongDept(iuser.getBelongDepartmentName());
            usAdminManager.setBelongDeptCode(iuser.getBelongDepartmentCode());
        }
        usAdminManager.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
        usAdminManager.setBelongOrgCode(iuser.getBelongOrgCode());
        usAdminManager.setBelongOrgName(iuser.getBelongOrgName());

        UsAdminManager usAdmin = this.insert(usAdminManager);
        if (!usAdmin.getRoleUserId().equals(Constants.DEP_OBSERVER)) {
            insertQueryLevel(usAdminManager);//调整对应的五级权限
        }

        return JsonResponse.success("操作成功");

    }


    /**
     * 修改管理员信息
     *
     * @param usAdminManager
     * @return
     */
    public JsonResponse updateInfo(UsAdminManager usAdminManager) {
        //判断要修改的管理元是不是已经存在
        IUser user = SecurityUtils.getCurrentUser();
        List<UsAdminManager> usAdminManagers = usAdminManagerRepository.findByUserNameAndEnabledIsNotIdAndroleUserId(usAdminManager.getUserName(), usAdminManager.getId(), usAdminManager.getRoleUserId());
        if (CollectionUtil.isNotEmpty(usAdminManagers)) {
            return JsonResponse.fail("该管理员信息已经存在，请勿重复添加！");
        }

        if (StringUtils.isNotEmpty(usAdminManager.getBelongComCode())) {
            if (!user.getBelongCompanyCode().equals("4772338661636601428")) {
                if (!usAdminManager.getBelongComCode().equals(user.getBelongCompanyCode())) {
                    return JsonResponse.fail("您无权修改其他公司管理员,请您核查后再进行添加");
                }
            }
        }
        // 先删除之前roleUser的数据
        UsAdminManager usAdminManagerOld = usAdminManagerRepository.findByIdAndEnabled(usAdminManager.getId());
        String userNameOld = usAdminManagerOld.getUserName();
        deleteRoleUser(usAdminManagerOld.getUserName(), usAdminManagerOld.getRoleUserId());
        //在添加
        addRoleUser(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
        SimpleUser iuser = uumsSysUserinfoApi.findByUsername(usAdminManager.getUserName(), Constants.APP_CODE);
        usAdminManager.setPhone(iuser.getPreferredMobile());
        if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyNameParent());
            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setBelongDepartmentName(iuser.getBelongCompanyName());
            usAdminManager.setBelongDepartmentCode(iuser.getBelongCompanyCode());
            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyNameParent());
            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setBelongCom(iuser.getBelongCompanyNameParent());
            usAdminManager.setBelongComCode(iuser.getBelongCompanyCodeParent());
            usAdminManager.setBelongDept(iuser.getBelongCompanyName());
            usAdminManager.setBelongDeptCode(iuser.getBelongCompanyCode());
        }
        if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyName());
            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCode());
            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyName());
            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCode());
            usAdminManager.setBelongDepartmentName(iuser.getBelongDepartmentName());
            usAdminManager.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
            usAdminManager.setBelongCom(iuser.getBelongCompanyName());
            usAdminManager.setBelongComCode(iuser.getBelongCompanyCode());
            usAdminManager.setBelongDept(iuser.getBelongDepartmentName());
            usAdminManager.setBelongDeptCode(iuser.getBelongDepartmentCode());
        }
        usAdminManager.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
        usAdminManager.setBelongOrgCode(iuser.getBelongOrgCode());
        usAdminManager.setBelongOrgName(iuser.getBelongOrgName());
        this.update(usAdminManager);
        updateAdmin(usAdminManager);//调整对应的五级权限
        //如果是修改网格 需要替换我的待办已办和统一待办
        //判断是否修改的是网格
        if (usAdminManager.getRoleUserId().equals(Constants.FJFUPT_BRO)) {
            //在判断修改的是不是同一个人  wangao修改成wangao  如果是不需要更换数据
            usAdminManagerOld.setUserName(userNameOld);
            if (!userNameOld.equals(usAdminManager.getUserName())) {
                commonService.update(usAdminManagerOld, usAdminManager);
            }
        }


        return JsonResponse.success("操作成功");
    }


    public JsonResponse addRoleUser(String username, String roleId) {
        IUser user = SecurityUtils.getCurrentUser();//获取当前人员
        JsonResponse jsonResponse = HttpClient.post(config.getUumsAddress() + Constants.USER_ROLE + "createRoleUsers" + Constants.SSO + "?loginuser=" + encryptor.encrypt(user.getUsername()) + "&appcode=" + Constants.APP_CODE)
                .param("roleId", roleId)
                .param("usernames", username)
                .asBean(JsonResponse.class);
        return jsonResponse;
    }

    /**
     * @Description 角色_用户表、显示菜单
     **/
    public JsonResponse deleteRoleUser(String username, String roleId) {
        IUser user = SecurityUtils.getCurrentUser();//获取当前人员
        JsonResponse jsonResponse = HttpClient.post(config.getUumsAddress() + Constants.USER_ROLE + "deleteUsers" + Constants.SSO + "?loginuser=" + encryptor.encrypt(user.getUsername()) + "&appcode=" + Constants.APP_CODE)
                .param("roleId", roleId)
                .param("usernames", username)
                .asBean(JsonResponse.class);
        return jsonResponse;
    }


    public JsonResponse delInfo(String id) {
        UsAdminManager usAdminManager1 = usAdminManagerRepository.findByIdAndEnabled(id);
        // 先删除之前roleUser的数据
        deleteRoleUser(usAdminManager1.getUserName(), usAdminManager1.getRoleUserId());
        deleteAdmimn(usAdminManager1);//删除本管理员信息的五级权限
        this.delete(usAdminManager1);
        return JsonResponse.success("操作成功");
    }


    public JsonResponse queryUserOrgInfo(String userName) {
        IUser user = uumsSysUserinfoApi.findByUsername(userName, Constants.APP_CODE);
        return JsonResponse.success(user);
    }

    /**
     * 导出模板
     *
     * @param request
     * @param response
     */

    public void exportHadmin(HttpServletRequest request, HttpServletResponse response) {
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "导入模板.xls";

        List<HadminExcel> list = Lists.newArrayList();
        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<HadminExcel> exportUtil = new ExcelUtil<HadminExcel>(HadminExcel.class);
            exportUtil.exportExcel(list, "管理员导入模板", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), request, response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    /**
     * 导入酒水数据
     *
     * @param request
     * @param response
     */
    @Override
    public void importInfoHadmin(HttpServletRequest request, HttpServletResponse response) {
        List<HadminExcel> newList = new ArrayList();
        PrintWriter out = null;

        boolean flag = true;
        JsonResponse jsonResponse = null;
        String workNumber = request.getParameter("workNumber");
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            for (MultipartFile uploadfile : multipartFiles.values()) {

                // 先上传至sys_file表,注意sheetName名要与excel保持一致
                UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), HadminExcel.class, "管理员导入模板");
                /** 获取excel表格:整理数据添加编号,匹配数据字典value值**/
                List<HadminExcel> details = uploadFileResponse.getListData();
                List<HadminExcel> list = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(details)) {
                    for (int i = 0; i < details.size(); i++) {
                        HadminExcel hadminExcel = details.get(i);

                        //   IUser user = SecurityUtils.getCurrentUser();
                        SimpleUser iuser = uumsSysUserinfoApi.findByUsernameFromCurrent(hadminExcel.getUserName(), Constants.APP_CODE);
                        if (iuser == null) {
                            String errorMsg = "第".concat(String.valueOf(i + 1)).concat("行,oa账号有误");
                            jsonResponse = JsonResponse.fail(-1, errorMsg);
                            flag = false;
                            break;

                        }
                        UsAdminManager usAdminManager = new UsAdminManager();
                        usAdminManager.setType("1");
                        if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongDepartmentName(iuser.getBelongCompanyName());
                            usAdminManager.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongCom(iuser.getBelongCompanyNameParent());
                            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyNameParent());
                            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongComCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongDept(iuser.getBelongCompanyName());
                            usAdminManager.setBelongDeptCode(iuser.getBelongCompanyCode());
                        }
                        if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyName());
                            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongDepartmentName(iuser.getBelongDepartmentName());
                            usAdminManager.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                            usAdminManager.setBelongCom(iuser.getBelongCompanyName());
                            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyName());
                            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCode());


                            usAdminManager.setBelongComCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongDept(iuser.getBelongDepartmentName());
                            usAdminManager.setBelongDeptCode(iuser.getBelongDepartmentCode());
                        }
                        usAdminManager.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                        usAdminManager.setBelongOrgCode(iuser.getBelongOrgCode());
                        usAdminManager.setBelongOrgName(iuser.getBelongOrgName());
                        usAdminManager.setTrueName(iuser.getTruename());
                        usAdminManager.setUserName(iuser.getUsername());
                        if (hadminExcel.getHadminType().equals("省公司党办管理员")) {
                            usAdminManager.setRoleUserId("djfupt_001");

                        } else if (hadminExcel.getHadminType().equals("分公司党办管理员")) {
                            usAdminManager.setRoleUserId("djfupt_003");
                        } else {
                            String errorMsg = "第".concat(String.valueOf(i + 1)).concat("行,管理员类型有误");
                            jsonResponse = JsonResponse.fail(-1, errorMsg);
                            flag = false;
                            break;
                        }

                        List<UsAdminManager> usAdminManagers = usAdminManagerRepository.findByUserNameAndEnabledAndroleUserId(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
                        if (CollectionUtil.isNotEmpty(usAdminManagers)) {
                            String errorMsg = "第".concat(String.valueOf(i + 1)).concat("行,管理员已存在");
                            jsonResponse = JsonResponse.fail(-1, errorMsg);
                            flag = false;
                            break;
                        }
                        this.insert(usAdminManager);
                        //添加 userRol表数据.
                        addRoleUser(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
                        insertQueryLevel(usAdminManager);//调整对应的五级权限


                    }

                    if (flag) {
                        jsonResponse = JsonResponse.success(details);
                        jsonResponse.setData(uploadFileResponse);
                    }

                } else {
                    String errorMsg = "表格数据有误";
                    jsonResponse = JsonResponse.fail(-1, errorMsg);
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1, "数据格式异常，请检查导入数据信息。");
        } finally {
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        }
    }


    /**
     * 导出模板
     *
     * @param request
     * @param response
     */

    public void exportPartyBuilding(HttpServletRequest request, HttpServletResponse response) {
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "导入模板.xls";
        List<PartyBuildingExcel> list = Lists.newArrayList();

        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<PartyBuildingExcel> exportUtil = new ExcelUtil<PartyBuildingExcel>(PartyBuildingExcel.class);
            exportUtil.exportExcel(list, "党建指导员管理导入模板", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), request, response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    /**
     * 导入党建指导员管理数据
     *
     * @param request
     * @param response
     */
    @Override
    public void importInfoPartyBuilding(HttpServletRequest request, HttpServletResponse response) {
        List<PartyBuildingExcel> newList = new ArrayList();
        PrintWriter out = null;

        boolean flag = true;
        JsonResponse jsonResponse = null;
        String workNumber = request.getParameter("workNumber");
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            for (MultipartFile uploadfile : multipartFiles.values()) {
                // 先上传至sys_file表,注意sheetName名要与excel保持一致
                UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), PartyBuildingExcel.class, "党建指导员管理导入模板");
                /** 获取excel表格:整理数据添加编号,匹配数据字典value值**/
                List<PartyBuildingExcel> details = uploadFileResponse.getListData();
                List<PartyBuildingExcel> list = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(details)) {
                    for (int i = 0; i < details.size(); i++) {
                        PartyBuildingExcel partyBuildingExcel = details.get(i);
                        SimpleUser iuser = uumsSysUserinfoApi.findByUsername(partyBuildingExcel.getUserName(), Constants.APP_CODE);
                        // IUser user = uumsSysUserinfoApi.findByUsername(partyBuildingExcel.getUserName(), Constants.APP_CODE);
                        if (iuser == null) {
                            String errorMsg = "第".concat(String.valueOf(i + 1)).concat("行,oa账号有误");
                            jsonResponse = JsonResponse.fail(-1, errorMsg);
                            flag = false;
                            break;
                        }
                        UsAdminManager usAdminManager = new UsAdminManager();
                        if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongDepartmentName(iuser.getBelongCompanyName());
                            usAdminManager.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongCom(iuser.getBelongCompanyNameParent());
                            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyNameParent());
                            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongComCode(iuser.getBelongCompanyCodeParent());
                            usAdminManager.setBelongDept(iuser.getBelongCompanyName());
                            usAdminManager.setBelongDeptCode(iuser.getBelongCompanyCode());
                        }
                        if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                            usAdminManager.setBelongCompanyName(iuser.getBelongCompanyName());
                            usAdminManager.setBelongCompanyCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongDepartmentName(iuser.getBelongDepartmentName());
                            usAdminManager.setParnetBelongComName(iuser.getBelongCompanyName());
                            usAdminManager.setParnetBelongComCode(iuser.getBelongCompanyCode());

                            usAdminManager.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                            usAdminManager.setBelongCom(iuser.getBelongCompanyName());
                            usAdminManager.setBelongComCode(iuser.getBelongCompanyCode());
                            usAdminManager.setBelongDept(iuser.getBelongDepartmentName());
                            usAdminManager.setBelongDeptCode(iuser.getBelongDepartmentCode());
                        }
                        usAdminManager.setTrueName(iuser.getTruename());
                        usAdminManager.setUserName(iuser.getUsername());
                        usAdminManager.setRoleUserId("djfupt_002");
                        usAdminManager.setType("0");
                        usAdminManager.setGridName(partyBuildingExcel.getGridName());
                        List<UsAdminManager> usAdminManagers = usAdminManagerRepository.findByUserNameAndEnabledAndroleUserId(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
                        if (CollectionUtil.isNotEmpty(usAdminManagers)) {
                            String errorMsg = "第".concat(String.valueOf(i + 1)).concat("行,管理员已存在");
                            jsonResponse = JsonResponse.fail(-1, errorMsg);
                            flag = false;
                            break;
                        }
                        this.insert(usAdminManager);
                        //添加 userRol表数据.
                        addRoleUser(usAdminManager.getUserName(), usAdminManager.getRoleUserId());
                        insertQueryLevel(usAdminManager);//调整对应的五级权限

                    }

                    if (flag) {
                        jsonResponse = JsonResponse.success(details);
                        jsonResponse.setData(uploadFileResponse);
                    }

                } else {
                    String errorMsg = "表格数据有误";
                    jsonResponse = JsonResponse.fail(-1, errorMsg);
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1, "数据格式异常，请检查导入数据信息。");
        } finally {
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        }
    }

    @Override
    public List<UsAdminManager> findByUserNameAndEnabled(String userName) {
        return usAdminManagerRepository.findByUserNameAndEnabled(userName);
    }

    @Override
    public List<String> findDjAdminByCode(String belongComCode, String type, String roleUserId) {
        return usAdminManagerRepository.findDjAdminByCode(belongComCode, type, roleUserId);
    }

    @Override
    public List<Map<String, Object>> fiveQuesinfo(String userName) {
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select t.*" +
                "  from US_QUERY_LEVEL_CONFIG t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null");
        Map<String, Object> param = Maps.newHashMap();
        if (StringUtils.isNotEmpty(userName)) {
            sql.append(" and t.username  like concat(concat('%', :userName), '%') ");
            param.put("userName", userName);
        }
        String sql2 = " order by t.created_time desc";
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 新增权限
     *
     * @param queryLevelConfig
     * @return
     */
    public String insertQueryLevel(UsAdminManager usAdminManager) {
        String tipsInfo = "";
        try {
            if (ObjectUtil.isNotEmpty(usAdminManager)) {
                String username = usAdminManager.getUserName();
                String[] usernames = username.split(",");
                List<QueryLevelConfig> queryLevelConfigList = Lists.newArrayList();
                for (int i = 0; i < usernames.length; i++) {
                    SimpleUser user = uumsSysUserinfoApi.findByUsername(usernames[i], DataPermissionConstants.APP_CODE);
                    if (ObjectUtil.isNotEmpty(user)) {
                        QueryLevelConfig queryLevelConfigAdd = new QueryLevelConfig();
                        queryLevelConfigAdd.setUsername(user.getUsername());
                        switch (user.getBelongCompanyTypeDictValue()) {
                            case DataPermissionConstants.PROVINCIAL_CODE:
                            case DataPermissionConstants.BRANCH_CODE:
                                queryLevelConfigAdd.setBelongCompanyName(user.getBelongCompanyName());
                                queryLevelConfigAdd.setBelongCompanyCode(user.getBelongCompanyCode());
                                queryLevelConfigAdd.setBelongDepartmentName(user.getBelongDepartmentName());
                                queryLevelConfigAdd.setBelongDepartmentCode(user.getBelongDepartmentCode());

                                break;
                            case DataPermissionConstants.COUNTY_CODE:
                                queryLevelConfigAdd.setBelongCompanyName(user.getBelongCompanyNameParent());
                                queryLevelConfigAdd.setBelongCompanyCode(user.getBelongCompanyCodeParent());
                                queryLevelConfigAdd.setBelongDepartmentName(user.getBelongCompanyName());
                                queryLevelConfigAdd.setBelongDepartmentCode(user.getBelongCompanyCode());
                                break;
                        }
                        queryLevelConfigAdd.setBelongCompanyTypeDictValue(user.getBelongCompanyTypeDictValue());
                        queryLevelConfigAdd.setBelongOrgCode(user.getBelongOrgCode());
                        queryLevelConfigAdd.setBelongOrgName(user.getBelongOrgName());
                        queryLevelConfigAdd.setTruename(user.getTruename());

                        if (usAdminManager.getRoleUserId().equals("djfupt_001") || usAdminManager.getRoleUserId().equals("djfupt_005")) {
                            queryLevelConfigAdd.setQueryLevelCode("FirstLevelQuery");
                            queryLevelConfigAdd.setQueryLevelName("一级查询权限");
                            queryLevelConfigAdd.setRemarks("查询全省数据");

                        }


                        if (usAdminManager.getRoleUserId().equals("djfupt_004")
                                || usAdminManager.getRoleUserId().equals("djfupt_007")) {
                            queryLevelConfigAdd.setQueryLevelCode("ThirdLevelQuery");
                            queryLevelConfigAdd.setQueryLevelName("查询归属公司和部门数据");
                            queryLevelConfigAdd.setRemarks("");

                        }
                        if (usAdminManager.getRoleUserId().equals("djfupt_003") || usAdminManager.getRoleUserId().equals("djfupt_006")) {
                            queryLevelConfigAdd.setQueryLevelCode("SecondLevelQuery");
                            queryLevelConfigAdd.setQueryLevelName("二级查询权限");
                            queryLevelConfigAdd.setRemarks("查询本公司数据");
                        }

                        if (usAdminManager.getRoleUserId().equals("djfupt_002")) {
                            queryLevelConfigAdd.setQueryLevelCode("FourLevelQuery");
                            queryLevelConfigAdd.setQueryLevelName("四级查询权限");
                            queryLevelConfigAdd.setRemarks("查询网格数据");

                        }


                        queryLevelConfigAdd.setQueryModuleCode("党建指导员支撑服务平台");
                        queryLevelConfigAdd.setQueryModuleName("系统管理");
                        queryLevelConfigList.add(queryLevelConfigAdd);
                    }
                }
                queryLevelConfigService.saveAll(queryLevelConfigList);
                tipsInfo = "添加成功";
            } else {
                tipsInfo = "添加失败";
            }
        } catch (Exception e) {
            tipsInfo = "账号异常,添加失败.";
            Exceptions.printException(e);
        } finally {
        }
        return tipsInfo;
    }


    /**
     * 修改五级权限信息
     */
    public String updateAdmin(UsAdminManager usAdminManager) {
        List<Map<String, Object>> list = usAdminManagerService.fiveQuesinfo(usAdminManager.getUserName());
        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> stringObjectMap : list) {
                String id = stringObjectMap.get("id").toString();
                QueryLevelConfig queryLevelConfig = queryLevelConfigService.findById(id);
                if (ObjectUtil.isNotEmpty(queryLevelConfig)) {
                    if (usAdminManager.getRoleUserId().equals("djfupt_001")) {
                        queryLevelConfig.setQueryLevelCode("FirstLevelQuery");
                        queryLevelConfig.setQueryLevelName("一级查询权限");
                        queryLevelConfig.setQueryModuleCode("党建指导员支撑服务平台");
                        queryLevelConfig.setQueryModuleName("系统管理");
                    }
                    if (usAdminManager.getRoleUserId().equals("djfupt_002")) {
                        queryLevelConfig.setQueryLevelCode("ThirdLevelQuery");
                        queryLevelConfig.setQueryLevelName("三级查询权限");
                        queryLevelConfig.setQueryModuleCode("党建指导员支撑服务平台");
                        queryLevelConfig.setQueryModuleName("系统管理");

                    }
                    if (usAdminManager.getRoleUserId().equals("djfupt_003")) {
                        queryLevelConfig.setQueryLevelCode("SecondLevelQuery");
                        queryLevelConfig.setQueryLevelName("二级查询权限");
                        queryLevelConfig.setQueryModuleCode("党建指导员支撑服务平台");
                        queryLevelConfig.setQueryModuleName("系统管理");

                    }
                    queryLevelConfigService.update(queryLevelConfig);
                }

            }
        }
        return "调整权限成功";
    }

    /**
     * 删除人员权限信息，恢复为默认权限
     */
    public String deleteAdmimn(UsAdminManager usAdminManager) {
        List<Map<String, Object>> list = usAdminManagerService.fiveQuesinfo(usAdminManager.getUserName());
        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> stringObjectMap : list) {
                String id = stringObjectMap.get("id").toString();
                QueryLevelConfig queryLevelConfig = queryLevelConfigService.findById(id);
                if (ObjectUtil.isNotEmpty(queryLevelConfig)) {
                    queryLevelConfigService.delete(queryLevelConfig);
                }

            }
        }
        return "调整权限成功";
    }


    /**
     * 党建大屏使用，计算网格数量和党建指导员数量
     *
     * @return
     */

    @Override
    public List<Map<String, Object>> findGridCount(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        String preach = "0";//应宣讲次数
        String dopreach = "0";//已完成宣讲次数
        StringBuffer sql = new StringBuffer("select t.belong_company_name," +
                "       count(t.grid_name) as gridCount," +
                "       count(t.user_name ) as adminCount" +
                "  from US_ADMIN_MANAGER t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and t.type='0'" +
                " ");
        String Sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')>=:startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')<=:endDate ");
            param.put("endDate", endDate);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(Sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 党建大屏使用，计算网格数量和党建指导员数量
     *
     * @return
     */

    @Override
    public List<Map<String, Object>> findAllGridCount(Map<String, Object> resultMap) {
        String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "city");//4度
        String startDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");//宣讲开始时间
        String endDate = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");//宣讲结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select " +
                "       count(t.grid_name) as gridCount," +
                "       count(t.user_name ) as adminCount" +
                "  from US_ADMIN_MANAGER t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and t.type='0'" +
                " ");
        Map<String, Object> param = Maps.newHashMap();
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(belongCompanyName)) {
            sql.append(" and t.belong_company_name  like concat(concat('%', :belongCompanyName), '%') ");
            param.put("belongCompanyName", belongCompanyName);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(startDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')>=:startDate");
            param.put("startDate", startDate);
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotEmpty(endDate)) {
            sql.append(" and to_char(t.policy_time,'yyyy-MM-dd')<=:endDate ");
            param.put("endDate", endDate);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    public int findByCompanyCodeAndEnabledAndRoleUserId(String belongCompanyName, String roleUserId) {
        return usAdminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserId(belongCompanyName, roleUserId);
    }

    public Boolean jiJian() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否是管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals("djfupt_jijian", simpleGroup.getSid()));
        if (isAdmin) {
            List<SysDictValue> dictValues = dictValueRepository.findDictValue("disciplineInspectionTime");
            if (dictValues.size() > 0) {
                SysDictValue dictValue = dictValues.get(0);
                Date startTime = sdf.parse(dictValue.getName());
                Date endTime = sdf.parse(dictValue.getValue());
                if (new Date().compareTo(startTime) >= 0 && endTime.compareTo(new Date()) >= 0) {
                    return true;
                } else {
                    return false;
                }

            } else {
                return false;
            }

        } else {
            return false;
        }

    }
}

