package com.simbest.boot.djfupt.policy.web;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.repository.WfWorkItemRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import com.simbest.boot.djfupt.policy.model.UsPolicyExport;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.service.IUsPantchDetailService;
import com.simbest.boot.djfupt.policy.service.IUsPolicyInfoService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 政策宣讲相关接口
 */
@Api(description = "政策宣讲相关接口")
@Slf4j
@RestController
@SuppressWarnings("ALL")
@RequestMapping(value = "/action/usPolicyInfo")
public class UsPolicyInfoController extends LogicController<UsPolicyInfo, String> {

    private IUsPolicyInfoService usPolicyInfoService;

    @Autowired
    public UsPolicyInfoController(IUsPolicyInfoService usPolicyInfoService) {
        super(usPolicyInfoService);
        this.usPolicyInfoService = usPolicyInfoService;
    }


    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private PaginationHelps paginationHelp;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IUsPantchDetailService usPantchDetailService;
    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;


    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    /**
     * 保存草稿
     *
     * @return
     */
    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/api/saveDraft", "/saveDraft/sso"})
    public JsonResponse saveDraft(@RequestBody UsPolicyInfo usPolicyInfo) {
        return usPolicyInfoService.saveDraft(usPolicyInfo);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/api/deleteDraft", "/deleteDraft/sso"})
    public JsonResponse deleteDraft(@RequestBody UsPolicyInfo usPolicyInfo) {
        return usPolicyInfoService.deleteDraft(usPolicyInfo);
    }


    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param notificationId  待阅id
     * @param bodyParam       提交参数
     * @param formId          表单id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "nextUserName", value = "审批人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "copyLocation", value = "抄送下一环节", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyMessage", value = "抄送意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyNextUserNames", value = "抄送人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
    })
    @PostMapping(value = {"/startSubmitProcess", "/api/startSubmitProcess", "/startSubmitProcess/sso", "/anonymous/startSubmitProcess"})
    public JsonResponse startSubmitProcess(@RequestParam String currentUserCode,
                                           @RequestParam String source,
                                           @RequestParam(required = false) String workItemId,
                                           @RequestParam(required = false) String outcome,
                                           @RequestParam(required = false) String location,
                                           @RequestParam(required = false) String copyLocation,
                                           @RequestParam(required = false) String notificationId,
                                           @RequestParam(required = false) String formId,
                                           @RequestBody Map<String, Object> bodyParam,
                                           HttpServletRequest request) throws Exception {
        return usPolicyInfoService.startSubmitProcess(source, currentUserCode, workItemId, outcome, location, copyLocation, bodyParam, formId, notificationId);
    }


    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "userCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getFormDetail", "/api/getFormDetail", "/getFormDetail/sso"})
    public JsonResponse getFormDetail(@RequestParam(required = false) Long processInstId,
                                      @RequestParam(required = false) String workFlag,
                                      @RequestParam(required = false) String source,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String pmInsId,
                                      @RequestParam(required = false) String currentUserCode) {
        return usPolicyInfoService.getFormDetail(processInstId, workFlag, source, currentUserCode, pmInsId, location);
    }


    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "userCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/findFormDetail", "/api/findFormDetail", "/findFormDetail/sso", "/anonymous/findFormDetail"})
    public JsonResponse findFormDetail(@RequestParam(required = false) Long processInstId,
                                       @RequestParam(required = false) String workFlag,
                                       @RequestParam(required = false) String source,
                                       @RequestParam(required = false) String workItemId,
                                       @RequestParam(required = false) String location,
                                       @RequestParam(required = false) String pmInsId,
                                       @RequestParam(required = false) String currentUserCode) {
        return usPolicyInfoService.findFormDetail(processInstId, workFlag, source, currentUserCode, pmInsId, location, workItemId);
    }


    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation(value = "查询决策", notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getDecisions", "/api/getDecisions", "/getDecisions/sso"})
    public JsonResponse getDecisions(
            @RequestParam(required = false) String processInstId,
            @RequestParam(required = false) String processDefName,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String currentUserCode,
            @RequestParam String processType) {
        return usPolicyInfoService.getDecisions(processInstId, processDefName, location, source, currentUserCode, processType);
    }

    /**
     * 获取到决策下组织人员
     *
     * @param processInstId 流程实例id
     * @param appDecision   决策对象
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getOrgAndUser", "/api/getOrgAndUser", "/getOrgAndUser/sso"})
    public JsonResponse getOrgAndUser(@RequestParam String source,
                                      @RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestBody SimpleAppDecision appDecision) {
        return usPolicyInfoService.getOrgAndUser(processInstId, source, currentUserCode, appDecision);
    }

    /**
     * 政策宣讲台账信息
     */
    @ApiOperation(value = "政策宣讲台账", notes = "政策宣讲台账")
    @PostMapping(value = {"/findPolicyLeder", "/api/findPolicyLeder", "/findPolicyLeder/sso","/anonymous/findPolicyLeder"})
    public JsonResponse findPolicyDeatilLeder(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                              @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                              @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                              @RequestParam(required = false) String properties, //排序规则（属性名称）
                                              @RequestBody(required = false) Map<String, Object> resultMap,
                                              @RequestParam(required = false) String currentUserCode,
                                              @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        IUser user = SecurityUtils.getCurrentUser();
        List<Map<String, Object>> resultList;
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin =  simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));

        if(user.getBelongCompanyTypeDictValue().equals("01")||isAdmin){
            resultList = usPolicyInfoService.findPolicyLeder(resultMap);
        }else {
            resultList = usPolicyInfoService.findPolicyLederOther(resultMap);
        }


        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");

    }


    /**
     * 政策宣讲台账信息--点击百分号后查出来的列表项
     */
    @ApiOperation(value = "政策宣讲台账--点击百分号后查出来的列表项", notes = "政策宣讲台账--点击百分号后查出来的列表项")
    @PostMapping(value = {"/findPolicyDeatilLeder", "/api/findPolicyDeatilLeder", "/findPolicyDeatilLeder/sso","/anonymous/findPolicyDeatilLeder"})
    public JsonResponse findPolicyInfo(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                       @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                       @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                       @RequestParam(required = false) String properties, //排序规则（属性名称）
                                       @RequestBody(required = false) Map<String, Object> resultMap,
                                       @RequestParam(required = false) String currentUserCode,
                                       @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        IUser user = SecurityUtils.getCurrentUser();
        List<Map<String, Object>> resultList;

            resultList = usPolicyInfoService.findPolicyDeatilLeder(resultMap);

        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);

            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");
    }


    /**
     * 政策宣讲台账信息--点击百分号后查出来的列表项-再点击hi详情
     */
    @ApiOperation(value = "政策宣讲台账--点击百分号后查出来的列表项", notes = "政策宣讲台账--点击百分号后查出来的列表项")
    @PostMapping(value = {"/findPolicyDeatir", "/api/findPolicyDeatir", "/findPolicyDeatir/sso"})
    public JsonResponse findPolicyInfo(@RequestParam String id) {
        UsPolicyInfo usPolicyInfo = usPolicyInfoService.findById(id);
        List<SysFile> feedBcakFile = new ArrayList<>();
        List<SysFile> policyOutlineile = new ArrayList<>();
        List<WfWorkItemModel> wfWorkItemByCode = wfWorkItemRepository.findWfWorkItemByCode(usPolicyInfo.getPmInsId());
        Long processInstId = null;
        if (wfWorkItemByCode.size() > 0) {
            processInstId = wfWorkItemByCode.get(0).getProcessInstId();
        }

//        if (null != processInstId) {
//            ActBusinessStatus actBusinessStatus = statusService.
//            if (actBusinessStatus != null) {
        //  usPolicyInfo = usPolicyInfoService.findUsPolicyInfoInfo(usPolicyInfo.getPmInsId(), usPolicyInfo.getCreator());
        if (usPolicyInfo != null) {
            List<UsPantchDetail> usPantchDetailInfo = usPantchDetailService.findUsPantchDetailInfo(usPolicyInfo.getPmInsId(), usPolicyInfo.getCreator());
            if (CollectionUtil.isNotEmpty(usPantchDetailInfo)) {
                usPolicyInfo.setUsPantchDetailList(usPantchDetailInfo);
            }
            if (StringUtils.isNotEmpty(usPolicyInfo.getFeedBcakFileIds())) {
                List<String> fileIdList = Arrays.asList(usPolicyInfo.getFeedBcakFileIds().split(","));
                if (CollectionUtil.isNotEmpty(fileIdList)) {
                    for (String fileIds : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileIds);
                        if (ObjectUtil.isNotEmpty(sysFile)) {
                            feedBcakFile.add(sysFile);
                        }

                    }
                }
                usPolicyInfo.setFeedBcakFile(feedBcakFile);
            }
            if (StringUtils.isNotEmpty(usPolicyInfo.getPolicyOutlineIds())) {
                List<String> fileIdList = Arrays.asList(usPolicyInfo.getPolicyOutlineIds().split(","));
                if (CollectionUtil.isNotEmpty(fileIdList)) {
                    for (String fileIds : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileIds);
                        if (ObjectUtil.isNotEmpty(sysFile)) {
                            policyOutlineile.add(sysFile);
                        }

                    }
                }
                usPolicyInfo.setPolicyOutline(policyOutlineile);
            }
        }
//            }
//        }
        return JsonResponse.success(usPolicyInfo);
    }


    @ApiOperation(value = "政策宣讲台账导出", notes = "政策宣讲台账导出")
    @PostMapping(value = {"/exportPolicyLeder", "/api/exportPolicyLeder", "/exportPolicyLeder/soo"})
    public void exportRisk(@RequestParam(required = false) String currentUserCode,
                           HttpServletRequest request,
                           HttpServletResponse response,
                           UsPolicyExport usPolicyExport
    ) throws Exception {
        usPolicyInfoService.exportPolicyLeder(currentUserCode, request, response, usPolicyExport);
    }


    @ApiOperation(value = "政策宣讲台账--点击百分号后的详情导出", notes = "政策宣讲台账--点击百分号后的详情导出")
    @PostMapping(value = {"/exportPolicyDeatilLeder", "/api/exportPolicyDeatilLeder", "/exportPolicyDeatilLeder/soo"})
    public void exportPolicyDeatilLeder(
            HttpServletRequest request,
            HttpServletResponse response,
            UsPolicyInfo policyInfo

    ) throws Exception {
        String currentUserCode = "";
        usPolicyInfoService.exportPolicyDeatilLeder(currentUserCode, request, response, policyInfo);
    }


    /**
     * 政策宣讲意见
     */
    @ApiOperation(value = "政策宣讲意见", notes = "政策宣讲意见")
    @PostMapping(value = {"/findAllMsg", "/api/findAllMsg", "/findAllMsg/sso"})
    public JsonResponse findAllMsg(@RequestParam String pmInsId,
                                   @RequestParam String currentUserCode) {

        UsPolicyInfo usPolicyInfo = usPolicyInfoService.findUsPolicyInfoInfo(currentUserCode, pmInsId);
        List<WfOptMsgModel> allMsg = new ArrayList<>();
        List<String> workList = new ArrayList<>();
        if (usPolicyInfo != null) {
            if (StringUtils.isNotEmpty(usPolicyInfo.getWorkItemId())) {
                workList = Arrays.asList(usPolicyInfo.getWorkItemId().split(","));
                allMsg = usPolicyInfoService.findAllMsg(pmInsId, workList);
            }
        }

        return JsonResponse.success(allMsg);
    }


    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    //党建智慧大屏  地图数据

    /**
     * 地图数据
     */
    @ApiOperation(value = "地图数据", notes = "地图数据")
    @PostMapping(value = {"/findMapDetail", "/api/findMapDetail", "/findMapDetail/sso"})
    public JsonResponse findMapDetail(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usPolicyInfoService.findMapDetail(resultMap);
        return JsonResponse.success(resultList);
    }


    /**
     * 政策宣讲数量
     */
    @ApiOperation(value = "党建指导员政策宣讲完成情况", notes = "党建指导员政策宣讲完成情况")
    @PostMapping(value = {"/policyCount", "/api/policyCount", "/policyCount/sso"})
    public JsonResponse policyCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usPolicyInfoService.findDpPolicyLeder(resultMap);
        return JsonResponse.success(resultList);
    }


    /**
     * 政策宣讲数量
     * 年度数据统计
     */
    @ApiOperation(value = "年度数据统计-政策宣讲数量", notes = "年度数据统计-政策宣讲数量")
    @PostMapping(value = {"/policyAllCount", "/api/policyAllCount", "/policyAllCount/sso"})
    public JsonResponse policyAllCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usPolicyInfoService.policyAllCount(resultMap);
        return JsonResponse.success(resultList);
    }


    @ApiOperation(value = "查询人员信息", notes = "查询人员信息")
    @PostMapping(value = {"/getUser", "/api/getUser", "/getUser/sso"})
    public JsonResponse getUser() {
        return usPolicyInfoService.getUser();
    }


    @ApiOperation(value = "获取第一议题数据", notes = "获取第一议题数据")
    @PostMapping(value = {"/selectAllPro", "/api/selectAllPro", "/selectAllPro/sso"})
    public JsonResponse selectAllPro(@RequestParam(required = false) String currentUserCode,
                                     @RequestParam(required = false) String source) {
        return usPolicyInfoService.selectAllPro(source, currentUserCode);
    }


    @ApiOperation(value = "支部近期", notes = "支部近期")
    @PostMapping(value = {"/selectAll", "/api/selectAll", "/selectAll/sso"})
    public JsonResponse selectAll(@RequestParam(required = false) String currentUserCode,
                                  @RequestParam(required = false) String source) {
        return usPolicyInfoService.selectAll(source, currentUserCode);
    }


    @ApiOperation(value = "第一议题", notes = "第一议题")
    @PostMapping(value = {"/selectCountYt", "/api/selectAll", "/selectAll/sso"})
    public JsonResponse selectCountYt(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String source) {
        return usPolicyInfoService.selectCountYt(source, currentUserCode);
    }


    @ApiOperation(value = "支部近期", notes = "支部近期")
    @PostMapping(value = {"/selectCountZb", "/api/selectCountZb", "/selectCountZb/sso"})
    public JsonResponse selectCountZb(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String source) {
        return usPolicyInfoService.selectCountZb(source, currentUserCode);
    }


    /**
     * 数智党建--最新通知--党建指导员
     * 根据当前登录人所在公司查询
     * 展示各公司台账信息，点击展示详情
     */
    @ApiOperation(value = "数智党建--最新通知--党建指导员", notes = "数智党建--最新通知--党建指导员")
    @PostMapping(value = {"/findLatest", "/api/findLatest", "/findLatest/sso"})
    public JsonResponse findLatest(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                   @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                   @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                   @RequestParam(required = false) String properties, //排序规则（属性名称）
                                   @RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usPolicyInfoService.findLatest(resultMap);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");
    }


    @ApiOperation(value = "合力攻坚-工作动态", notes = "合力攻坚-工作动态")
    @PostMapping(value = {"/workDynamicsList", "/api/workDynamicsList", "/workDynamicsList/sso"})
    public JsonResponse workDynamicsList(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String source) {
        return usPolicyInfoService.workDynamicsList(source, currentUserCode);
    }


    @ApiOperation(value = "合力攻坚-整体进度", notes = "合力攻坚-整体进度")
    @PostMapping(value = {"/overallProgress", "/api/overallProgress", "/overallProgress/sso"})
    public JsonResponse overallProgress(@RequestParam(required = false) String currentUserCode,
                                         @RequestParam(required = false) String source) {
        return usPolicyInfoService.overallProgress(source, currentUserCode);
    }

    @ApiOperation(value = "合力攻坚-各公司完成率", notes = "合力攻坚-各公司完成率")
    @PostMapping(value = {"/companyCompletionRate", "/api/companyCompletionRate", "/companyCompletionRate/sso"})
    public JsonResponse companyCompletionRate(@RequestParam(required = false) String currentUserCode,
                                         @RequestParam(required = false) String source) {
        return usPolicyInfoService.companyCompletionRate(source, currentUserCode);
    }

    @ApiOperation(value = "合力攻坚-公司本部完成率", notes = "合力攻坚-公司本部完成率")
    @PostMapping(value = {"/deptCompletionRate", "/api/deptCompletionRate", "/deptCompletionRate/sso"})
    public JsonResponse deptCompletionRate(@RequestParam(required = false) String currentUserCode,
                                         @RequestParam(required = false) String source) {
        return usPolicyInfoService.deptCompletionRate(source, currentUserCode);
    }


    @PostMapping(value = {"/updatePolicyInfo", "/api/updatePolicyInfo", "/updatePolicyInfo/sso"})
    public JsonResponse updatePolicyInfo(@RequestBody UsPolicyInfo policyInfo ){
        return usPolicyInfoService.updatePolicyInfo(policyInfo);
    }

    @PostMapping(value = {"/test1", "/api/test1", "/test1/sso"})
    public JsonResponse test1(){
        return usPolicyInfoService.test1();
    }


}
