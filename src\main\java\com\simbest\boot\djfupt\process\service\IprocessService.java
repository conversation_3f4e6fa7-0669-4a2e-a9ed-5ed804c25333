package com.simbest.boot.djfupt.process.service;

import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.security.IUser;

import java.util.Map;

/**
 * <strong>Title :IprocessService </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/21</strong><br>
 * <strong>Modify on : 2022/6/21</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

public interface IprocessService {

    /**
     * 启动流程
     * @param processDefId      流程名称
     * @param map               其他业务参数
     * @return
     */
    Long startProcess(String processDefId , Map<String , Object> map);

    /**
     * 完成工作项
     * @param workItemId
     * @param map
     * @return
     */
    Long completeWorkItem(Long workItemId , Map<String , Object> map);

    /**
     * 保存意见
     * @param workItemId
     * @param msg
     * @return
     */
    boolean saveMsg( Long workItemId, String msg);

}
