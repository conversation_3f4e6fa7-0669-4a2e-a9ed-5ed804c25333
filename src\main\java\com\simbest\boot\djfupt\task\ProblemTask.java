package com.simbest.boot.djfupt.task;


import com.google.common.collect.Maps;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.repository.UsProblemInfoRepository;
import com.simbest.boot.djfupt.problem.service.impl.UsProblemInfoServiceImpl;
import com.simbest.boot.djfupt.util.BpsConfig;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.DateUtil;
import com.simbest.boot.djfupt.wfquey.repository.DictValueRepository;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.repository.SysDictRepository;
import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.login.WorkFlowBpsLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class ProblemTask extends AbstractTaskSchedule {

    public ProblemTask(AppRuntimeMaster appRuntime, SysTaskExecutedLogRepository repository) {
        super(appRuntime, repository);
    }

    @Override
    @Scheduled(cron = "59 59 23 * * ?")
    public void checkAndExecute() {
        super.checkAndExecute(true);
    }

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private WorkFlowBpsLoginService workFlowBpsLoginService;


    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UsProblemInfoRepository usProblemInfoRepository;


    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private UsProblemInfoServiceImpl problemInfoService;

    @Autowired
    private DictValueRepository dictValueRepository;




    @Override
    public String execute() {
        JsonResponse jsonResponse = null;
        Date day = new Date();
        day= DateUtil.subDays(3);

        //查询所有未归档工单
        List<UsProblemInfo> problemInfos = usProblemInfoRepository.findAllByStateAndTimes();
        if (problemInfos.size() > 0) {
            for (UsProblemInfo problemInfo : problemInfos) {
                //模拟登陆起草人
                List<Map<String, Object>> wfWorkItemModels = usProblemInfoRepository.findAllByReceiptCode(problemInfo.getPmInsId());
                if(wfWorkItemModels.size()==0){
                    continue;
                }
                String userName = wfWorkItemModels.get(0).get("participant").toString();
                String workItemId = wfWorkItemModels.get(0).get("work_item_id").toString();
                String activityDefId = wfWorkItemModels.get(0).get("activity_def_id").toString();
                String username = rsaEncryptor.encrypt(userName);
                String createdTime=wfWorkItemModels.get(0).get("created_time").toString();
                Date createDate=null;
                DateFormat fmt =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    createDate  = fmt.parse(createdTime);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                //判断事是否实是在分公司党办管理员这一环节djfupt.braAdminFeedback  判断创建时间是否已经超过三天，超过三天需要手动推送到 分公司党委主任
                if (activityDefId.equals(Constants.BRAADM_INFEED_BACK) &&day.compareTo(createDate)>0 ) {
                    loginUtils.manualLogin(username, Constants.APP_CODE);
                    doFilter(userName);
                    //根据字典表获取下一步出人
                    IUser user = SecurityUtils.getCurrentUser();
                    List<SysDictValue> dictValues = dictValueRepository.findAllByNameAndType(Constants.DIRECTOR, user.getBelongCompanyName());
                    if (dictValues.size() > 0) {
                        jsonResponse =problemInfoService.saveSubmitTask(problemInfo,
                                workItemId,
                                Constants.OVERTIME_RETURN,
                                "超时三天，请分公司党委主任处理",
                                dictValues.get(0).getValue(), activityDefId, null,
                                null, null, null, "PC", userName);
                        log.debug(jsonResponse.getMessage());
                    }

                }
                //判断事是否实是在分公司党办管理员这一环节djfupt.braAdminFeedback  判断创建时间是否已经超过三天，超过三天需要手动推送到 分公司党委书记
                if (activityDefId.equals(Constants.OVERTIME_APPROVAL)&&day.compareTo(createDate)>0 ) {
                    loginUtils.manualLogin(username, Constants.APP_CODE);
                    doFilter(userName);
                    //根据字典表获取下一步出人
                    IUser user = SecurityUtils.getCurrentUser();
                    List<SysDictValue> dictValues = dictValueRepository.findAllByNameAndType(Constants.SECRETARY, user.getBelongCompanyName());
                    if (dictValues.size() > 0) {
                        jsonResponse = problemInfoService.saveSubmitTask(problemInfo,
                                workItemId,
                                Constants.OVERTIME_APPROVAL_PASS,
                                "超时三天，请分公司党委书记处理",
                                dictValues.get(0).getValue(), activityDefId, null,
                                null, null, null, "PC", userName);
                        log.debug(jsonResponse.getMessage());
                    }

                }

            }
        }
        return null;
    }


    public void doFilter(String userName) {
        boolean bpsTenant = Boolean.valueOf(bpsConfig.bpsTenant);
        String bpsTenantId = bpsConfig.bpsTenantId;
        Map<String, Object> map = Maps.newConcurrentMap();
        map.put("tenant", bpsTenant);
        map.put("userName", userName);
        map.put("tenantId", bpsTenantId);
        workFlowBpsLoginService.bpsLogin(map);
    }

}
