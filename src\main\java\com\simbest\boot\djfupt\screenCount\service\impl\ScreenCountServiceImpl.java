package com.simbest.boot.djfupt.screenCount.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.djfupt.admin.model.ScreenAdminManager;
import com.simbest.boot.djfupt.admin.service.IScreenAdminManagerService;
import com.simbest.boot.djfupt.screenCount.model.ScreenCount;
import com.simbest.boot.djfupt.screenCount.repository.ScreenCountRepository;
import com.simbest.boot.djfupt.screenCount.service.IScreenCountService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IgnoredErrorType;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.LinkedCaseInsensitiveMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class ScreenCountServiceImpl extends LogicService<ScreenCount, String> implements IScreenCountService {

    private final ScreenCountRepository repository;
    private final IScreenAdminManagerService screenAdminManagerService;

    private final UumsSysUserinfoApi uumsSysUserinfoApi;
    private final UumsSysOrgApi uumsSysOrgApi;
    private final CustomDynamicWhere dynamicWhere;


    public ScreenCountServiceImpl(ScreenCountRepository screenCountRepository, IScreenAdminManagerService screenAdminManagerService, UumsSysUserinfoApi uumsSysUserinfoApi, UumsSysOrgApi uumsSysOrgApi, CustomDynamicWhere dynamicWhere) {
        super(screenCountRepository);
        this.repository = screenCountRepository;
        this.screenAdminManagerService = screenAdminManagerService;
        this.uumsSysUserinfoApi = uumsSysUserinfoApi;
        this.uumsSysOrgApi = uumsSysOrgApi;
        this.dynamicWhere = dynamicWhere;
    }

    /**
     * 分页
     * <br/>params [o, pageable]
     *
     * @return {@link Page<ScreenCount>}
     * <AUTHOR>
     * @since 2023/5/19 9:46
     */
    @Override
    public Page<Map<String, Object>> findAllInfo(ScreenCount o, Pageable pageable) {
        List<Map<String, Object>> list = this.getList(o);
        return PageTool.getPage(list, pageable);
    }

    /**
     * 查询报表信息
     * <br/> params [o]
     *
     * @return {@link List< Map< String, Object>>}
     * <AUTHOR>
     * @since 2023/7/24 11:29
     */
    private List<Map<String, Object>> getList(ScreenCount o) {
        // 非超级管理员只能看自己单位
        String userName = SecurityUtils.getCurrentUserName();
        ScreenAdminManager one = screenAdminManagerService.findOne(Specifications.<ScreenAdminManager>and().eq("username", userName).build());
        Assert.notNull(one, "没有权限，请联系管理员处理！");
        if (!Objects.equals(one.getType(), "0")) {
            o.setCompanyCode(Objects.equals(one.getCompanyType(), "03") ? one.getCompanyParentCode() : one.getCompanyCode());
        }
        // 获取查询条件
        String sql = "";
        if (Objects.nonNull(o.getSdate())) {
            sql += " AND t.modified_time >= to_date('%s', 'yyyy-mm-dd hh24:mi:ss')";
            sql = String.format(sql, o.getSdate());
        }
        if (Objects.nonNull(o.getEdate())) {
            sql += " AND t.modified_time <= to_date('%s', 'yyyy-mm-dd hh24:mi:ss')";
            sql = String.format(sql, o.getEdate());
        }
        if (StringUtils.isNotBlank(o.getCompanyCode())) {
            sql += " AND (t.company_code = '%s' OR t.company_parent_code = '%s')";
            sql = String.format(sql, o.getCompanyCode(), o.getCompanyCode());
        }
        String sqla, sqlb;
        if (StringUtils.isNotBlank(o.getCompanyCode())) {
            sqla = "SELECT t.company_name dept_name,\n" +
                    "       SUM(t.djwh) djwh,\n" +
                    "       SUM(t.djzdy) djzdy,\n" +
                    "       SUM(t.dyyt) dyyt,\n" +
                    "       SUM(t.hlgj) hlgj,\n" +
                    "       SUM(t.jqxx) jqxx,\n" +
                    "       SUM(t.jyxc) jyxc,\n" +
                    "       SUM(t.szyg) szyg,\n" +
//                    "       SUM(t.xhdj) xhdj,\n" +
                    "       SUM(t.fdzl) fdzl,\n" +
                    "       SUM(t.yglt) yglt,\n" +
                    "       SUM(t.djzs) djzs,\n" +
                    "       SUM(t.yxf) yxf ,\n" +
                    "       SUM(t.dfglxt) dfglxt "+
                    "  FROM us_screen_count t\n" +
                    " WHERE t.enabled = 1\n" +
                    "   AND t.company_parent_code != '00000000000000000000'\n" +
                    sql +
                    " GROUP BY t.company_name";
            sqlb = " UNION ALL\n" +
                    "SELECT t.dept_name dept_name,\n" +
                    "       SUM(t.djwh) djwh,\n" +
                    "       SUM(t.djzdy) djzdy,\n" +
                    "       SUM(t.dyyt) dyyt,\n" +
                    "       SUM(t.hlgj) hlgj,\n" +
                    "       SUM(t.jqxx) jqxx,\n" +
                    "       SUM(t.jyxc) jyxc,\n" +
                    "       SUM(t.szyg) szyg,\n" +
//                    "       SUM(t.xhdj) xhdj,\n" +
                    "       SUM(t.fdzl) fdzl,\n" +
                    "       SUM(t.yglt) yglt,\n" +
                    "       SUM(t.djzs) djzs,\n" +
                    "       SUM(t.yxf) yxf ,\n" +
                    "       SUM(t.dfglxt) dfglxt "+
                    "  FROM us_screen_count t\n" +
                    " WHERE t.enabled = 1\n" +
                    "   AND t.company_parent_code = '00000000000000000000'\n" +
                    sql +
                    " GROUP BY t.dept_name";
        } else {
            sqla = "SELECT t.company_parent_name dept_name,\n" +
                    "       SUM(t.djwh) djwh,\n" +
                    "       SUM(t.djzdy) djzdy,\n" +
                    "       SUM(t.dyyt) dyyt,\n" +
                    "       SUM(t.hlgj) hlgj,\n" +
                    "       SUM(t.jqxx) jqxx,\n" +
                    "       SUM(t.jyxc) jyxc,\n" +
                    "       SUM(t.szyg) szyg,\n" +
//                    "       SUM(t.xhdj) xhdj,\n" +
                    "       SUM(t.fdzl) fdzl,\n" +
                    "       SUM(t.yglt) yglt,\n" +
                    "       SUM(t.djzs) djzs,\n" +
                    "       SUM(t.yxf) yxf ,\n" +
                    "       SUM(t.dfglxt) dfglxt "+
                    "  FROM us_screen_count t\n" +
                    " WHERE t.enabled = 1\n" +
                    "   AND t.company_parent_code != '00000000000000000000'\n" +
                    sql +
                    " GROUP BY t.company_parent_name";
            sqlb = " UNION ALL\n" +
                    "SELECT t.company_name dept_name,\n" +
                    "       SUM(t.djwh) djwh,\n" +
                    "       SUM(t.djzdy) djzdy,\n" +
                    "       SUM(t.dyyt) dyyt,\n" +
                    "       SUM(t.hlgj) hlgj,\n" +
                    "       SUM(t.jqxx) jqxx,\n" +
                    "       SUM(t.jyxc) jyxc,\n" +
                    "       SUM(t.szyg) szyg,\n" +
//                    "       SUM(t.xhdj) xhdj,\n" +
                    "       SUM(t.fdzl) fdzl,\n" +
                    "       SUM(t.yglt) yglt,\n" +
                    "       SUM(t.djzs) djzs,\n" +
                    "       SUM(t.yxf) yxf,\n" +
                    "       SUM(t.dfglxt) dfglxt "+
                    "  FROM us_screen_count t\n" +
                    " WHERE t.enabled = 1\n" +
                    "   AND t.company_parent_code = '00000000000000000000'\n" +
                    sql +
                    " GROUP BY t.company_name";
        }
        sql = "SELECT t.dept_name,\n" +
                "       SUM(t.djwh) djwh,\n" +
                "       SUM(t.djzdy) djzdy,\n" +
                "       SUM(t.dyyt) dyyt,\n" +
                "       SUM(t.hlgj) hlgj,\n" +
                "       SUM(t.jqxx) jqxx,\n" +
                "       SUM(t.jyxc) jyxc,\n" +
                "       SUM(t.szyg) szyg,\n" +
//                "       SUM(t.xhdj) xhdj,\n" +
                "       SUM(t.fdzl) fdzl,\n" +
                "       SUM(t.yglt) yglt,\n" +
                "       SUM(t.djzs) djzs,\n" +
                "       SUM(t.yxf) yxf,\n" +
                "       SUM(t.dfglxt) dfglxt "+
                "  FROM (" +
                sqla + sqlb +
                ") t\n" +
                " GROUP BY t.dept_name";
        List<Map<String, Object>> list = dynamicWhere.queryForList(sql);

        // 排序
        List<SimpleOrg> orgList;
        if (StringUtils.isNotBlank(o.getCompanyCode())) {
            orgList = uumsSysOrgApi.findSonByParentOrgId(Constants.APP_CODE, o.getCompanyCode());
        } else {
            orgList = uumsSysOrgApi.findPOrgAnd18CityOrg(Constants.APP_CODE);
            orgList.remove(0);
        }
        list.forEach(v -> {
            v.put("displayOrder", 100000);
            for (SimpleOrg org : orgList) {
                if (Objects.equals(v.get("DEPT_NAME"), org.getOrgName())) {
                    v.put("displayOrder", org.getDisplayOrder());
                    break;
                }
            }
        });
        // 超级管理员添加汇总信息
        if (Objects.equals(one.getType(), "0")) {
            Map<String, Object> map = new LinkedCaseInsensitiveMap<>();
            list.forEach(v -> {
                for (String s : v.keySet()) {
                    if (Objects.equals(s.toUpperCase(), "DEPT_NAME")) continue;
                    Long value = (Long) map.getOrDefault(s, 0L);
                    Long value1 = Long.valueOf(v.get(s).toString());
                    map.put(s, value + value1);
                }
            });
            map.put("DEPT_NAME", "河南移动");
            map.put("displayOrder", 0);
            list.add(map);
        }
        list.sort(Comparator.comparing(a -> ((Integer) a.get("displayOrder"))));
        return list;
    }

    /**
     * 同步方法记录点击量
     * <br/> params [name]
     *
     * <AUTHOR>
     * @since 2023/7/21 14:39
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void record(String name) {
        IUser iUser = SecurityUtils.getCurrentUser();
        String userName =iUser.getUsername();
        DistributedRedisLock.lock(userName , 10);
        try {
            String belongDepartmentCode = iUser.getBelongDepartmentCode();
            if (StrUtil.equals(iUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE)) {
                belongDepartmentCode = iUser.getBelongCompanyCode();
            }
            List<ScreenCount> screenCounts = super.findAllNoPage(Specifications.<ScreenCount>and().eq("username", userName).eq("deptCode", belongDepartmentCode).build());
            if (CollectionUtil.isNotEmpty(screenCounts)) {
                ScreenCount one = screenCounts.get(0);
                this.addCount(one, name);
                super.update(one);
            } else {
                super.insert(new ScreenCount().created(iUser));
            }
        } catch (Exception e) {
           Exceptions.printException(e);
        } finally {
            DistributedRedisLock.unlock(userName);
        }
    }

    /**
     * 增加对应模块点击量
     * <br/> params [o, name]
     *
     * <AUTHOR>
     * @since 2023/7/21 14:35
     */
    private void addCount(ScreenCount o, String name) throws IllegalAccessException, NoSuchFieldException {
        Field field = ScreenCount.class.getDeclaredField(name);
        field.setAccessible(Boolean.TRUE);
        Long i = (Long) field.get(o);
        field.set(o, ++i);
    }

    /**
     * <br/> params [name]
     *
     * <AUTHOR>
     * @since 2023/7/21 17:59
     */
    @Override
    public void export(HttpServletResponse response, HttpServletRequest request, ScreenCount o) {
        String random = this.getRandom();
        String s = random + "报表统计导出";
        List<Map<String, Object>> collect = this.getList(o);

        this.exportInfo(request, response, s, collect);
    }

    /**
     * 动态导出excel 处理导出和下载
     * <br/> params [request, response, name, list]
     *
     * <AUTHOR>
     * @since 2023/6/28 10:05
     */
    private void exportInfo(HttpServletRequest request, HttpServletResponse response, String name, List<Map<String, Object>> list) {
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        String fileName = name + ".xls";

        try {
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            this.exportInfo0(targetFile, list);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 动态导出excel 处理数据，及导出到excel
     * <br/> params [targetFile, list]
     *
     * <AUTHOR>
     * @since 2023/6/28 10:05
     */
    private void exportInfo0(File targetFile, List<Map<String, Object>> list) {
        // 写入excel
        try {
            String path = "model/大屏报表模板.xls";
            Map<String, Object> map = new HashMap<>();
            map.put("record", list);
            TemplateExportParams params = new TemplateExportParams(path);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            FileOutputStream fos = new FileOutputStream(targetFile);
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取一个随机字符串 日期+一个随机数  eg: 2023-05-20_10
     * <br/> params []
     *
     * @return {@link String}
     * <AUTHOR>
     * @since 2023/5/30 14:45
     */
    private String getRandom() {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now()) + "_" + RandomUtils.nextInt(0, 100);
    }

}
