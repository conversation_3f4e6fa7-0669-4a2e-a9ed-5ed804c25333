package com.simbest.boot.djfupt.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.model.UsGidSupervisionManager;
import com.simbest.boot.djfupt.admin.model.UsGidSupervisionManagerExcel;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.admin.repository.UsGidSupervisionRepository;
import com.simbest.boot.djfupt.admin.service.IUsAdminManagerService;
import com.simbest.boot.djfupt.admin.service.IUsGidSupervisionService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

@Slf4j
@Service(value = "usGidSupervisionService")
@SuppressWarnings("ALL")
public class UsGidSupervisionServiceImpl extends LogicService<UsGidSupervisionManager, String> implements IUsGidSupervisionService {

    private UsGidSupervisionRepository usGidSupervisionRepository;

    @Autowired
    UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private SysFileService fileService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    LoginUtils loginUtils;

    @Autowired
    AppConfig appConfig;

    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    IUsAdminManagerService usAdminManagerService;
    @Autowired
    PaginationHelp paginationHelp;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;

    @Autowired
    public UsGidSupervisionServiceImpl(UsGidSupervisionRepository usGidSupervisionRepository) {
        super(usGidSupervisionRepository);
        this.usGidSupervisionRepository = usGidSupervisionRepository;
    }

    @Override
    public JsonResponse insertInfo(UsGidSupervisionManager usGidSupervisionManager) {
        Assert.notNull(usGidSupervisionManager.getCityCode(), "地市编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getCity(), "地市不能为空！");
        Assert.notNull(usGidSupervisionManager.getCountyCode(), "区县编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getCounty(), "区县不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridCode(), "网格编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridTrueName(), "网格长姓名不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridName(), "网格长OA账号不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridPhone(), "网格长手机号不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionTrueName(), "监督员姓名不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionName(), "监督员OA账号不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionPhone(), "监督员手机号不能为空！");

        List<UsGidSupervisionManager> usGidSupervisionManagerList = this.usGidSupervisionRepository.findByCountyCodeOrGridNameAndActive(usGidSupervisionManager.getGridCode(), usGidSupervisionManager.getCountyCode());
        if (usGidSupervisionManagerList.size() > 0) {
            return JsonResponse.fail("当前网格信息已存在，请检查区县编码及网格编码信息");
        }
        List<UsGidSupervisionManager> usGidSupervisionManagers = this.usGidSupervisionRepository.findUsGidSupervisionManagersByGridUserNameAndEnabled( usGidSupervisionManager.getGridUserName(),Boolean.TRUE);
        if (usGidSupervisionManagers.size()>0){
            return JsonResponse.fail("当前网格员已存在其他网格，请检查后再次修改！");
        }
        //根据手机号查询用户信息
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("preferredMobile", usGidSupervisionManager.getGridPhone());
        paramMap.put("enabled", true);
        List<SimpleUser> list = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, paramMap);
        if (list.size() != 1) {
            return JsonResponse.fail("网格长手机号不存在！");
        }

        SimpleUser user = list.get(0);
        SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(user.getUsername(), Constants.APP_CODE);
        usGidSupervisionManager.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode());
        usGidSupervisionManager.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
        usGidSupervisionManager.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
        usGidSupervisionManager.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
        usGidSupervisionManager.setBelongOrgCode(simpleUser.getBelongOrgCode());
        usGidSupervisionManager.setBelongOrgName(simpleUser.getAuthOrgs().iterator().next().getDisplayName());
        usGidSupervisionManager.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
        this.insert(usGidSupervisionManager);
        return JsonResponse.success("操作成功！");
    }

    @Override
    public JsonResponse updateInfo(UsGidSupervisionManager usGidSupervisionManager) {
        Assert.notNull(usGidSupervisionManager.getId(), "id不能为空！");
        Assert.notNull(usGidSupervisionManager.getCityCode(), "地市编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getCity(), "地市不能为空！");
        Assert.notNull(usGidSupervisionManager.getCountyCode(), "区县编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getCounty(), "区县不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridCode(), "网格编码不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridTrueName(), "网格长姓名不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridName(), "网格长OA账号不能为空！");
        Assert.notNull(usGidSupervisionManager.getGridPhone(), "网格长手机号不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionTrueName(), "监督员姓名不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionName(), "监督员OA账号不能为空！");
        Assert.notNull(usGidSupervisionManager.getSupervisionPhone(), "监督员手机号不能为空！");

        List<UsGidSupervisionManager> usGidSupervisionManagerList = this.usGidSupervisionRepository.findByGridCodeOrGridNameAndIdAndActive(usGidSupervisionManager.getId(), usGidSupervisionManager.getGridCode(), usGidSupervisionManager.getGridName());
        if (usGidSupervisionManagerList.size() > 0) {
            return JsonResponse.fail("当前网格已存在，请检查区县编码及网格编码信息");
        }
        List<UsGidSupervisionManager> usGidSupervisionManagers = this.usGidSupervisionRepository.findByGridUserNameAndIdAndActive(usGidSupervisionManager.getId(), usGidSupervisionManager.getGridUserName());
        if (usGidSupervisionManagers.size()>0){
            return JsonResponse.fail("当前网格员已存在其他网格，请检查后再次修改！");
        }
        //根据手机号查询用户信息
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("preferredMobile", usGidSupervisionManager.getGridPhone());
        paramMap.put("enabled", true);
        List<SimpleUser> list = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, paramMap);
        if (list.size() != 1) {
            return JsonResponse.fail("网格长手机号不存在！");
        }

        SimpleUser user = list.get(0);
        SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(user.getUsername(), Constants.APP_CODE);
        usGidSupervisionManager.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode());
        usGidSupervisionManager.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
        usGidSupervisionManager.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
        usGidSupervisionManager.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
        usGidSupervisionManager.setBelongOrgCode(simpleUser.getBelongOrgCode());
//        usGidSupervisionManager.setBelongOrgName(simpleUser.getAuthOrgs().iterator().next().getDisplayName());
        usGidSupervisionManager.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
        this.update(usGidSupervisionManager);
        return JsonResponse.success("操作成功！");
    }

    @Override
    public JsonResponse delInfo(String id) {
        try {
            this.deleteById(id);
            return JsonResponse.success("操作成功！");
        } catch (Exception e) {
            log.error("--->>> usGidSupervisionService.delInfo 删除失败：id{},e:{}", id, e.toString());
            return JsonResponse.fail("删除失败！");
        }
    }

    @Override
    public Page<UsGidSupervisionManager> findListByPage(int page, int size, String direction, String properties, String belongCompanyName, String gridName, String gridTrueName, String supervisionTrueName) {
        Pageable pageable = paginationHelp.getPageable(page, size, direction, properties);
        Specification<UsGidSupervisionManager> specification = Specifications.<UsGidSupervisionManager>and()
                .eq(StringUtils.isNotEmpty(belongCompanyName), "belongCompanyName", belongCompanyName)
                .like(StringUtils.isNotEmpty(gridName), "gridName", "%" + gridName + "%")
                .eq(StringUtils.isNotEmpty(gridTrueName), "gridTrueName", gridTrueName)
                .eq(StringUtils.isNotEmpty(supervisionTrueName), "supervisionTrueName", supervisionTrueName)
                .build();

        Page<UsGidSupervisionManager> all = this.findAll(specification, pageable);
        return all;
    }

    @Override
    public void importGidSupervision(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        boolean flag = true;
        try {
            out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            StringBuffer stringBuffer = new StringBuffer();

            for (MultipartFile multipartFile : multipartFiles.values()) {
                UploadFileResponse uploadFileResponse = fileService.importExcel(multipartFile, null, null, null, UsGidSupervisionManagerExcel.class, "Sheet1");
                List<UsGidSupervisionManagerExcel> listData = uploadFileResponse.getListData();
                List<UsGidSupervisionManager> usGidSupervisionManagers = dataCheckAndFormate(jsonResponse, listData);
                    this.saveAll(usGidSupervisionManagers);
            }
            log.info("--->>>未插入的数据{}", stringBuffer.toString());

        } catch (Exception e) {
            jsonResponse=jsonResponse.fail(e.getMessage());
        }
        String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
        out.println(result);
        out.close();
    }

    @Override
    public UsGidSupervisionManager getByGridCode(String gridCode) {
        return usGidSupervisionRepository.findUsGidSupervisionManagerByGridCodeAndEnabled(gridCode, Boolean.TRUE);
    }

    @Override
    public void dolwnLoad(HttpServletRequest request, HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/网格及监督员导入模板.xls");
        try {
            FileTool.downloadIn(in, "网格及监督员导入模板.xls", response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public JsonResponse getCountyByCityName(String currentUserCode, String source, String city) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("firstParam", city);
        String json = JacksonUtils.obj2json(paramMap);
        JsonResponse jsonResponse = HttpClient.textBody(appConfig.getUumsAddress() + "/action/uumsSelect/selectBySqlSelectType/sso" + "?sqlId=sjzxcr_005&selectType=replacetype" + "&loginuser=" + encryptor.encrypt("hadmin") + "&appcode=" + Constants.APP_CODE + "&appCode=" + Constants.APP_CODE)
                .json(json)
                .asBean(JsonResponse.class);
        List<Map<String, String>> list = (List<Map<String, String>>) jsonResponse.getData();
        String orgCode = list.get(0).get("ORG_CODE");
        List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findSonByParentOrgId(Constants.APP_CODE, orgCode);
        List<SimpleOrg> resultList = new ArrayList<>(simpleOrgList);
        Iterator<SimpleOrg> iterator = resultList.iterator();
        while (iterator.hasNext()){
            SimpleOrg simpleOrg = iterator.next();
            if ("05".equals(simpleOrg.getStyleDictValue())){//去除掉直属部门
                iterator.remove();
            }
        }
        return JsonResponse.success(resultList);
    }

    @Override
    public JsonResponse getGridAndSupervision(String currentUserCode, String source) {
        if (Constants.MOBILE.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }

        List<UsAdminManager> usAdminManagerList = adminManagerRepository.findByUserNameAndEnabledAndRoleUserId(SecurityUtils.getCurrentUser().getUsername(),Boolean.TRUE,Constants.FJFUPT_BRO);
        if (usAdminManagerList.size()==1){
            UsAdminManager usAdminManager = usAdminManagerList.get(0);
            String gridName = usAdminManager.getGridName();

            List<UsGidSupervisionManager> usGidSupervisionManagerList = this.usGidSupervisionRepository.findUsGidSupervisionManagersByGridNameAndEnabled(gridName, Boolean.TRUE);
            if (usGidSupervisionManagerList.size()==1){
                return JsonResponse.success(usGidSupervisionManagerList.get(0));
            }
        }
        return JsonResponse.success(null);
    }

    /**
     * 导入数据重复性校验
     *
     * @param jsonResponse
     * @param listData
     */
    private List<UsGidSupervisionManager> dataCheckAndFormate(JsonResponse jsonResponse, List<UsGidSupervisionManagerExcel> listData) throws Exception{
        List<UsGidSupervisionManager> resultList = new ArrayList<>();
            for (UsGidSupervisionManagerExcel usGidSupervisionManagerExcel : listData) {
                Assert.notNull(usGidSupervisionManagerExcel.getCityCode(), "地市编码不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getCity(), "地市不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getCountyCode(), "区县编码不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getCounty(), "区县不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getGridCode(), "网格编码不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getGridTrueName(), "网格长姓名不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getGridName(), "网格长OA账号不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getGridPhone(), "网格长手机号不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getSupervisionTrueName(), "监督员姓名不能为空！");
                Assert.notNull(usGidSupervisionManagerExcel.getSupervisionPhone(), "监督员手机号不能为空！");
                List<UsGidSupervisionManager> usGidSupervisionManagerList = this.usGidSupervisionRepository.findByCountyCodeOrGridNameAndActive(usGidSupervisionManagerExcel.getGridCode(), usGidSupervisionManagerExcel.getCountyCode());
                if (usGidSupervisionManagerList.size() > 0) {
                    throw  new Exception(usGidSupervisionManagerExcel.getGridName() + "当前信息已存在，请检查区县编码及网格信息");
                }

                //根据手机号查询用户信息
                Map<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("preferredMobile", usGidSupervisionManagerExcel.getGridPhone());
                paramMap.put("enabled", true);
                List<SimpleUser> list = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, paramMap);
                if (list.size() == 0) {
                    throw  new Exception(usGidSupervisionManagerExcel.getGridName() + "网格长手机号不存在！");
                }
                SimpleUser user = list.get(0);
                SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(user.getUsername(), Constants.APP_CODE);
                UsGidSupervisionManager usGidSupervisionManager = new UsGidSupervisionManager();
                BeanUtils.copyProperties(usGidSupervisionManager,usGidSupervisionManagerExcel );

                List<UsGidSupervisionManager> usGidSupervisionManagers = this.usGidSupervisionRepository.findUsGidSupervisionManagersByGridUserNameAndEnabled(simpleUser.getUsername(),Boolean.TRUE);
                if (usGidSupervisionManagers.size()>0){
                    throw  new Exception("当前网格员已存在其他网格，请检查后再次修改！");
                }
                usGidSupervisionManager.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode());
                usGidSupervisionManager.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
                usGidSupervisionManager.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
                usGidSupervisionManager.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
                    usGidSupervisionManager.setBelongOrgCode(simpleUser.getBelongOrgCode());
                usGidSupervisionManager.setBelongOrgName(simpleUser.getAuthOrgs().iterator().next().getDisplayName());
                usGidSupervisionManager.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
                resultList.add(usGidSupervisionManager);
            }

        return resultList;
    }
}

