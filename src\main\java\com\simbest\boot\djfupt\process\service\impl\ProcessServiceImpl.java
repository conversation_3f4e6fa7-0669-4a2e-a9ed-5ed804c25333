package com.simbest.boot.djfupt.process.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WFProcessInstModel;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfOptMsgModelService;
import com.simbest.boot.bps.process.listener.service.IWfProcessInstModelService;
import com.simbest.boot.bps.process.listener.service.IWfWorkItemModelService;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.service.IUsPolicyInfoService;
import com.simbest.boot.djfupt.process.model.SysProcessInfo;
import com.simbest.boot.djfupt.process.service.ISysProcessInfoService;
import com.simbest.boot.djfupt.process.service.IprocessService;
import com.simbest.boot.djfupt.todo.TodoBusOperatorService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.CreatNumUtil;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <strong>Title :ProcessServiceImpl </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/21</strong><br>
 * <strong>Modify on : 2022/6/21</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

@Service
@Slf4j
public class ProcessServiceImpl implements IprocessService {

    @Autowired
    private IWfProcessInstModelService processInstModelService;

    @Autowired
    private IUsPolicyInfoService usPolicyInfoService;

    @Autowired
    private IWfWorkItemModelService workItemModelService;

    @Autowired
    private IWfOptMsgModelService optMsgService;

    @Autowired
    private IActBusinessStatusService businessStatusService;

    @Autowired
    private ISysProcessInfoService processInfoService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private ICommonService commonService;


    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private CreatNumUtil creatNumUtil;

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;


    /**
     * 启动流程
     *
     * @param processDefId 流程名称
     * @param map          其他业务参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long startProcess(String processDefId, Map<String, Object> map) {
        IUser iUser = SecurityUtils.getCurrentUser();
        //流程实例表
        WFProcessInstModel processInstInfo = saveProcessInstInfo(processDefId, map);
        ActBusinessStatus businessStatus = createActBusinessStatus(processInstInfo);
        if (ObjectUtil.isNotEmpty(processInstInfo) && ObjectUtil.isNotEmpty(businessStatus)) {
            WfWorkItemModel workItem = createWorkItem(processInstInfo.getProcessInstId(), processDefId, null, iUser.getUsername(), null, map, businessStatus);
            if (ObjectUtil.isNotEmpty(workItem)) {
                updateBusinessStatus(businessStatus);
                return workItem.getWorkItemId();
            } else {
                throw new RuntimeException("流程启动失败");
            }
        } else {
            throw new RuntimeException("流程启动失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long completeWorkItem(Long workItemId, Map<String, Object> map) {
        long newWorkItemId = 0L;
        List<String> idList = new ArrayList<>();

        String outcome = MapUtil.getStr(map, "outcome");
        String ids = MapUtil.getStr(map, "ids");
        if (StringUtils.isNotEmpty(ids)) {
            idList = Arrays.asList(ids.split(","));
        }
        String nextUsernames = MapUtil.getStr(map, "inputUserIds");
        Assert.notNull(outcome, "连线规则不能为空！");
        Assert.notNull(workItemId, "环节流程编码不能为空！");
        IUser iUser = SecurityUtils.getCurrentUser();
        WfWorkItemModel workItem = workItemModelService.getByWorkItemID(workItemId);

        if (workItem.getCurrentState() == 12) {
            throw new RuntimeException("当前执行状态为已完成，不能在此执行。");
        }
        if (!StrUtil.equals(iUser.getUsername(), workItem.getParticipant())) {
            throw new RuntimeException("当前登录人与办理人员信息不一致！");
        }
        ActBusinessStatus actBusinessStatus = businessStatusService.getByProcessInst(workItem.getProcessInstId());
        Assert.notNull(actBusinessStatus, "查询流程状态信息失败！");
        //结束当前流程
        finishWorkItem(workItem, actBusinessStatus);
        actBusinessStatus.setPreviousAssistantName(workItem.getAgentUserName());
        actBusinessStatus.setPreviousAssistant(workItem.getAssistant());
        actBusinessStatus.setPreviousAssistantDate(LocalDateTime.now());
        newWorkItemId = 1L;
        if (StrUtil.isNotEmpty(nextUsernames)) {
            String[] strings = nextUsernames.split(",");
            for (String nextUsername : strings) {
                WfWorkItemModel newWorkItem = createWorkItem(workItem.getProcessInstId(), workItem.getProcessDefName(), workItem.getActivityDefId(), nextUsername, outcome, map, actBusinessStatus);
                newWorkItemId = newWorkItem.getWorkItemId();
                if (CollectionUtil.isNotEmpty(idList)) {
                    for (String id : idList) {
                        UsPolicyInfo usPolicyInfo = usPolicyInfoService.findById(id);
                        if (ObjectUtil.isNotEmpty(usPolicyInfo)) {
                            if (nextUsername.equals(usPolicyInfo.getCreator())) {
                                usPolicyInfo.setWorkItemId(usPolicyInfo.getWorkItemId() + "," + workItemId);
                                usPolicyInfoService.update(usPolicyInfo);
                            }
                        }
                    }
                }
            }
        }
        List<WfWorkItemModel> wfWorkItemModels = workItemModelService.getByProcessInstId(workItem.getProcessInstId());
        if (CollectionUtil.isEmpty(wfWorkItemModels)) {
            actBusinessStatus.setCurrentState(7);
        }
        updateBusinessStatus(actBusinessStatus);
        return newWorkItemId;
    }

    /**
     * 生成流程实力表
     *
     * @param processDefId
     * @param map
     * @return
     */
    private WFProcessInstModel saveProcessInstInfo(String processDefId, Map<String, Object> map) {
        String title = MapUtil.getStr(map, "title");
        String receiptId = MapUtil.getStr(map, "receiptId");
        String code = MapUtil.getStr(map, "code");
        Assert.notNull(processDefId, "流程名称不能为空！");
        Assert.notNull(title, "流程标题不能为空！");
        Assert.notNull(code, "业务编码不能为空！");
        Assert.notNull(receiptId, "业务主键不能为空！");
        WFProcessInstModel processInstModel = new WFProcessInstModel();
        processInstModel.setCatalogUUID("-1");
        processInstModel.setCreateTime(LocalDateTime.now());
        processInstModel.setCurrentState(2);
        processInstModel.setEndTime(LocalDateTime.now());
        processInstModel.setFinalTime(LocalDateTime.now());
        processInstModel.setIsTimeOut("N");
        processInstModel.setOwner("hamin");
        processInstModel.setParentActId(0L);
        processInstModel.setParentProcId(0L);
        processInstModel.setProcessDefId(1L);
        processInstModel.setProcessDefName(processDefId);
        processInstModel.setProcessInstDesc(title);
        processInstModel.setProcessInstId(creatNumUtil.autoIncrementNum("1"));
        processInstModel.setProcessInstName(title);
        processInstModel.setReceiptCode(code);
        processInstModel.setReceiptId(receiptId);
        processInstModel.setReceiptTitle(title);
        processInstModel.setRemindTime(LocalDateTime.now());
        processInstModel.setStartTime(LocalDateTime.now());
        processInstModel.setTimeOutNum(0);
        processInstModelService.insert(processInstModel);
        return processInstModel;
    }


    /**
     * 生成环节信息表
     *
     * @param processInstId
     * @param map
     * @return
     */
    private WfWorkItemModel createWorkItem(Long processInstId, String processDefId, String fromLocation, String nextUsername, String outcome, Map<String, Object> map, ActBusinessStatus businessStatus) {
        WfWorkItemModel itemModel = new WfWorkItemModel();
        SysProcessInfo processInfo = null;
        //流程启动
        String location = fromLocation;
        if (StrUtil.isEmpty(fromLocation)) {
            location = Constants.ACTIVITY_START;
        }
        String title = MapUtil.getStr(map, "title");

//        if(StringUtils.isEmpty(outcome)){
//            outcome = MapUtil.getStr(map, "outcome");
//        }

        String code = MapUtil.getStr(map, "code");
        String receiptId = MapUtil.getStr(map, "receiptId");
        Assert.notNull(processDefId, "流程名称不能为空！");
        Assert.notNull(processInstId, "流程实例编码不能为空！");
        Assert.notNull(nextUsername, "下一步流转人不能为空！");
        Assert.notNull(title, "流程标题不能为空！");
        Assert.notNull(code, "业务编码不能为空！");
        Assert.notNull(receiptId, "业务主键id不能为空！");
        processInfo = processInfoService.findByOutcome(processDefId, location, outcome);
       Assert.notNull(processInfo, "获取环节信息失败！");
        if (StrUtil.isNotEmpty(fromLocation)) {
            itemModel.setActivityDefId(processInfo.getTargetActivityId());
            itemModel.setActivityInstName(processInfo.getTargetActivityName());
            itemModel.setWorkItemName(processInfo.getTargetActivityName());
            itemModel.setWorkItemDesc(processInfo.getTargetActivityName());
        } else {
            itemModel.setActivityDefId(processInfo.getFromActivityId());
            itemModel.setActivityInstName(processInfo.getFromActivityName());
            itemModel.setWorkItemName(processInfo.getFromActivityName());
            itemModel.setWorkItemDesc(processInfo.getFromActivityName());
        }
        itemModel.setAllowAgent("0");
        itemModel.setBizState(0);
        itemModel.setCatalogUUID("-1");
        itemModel.setCatalogName("无");
        itemModel.setCreateTime(LocalDateTime.now());
        itemModel.setCurrentState(10);
        itemModel.setFinalTime(LocalDateTime.now());
        itemModel.setIsTimeOut("N");
        SimpleUser user = uumsSysUserinfoApi.findByUsername(nextUsername, Constants.APP_CODE);
        Assert.notNull(user, "流转人员信息不能为空！");
        itemModel.setParticipant(user.getUsername());
        itemModel.setPartiName(user.getTruename());
        itemModel.setPriority(60);
        itemModel.setProcessDefId(1L);
        itemModel.setProcessDefName(processDefId);
        itemModel.setProcessInstId(processInstId);
        itemModel.setProcessInstName(title);
        itemModel.setReceiptId(receiptId);
        itemModel.setReceiptCode(code);
        itemModel.setReceiptTitle(title);
        itemModel.setRootProcInstId(processInstId);
        itemModel.setStartTime(LocalDateTime.now());
        itemModel.setRemindTime(LocalDateTime.now());
        itemModel.setUrlType("N");
        itemModel.setWorkItemId(creatNumUtil.autoIncrementNum("2"));
        itemModel.setActivityInstId(itemModel.getWorkItemId());
        workItemModelService.insert(itemModel);
        //推送统一待办
        try {
            Boolean isTodoFlag = false;  //待办开关 false代表不推送  true推送
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, SecurityUtils.getCurrentUserName());
            if (simpleApp != null) {
                isTodoFlag = simpleApp.getTodoOpen();
            }
            if (isTodoFlag) {
                businessStatus.setWorkItemId(itemModel.getWorkItemId());
                businessStatus.setActivityDefId(itemModel.getActivityDefId());
                todoBusOperatorService.openTodo(businessStatus, nextUsername);
                //commonService.sendCarShortMessage(businessStatus,SecurityUtils.getCurrentUserName());
            }
        } catch (Exception e) {

        }
        return itemModel;
    }

    /**
     * 结束当前环节
     *
     * @return
     */
    private void finishWorkItem(WfWorkItemModel workItemModel, ActBusinessStatus actBusinessStatus) {
        IUser iUser = SecurityUtils.getCurrentUser();
        workItemModel.setEndTime(LocalDateTime.now());
        workItemModel.setFinalTime(null);
        workItemModel.setCurrentState(12);
        workItemModel.setAssistant(iUser.getUsername());
        workItemModelService.updateWithNull(workItemModel);
        //核销统一待办
        try {
            actBusinessStatus.setWorkItemId(workItemModel.getWorkItemId());
            actBusinessStatus.setActivityDefId(workItemModel.getActivityDefId());
            todoBusOperatorService.closeTodo(actBusinessStatus, iUser.getUsername());
        } catch (Exception e) {
            Exceptions.printException(e);
        }

    }

    /**
     * 创建电子流程状态信息
     *
     * @param processInstModel
     * @return
     */
    private ActBusinessStatus createActBusinessStatus(WFProcessInstModel processInstModel) {
        IUser iUser = SecurityUtils.getCurrentUser();
        ActBusinessStatus actBusinessStatus = new ActBusinessStatus();
        actBusinessStatus.setBusinessKey(processInstModel.getReceiptId());
        actBusinessStatus.setCreateOrgCode(iUser.getBelongOrgCode());
        SimpleOrg simpleOrg = uumsSysOrgApi.findListByOrgCode(Constants.APP_CODE, iUser.getBelongOrgCode());
        if (ObjectUtil.isNotEmpty(simpleOrg)) {
            actBusinessStatus.setCreateOrgName(simpleOrg.getDisplayName());
        }
        actBusinessStatus.setCreateTime(LocalDateTime.now());
        actBusinessStatus.setCreateUserCode(iUser.getUsername());
        actBusinessStatus.setCreateUserName(iUser.getTruename());
        actBusinessStatus.setCurrentState(2);
        actBusinessStatus.setEnabled(Boolean.TRUE);
        actBusinessStatus.setEndTime(LocalDateTime.now());
        actBusinessStatus.setParentProcId(0L);
        actBusinessStatus.setProcessDefId(processInstModel.getProcessDefId());
        actBusinessStatus.setProcessDefName(processInstModel.getProcessDefName());
        actBusinessStatus.setProcessInstId(processInstModel.getProcessInstId());
        actBusinessStatus.setReceiptCode(processInstModel.getReceiptCode());
        actBusinessStatus.setReceiptTitle(processInstModel.getReceiptTitle());
        actBusinessStatus.setRemoved(Boolean.FALSE);
        actBusinessStatus.setStartTime(LocalDateTime.now());
        actBusinessStatus.setUpdateTime(LocalDateTime.now());
        actBusinessStatus = businessStatusService.insert(actBusinessStatus);
        return actBusinessStatus;
    }

    /**
     * 更新流程状态信息
     *
     * @return
     */
    private ActBusinessStatus updateBusinessStatus(ActBusinessStatus businessStatus) {
        Assert.notNull(businessStatus, "流程状态信息不能为空！");
        IUser iUser = SecurityUtils.getCurrentUser();
        businessStatus.setUpdateTime(LocalDateTime.now());
        businessStatus.setPreviousAssistant(iUser.getUsername());
        businessStatus.setPreviousAssistantDate(LocalDateTime.now());
        businessStatus.setPreviousAssistantName(iUser.getTruename());
        businessStatus.setPreviousAssistantOrgCode(iUser.getBelongOrgCode());
        SimpleOrg simpleOrg = uumsSysOrgApi.findListByOrgCode(Constants.APP_CODE, iUser.getBelongOrgCode());
        if (ObjectUtil.isNotEmpty(simpleOrg)) {
            businessStatus.setPreviousAssistantOrgName(simpleOrg.getDisplayName());
        }
        Integer currentStatus = getCurrentStatus(businessStatus.getProcessInstId());
        if (currentStatus == 12) {
            businessStatus.setCurrentState(7);
            WFProcessInstModel processInstModel = processInstModelService.getByProcessInst(businessStatus.getProcessInstId());
            if (ObjectUtil.isNotEmpty(processInstModel)) {
                processInstModel.setCurrentState(currentStatus);
                processInstModel.setEndTime(LocalDateTime.now());
                processInstModelService.update(processInstModel);
            }
        }
        businessStatusService.update(businessStatus);
        return businessStatus;
    }

    private Integer getCurrentStatus(Long processInstId) {
        Integer currentStatus = 10;
        Specification<WfWorkItemModel> build = Specifications.<WfWorkItemModel>and()
                .eq("enabled", Boolean.TRUE)
                .eq("processInstId", processInstId)
                .eq("currentState", currentStatus)
                .eq("endTime", null)
                .build();
        List<WfWorkItemModel> wfWorkItemModels = workItemModelService.findAllNoPage(build);
        if (CollectionUtil.isEmpty(wfWorkItemModels)) {
            currentStatus = 12;
        }
        return currentStatus;
    }


    /**
     * 保存意见
     *
     * @param workItemId
     * @param msg
     */
    public boolean saveMsg(Long workItemId, String msg) {
        boolean ret = Boolean.TRUE;
        try {
            IUser iUser = SecurityUtils.getCurrentUser();
            WfWorkItemModel workItemModel = workItemModelService.getByWorkItemID(workItemId);
            WfOptMsgModel optMsgModel = new WfOptMsgModel();
            optMsgModel.setActivityinstid(workItemModel.getActivityInstId());
            optMsgModel.setContent(msg);
            optMsgModel.setCorrelationid(workItemId);
            optMsgModel.setCorrelationtype("WORKITEM");
            optMsgModel.setProcessinstid(workItemModel.getProcessInstId());
            optMsgModel.setOperationtype("APPROVAL");
            optMsgModel.setMessageid(workItemId);
            optMsgModel.setProcessdefid(workItemModel.getProcessDefId());
            optMsgModel.setProducer(iUser.getUsername());
            optMsgModel.setProducerName(iUser.getTruename());
            optMsgModel.setReceiptCode(workItemModel.getReceiptCode());
            optMsgModel.setReceiptId(workItemModel.getReceiptId());
            optMsgModel.setReceiptTitle(workItemModel.getReceiptTitle());
            optMsgModel.setRootProcInstId(workItemModel.getProcessInstId());
            optMsgModel.setWorkitemid(workItemId);
            optMsgService.insert(optMsgModel);
        } catch (Exception e) {
            Exceptions.printException(e);
            ret = Boolean.FALSE;
        }
        return ret;
    }

    private List<WfWorkItemModel> findByProcessInstId(Long processInstId) {
        Specification<WfWorkItemModel> build = Specifications.<WfWorkItemModel>and()
                .eq("enabled", Boolean.TRUE)
                .eq("processInstId", processInstId)
                .eq("currentStatus", "10")
                .eq("endTime", null)
                .build();
        List<WfWorkItemModel> all = workItemModelService.findAllNoPage(build);
        return all;
    }
}
