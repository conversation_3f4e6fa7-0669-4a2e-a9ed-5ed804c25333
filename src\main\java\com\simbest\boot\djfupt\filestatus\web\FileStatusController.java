package com.simbest.boot.djfupt.filestatus.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.filestatus.model.FileStatus;
import com.simbest.boot.djfupt.filestatus.service.IFileStatusService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

/**
 * <strong>Title : FileStatusController</strong><br>
 * <strong>Description : 文件状态控制层</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Api(description = "文件状态相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/fileStatus")
public class FileStatusController extends LogicController<FileStatus, String> {

    private final IFileStatusService service;
    private final LoginUtils loginUtils;

    @Autowired
    public FileStatusController(IFileStatusService service, LoginUtils loginUtils) {
        super(service);
        this.service = service;
        this.loginUtils = loginUtils;
    }

    /**
     * 新增文件状态
     */
    @ApiOperation(value = "新增文件状态", notes = "根据文件ID新增文件状态，默认状态为true")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, dataType = "String")
    @PostMapping({"/add", "/add/sso", "/add/api"})
    public JsonResponse add(@RequestParam String fileId) {
        try {
            FileStatus fileStatus = new FileStatus();
            fileStatus.setFileId(fileId);
            fileStatus.setStatus("true");
            FileStatus savedFileStatus = service.updateFileStatus(fileStatus);
            return JsonResponse.success(savedFileStatus);
        } catch (Exception e) {
            log.error("新增文件状态失败，文件ID: {}, 错误信息: {}", fileId, e.getMessage(), e);
            return JsonResponse.fail("新增文件状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件状态
     */
    @ApiOperation(value = "更新文件状态", notes = "更新文件状态")
    @PostMapping({"/updateFileStatus", "/updateFileStatus/sso", "/updateFileStatus/api"})
    public JsonResponse updateFileStatus(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestBody FileStatus fileStatus) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.updateFileStatus(fileStatus));
    }

    /**
     * 根据ID查询文件状态
     */
    @ApiOperation(value = "根据ID查询文件状态", notes = "根据ID查询文件状态")
    @ApiImplicitParam(name = "id", value = "文件状态ID", required = true, dataType = "String")
    @PostMapping({"/findById", "/findById/sso", "/findById/api"})
    public JsonResponse findById(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                 @RequestParam(required = false) String currentUserCode,
                                 @RequestParam String id) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.findById(id));
    }

    /**
     * 根据文件ID查询文件状态
     */
    @ApiOperation(value = "根据文件ID查询文件状态", notes = "根据文件ID查询文件状态")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, dataType = "String")
    @PostMapping({"/findByFileId", "/findByFileId/sso", "/findByFileId/api"})
    public JsonResponse findByFileId(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                     @RequestParam(required = false) String currentUserCode,
                                     @RequestParam String fileId) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.findByFileId(fileId));
    }

    /**
     * 根据文件ID查询文件状态
     */
    @ApiOperation(value = "根据文件ID查询文件状态", notes = "根据文件ID查询文件状态")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, dataType = "String")
    @PostMapping({"/anonymous/findByFileId", "/findByFileId/sso", "/findByFileId/api"})
    public JsonResponse newfindByFileId(@RequestParam String fileId) {
        return JsonResponse.success(service.findByFileId(fileId));
    }


    /**
     * 分页查询文件状态
     */
    @ApiOperation(value = "分页查询文件状态", notes = "分页查询文件状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页条数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "direction", value = "排序方向", required = false, dataType = "String"),
            @ApiImplicitParam(name = "properties", value = "排序字段", required = false, dataType = "String")
    })
    @PostMapping({"/findAll", "/findAll/sso", "/findAll/api"})
    public JsonResponse findAll(@RequestParam(required = false, defaultValue = "1") int page,
                                @RequestParam(required = false, defaultValue = "10") int size,
                                @RequestParam(required = false, defaultValue = "desc") String direction,
                                @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                @RequestParam(required = false, defaultValue = Constants.PC) String source,
                                @RequestParam(required = false) String currentUserCode,
                                @RequestBody(required = false) FileStatus fileStatus) {
        mobileLogin(source, currentUserCode);
        if (fileStatus == null) {
            fileStatus = new FileStatus();
        }
        Pageable pageable = service.getPageable(page, size, direction, properties);
        return JsonResponse.success(service.findAllInfo(fileStatus, pageable));
    }

    /**
     * 切换文件状态
     */
    @ApiOperation(value = "切换文件状态", notes = "根据文件ID切换文件状态，true变false，false变true")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, dataType = "String")
    @PostMapping({"/toggleStatus", "/toggleStatus/sso", "/toggleStatus/api"})
    public JsonResponse toggleStatus(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                     @RequestParam(required = false) String currentUserCode,
                                     @RequestParam String fileId) {
        mobileLogin(source, currentUserCode);
        try {
            FileStatus updatedFileStatus = service.toggleFileStatus(fileId);
            return JsonResponse.success(updatedFileStatus);
        } catch (Exception e) {
            log.error("切换文件状态失败，文件ID: {}, 错误信息: {}", fileId, e.getMessage(), e);
            return JsonResponse.fail("切换文件状态失败: " + e.getMessage());
        }
    }

    /**
     * 手机端模拟登录
     *
     * @param source          手机端还是PC端
     * @param currentUserCode 当前用户code
     */
    private void mobileLogin(String source, String currentUserCode) {
        if (!Constants.MOBILE.equals(source)) return;
        Assert.state(StringUtils.isNotBlank(currentUserCode), "未登录状态,OA账户不能为空!");
        loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
    }
}
