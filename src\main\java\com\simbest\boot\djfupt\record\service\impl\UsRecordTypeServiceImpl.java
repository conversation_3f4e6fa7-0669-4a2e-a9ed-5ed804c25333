package com.simbest.boot.djfupt.record.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.record.model.UsRecordType;
import com.simbest.boot.djfupt.record.repository.UsRecordTypeRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordTypeService;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service
@SuppressWarnings("ALL")
public class UsRecordTypeServiceImpl extends LogicService<UsRecordType, String> implements IUsRecordTypeService {

    private UsRecordTypeRepository UsRecordTypeRepository;

    @Autowired
    public UsRecordTypeServiceImpl(com.simbest.boot.djfupt.record.repository.UsRecordTypeRepository repository) {
        super(repository);
        this.UsRecordTypeRepository = repository;
    }

    @Autowired
    private PaginationHelps paginationHelp;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Override
    public List<Map<String, Object>> findInfoNoPage(Map<String, String> resultMap) {
        List<Map<String, Object>> list = new ArrayList<>();

        String name = resultMap.get("name");
        Map<String, Object> map = CollectionUtil.newHashMap();
        StringBuffer sql = new StringBuffer(" select t.* from Us_Record_Type t where t.enabled=1 ");
        if (StringUtils.isNotEmpty(name)) {
            sql.append(" and   t.name  like concat( concat('%',:riskAreas),'%')  ");
            map.put("name", name);
        }
        sql.append(" order by t.created_time desc");
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    @Override
    public JsonResponse findInfoPage(int page, int rows, Map<String, String> resultMap) {
        List<Map<String, Object>> resultList = this.findInfoNoPage(resultMap);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null);
    }


}
