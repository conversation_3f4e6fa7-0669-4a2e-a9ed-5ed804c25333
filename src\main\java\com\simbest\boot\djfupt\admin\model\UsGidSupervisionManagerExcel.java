package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "网格监督员EXCEL导出类")
public class UsGidSupervisionManagerExcel   {

    @ExcelVOAttribute(name = "地市编码", column = "A")
    protected String cityCode;

    @ExcelVOAttribute(name = "地市", column = "B")
    protected String city;

    @ExcelVOAttribute(name = "县区编码", column = "C")
    protected String countyCode;

    @ExcelVOAttribute(name = "县区", column = "D")
    protected String county;

    @ExcelVOAttribute(name = "网格编码", column = "E")
    protected String gridCode;

    @ExcelVOAttribute(name = "网格", column = "F")
    protected String gridName;

    @ExcelVOAttribute(name = "网格长姓名", column = "G")
    protected String gridTrueName;

    @ExcelVOAttribute(name = "网格长手机号", column = "H")
    protected String gridPhone;

    @ExcelVOAttribute(name = "监督员姓名", column = "I")
    protected String supervisionTrueName;

    @ExcelVOAttribute(name = "监督员手机号", column = "J")
    protected String supervisionPhone;

}
