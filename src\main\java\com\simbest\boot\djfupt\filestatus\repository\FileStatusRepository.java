package com.simbest.boot.djfupt.filestatus.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.filestatus.model.FileStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <strong>Title : FileStatusRepository</strong><br>
 * <strong>Description : 文件状态持久层</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Repository
public interface FileStatusRepository extends LogicRepository<FileStatus, String> {
    
    /**
     * 根据文件ID查询文件状态
     * @param fileId 文件ID
     * @return 文件状态
     */
    FileStatus findByFileId(String fileId);
}
