<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>访客分析</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <!-- 覆盖css样式表 -->
    <link href="../../css/easyui.css?v=svn.revision" rel="stylesheet" />
    <link href="../../css/index.css?v=svn.revision" rel="stylesheet" />
    
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.aduq.js?v=svn.revision" th:src="@{/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>

    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <!-- <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script> -->
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        getCurrent()
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/screenCount/findAllInfo", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "columns": [
                        [ //列
                            { title: "单位组织", field: "DEPT_NAME", width: 50, tooltip: true, align: "center" },
                            { title: "党建数智员工", field: "SZYG", width: 50, tooltip: true, align: "center" },
                            { title: "党委第一议题", field: "DYYT", width: 50, tooltip: true, align: "center" },
                            { title: "支部近期学习重点", field: "JQXX", width: 50, tooltip: true, align: "center" },
                            { title: "党建指导员", field: "DJZDY", width: 50, tooltip: true, align: "center" },
                            // { title: "星火党建", field: "XHDJ", width: 50, tooltip: true, align: "center" },
                            { title: "党建资源共享", field: "FDZL", width: 50, tooltip: true, align: "center" },
                            { title: "党建文化模块", field: "DJWH", width: 50, tooltip: true, align: "center" },
                            { title: "党员突击队", field: "YXF", width: 50, tooltip: true, align: "center" },
                            { title: "统战建言献策", field: "JYXC", width: 50, tooltip: true, align: "center" },
                            { title: "领题破题 合力攻坚", field: "HLGJ", width: 50, tooltip: true, align: "center" },
                            { title: "畅所豫言", field: "YGLT", width: 50, tooltip: true, align: "center" },
                            { title: "党建工作助手", field: "DJZS", width: 50, tooltip: true, align: "center" },
                            { title: "党费管理", field: "DFGLXT", width: 50, tooltip: true, align: "center" },
                        ]
                    ]
                }
            };

            if(gps.companyCode){
                pageparam.listtable.queryParams = {
                    belongDepartmentCode: gps.companyCode
                }
            }
            loadGrid(pageparam);
            $('#belongDepartmentCode').val(gps.companyCode)
            
            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir +"action/screenCount/export");
                $("#taskTableQueryForm").attr("method", "post");
                // $("#taskTableQueryForm").attr("contentType", "application/json; charset=utf-8");
                $("#taskTableQueryForm").submit();
            });
            $('#taskTableQueryForm').resize()
        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
            $('#taskTableQueryForm').resize()
        };

    </script>


    <style>
    </style>
</head>

<body class="body_page">
    <!--searchform-->
    <!-- <form id="taskTableQueryForm" > -->
        <form id="taskTableQueryForm" >
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">时间</td>
                <td width="400">
                    <input id="sdate" name="sdate" type="text" class="easyui-datebox"
                        style="width:35%;height:32px;" validType="startDateCheck['edate','sdate']"
                        data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="edate" name="edate" type="text" class="easyui-datebox" style="width:35%;height:32px;"
                        validType="endDateCheck['sdate','edate']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>

                <td width="600">
                    <div class=" w200">
                        <a class="btn fr a_success exporttable "> <font>下载报表</font> </a>
                        <a class="btn fr mr10 searchtable"> <font>查询</font> </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>