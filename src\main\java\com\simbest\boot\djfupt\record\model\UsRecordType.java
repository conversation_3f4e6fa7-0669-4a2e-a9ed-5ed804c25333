package com.simbest.boot.djfupt.record.model;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_Record_Type")
@ApiModel(value = "思政纪实类型")
public class UsRecordType extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 500)
    @ApiModelProperty(value = "名称")
    private String name;
}
