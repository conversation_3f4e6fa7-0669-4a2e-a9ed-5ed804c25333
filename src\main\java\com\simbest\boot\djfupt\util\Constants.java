package com.simbest.boot.djfupt.util;

import java.io.Serializable;

/**
 * <strong>Title :项目全局常量类，该类不允许new 继承等操作 </strong><br>
 * <strong>Description : 项目全局常量类，该类不允许new 继承等操作</strong><br>
 *
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public final class Constants implements Serializable {

    /**
     * 超级管理员
     */
    public static final String ROLE_SCREEN_SS_ADMIN = "ROLE_SCREEN_SS_ADMIN";
    /**
     * 省公司管理员
     */
    public static final String ROLE_SCREEN_S_ADMIN = "ROLE_SCREEN_S_ADMIN";
    /**
     * 分公司管理员
     */
    public static final String ROLE_SCREEN_F_ADMIN = "ROLE_SCREEN_F_ADMIN";

    /**
     * 党建指导员-找茬活动管理员
     */
    public static final String GROUP_ZC_ADMIN = "G6000";
    /**
     * 党建指导员-找茬活动分公司管理员
     */
    public static final String GROUP_ZC_F_ADMIN = "G6001";

    /**
     * 私有构造方法，不允许new操作
     */
    private Constants() {

    }

    /**
     * 项目code/
     */
    public static final String APP_CODE = "djfupt";

    /**
     * 项目中文名称
     */
    public static final String APP_NAME = "党建指导员支撑服务平台";


    /**
     * 统一待办中系统id
     */
    public static final String APP_SYS_ID = "1239";

    /**
     * 统一待办中待办标识
     */
    public static final String APP_WORK_TYPE = "1";

    /**
     * 操作信息通知
     */
    public static final String MESSAGE_SUCCESS = "操作成功";    //操作成功
    public static final String MESSAGE_FAIL = "操作失败";       //操作失败

    /**
     * 网络请求来源
     */
    public static final String MOBILE = "MOBILE";   // 手机端
    public static final String PC = "PC";           // PC端


    /**
     * PC端
     */
    public static final String TYPE_DJFUPT = "DJFUPT";

    /**
     * 起草环节
     */
    public static final String ACTIVITY_START = "djfupt.start";
    public static final String STR_NULL = "null";
    public static final String OVERTIME_RETURN = "djfupt.overtimeReturn"; //超时三天，请分公司党委书记处理
    public static final String OVERTIME_APPROVAL_PASS = "djfupt.overtimeApprovalPass"; //超时三天，请分公司党委主任处理
    public static final String BRAADM_INFEED_BACK = "djfupt.braAdminFeedback"; //分公司党办管理员审核
    public static final String OVERTIME_APPROVAL = "djfupt.overtimeApproval"; //分公司党委主任

    public static final String SECRETARY = "secretary"; //书记

    public static final String DIRECTOR = "director"; //主任




    /**
     * 公司级别
     */
    public static final String COMPANY_TYPE_PROVINCE = "01";    //01 省公司
    public static final String COMPANY_TYPE_BRANCH = "02";  //02 地市分公司
    public static final String COMPANY_TYPE_COUNTY = "03";  //03 县区分公司


    public static final String CODE_PROVINCE = "4772338661636601428";  //省公司代码

    public static final String PM_INS_TYPE_A = "A";    //积极经验推广做法
    public static final String PM_INS_TYPE_B = "B";    //问题上报

    public static final String OVER_TIME_RETURN = "djfupt.overtimeReturn";    //问题上报


    public static final String PM_INS_TYPE_C = "C";    //政策宣讲
    public static final String PM_INS_TYPE_D = "D";    //找茬流程
    public static final String PM_INS_TYPE_E = "E";    //找茬流程-三期

    /**
     * 应用流程ID，后续应用按照格式定义
     * procurement_manage 采购
     * document_manage 公文
     * account_platform 报账
     * protocol_manage 合同
     */

    public static final String CASE_REPORT = "com.djfupt.flow.caseReport"; // 积极经验推广做法
    public static final String PROBLEM_REPOR = "com.djfupt.flow.problemRepor"; //问题上报


    public static final String POLICY_PROPAGANDA = "com.djglpt.flow.policyPropaganda"; //政策宣讲

    public static final String FIND_FAULTS = "com.djfupt.flow.findfaults"; //找茬流程
    public static final String FIND_FAULTS_THREE = "com.djfupt.flow.findfaults_three"; //找茬流程-三期

    /**
     * 环节id
     */
    public static final String ACTIVITY_DEF_ID = "djfupt.start";   //起草


    /**
     * 起草人确认决策规则
     */
    public static final String DEC_CONFIRM = "djfupt.partyCommitteeSecretary_pass";  //ndwyz.partyCommitteeSecretary_pass

    /**
     * 通用状态整数（0 | 1 | 2）
     */
    public static final Integer COMMON_STATUS_UNENABLED = 0; //否
    public static final Integer COMMON_STATUS_ENABLED = 1; //是
    public static final Integer COMMON_STATUS_DEFAULT = 2; //请选择

    //统一待办接口处理标识返回标识
    public static final String SUCCESS_FLAG = "Y";
    public static final String FAILED_FLAG = "N";


    /**
     * 一级查询(查全省)
     * 二级查询(查所在公司)
     * 三级查询(查所在部门)
     * 四级查询(查所在科室)
     * 五级查询(查本人)
     */
    public static final String QUERY_LEVEL_FIRST = "FirstLevelQuery";
    public static final String QUERY_LEVEL_SECOND = "SecondLevelQuery";
    public static final String QUERY_LEVEL_THIRD = "ThirdLevelQuery";
    public static final String QUERY_LEVEL_FOUR = "FourLevelQuery";
    public static final String QUERY_LEVEL_DEFAULT = "DefaultQuery";

    public static final String SOURCE_PC = "PC";

    //省公司管理员
    public static final String PROVINCE_ADMIN_GROUP = "G0610";


    //部门管理员
    public static final String PROVINCE_department_GROUP = "G0611";

    public static final String FJFUPT_PRO = "djfupt_001";//省公司党办管理员

    public static final String FJFUPT_BRO = "djfupt_002";//党键指导员

    public static final String FJFUPT_City = "djfupt_003";//分公司党办管理员

    public static final String FJFUPT_COUNTY = "djfupt_004";//县公司党办管理员

    public static final String PROVINCE_OBSERVER = "djfupt_005";//省公司观察员

    public static final String CITY_OBSERVER = "djfupt_006";//分公司观察员

    public static final String COUNTY_OBSERVER = "djfupt_007";//县公司观察员

    public static final String DEP_OBSERVER = "djfupt_010";//分公司部门管理员

    public static final String SSO = "/sso";

    public static final String USER_ROLE = "/action/user/role/";
    /**
     * 省公司
     */
    public static final String PROVINCIAL_CODE = "01";//01 省公司

    /**
     * 分公司
     */
    public static final String BRANCH_CODE = "02";//02 地市分公司

    /**
     * 县公司
     */
    public static final String COUNTY_CODE = "03";//03 县/市区分公司

    public static final String PRD = "prd";
    /**
     * 连接符
     */
    public static final String XTGJ_CONNECT_SIGN = ":";

    /**
     * 分隔符
     */
    public static final String XTGJ_SEPARATE_SIGN = "!@";

    /**
     * 字典-联合跟装活动-意见类别
     */
    public static final String XTGJ_LHGZHD01_TYPE = "XTGJ_LHGZHD01_TYPE";

    /**
     * 字典-联合跟装活动
     */
    public static final String XTGJ_LHGZHD_TYPE = "XTGJ_LHGZHD_TYPE";

    /**
     * 字典-装维入格调研
     */
    public static final String XTGJ_ZWRGDY_TYPE = "XTGJ_ZWRGDY_TYPE";


    public static final String GROUP_FAULTS002 = "faults002";//找茬三期-省公司管理员-电脑版

    public static final String GROUP_FAULTS003 = "faults003";//找茬三期-省公司管理员-手机版

    public static final String USER_ADMIN = "hadmin";
    public static final String APP_ID = "uni_2304_ha_djfupt";



}
