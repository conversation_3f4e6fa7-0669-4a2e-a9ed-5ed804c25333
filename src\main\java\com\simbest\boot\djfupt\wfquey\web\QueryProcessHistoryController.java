package com.simbest.boot.djfupt.wfquey.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.wfquey.service.IQueryProcessHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用途：查询审批流程
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Api(description = "查询审批流程相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/queryProcessHistory")
public class QueryProcessHistoryController {

    @Autowired
    private IQueryProcessHistoryService queryProcessHistoryService;

    /**
     * 查询流程流转过得工作项
     *
     * @param processInstId   流程实例ID
     * @param currentUserCode 当前人
     * @return
     */
    @ApiOperation(value = "流程跟踪", notes = "流程跟踪")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "工作项ID", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前人", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getWorkItems", "/api/getWorkItems", "/getWorkItems/sso"})
    public JsonResponse getWorkItems(@RequestParam Long processInstId,
                                     @RequestParam(required = false) String currentUserCode) {
        return JsonResponse.success(queryProcessHistoryService.getWorkItems(processInstId, currentUserCode));
    }
}
