package com.simbest.boot.djfupt.policy.web;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import com.simbest.boot.djfupt.policy.service.IUsPantchDetailService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 政策宣讲清单相关接口
 */
@Api(description = "政策宣讲清单相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/usPantchDetail")
public class UsPantchDetailController extends LogicController<UsPantchDetail, String> {

    private IUsPantchDetailService usPantchDetailService;

    @Autowired
    public UsPantchDetailController(IUsPantchDetailService usPantchDetailService) {
        super(usPantchDetailService);
        this.usPantchDetailService = usPantchDetailService;
    }


    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private IFileExtendService fileExtendService;
    @Autowired
    private PaginationHelps paginationHelp;

    /**
     * 新增政策宣讲事项
     */
    @ApiOperation(value = "新增政策宣讲事项", notes = "新增政策宣讲事项")
    @PostMapping(value = {"/insertUsPantchDetail", "/api/insertUsPantchDetail", "/insertUsPantchDetail/sso"})
    public JsonResponse insertUsPantchDetail(@RequestBody UsPantchDetail usPantchDetail) {
        if (usPantchDetail.getSource().equals("MOBILE")) {
            loginUtils.manualLogin(usPantchDetail.getCurrentUserCode(), Constants.APP_CODE);
        }
        IUser user = SecurityUtils.getCurrentUser();
        UsPantchDetail newUsPantchDetail = null;
        if (ObjectUtil.isNotEmpty(usPantchDetail)) {
            if (ObjectUtil.isNotEmpty(user)) {
                if (user.getBelongCompanyTypeDictValue().equals("01")) {
                    usPantchDetail.setWebId(usPantchDetail.getWebId());//省公司起草时或者编辑新增时，webid是页面生成的uuid信息
                    usPantchDetail.setIsFlag("1");
                }
                if (user.getBelongCompanyTypeDictValue().equals("02")) {
                    usPantchDetail.setWebId(usPantchDetail.getWebId());//分公司编辑待办时或者编辑新增时，webid是宣讲清单ID信息
                    usPantchDetail.setIsFlag("2");
                }
            }
            if (user.getBelongCompanyTypeDictValue().equals("03")) {
                usPantchDetail.setParentCompanyCode(user.getBelongCompanyCodeParent());
                usPantchDetail.setParentCompanyName(user.getBelongCompanyNameParent());
                usPantchDetail.setBelongCompanyName(user.getBelongCompanyNameParent());
                usPantchDetail.setBelongCompanyCode(user.getBelongCompanyCodeParent());
                usPantchDetail.setBelongDepartmentName(user.getBelongCompanyName());
                usPantchDetail.setBelongDepartmentCode(user.getBelongCompanyCode());
            }
            if (user.getBelongCompanyTypeDictValue().equals("02") || user.getBelongCompanyTypeDictValue().equals("01")) {
                usPantchDetail.setBelongCompanyName(user.getBelongCompanyName());
                usPantchDetail.setBelongCompanyCode(user.getBelongCompanyCode());
                usPantchDetail.setBelongDepartmentName(user.getBelongDepartmentName());
                usPantchDetail.setBelongDepartmentCode(user.getBelongDepartmentCode());
                usPantchDetail.setParentCompanyCode(user.getBelongCompanyCode());
                usPantchDetail.setParentCompanyName(user.getBelongCompanyName());
            }
            newUsPantchDetail = usPantchDetailService.insert(usPantchDetail);
            usPantchDetailService.updateFileInfoById(usPantchDetail);//更新附件

        }
        if (ObjectUtil.isNotEmpty(newUsPantchDetail)) {
            return JsonResponse.success("添加成功");
        } else {
            return JsonResponse.success("添加失败");
        }
    }


    /**
     * 编辑政策宣讲事项
     */
    @ApiOperation(value = "编辑政策宣讲事项", notes = "编辑政策宣讲事项")
    @PostMapping(value = {"/updateUsPantchDetail", "/api/updateUsPantchDetail", "/updateUsPantchDetail/sso","/anonymous/updateUsPantchDetail"})
    public JsonResponse updateUsPantchDetail(@RequestBody UsPantchDetail usPantchDetail) {
        if (usPantchDetail.getSource().equals("MOBILE")) {
            loginUtils.manualLogin(usPantchDetail.getCurrentUserCode(), Constants.APP_CODE);
        }
        IUser user = SecurityUtils.getCurrentUser();
        UsPantchDetail newUsPantchDetail = null;
        if (ObjectUtil.isNotEmpty(usPantchDetail)) {
            if (user.getBelongCompanyTypeDictValue().equals("03")) {
                usPantchDetail.setParentCompanyCode(user.getBelongCompanyCodeParent());
                usPantchDetail.setParentCompanyName(user.getBelongCompanyNameParent());
                usPantchDetail.setBelongCompanyName(user.getBelongCompanyNameParent());
                usPantchDetail.setBelongCompanyCode(user.getBelongCompanyCodeParent());
                usPantchDetail.setBelongDepartmentName(user.getBelongCompanyName());
                usPantchDetail.setBelongDepartmentCode(user.getBelongCompanyCode());
            }
            if (user.getBelongCompanyTypeDictValue().equals("02") || user.getBelongCompanyTypeDictValue().equals("01")) {
                usPantchDetail.setBelongCompanyName(user.getBelongCompanyName());
                usPantchDetail.setBelongCompanyCode(user.getBelongCompanyCode());
                usPantchDetail.setBelongDepartmentName(user.getBelongDepartmentName());
                usPantchDetail.setBelongDepartmentCode(user.getBelongDepartmentCode());
                usPantchDetail.setParentCompanyCode(user.getBelongCompanyCode());
                usPantchDetail.setParentCompanyName(user.getBelongCompanyName());
            }
            newUsPantchDetail = usPantchDetailService.update(usPantchDetail);
            usPantchDetailService.updateFileInfoById(usPantchDetail);//更新附件
        }
        if (ObjectUtil.isNotEmpty(newUsPantchDetail)) {
            return JsonResponse.success("编辑成功");
        } else {
            return JsonResponse.success("编辑失败");
        }
    }


    /**
     * 删除政策宣讲事项
     */
    @ApiOperation(value = "删除政策宣讲事项", notes = "删除政策宣讲事项")
    @PostMapping(value = {"/deleteUsPantchDetail", "/api/deleteUsPantchDetail", "/deleteUsPantchDetail/sso"})
    public JsonResponse deleteUsRecordConfig(@RequestParam(required = false) String source,
                                             @RequestParam String id) {
        if (StringUtils.isNotEmpty(id)) {
            usPantchDetailService.deleteById(id);
        }
        return JsonResponse.defaultSuccessResponse();
    }


    /**
     * 宣讲清单列表
     *
     * @param source
     * @param currentUserCode
     * @param webId           页面UUID
     * @param policyId        宣讲清单页面ID
     * @return
     */
    @ApiOperation(value = "宣讲清单列表", notes = "宣讲清单列表")
    @PostMapping(value = {"/mattersList", "/api/mattersList", "/mattersList/sso"})
    public JsonResponse mattersList(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                    @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                    @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                    @RequestParam(required = false) String properties, //排序规则（属性名称）
                                    @RequestParam(required = false) String source,
                                    @RequestParam String currentUserCode,
                                    @RequestParam(required = false) String webId,
                                    @RequestParam(required = false) String pmInsId
    ) {

        List<Map<String, Object>> resultList = usPantchDetailService.mattersList(source, currentUserCode, pmInsId, webId);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");

    }


    /**
     * 政策宣讲事项查看
     */
    @ApiOperation(value = "政策宣讲事项查看", notes = "政策宣讲事项查看")
    @PostMapping(value = {"/checkUsPantchDetail", "/api/checkUsPantchDetail", "/checkUsPantchDetail/sso"})
    public JsonResponse checkUsPantchDetail(@RequestParam String id) {
        UsPantchDetail usPantchDetail = null;
        if (StringUtils.isNotEmpty(id)) {
            List<SysFile> file = new ArrayList<>();
            usPantchDetail = usPantchDetailService.findById(id);
            if (ObjectUtil.isNotEmpty(usPantchDetail)) {
                if (StringUtils.isNotEmpty(usPantchDetail.getFileIds())) {
                    List<String> fileIdList = Arrays.asList(usPantchDetail.getFileIds().split(","));
                    if (CollectionUtil.isNotEmpty(fileIdList)) {
                        for (String fileIds : fileIdList) {
                            SysFile sysFile = fileExtendService.findById(fileIds);
                            if (ObjectUtil.isNotEmpty(sysFile)) {
                                file.add(sysFile);
                            }

                        }
                    }
                    usPantchDetail.setDrawFiles(file);
                }
            }
        }
        return JsonResponse.success(usPantchDetail);
    }



    @ApiOperation(value = "一键下载", notes = "一键下载")
    @PostMapping(value = {"/usPantchDetailExport", "/api/usPantchDetailExport", "/usPantchDetailExport/sso"})
    public ResponseEntity<?> usPantchDetailExport(@RequestParam String usPantchId,
                                                  HttpServletResponse response,
                                                  HttpServletRequest request) {
        return usPantchDetailService.usPantchDetailExport(usPantchId, response, request);
    }







}
