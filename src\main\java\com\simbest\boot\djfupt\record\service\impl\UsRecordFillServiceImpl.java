package com.simbest.boot.djfupt.record.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.record.model.RecordVo;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.record.model.UsTalkContent;
import com.simbest.boot.djfupt.record.repository.UsRecordConfigRepository;
import com.simbest.boot.djfupt.record.repository.UsRecordFillRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordFillService;
import com.simbest.boot.djfupt.record.service.IUsTalkContentService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.wfquey.repository.DictValueRepository;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.ObjectUtil;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service(value = "usRecordFillService")
public class UsRecordFillServiceImpl extends LogicService<UsRecordFill, String> implements IUsRecordFillService {

    private UsRecordFillRepository usRecordFillRepository;

    @Autowired
    public UsRecordFillServiceImpl(UsRecordFillRepository usRecordFillRepository) {
        super(usRecordFillRepository);
        this.usRecordFillRepository = usRecordFillRepository;
    }


    String param1 = "/action/usRecordFill";

    @Autowired
    private IUsRecordFillService usRecordFillService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private UsCaseInfoRepository usCaseInfoRepository;


    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;

    @Autowired
    private DictValueRepository dictValueRepository;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;

    @Autowired
    private UsRecordConfigRepository usRecordConfigRepository;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private IUsTalkContentService talkContentService;

    /**
     * 查询思政纪实草稿状态工单
     *
     * @param creator
     * @return
     */
    @Override
    public List<UsRecordFill> getFromDetail(String creator) {
        return usRecordFillRepository.getFromDetail(creator);
    }


    /**
     * 更新附件
     */
    @Override
    public void updateFileInfoBy(UsRecordFill usRecordFill) {
        List<SysFile> drawFiles = usRecordFill.getDrawFiles();
        List<String> drawFileIds = new ArrayList<>();
        String fileid = "";

        if (CollectionUtil.isNotEmpty(drawFiles)) {
            if (drawFiles != null || drawFiles.size() > 0) {
                for (SysFile drawFile : drawFiles) {
                    drawFileIds.add(drawFile.getId());
                }
            }

        }
        fileid = Joiner.on(",").join(drawFileIds);
        usRecordFill.setFileIds(fileid);
        usRecordFillService.update(usRecordFill);
    }


    /**
     * 展示所有思政纪实台账
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllUsRecordFill(Map<String, Object> resultMap) {
        String configDeptName = cn.hutool.core.map.MapUtil.getStr(resultMap, "configDeptName");//谈话主题或走访事项
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        String belongDepartmentCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");
        String applyTime = "";
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(year) || org.apache.commons.lang3.StringUtils.isNotEmpty(month)) {
            applyTime = year + "-" + month;
        }
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select * from US_RECORD_FILL t where t.enabled=1 and t.removed_time is null " +
                "and t.is_draft='1' ");
        Map<String, Object> param = Maps.newHashMap();

        if (StringUtils.isNotEmpty(startTime)) {
            startTime = startTime + "00:00:00";
            sql.append(" and t.talk_time>=:startTime ");
            param.put("startTime", startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            endTime = endTime + "24:00:00";
            sql.append(" and t.talk_time<=:endTime");
            param.put("endTime", endTime);
        }

        if (StringUtils.isNotEmpty(configDeptName)) {

            sql.append(" and t.config_dept_name  like concat(concat('%', :configDeptName), '%') ");
            param.put("configDeptName", configDeptName);
        }
//
//        if (StringUtils.isNotEmpty(configDeptName)) {
//            sql.append(" and t.config_dept_name  like concat(concat('%', :configDeptName), '%') ");
//            param.put("configDeptName", configDeptName);
//        }


        if (org.apache.commons.lang3.StringUtils.isNotEmpty(belongDepartmentCode)) {

            sql.append(" and (t.belong_company_code = :companyCode or t.belong_company_code=:companyCode ) ");
            param.put("companyCode", belongDepartmentCode);


        } else {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
//                        case DataPermissionConstants.QUERY_LEVEL_FOUR:
//                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
//                            break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    param.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        String sql2 = " order by t.apply_time desc,talk_time desc";
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    /**
     * 导出思政纪实台账数据
     *
     * @param currentUserCode
     * @param request
     * @param response
     */
    @Override
    public void exportUsRecordFillDate(String currentUserCode, HttpServletRequest request, HttpServletResponse response, UsRecordFill usRecordFill) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            String configDeptName = cn.hutool.core.map.MapUtil.getStr(paramMap, "configDeptName");//谈话主题或走访事项
            String startTime = cn.hutool.core.map.MapUtil.getStr(paramMap, "startTime");//talkTime--开始时间
            String endTime = cn.hutool.core.map.MapUtil.getStr(paramMap, "endTime");//talkTime--结束时间

            String year = usRecordFill.getYear();
            String month = usRecordFill.getMonth();

            if (StrUtil.isNotEmpty(year) &&  StrUtil.isNotEmpty(month)) {
                startTime = year + "-" + month + "-01";
                endTime = year+ "-" + month + getLastMonthDay(year , month);
            }
            usRecordFill.setStartTime(startTime);
            usRecordFill.setEndTime(endTime);

            if (ObjectUtil.isNotEmpty(usRecordFill)) {
                if (StringUtils.isNotEmpty(usRecordFill.getConfigDeptName())) {
                    paramMap.put("configDeptName", usRecordFill.getConfigDeptName());
                }
                if (StringUtils.isNotEmpty(usRecordFill.getStartTime())) {
                    paramMap.put("startTime", usRecordFill.getStartTime());
                }
                if (StringUtils.isNotEmpty(usRecordFill.getEndTime())) {
                    paramMap.put("endTime", usRecordFill.getEndTime());
                }
                if (StringUtils.isNotEmpty(usRecordFill.getBelongDepartmentCode())) {
                    paramMap.put("belongDepartmentCode", usRecordFill.getBelongDepartmentCode());
                }
                if (StringUtils.isNotEmpty(usRecordFill.getType())) {
                    paramMap.put("type", usRecordFill.getType());
                }
                paramMap.put("year", usRecordFill.getYear());
                paramMap.put("month", usRecordFill.getMonth());

            }
            String path = request.getServletContext().getRealPath("down");
            List<Map<String, Object>> resultData = this.findAllUsRecordFills(paramMap);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = "思政纪实台账列表" + dateStr + ".xls";
            List<UsRecordFill> usRecordFillList = new ArrayList<>();
            for (Map<String, Object> resultDatum : resultData) {
                UsRecordFill usRecordFill1 = creatModelRecordDate(resultDatum);
                usRecordFill1.setCompanyName(usRecordFill1.getBelongCompanyName());
                usRecordFill1.setDepName(usRecordFill1.getBelongDepartmentName());
                usRecordFillList.add(usRecordFill1);
            }
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Type", "application/msexcel");
            response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String targetFileName = path + "" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<UsRecordFill> exportUtil = new ExcelUtil<>(UsRecordFill.class);
            exportUtil.exportExcel(usRecordFillList, "思政纪实台账列表", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    private static String getLastMonthDay(String year , String month) {
        Calendar calendar = Calendar.getInstance();
        // 设置日期为指定的年份和月份
        calendar.set(Calendar.YEAR, Integer.valueOf(year));
        calendar.set(Calendar.MONTH, Integer.valueOf(month) - 1);
        // 获取最后一天
        int actualMaximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return String.valueOf(actualMaximum);
    }

    private UsRecordFill creatModelRecordDate(Map<String, Object> map) {
        UsRecordFill usRecordFill = new UsRecordFill();
        usRecordFill.setTalkTime(  MapUtil.getStr(map,"talkTime"));//填报人
        usRecordFill.setTalkAddress(  MapUtil.getStr(map,"talkAddress"));
        usRecordFill.setNumberOfPanel(  MapUtil.getStr(map,"numberOfPanel"));
        usRecordFill.setConfigDeptName(  MapUtil.getStr(map,"configDeptName"));
        usRecordFill.setConfigTime(  MapUtil.getStr(map,"configTime"));
        usRecordFill.setBelongCompanyName(  MapUtil.getStr(map,"belongCompanyName"));
        usRecordFill.setBelongDepartmentName(  MapUtil.getStr(map,"belongDepartmentName"));
        usRecordFill.setApplyUser(  MapUtil.getStr(map,"applyUser"));

        StringBuffer talkBuf = new StringBuffer("");
        //获取谈话内容列表
        Specification<UsTalkContent> talkContentSpec = Specifications.<UsTalkContent>and()
                .eq("enabled", Boolean.TRUE)
                .eq("mainId",  MapUtil.getStr(map,"id"))
                .build();
        List<UsTalkContent> talkContentList = talkContentService.findAllNoPage(talkContentSpec, Sort.by(Sort.Direction.ASC, "createdTime"));
        int count = 1;
        for(UsTalkContent item:talkContentList){
            String talk = count+"、类别:"+item.getType()+",问题描述:"+item.getRemark()+"。";
            if(count != 1){
                talkBuf.append(System.lineSeparator());
            }
            talkBuf.append(talk);
            count ++;
        }
        usRecordFill.setTalkContentListString(talkBuf.toString());
        return usRecordFill;
    }


    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 思政数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findRecordCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select t.belong_company_name, count(*) as filCount" +
                "  from US_RECORD_FILL t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and t.is_draft = '1' and SUBSTR(nvl(t.talk_time,'1950-01'),0,7) = TO_CHAR(SYSDATE-1,'YYYY-MM')  and  t.belong_company_name is not null ");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
//        if (StringUtils.isNotEmpty(startTime)) {
//            sql.append("  ");
//          //  param.put("startTime", startTime);
//        }
//        if (StringUtils.isNotEmpty(endTime)) {
//            sql.append(" and to_char(nvl(t.talk_time,'1950-01'),'yyyy-MM-dd')<=:endTime");
//            param.put("endTime", endTime);
//        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 思政数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllRecordCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select count(*) as filCount" +
                "  from US_RECORD_FILL t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and t.is_draft = '1' ");
        Map<String, Object> param = Maps.newHashMap();
        if (StringUtils.isNotEmpty(startTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')>=:startTime ");
            param.put("startTime", startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')<=:endTime");
            param.put("endTime", endTime);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    @Override
    public JsonResponse recordDetail(String id) {
        if (StringUtils.isEmpty(id)) {
            return JsonResponse.fail("参数有误");
        }
        UsRecordFill recordFill = usRecordFillRepository.findByIdActive(id);
        if (StringUtils.isNotEmpty(recordFill.getFileIds())) {
            List<SysFile> drawFiles = new ArrayList<>();
            List<String> fileIdList = Arrays.asList(recordFill.getFileIds().split(","));
            for (String fileId : fileIdList) {
                SysFile sysFile = fileExtendService.findById(fileId);
                drawFiles.add(sysFile);
            }
            recordFill.setDrawFiles(drawFiles);
        }
        if (recordFill != null && StringUtils.isNotEmpty(recordFill.getTaskDescriptionFileId())) {
            List<SysFile> sysFiles = new ArrayList<>();
            List<String> fileIdList = Arrays.asList(recordFill.getTaskDescriptionFileId().split(","));
            for (String fileId : fileIdList) {
                SysFile sysFile = fileExtendService.findById(fileId);
                if (sysFile != null) {
                    sysFiles.add(sysFile);
                }

            }
            recordFill.setTaskDescriptionFiles(sysFiles);
        }
        //获取谈话内容列表
        Specification<UsTalkContent> talkContentSpec = Specifications.<UsTalkContent>and()
                .eq("enabled", Boolean.TRUE)
                .eq("mainId", recordFill.getId())
                .build();
        List<UsTalkContent> talkContentList = talkContentService.findAllNoPage(talkContentSpec, Sort.by(Sort.Direction.ASC, "createdTime"));
        recordFill.setTalkContentList(talkContentList);
        return JsonResponse.success(recordFill);
    }

    @Override
    public List<RecordVo> recordStatistics(int page, int rows, Map<String, Object> resultMap) {

        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//4度
        String applyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(year) || org.apache.commons.lang3.StringUtils.isNotEmpty(month)) {
            applyTime = year + "-" + month;
        }
        List<Map<String, Object>> sysDictValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(companyName)) {
            sysDictValues = usRecordFillRepository.findAllByCompanyAndName("company", companyName);
        } else {
            sysDictValues = usRecordFillRepository.findAllByCompany("company");
        }

        List<RecordVo> recordVos = new ArrayList<>();
        if (sysDictValues.size() > 0) {
            for (Map<String, Object> dictValue : sysDictValues) {
                StringBuffer sql = new StringBuffer(" select distinct t.creator nums" +
                        "    from US_RECORD_FILL t" +
                        "    where t.enabled = 1" +
                        "    and SUBSTR(nvl(t.talk_time, '1950-01'), 0, 7) = :applyTime" +
                        "  and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode   " +
                        "   or t.belong_department_code=:companyCode )   and t.is_draft = 1 ");


                Map<String, Object> param = Maps.newHashMap();
                param.put("companyCode", dictValue.get("VALUE"));

                param.put("applyTime", applyTime);
                String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                switch (queryLevel) {
                    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                        DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_SECOND);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                        DataPermissionTool.handleSql(sql, param, DataPermissionConstants.QUERY_LEVEL_THIRD);
                        break;
                    default:
                        sql.append(" and  t.creator =:username  ");
                        param.put("username", SecurityUtils.getCurrentUser().getUsername());
                        break;
                }

                List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
                RecordVo recordVo = new RecordVo();
                String completionRate = "";
                recordVo.setCompanyCode(dictValue.get("VALUE").toString());
                recordVo.setCompanyName(dictValue.get("NAME").toString());
                recordVo.setGrids(Integer.parseInt(dictValue.get("FLAG").toString()));
                recordVo.setNums(list.size());
                float f1 = Float.parseFloat(dictValue.get("FLAG").toString());
                float f2 = Float.parseFloat(String.valueOf(list.size()));
                if (dictValue.get("FLAG").toString().equals("0") || list.size() == 0) {
                    completionRate = "0";
                } else {
                    float f3 = (f2 / f1) * 100;
                    BigDecimal b = new BigDecimal(f3);
                    f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                    if(Float.compare(f3, 100.0f)>0){
                        f3=100;
                    }
                    completionRate = f3 + "%";
                }
                recordVo.setCompletionRate(completionRate);
                recordVos.add(recordVo);

            }
        }


        return recordVos;
    }


    @Override
    public List<RecordVo> recordStatisticsOther(int page, int rows, Map<String, Object> resultMap) {
        IUser user=SecurityUtils.getCurrentUser();
        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//4度
        String companyCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");//4度
        String applyTime = null;
        String year = cn.hutool.core.map.MapUtil.getStr(resultMap, "year");//
        String month = cn.hutool.core.map.MapUtil.getStr(resultMap, "month");//
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(year) || org.apache.commons.lang3.StringUtils.isNotEmpty(month)) {
            applyTime = year + "-" + month;
        }
        List<SysDictValue> sysDictValues = new ArrayList<>();

        List<RecordVo> recordVos = new ArrayList<>();
        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));

        if (isAdmin) {
            RecordVo recordVo = new RecordVo();
            recordVo.setCompanyName(user.getBelongDepartmentName());
            recordVo.setCompanyCode(user.getBelongDepartmentCode());
            int num = adminManagerRepository.findByDepCodeAndEnabledAndRoleUserIdss(user.getBelongDepartmentCode(), Constants.FJFUPT_BRO);
            StringBuffer sql = new StringBuffer(" select distinct t.creator nums" +
                    "    from US_RECORD_FILL t" +
                    "    where t.enabled = 1" +
                    "    and SUBSTR(nvl(t.talk_time, '1950-01'), 0, 7) = :applyTime" +
                    "     and t.is_draft = 1 ");
            Map<String, Object> param = Maps.newHashMap();
                sql.append("  and t.belong_department_code = :companyCode   ");
            param.put("companyCode", user.getBelongDepartmentCode());
            param.put("applyTime", applyTime);
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
            String completionRate = "";
            recordVo.setGrids(num);
            recordVo.setNums(list.size());
            float f1 = Float.parseFloat(String.valueOf(num));
            float f2 = Float.parseFloat(String.valueOf(list.size()));
            if (recordVo.getGrids()==0 || list.size() == 0) {
                completionRate = "0";
            } else {
                float f3 = (f2 / f1) * 100;
                BigDecimal b = new BigDecimal(f3);
                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                if(Float.compare(f3, 100.0f)>0){
                    f3=100;
                }
                completionRate = f3 + "%";
            }
            recordVo.setCompletionRate(completionRate);
            recordVos.add(recordVo);

        } else {
            List<SimpleOrg> simpleOrgList = new ArrayList<>();
            //查出当前下的组织
            simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
            int i = 0;
            if (simpleOrgList.size() > 0) {
                for (SimpleOrg simpleOrg : simpleOrgList) {
                    if (i == 0) {
                        simpleOrg.setOrgName(simpleOrg.getDisplayName());

                    }
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(companyCode)) {
                        if (!simpleOrg.getOrgCode().equals(companyCode)) {
                            continue;
                        }
                    }
                    if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                        continue;
                    }
                    SysDictValue sysDictValue = new SysDictValue();
                    sysDictValue.setValue(simpleOrg.getOrgCode());
                    sysDictValue.setName(simpleOrg.getOrgName());
                    sysDictValues.add(sysDictValue);
                    i++;
                }
            }

            if (sysDictValues.size() > 0) {
                int is = 0;
                for (SysDictValue sysDictValue : sysDictValues) {
                    int num=0;
                    if(is==0) {
                        num= adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                    }else {
                        num= adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIds(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                    }


                    StringBuffer sql = new StringBuffer(" select distinct t.creator nums" +
                            "    from US_RECORD_FILL t" +
                            "    where t.enabled = 1" +
                            "    and SUBSTR(nvl(t.talk_time, '1950-01'), 0, 7) = :applyTime" +
                            "     and t.is_draft = 1 ");
                    Map<String, Object> param = Maps.newHashMap();
                    if (is == 0) {
                        sql.append("  and (t.belong_department_code = :companyCode  ) ");
                    } else {
                        sql.append("  and (t.belong_company_code = :companyCode or t.belong_department_code = :companyCode  ) ");
                    }
                    param.put("companyCode", sysDictValue.getValue());
                    param.put("applyTime", applyTime);
                    List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
                    RecordVo recordVo = new RecordVo();
                    String completionRate = "";
                    recordVo.setCompanyCode(sysDictValue.getValue());
                    recordVo.setCompanyName(sysDictValue.getName());
                    recordVo.setGrids(num);
                    recordVo.setNums(list.size());
                    float f1 = Float.parseFloat(String.valueOf(num));
                    float f2 = Float.parseFloat(String.valueOf(list.size()));
                    if (recordVo.getGrids()==0|| list.size() == 0) {
                        completionRate = "0";
                    } else {
                        float f3 = (f2 / f1) * 100;
                        BigDecimal b = new BigDecimal(f3);
                        f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                        if(Float.compare(f3, 100.0f)>0){
                            f3=100;
                        }
                        completionRate = f3 + "%";
                    }
                    recordVo.setCompletionRate(completionRate);
                    recordVos.add(recordVo);

                    is++;
                }
            }
        }



        return recordVos;
    }


    @Override
    public void exportProblemStatistics(Map<String, Object> resultMap, HttpServletResponse response, HttpServletRequest request) {
        Boolean flag = false;
        String fileName = "数据统计.xls";

        IUser user = SecurityUtils.getCurrentUser();
        List<RecordVo> list;
        if (user.getBelongCompanyTypeDictValue().equals("01")) {
            list = usRecordFillService.recordStatistics(1, 99999, resultMap);
        } else {
            list = usRecordFillService.recordStatisticsOther(0, 9999999, resultMap);
        }

        try {

            if (list.size() > 0) {
                int i = 1;
                for (RecordVo usCaseExcel : list) {
                    usCaseExcel.setNum(i);
                    i++;
                }
            }
            //获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");


            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            // 生成workbook 并导出
            // 创建参数对象（用来设定excel得sheet得内容等信息）
            ExportParams systemCompilation1 = new ExportParams();
            // 设置sheet的名称
            systemCompilation1.setSheetName("sheet1");
            // 创建sheet1使用得map
            Map<String, Object> systemCompilationMap = Maps.newHashMap();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            systemCompilationMap.put("title", systemCompilation1);
            // 模版导出对应得实体类型
            systemCompilationMap.put("entity", RecordVo.class);
            // sheet中要填充得数据
            systemCompilationMap.put("data", list);
            // 将sheet1、sheet2.......sheet13使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(systemCompilationMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            // 导出操作
            FileOutputStream fos = new FileOutputStream(targetFileName);
            workbook.write(fos);
            fos.close();
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }

    }


    public JsonResponse configuration() {
        UsRecordConfig recordConfig = new UsRecordConfig();
        List<UsRecordConfig> usRecordConfigs = usRecordConfigRepository.findAllByEnabledOrderByCreatedTimeDesc(Boolean.TRUE);
        if (usRecordConfigs != null && usRecordConfigs.size() > 0) {
            recordConfig = usRecordConfigs.get(0);
        }
        if (recordConfig != null && StringUtils.isNotEmpty(recordConfig.getTaskDescriptionFileId())) {
            List<SysFile> sysFiles = new ArrayList<>();
            List<String> fileIdList = Arrays.asList(recordConfig.getTaskDescriptionFileId().split(","));
            for (String fileId : fileIdList) {
                SysFile sysFile = fileExtendService.findById(fileId);
                if (sysFile != null) {
                    sysFiles.add(sysFile);
                }

            }
            recordConfig.setTaskDescriptionFiles(sysFiles);
        }
        return JsonResponse.success(recordConfig);

    }


    public List<Map<String, Object>> findAllUsRecordFills(Map<String, Object> resultMap) {

        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//
        String configDeptName = cn.hutool.core.map.MapUtil.getStr(resultMap, "configDeptName");//谈话主题或走访事项
        String belongDepartmentCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");//
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//
        String type = cn.hutool.core.map.MapUtil.getStr(resultMap, "type");//
        Map<String, Object> map = new HashMap<>();
        startTime=startTime+" 00:00:01";
        endTime=endTime+" 23:59:59";
        StringBuffer sql = new StringBuffer("" +
                "select t.true_name," +
                "       t.grid_name," +
                "       tad.id," +
                "       t.belong_department_name," +
                "       t.belong_company_name," +
                "       tad.config_dept_name," +
                "       tad.talk_time," +
                "       tad.apply_time," +
                "       t.true_name," +
                "       tad.created_time,  " +
                "  tad.apply_user, " +
                "       tad.talk_address," +
                "       tad.config_time," +
                "       tad.number_of_panel" +
                "  from djfupt.us_admin_manager t" +
                "  left join (select * from  djfupt.us_record_fill tadd where      tadd.talk_time >=:startTime " +
                "   and tadd.talk_time <=:endTime and tadd.enabled = 1  ) tad" +
                "    on t.user_name = tad.creator" +
                "   and t.enabled = 1   and tad.is_draft=1" +
                " where t.enabled = 1" +
                "   and t.role_user_id = 'djfupt_002'");


        if (StringUtils.isNotEmpty(configDeptName)) {

            sql.append(" and tad.config_dept_name  like concat(concat('%', :configDeptName), '%') ");
            map.put("configDeptName", configDeptName);
        }



        if (StringUtils.isNotEmpty(companyName)) {

            sql.append(" and tad.belong_company_name  like concat(concat('%', :companyName), '%') ");
            map.put("companyName", companyName);
        }

        if (StringUtils.isNotEmpty(type)) {
            if (type.equals("1")) {
                sql.append(" and tad.talk_time is not null");
            } else {
                sql.append(" and tad.talk_time is  null");
            }

        }

        map.put("startTime",startTime);
        map.put("endTime",endTime);


        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(belongDepartmentCode)) {

            sql.append(" and (t.belong_company_code = :companyCode or t.belong_department_code=:companyCode ) ");
            map.put("companyCode", belongDepartmentCode);


        } else if (isAdmin) {

            sql.append(" and t.belong_department_code=:belongDepartmentCode");
            map.put("belongDepartmentCode", SecurityUtils.getCurrentUser().getBelongDepartmentCode());

        } else {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;

                default:
                    sql.append(" and  t.user_name =:username  ");
                    map.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }

        sql.append("   order by nvl(tad.talk_time, '2009-01-01 01:01:01') desc ");
        List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
        list = FormatTool.formatConversion(list);//驼峰转换


        return list;
    }

}
