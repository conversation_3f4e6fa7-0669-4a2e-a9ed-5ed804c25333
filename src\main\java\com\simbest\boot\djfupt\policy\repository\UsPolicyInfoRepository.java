package com.simbest.boot.djfupt.policy.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UsPolicyInfoRepository extends LogicRepository<UsPolicyInfo, String> {


    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.creator =:creator" +
                    "   and t.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    UsPolicyInfo findUsPolicyInfoInfo( @Param("pmInsId") String pmInsId,
                                       @Param("creator") String creator);


    //查询月份下当前人有没有起草工单
    @Query(
            value = " select t.* " +
                    "  from US_POLICY_INFO t " +
                    " where t.enabled = 1 " +
                    "   and t.creator =:creator " +
                    "   and to_CHAR(t.created_time, 'MM') = :month   ",
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByCreatorAndYear(@Param("month") int month,
                                                @Param("creator") String creator);


    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    List<UsPolicyInfo> findUsPolicyInfoByPminsId(@Param("pmInsId") String pmInsId);

    /**
     * 根据创建人查询工单
     * @param creator
     * @return
     */
    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.creator =:creator",
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByCreator(@Param("creator") String creator);

    @Modifying
    @Query(
            value = "  update US_POLICY_INFO t set t.creator=:creator  ,t.modifier=:creator where id=:id ",
            nativeQuery = true
    )

    int updateCreatorById(@Param("creator") String creator,
                          @Param("id") String id);



    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.creator =:creator" +
                    "   and t.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    List<UsPolicyInfo> findUsPolicyInfoInfos( @Param("pmInsId") String pmInsId,
                                       @Param("creator") String creator);



    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.belong_company_code='4772338661636601428'" +
                    "   and t.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    List<UsPolicyInfo> findUsPolicyInfoInfoAndCompanyCode( @Param("pmInsId") String pmInsId  );


    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where  SUBSTR(nvl(t.policy_time, '1950-01'), 0, 7) = :time" +
                    " and  t.enabled=1" +
                //    " and  t.belong_org_code=:companyCode ",
                   " and ( t.belong_company_code=:companyCode or t.belong_department_code=:companyCode or t.belong_org_code=:companyCode) ",
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByCountComCode(@Param("companyCode") String companyCode,
                              @Param("time") String time);



    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where  SUBSTR(nvl(t.policy_time, '1950-01'), 0, 7) = :time" +
                    " and  t.enabled=1" +
                       " and  t.belong_org_code=:companyCode ",
                    //" and ( t.belong_company_code=:companyCode or t.belong_department_code=:companyCode or t.belong_org_code=:companyCode) ",
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByCountComCodes(@Param("companyCode") String companyCode,
                                             @Param("time") String time);

    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where  SUBSTR(nvl(t.policy_time, '1950-01'), 0, 7) = :time" +
                    " and  t.enabled=1" +
                    " and  t.belong_department_code=:companyCode ",
            //" and ( t.belong_company_code=:companyCode or t.belong_department_code=:companyCode or t.belong_org_code=:companyCode) ",
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByCountDep(@Param("companyCode") String companyCode,
                                              @Param("time") String time);


    @Query(
            value = " select count(1)" +
                    "  from US_POLICY_INFO t" +
                    " where  SUBSTR(nvl(t.policy_time, '1950-01'), 0, 7) = :time" +
                    " and  t.enabled=1" +
                    " and  t.belong_company_name=:companyName ",
            //" and ( t.belong_company_code=:companyCode or t.belong_department_code=:companyCode or t.belong_org_code=:companyCode) ",
            nativeQuery = true
    )
    int findAllByEnabledAndPolicyTime(@Param("companyName") String companyName,@Param("time") String time);



    @Query(
            value = " select t.*" +
                    "  from US_POLICY_INFO t" +
                    " where  SUBSTR(nvl(t.policy_time, '1950-01'), 0, 7) = :time and t.enabled=1       and t.creator =:creator " +
                    " and  t.enabled=1" ,
            nativeQuery = true
    )
    List<UsPolicyInfo> findAllByTimeAndCreate( @Param("time") String time,
                                               @Param("creator") String creator);
}
