package com.simbest.boot.djfupt.stat.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.stat.model.VisitorStat;
import com.simbest.boot.djfupt.stat.service.IVisitorStatService;
import com.simbest.boot.djfupt.util.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <strong>Title : VisitorStatController</strong><br>
 * <strong>Description : 访客统计分析控制器</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Api(description = "访客统计分析")
@Slf4j
@RestController
@RequestMapping(value = "/action/visitorStat")
public class VisitorStatController extends LogicController<VisitorStat, String> {

    private final IVisitorStatService service;

    @Autowired
    public VisitorStatController(IVisitorStatService service) {
        super(service);
        this.service = service;
    }

    /**
     * 保存访客统计数据
     */
    @ApiOperation(value = "保存访客统计数据", notes = "保存访客统计数据")
    @PostMapping(value = {"/saveVisitorStat", "/saveVisitorStat/sso", "/saveVisitorStat/api"})
    public JsonResponse saveVisitorStat(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                        @RequestParam(required = false) String currentUserCode,
                                        @RequestBody VisitorStat visitorStat) {
        return service.saveVisitorStat(source, currentUserCode, visitorStat);
    }

    /**
     * 更新访客统计数据
     */
    @ApiOperation(value = "更新访客统计数据", notes = "更新访客统计数据")
    @PostMapping(value = {"/updateVisitorStat", "/updateVisitorStat/sso", "/updateVisitorStat/api"})
    public JsonResponse updateVisitorStat(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                          @RequestParam(required = false) String currentUserCode,
                                          @RequestBody VisitorStat visitorStat) {
        return service.updateVisitorStat(source, currentUserCode, visitorStat);
    }

    /**
     * 根据ID查询访客统计数据
     */
    @ApiOperation(value = "根据ID查询访客统计数据", notes = "根据ID查询访客统计数据")
    @PostMapping(value = {"/findVisitorStatById", "/findVisitorStatById/sso", "/findVisitorStatById/api"})
    public JsonResponse findVisitorStatById(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                            @RequestParam(required = false) String currentUserCode,
                                            @RequestParam String id) {
        return service.findVisitorStatById(source, currentUserCode, id);
    }

    /**
     * 删除访客统计数据
     */
    @ApiOperation(value = "删除访客统计数据", notes = "删除访客统计数据")
    @PostMapping(value = {"/deleteVisitorStat", "/deleteVisitorStat/sso", "/deleteVisitorStat/api"})
    public JsonResponse deleteVisitorStat(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                          @RequestParam(required = false) String currentUserCode,
                                          @RequestParam String id) {
        return service.deleteVisitorStat(source, currentUserCode, id);
    }

    /**
     * 查询访客统计数据列表
     */
    @ApiOperation(value = "查询访客统计数据列表", notes = "查询访客统计数据列表")
    @PostMapping(value = {"/findAllVisitorStat", "/findAllVisitorStat/sso", "/findAllVisitorStat/api"})
    public JsonResponse findAllVisitorStat(@RequestParam(required = false, defaultValue = "1") int page,
                                           @RequestParam(required = false, defaultValue = "10") int size,
                                           @RequestParam(required = false, defaultValue = "desc") String direction,
                                           @RequestParam(required = false, defaultValue = "createdAt") String properties,
                                           @RequestParam(required = false, defaultValue = Constants.PC) String source,
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestBody(required = false) VisitorStat visitorStat) {
        return service.findAllVisitorStat(visitorStat, service.getPageable(page, size, direction, properties));
    }

    /**
     * 统计各单位的访问量
     */
    @ApiOperation(value = "统计各单位的访问量", notes = "统计各单位的访问量")
    @PostMapping(value = {"/countVisitsByCompany", "/countVisitsByCompany/sso", "/countVisitsByCompany/api"})
    public JsonResponse countVisitsByCompany(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                             @RequestParam(required = false) String currentUserCode) {
        return service.countVisitsByCompany(source, currentUserCode);
    }

    /**
     * 根据条件查询访客统计数据
     */
    @ApiOperation(value = "根据条件查询访客统计数据", notes = "根据条件查询访客统计数据")
    @PostMapping(value = {"/queryVisitorStatByCondition", "/queryVisitorStatByCondition/sso", "/queryVisitorStatByCondition/api"})
    public JsonResponse queryVisitorStatByCondition(@RequestParam(required = false, defaultValue = "1") int page,
                                                    @RequestParam(required = false, defaultValue = "10") int size,
                                                    @RequestParam(required = false, defaultValue = "desc") String direction,
                                                    @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                                    @RequestBody(required = false) Map<String, Object> params) {
        return service.queryVisitorStatByCondition(page, size, direction, properties, params);
    }

    /**
     * 查询访客统计数据的公司层级树结构
     */
    @ApiOperation(value = "查询访客统计数据的公司层级树结构", notes = "查询访客统计数据的公司层级树结构，包含累计使用人次、提问问题数量和点赞次数")
    @PostMapping(value = {"/getCompanyHierarchyTree", "/getCompanyHierarchyTree/sso", "/getCompanyHierarchyTree/api"})
    public JsonResponse getCompanyHierarchyTree(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                                @RequestParam(required = false) String currentUserCode) {
        return service.getCompanyHierarchyTree(source, currentUserCode);
    }

    /**
     * 同步会话数据到访客统计
     */
    @ApiOperation(value = "同步会话数据到访客统计", notes = "根据用户名和会话ID从外部API获取数据并同步到访客统计表")
    @PostMapping(value = {"/syncConversationData", "/syncConversationData/sso", "/syncConversationData/api"})
    public JsonResponse syncConversationData(@RequestParam String username,
                                             @RequestParam String conversationId,
                                             @RequestParam String apiKey) {

        return service.syncConversationData(username, conversationId,apiKey);
    }

    /**
     * 新的查询接口 - 支持模糊查询和分页
     */
    @ApiOperation(value = "新的查询接口", notes = "支持基本模糊查询和分页功能")
    @PostMapping(value = {"/newFindAll", "/newFindAll/sso", "/newFindAll/api"})
    public JsonResponse newFindAll(@RequestParam(required = false, defaultValue = "1") int page,
                                   @RequestParam(required = false, defaultValue = "10") int size,
                                   @RequestParam(required = false, defaultValue = "desc") String direction,
                                   @RequestParam(required = false, defaultValue = "createdAt") String properties,
                                   @RequestParam(required = false, defaultValue = Constants.PC) String source,
                                   @RequestParam(required = false) String currentUserCode,
                                   @RequestParam(required = false) String keyword) {
        return service.newFindAll(source, currentUserCode, page, size, direction, properties, keyword);
    }

    /**
     * 统计访客数据总数和sum字段总和，按belongCompanyName分类
     */
    @ApiOperation(value = "统计访客数据", notes = "按belongCompanyName分类统计访客数据总条数和sum字段总和")
    @PostMapping(value = {"/findCount", "/findCount/sso", "/findCount/api"})
    public JsonResponse findCount(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody(required = false) Map<String, Object> params) {
        return service.findCount(source, currentUserCode, params);
    }

    /**
     * 导出访客统计数据
     */
    @ApiOperation(value = "导出访客统计数据", notes = "导出访客统计数据到Excel文件")
    @PostMapping(value = {"/exportVisitorStat", "/exportVisitorStat/sso", "/exportVisitorStat/api"})
    public void exportVisitorStat(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  HttpServletResponse response,
                                  HttpServletRequest request) {
        service.exportVisitorStat(source, currentUserCode, response, request);
    }
}
