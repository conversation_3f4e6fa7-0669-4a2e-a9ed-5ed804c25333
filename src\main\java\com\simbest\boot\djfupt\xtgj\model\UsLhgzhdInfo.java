package com.simbest.boot.djfupt.xtgj.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_lhgzhd_info")
@ApiModel(value = "联合跟装活动")
public class UsLhgzhdInfo extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ULI")
    private String id;


    @Column(length = 50)
    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @Column(length = 40)
    @ApiModelProperty(value = "发起人")
    private String username;

    @Column(length = 40)
    @ApiModelProperty(value = "发起人")
    private String truename;

    @Column(length = 20)
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 1. 字典id:对应操作1/0
     * 2. 以 !@ 为分隔符
     */
    @Column(length = 2000)
    @ApiModelProperty(value = "操作 A:1!@B:0")
    private String operationInfo;


    @Transient
    private LocalDateTime sdate;

    @Transient
    private LocalDateTime edate;

    @Transient
    private List<XtgjDictValueVO> operationList = new ArrayList<>();

    @Transient
    @ApiModelProperty(value = "意见建议")
    private List<UsLhgzhdItemInfo> itemInfoList;

}
