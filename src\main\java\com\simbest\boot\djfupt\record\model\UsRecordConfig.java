package com.simbest.boot.djfupt.record.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_record_config")
@ApiModel(value = "思政纪实配置")
public class UsRecordConfig extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ExcelVOAttribute(name = "工作设置", column = "A")
    @ApiModelProperty(value = "工作设置", required = true)
    private String workSettings;               //主单据ID  关联us_pm_instence表中的单据ID




    @Column(length = 40)
    @ExcelVOAttribute(name = "思政工作频次类型", column = "B")
    @ApiModelProperty(value = "思政工作频次类型", required = true)
    private String frequencyType;


    @Column(length = 40)
    @ExcelVOAttribute(name = "思政工作开始时间", column = "C")
    @ApiModelProperty(value = "思政工作时间-周-思政工作开始时间", required = true)
    private String startTime;

    @Column(length = 40)
    @ExcelVOAttribute(name = "思政工作结束时间", column = "D")
    @ApiModelProperty(value = "思政工作时间-周-思政工作结束时间", required = true)
    private String endTime;




    @Column(length = 30)
    @ExcelVOAttribute(name = "思政工作开始年", column = "E")
    @ApiModelProperty(value = "思政工作时间-年度", required = true)

    private String year;


    @Column(length = 30)
    @ExcelVOAttribute(name = "思政工作开始月", column = "F")
    @ApiModelProperty(value = "思政工作时间-月份", required = true)

    private String month;



    @Column(length = 30)
    @ExcelVOAttribute(name = "思政工作开始季度", column = "G")
    @ApiModelProperty(value = "思政工作时间-季度", required = true)

    private String quarterly;

    @Column(length = 40)
    @ExcelVOAttribute(name = "思政工作频次", column = "H")
    @ApiModelProperty(value = "思政工作频次", required = true)
    private String frequency;

    @Column(length = 40)
    @ExcelVOAttribute(name = "配置人", column = "I")
    @ApiModelProperty(value = "配置人", required = true)
    private String configuration;



    @ExcelVOAttribute(name = "配置部门", column = "J")
    @ApiModelProperty(value = "配置部门", required = true)
    private String configDeptName;


    @Column(length = 40)
    @ExcelVOAttribute(name = "配置时间", column = "K")
    @ApiModelProperty(value = "配置时间", required = true)
    private String configTime;

    @Column(length = 40)
    //@ExcelVOAttribute(name = "任务描述", column = "L")
    private String taskDescription;//任务描述

    @Column(length = 40)
    //@ExcelVOAttribute(name = "任务描述", column = "L")
    private String taskDescriptionFileId;//任务描述附件

    @Column(length = 500)
    @ApiModelProperty(value = "思政纪实分类")
    private String recordType;//多选，例如1，2，3，4，5，6，7

    @Transient
    private List<SysFile> taskDescriptionFiles;
}
