package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.security.IUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_screen_admin_manager")
@ApiModel(value = "数智大屏管理员")
public class ScreenAdminManager extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "US_SAM")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(value = "管理员类型 0超级管理员 1省公司管理员 2分公司管理员", required = true)
    protected String type;

    @Column(length = 100)
    @ApiModelProperty(value = "部门")
    private String deptName;

    @Column(length = 100)
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    private String companyName;

    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String companyCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String companyParentCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    private String companyParentName;

    @Column(length = 100)
    @ApiModelProperty(value = "公司类型")
    private String companyType;

    @Column(length = 40)
    @ApiModelProperty(value = "OA账号")
    private String username;

    @Column(length = 100)
    @ApiModelProperty(value = "管理员姓名")
    private String truename;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "排序")
    private Integer displayOrder;

    public ScreenAdminManager created(IUser user) {
        username = user.getUsername();
        truename = user.getTruename();

        deptCode = user.getBelongDepartmentCode();
        deptName = user.getBelongDepartmentName();
        companyCode = user.getBelongCompanyCode();
        companyName = user.getBelongCompanyName();
        companyParentCode = user.getBelongCompanyCodeParent();
        companyParentName = user.getBelongCompanyNameParent();

        companyType = user.getBelongCompanyTypeDictValue();

        type = Objects.equals(companyType, "01") ? "1" : "2";
        if (Objects.equals(username, "liushifeng")) type = "0";
        return this;
    }

}
