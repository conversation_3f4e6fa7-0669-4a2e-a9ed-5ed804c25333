package com.simbest.boot.djfupt.caseinfo.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.caseinfo.model.CaseStatisticsVo;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IUsCaseInfoService extends ILogicService<UsCaseInfo, String> {

    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param copyLocation    抄送环节
     * @param bodyParam       提交审批参数
     * @param formId          表单id
     * @param notificationId  待阅id
     * @return
     */
    JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId);


    /**
     * 获取表单详情 (location 当前环节 workItemId 工作项id 暂无用到)
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @param location      当前环节
     * @return
     */
    JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location);



    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(String source, String currentUserCode, UsCaseInfo usCaseInfo);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsCaseInfo usCaseInfo);

    /**
     * 经验推广台账
     * @param startTime
     * @param endTime
     * @param problemName
     * @param belongDepartmentCode
     * @return
     */
    JsonResponse queryApplication(Integer page,Integer rows,String source,String userCode,
                                  String startTime,String endTime,String problemName,String belongDepartmentCode,String state);





    List<Map<String, Object>> findAllApplication(Map<String, Object> resultMap);

    void exportParameter(Integer page,Integer rows,
                         String source,String userCode,String startTime,
                         String endTime,String problemName,String companyCode,
                         HttpServletResponse response,
                         HttpServletRequest request,
                         String state);

    /**
     * 根据pmINsId查询
     * @param pmInsId
     * @return
     */
    UsCaseInfo getFormDetailByPmInsId(String pmInsId);






    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 优秀案例数量
     * @return
     */
    List<Map<String,Object>> caseCount(Map<String, Object> resultMap);


    /**
     * 优秀案例数量
     * @return
     */
    List<Map<String,Object>> caseAllCount(Map<String, Object> resultMap);

    /**
     * 分页方法
     *
     * @param page
     * @param rows
     * @param resultList
     * @return
     */
   Page<List<Map<String, Object>>> listPage(Integer page, Integer rows, List<Map<String, Object>> resultList) ;


    List<CaseStatisticsVo> caseStatistics(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                          @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                          @RequestBody(required = false) Map<String, Object> resultMap);


    void caseProblemStatistics(  Map<String, Object> resultMap,
                                   HttpServletResponse response,
                                   HttpServletRequest request);


    List<CaseStatisticsVo> caseStatisticsOther(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                          @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                          @RequestBody(required = false) Map<String, Object> resultMap);


    JsonResponse updateCaseInfo(  UsCaseInfo caseInfo);


}
