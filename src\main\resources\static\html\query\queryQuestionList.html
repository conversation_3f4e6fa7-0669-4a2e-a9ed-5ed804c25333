<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>问题协调台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        var pageparam;
        getCurrent()
        var abol = false  //纪检身份
        abolFun()
        $(function () {
             pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usProblemInfo/problemStatistics", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "queryParams":{},
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {title: "单位", field: "companyName", width: 100, tooltip: true, align: "center"},
                            // {title: "上报问题数", field: "problemNums", width: 120, tooltip: true, align: "center"},
                            {title: "已解决问题数", field: "solveNums", width: 120, tooltip: true, align: "center"},
                            {title: "完成率", field: "completionRate", width: 50, tooltip: true, align: "center",
                            formatter: function (value, row, index) {
                                if(value == '100.00' || value == '100.0'){
                                    value = '100%'
                                }else if(value !== '0'){
                                    value = Number(value.replace('%', '')).toFixed(2) + '%'
                                }else{
                                    value = '0'
                                }
                                    var g = "<span class='check' index='" + index + "'>"+value+"</span>"; 
                                    return g
                                }
                            },
                           
                        ]
                    ]
                }
            };
            // if(web.currentUser.belongCompanyTypeDictValue=='02'){
            //     pageparam.listtable.queryParams = {
            //         companyName: '',
            //         companyCode: ''
                    
            //     }
            //     $('#companyName').combobox('setValue', web.currentUser.belongCompanyName);
            //     $("#taskTableQueryForm input[name=companyCode]").val(web.currentUser.belongCompanyCode);
            //     idReadonly('companyName')
            // }
            // if(web.currentUser.belongCompanyTypeDictValue=='03'){
            //     pageparam.listtable.queryParams = {
            //         companyName: '',
            //         companyCode: ''
            //     }
            //     $('#companyName').combobox('setValue', web.currentUser.belongCompanyNameParent);
            //     $("#taskTableQueryForm input[name=companyCode]").val(web.currentUser.belongCompanyCodeParent);
            //     idReadonly('companyName')
            // }
            loadGrid(pageparam);
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir + "action/usProblemInfo/exportProblemStatistics");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });
             //选择组织
             $(".chooseOrgs").on("click",function(){
                var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'C'};
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                var url=tourl('html/choose/chooseCompanyII.html',href);
                top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
            });
            if(web.currentUser.belongCompanyTypeDictValue == '01'||abol){
                $('.c02').show()
            }else{
                $('.c01').show()
            }
            //分公司人员新增接口做个判断，控制是否显示c03
            if(web.currentUser.belongCompanyTypeDictValue == '02'&&!abol){
                ajaxgeneral({
                    url: 'action/index/judgeUser',
                    contentType: 'application/json; charset=utf-8',
                    success: function (res) { 
                        if(!res.data.isAdmin){
                            $('.c03').hide()
                        }
                    }
                });
            }
        });

        function abolFun(){
            for(var i in web.currentUser.authRoles){
                if(web.currentUser.authRoles[i].authority == 'djfupt_001'){
                    return abol = true
                }
            }
        }
        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };
        //详情查看
        $(document).on('click', 'a.check', function () {
            var index = $(this).attr('index');
            var row = $('#taskTable').datagrid('getRows')[index];
            var param = {
                companyCode: row.companyCode
            }
            var url = tourl('html/query/queryQuestionListDialog.html', param)
            top.dialogP(url, window.name, '完成情况', 'groupCheck', true, 'maximized', 'maximized')
        })

         //选择组织
         window.chooseOrgs=function(data) {
            var names = [], codes = [],comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#taskTableQueryForm input[name=companyCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $('#companyName').combobox('setValue',names.join(","));
        };
        function companyFun(data){
            $("#taskTableQueryForm input[name=companyCode]").val(data.value);
        }
    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <!-- 传给后端  -->
        <input name="companyCode" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="120" align="right">上报时间</td>
                <td width="400">
                    <input id="startTime" name="startTime" type="text" class="easyui-datebox"
                        style="width:30%;height:32px;" validType="startDateCheck['endTime','startTime']"
                        data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="endTime" name="endTime" type="text" class="easyui-datebox" style="width:30%;height:32px;"
                        validType="endDateCheck['startTime','endTime']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>
                 <td width="90" align="right" class="c03">上报部门</td>
                 <td width="200" class="c01 c03 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" value="" />
                </td>

                <td width="200" class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>
                <td width="600">
                    <div class="w100">
                      
                        <a class="btn fr ml10 exporttable ">
                            <font>导出</font>
                        </a>
                        <a class="btn fr searchtable">
                            <font>查询</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>