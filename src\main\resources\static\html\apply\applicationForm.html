<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>党建指导员支撑服务平台申请单</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        function loadF() {
            $("#phone").val(web.currentUser.preferredMobile);
            $("#applyUser").val(web.currentUser.truename);
           /* $("#belongCompanyName").val(web.currentUser.belongCompanyName);
            $("#belongDepartmentName").val(web.currentUser.belongDepartmentName);*/
            $("#displayName").val(web.currentUser.authOrgs[0].displayName);
            $("#applyDate").val(getNow("yyyy-MM-dd hh:mm:ss"));
        };
        $(function () {
            if (!gps.location) {
                getCurrent(loadF);
            }
            var param = {
                "htmlName": "applicationForm",
                "formId": "applicationForm",
                "processNextCmd": "action/applicationForm/startSubmitProcess",
                "processDeleteCmd": "action/applicationForm/deleteProcess",
                "processDraftDeleteCmd": "action/applicationForm/deleteDraft",
            };
            loadProcess(param);
        });

        function getcallback() {
            if (gps.type && ((gps.type == "task" && gps.location && gps.location != "djfupt.start") || (gps.type != "task"))) {
                if ($("#drawFiles").val() == "") {
                    $(".drawFilesNo").show();
                }
            }

        }

        //流程下一步携带信息
        function nextBtnOther() {
            var type = $("#type").combobox("getValue");
            return {"processType": type};
        }

        //选择来源
        function sourceV(record) {
            if (record.description) $("#urgencyDegreeName").val(record.description);
        };

        //选择类别
        function workTypeV(record) {
            if (record.name) $("#typeName").val(record.name);
        };

        function onSubmit(data) {
            return true;
        };
    </script>
    <style>
        table.tabForm {
			border-collapse: collapse;
			border-spacing: 0;
			empty-cells: show;
			font-size: 15px;
			margin-top: 10px;
			table-layout: fixed;
		}

		input,
		textarea,
		select {
			font-size: 14px !important;
		}

		table.tabForm td.tit {
			background-color: #E8F8FF;
			text-align: center;
		}
        table.tabForm td{
            border-color: #e9e9e9;
        }
        .tabForm .col_r {
            width: 8px;
            display: inline-block;
        }
    </style>
</head>
<body class="body_page" style="padding-top:85px;">
<form id="applicationForm" method="post" formLocation="djfupt.start" noNextUserDecisionId="end"
      contentType="application/json; charset=utf-8" nextBtnOther="nextBtnOther()"
      cmd-select="action/applicationForm/getFormDetail" getcallback="getcallback()" onSubmit="onSubmit()">
    <div class="pageInfo">
        <div class="pageInfoD">
            <a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i><font>流转下一步</font></a>
            <a class="hide btn small fl mr15 saveDraft" onclick="formsubmit('applicationForm','action/applicationForm/saveDraft')"><i class="iconfont">&#xe646;</i><font>保存草稿</font></a>
            <a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i><font>流程跟踪</font></a>
            <a class="hide btn small fl mr15 abolish"><i class="iconfont">&#xe6ec;</i><font>废除草稿</font></a>
            <a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i><font>查看意见</font></a>
            <a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i><font>流程图</font></a>
            <a class="hide btn small fl mr15 formReset" onclick="formreset('applicationForm');"><i class="iconfont">&#xe646;</i><font>重置</font></a>
        </div>
    </div>
    <fieldset class="title">
        <legend><font>申请单信息</font><font class="ml15 f12 col_r">提示：1.带*为必填项。</font><font class="ml15 f12 col_r">2.IE8及IE9，请双击鼠标进行上传</font>
        </legend>
    </fieldset>
    <input id="id" name="id" type="hidden"/>
    <input id="pmInsId" name="pmInsId" type="hidden"/>
    <input id="typeName" name="typeName" type="hidden"/>
    <table border="1" cellpadding="0" class="tabForm" cellspacing="15" width="100%" bordercolor="#dedede">
        <tr>
            <td align="center" class="tit" width="100" >申&nbsp;&nbsp;&nbsp;&nbsp;请&nbsp;&nbsp;&nbsp;&nbsp;人<font class="col_r"></font></td>
            <td width="260">
                <input id="applyUser" name="applyUser" readonly="readonly" type="text" noReset="true"/>
            </td>
            <td align="center" class="tit" width="100" >申请人电话<font class="col_r"></font></td>
            <td width="260">
                <input id="phone" name="phone" type="text" readonly="readonly" noReset="true"/>
            </td>
            <td align="center" class="tit" width="100" >电子流类别<font class="col_r">*</font></td>
            <td width="260">
                <input id="type" name="type" class="easyui-combobox" required='required'
                       style="width: 100%; height: 32px;" data-options="
                    valueField: 'value',
                    ischooseall:true,
                    panelHeight:'auto',
                    textField: 'name',
                    editable:false,
                    queryParams:{'dictType':'applyType'},
                    url: web.rootdir+'action/queryDictValue/queryByType',onSelect:workTypeV"/>
            </td>
        </tr>
        <tr>
            <td align="center" class="tit" width="100" >申请人单位<font class="col_r"></font></td>
            <td colspan="5">
                <input id="displayName" name="displayName" type="text" readonly="readonly" noReset="true"/>
            </td>
        </tr>
        <tr>
            <td align="center" class="tit" width="100" >标&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;题<font class="col_r">*</font></td>
            <td colspan="5">
                <input id="title" name="title" class="easyui-validatebox" required="true" type="text"/>
            </td>
        </tr>
        <tr>
            <td align="center" class="tit" valign="middle" >申&nbsp;请&nbsp;原&nbsp;因<font class="col_r">*</font></td>
            <td colspan="5">
                <textarea id="applyContent" name="applyContent" class="easyui-validatebox" maxlength="2000"
                          validType="maxLength[2000,'applyContentTip']" style="min-height:100px;"
                          required="true"></textarea>
                <span class="applyContentTip"></span>
            </td>
        </tr>
        <tr>
            <td align="center" class="tit" width="100" >申请单附件<font class="col_r"></font></td>
            <td colspan="5" valign="top"><!-- required="true" mesInfo="请上传工作单附件"-->
                <span class="drawFilesNo lh32 hide">无</span>
                <input id="drawFiles" name="drawFiles" type="text" file="true" mulaccept="true"
                       class="cselectorImageUpload" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                       href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
            </td>
        </tr>
    </table>
</form>
</body>
</html>
