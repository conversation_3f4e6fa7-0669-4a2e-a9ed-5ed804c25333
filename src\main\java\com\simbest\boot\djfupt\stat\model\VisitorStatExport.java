package com.simbest.boot.djfupt.stat.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <strong>Title : VisitorStatExport</strong><br>
 * <strong>Description : 访客统计数据导出模型</strong><br>
 * <strong>Create on : 2025-01-27</strong><br>
 */
@Data
public class VisitorStatExport {

    @ExcelVOAttribute(name = "会话标题", column = "A")
    @ApiModelProperty(value = "会话标题")
    private String title;

    @ExcelVOAttribute(name = "用户名", column = "B")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @ExcelVOAttribute(name = "所属单位", column = "C")
    @ApiModelProperty(value = "所属单位")
    private String belongCompanyName;

    @ExcelVOAttribute(name = "所属部门", column = "D")
    @ApiModelProperty(value = "所属部门")
    private String belongDepartmentName;

    @ExcelVOAttribute(name = "消息数", column = "E")
    @ApiModelProperty(value = "消息数")
    private String messageCount;

    @ExcelVOAttribute(name = "创建时间", column = "F")
    @ApiModelProperty(value = "创建时间")
    private String created_at;

    @ExcelVOAttribute(name = "更新时间", column = "G")
    @ApiModelProperty(value = "更新时间")
    private String updated_at;
}
