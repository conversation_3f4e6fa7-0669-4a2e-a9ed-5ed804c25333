package com.simbest.boot.djfupt.caseinfo.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.mzlion.easyokhttp.HttpClient;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.common.BpsRedisUtil;
import com.simbest.boot.bps.process.bussiness.mapper.ActBusinessStatusMapper;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.mapper.WfProcessInstModelMapper;
import com.simbest.boot.bps.process.listener.mapper.WfWorkItemModelMapper;
import com.simbest.boot.bps.process.listener.model.WFProcessInstModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfProcessInstModelService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.repository.WfWorkItemRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.caseinfo.model.CaseStatisticsVo;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseExcel;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.common.repository.ActBusinessStatusRepository;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsproblemIbnfoExcel;
import com.simbest.boot.djfupt.problem.repository.UsProblemInfoRepository;
import com.simbest.boot.djfupt.util.*;
import com.simbest.boot.djfupt.wfquey.repository.DictValueRepository;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.CustomBeanUtil;
import com.simbest.boot.util.MapUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service(value = "usCaseInfoService")
@SuppressWarnings("ALL")
public class UsCaseInfoServiceImpl extends LogicService<UsCaseInfo, String> implements IUsCaseInfoService {

    private UsCaseInfoRepository usCaseInfoRepository;

    @Autowired
    public UsCaseInfoServiceImpl(UsCaseInfoRepository usCaseInfoRepository) {
        super(usCaseInfoRepository);
        this.usCaseInfoRepository = usCaseInfoRepository;
    }

    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;


    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private WfWorkItemModelMapper workItemModelMapper;

    @Autowired
    private BpsRedisUtil bpsRedisUtil;

    @Autowired
    private ActBusinessStatusMapper actBusinessStatusMapper;

    @Autowired
    private IWfProcessInstModelService wfProcessInstModelService;

    @Autowired
    private WfProcessInstModelMapper wfProcessInstModelMapper;

    @Autowired
    private IUsCaseInfoService usCaseInfoService;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private UsProblemInfoRepository usProblemInfoRepository;


    String param1 = "/action/usCaseInfoService";


    @Autowired
    private DictValueRepository dictValueRepository;

    @Autowired
    private AppConfig config;

    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private ActBusinessStatusRepository actBusinessStatusRepository;


    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param copyLocation    抄送环节
     * @param bodyParam
     * @param formId          表单id
     * @return
     */
    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId) {
        JsonResponse jsonResponse = new JsonResponse();
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                UsCaseInfo form = null;
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    form = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), UsCaseInfo.class);
                } else {
                    if (StringUtils.isNotEmpty(formId) && "MOBILE".equals(source)) {
                        form = this.findById(formId);
                    }
                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }
                tempList = (List<Map<String, String>>) map.get("copyNextUserNames");
                String copyNextUserNames = "";
                if (null != tempList && !tempList.isEmpty()) {
                    for (Map<String, String> mapObj : tempList) {
                        String copyName = mapObj.get("value");
                        if (!org.springframework.util.StringUtils.isEmpty(copyName)) {
                            copyNextUserNames = copyName + "," + copyNextUserNames;
                        }
                    }
                }
                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;
                String copyMessage = map.get("copyNextUserName") != null ? map.get("message").toString() : null;

                /**如果表单的id不为空，则不是起草环节，走审批流程**/
                if (form != null && form.getId() != null && StringUtils.isNotEmpty(workItemId)) {
                    //起草环节
                    jsonResponse = saveSubmitTask(form, workItemId, outcome, message, nextUserName, location, copyLocation, copyMessage, copyNextUserNames, notificationId, source, currentUserCode);
                } else {
                    //审批流程环节
                    jsonResponse = startProcess(form, nextUserName, outcome, message, source, currentUserCode);//创建提交
                }
            }
        }
        return jsonResponse;
    }

    /**
     * 审批提交
     *
     * @param usCaseInfo        表单
     * @param workItemId        活动实例id
     * @param outcome           连线规则
     * @param message           审批意见
     * @param nextUserName      审批人
     * @param location          当前环节
     * @param copyLocation      抄送下一环节
     * @param copyMessage       抄送意见
     * @param copyNextUserNames 抄送人员
     * @param notificationId    待阅id
     * @param source            来源
     * @param userCode          当前用户OA账号
     * @return
     */

    public JsonResponse saveSubmitTask(UsCaseInfo usCaseInfo, String workItemId, String outcome, String message, String nextUserName, String location, String copyLocation, String copyMessage, String copyNextUserNames, String notificationId, String source, String userCode) {
        log.debug("起草接口----------saveSubmitTask---------->" + usCaseInfo.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "applicationForm=" + usCaseInfo.toString() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + copyLocation + ",copyMessage"
                + copyMessage + ",copyNextUserNames=" + copyNextUserNames + ",notificationId=" + notificationId + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            String pmInsId = usCaseInfo.getPmInsId();
            operateLog.setBussinessKey(pmInsId);
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**相关流转审批操作**/
            if (pmInstence != null) {
                //省公司的审批通过的台账全省的人看
                if (StringUtils.isNotEmpty(pmInsId)) {
                    List<WfWorkItemModel> wfWorkItem = wfWorkItemRepository.findWfWorkItemDone(pmInsId);
                    if (CollectionUtil.isNotEmpty(wfWorkItem)) {
                        location = wfWorkItem.get(0).getActivityDefId();
                    }

                }
                if ("djfupt.proAdminCheck".equals(location)) {
                    usCaseInfo.setIsProvince("1");
                    usCaseInfoService.update(usCaseInfo);
                }
                /**获取用户**/
                IUser user = SecurityUtils.getCurrentUser();
                /**审批流转**/
                if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                    if ((!"end".equals(outcome) && StringUtils.isNotEmpty(nextUserName)) || "end".equals(outcome)) {
                        ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence);
                    } else {
                        ret = 0;
                        operateLog.setErrorMsg("审批人不能为空");
                        return JsonResponse.fail("审批人不能为空");
                    }
                }
                /**相关退回修改后操作**/    // 积极案例上报暂无退回修改
                if (Constants.ACTIVITY_START.equals(location) && StringUtils.isNotEmpty(workItemId)) {
                    JsonResponse upadeResult = this.updateDate(pmInstence, usCaseInfo);
                    if (upadeResult != null) {
                        return upadeResult;
                    }
                }
                if (outcome.equals("end") && location.equals("djfupt.start")) {
                    actBusinessStatusRepository.updateCurrDate(usCaseInfo.getPmInsId());

                }

            } else {
                operateLog.setErrorMsg("请联系管理员，主数据查找异常！pmInsId = " + pmInsId);
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
            /**提醒流转下一步信息**/
            String showMessage = this.getTemplate(nextUserName);
            return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        }
    }


    /**
     * 提交起草流程
     *
     * @param usCaseInfo   割接计划表单
     * @param nextUserName 审批人
     * @param outcome      连线规则
     * @param message      审批意见
     * @param source       来源
     * @param userCode     当前用户
     * @return
     */
    public JsonResponse startProcess(UsCaseInfo usCaseInfo, String nextUserName, String outcome, String message, String source, String userCode) {
        log.debug("起草接口----------startProcess---------->" + usCaseInfo.toString());
        long ret = 0;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",userCode=" + userCode + ",applicationForm=" + usCaseInfo.toString() + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser iuser = SecurityUtils.getCurrentUser();
            /**校验表单和下一步审批人是否为空**/
            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                if (StringUtils.isNotEmpty(nextUserName)) {
                    /**获取登录人所在公司应启动的流程**/

                    Map<String, String> map = commonService.getProcessMap(usCaseInfo.getType());
                    String processDefId = map.get("processName");
                    String processType = map.get("processType");
                    if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                        boolean flag = false;
                        UsPmInstence usPmInstence = new UsPmInstence();
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        if (StringUtils.isEmpty(usCaseInfo.getId())) {
                            flag = this.savePlanTask(usCaseInfo, usPmInstence);
                        } else {
                            usPmInstence = usPmInstenceService.findByPmInsId(usCaseInfo.getPmInsId());
                            flag = true;
                        }
                        /**启动发起流程**/
                        if (flag) {
                            Map<String, Object> variables = Maps.newHashMap();
                            String currentUserCode = iuser.getUsername();
                            String currentUserName = iuser.getTruename();
                            variables.put("inputUserId", currentUserCode);
                            variables.put("receiptId", usPmInstence.getId());
                            variables.put("title", usPmInstence.getPmInsTitle());
                            variables.put("code", usPmInstence.getPmInsId());
                            variables.put("currentUserCode", currentUserCode);
                            variables.put("activityDefID", Constants.ACTIVITY_START);
                            variables.put("appCode", Constants.APP_CODE);
                            //第一个参数为流程定义名称
                            Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                            if (workItemId != 0) {
                                /**提交表单审批处理**/
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                operateLog.setErrorMsg("启动流程失败");
                                JsonResponse.fail(null, "启动流程失败");
                            }
                        } else {
                            operateLog.setErrorMsg("保存割接计划失败");
                            JsonResponse.fail(null, "保存割接计划失败");
                        }
                    } else {
                        operateLog.setErrorMsg("获取流程失败");
                        JsonResponse.fail(null, "操作失败，获取流程失败!");
                    }
                } else {
                    operateLog.setErrorMsg("表单为空或审批人为空");
                    JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
                }
            } else {

                Map<String, String> map = commonService.getProcessMap(usCaseInfo.getType());
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                    boolean flag = false;
                    UsPmInstence usPmInstence = new UsPmInstence();
                    usPmInstence.setPmInsType(processType);
                    /**保存业务数据**/
                    if (StringUtils.isEmpty(usCaseInfo.getId())) {
                        flag = this.savePlanTask(usCaseInfo, usPmInstence);
                    } else {
                        usPmInstence = usPmInstenceService.findByPmInsId(usCaseInfo.getPmInsId());
                        flag = true;
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);
                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {

                            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                                /**提交表单审批处理**/
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);

                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存割接计划失败");
                        JsonResponse.fail(null, "保存割接计划失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
    }

    /**
     * 流转下一步
     *
     * @param workItemID      活动实例id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人姓名
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param location        当前所处环节
     * @param message         审批意见
     * @param pmInstence      主单据
     * @return
     */
    private long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence) {
        long ret;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            workItemService.submitApprovalMsg(workItemID, message);
            //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
            ret = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }


    /**
     * 更新表单、主数据以及流程操作
     *
     * @param pmInstence 主数据
     * @param usCaseInfo 表单
     * @return
     */
    private JsonResponse updateDate(UsPmInstence pmInstence, UsCaseInfo usCaseInfo) {
        String title = "经验推广上报";
        JsonResponse jsonResponse = null;
        try {
            int result = processInstanceService.updateTitleByBusinessKey(pmInstence.getId(), title);
            if (result > 0) {
                pmInstence.setPmInsTitle(title);
                UsPmInstence pmInstence1 = usPmInstenceService.update(pmInstence);//更新主数据标题
                if (pmInstence1 != null) {
                    this.updateFileByPmInsId(usCaseInfo, pmInstence.getPmInsType());//更新附件
                    UsCaseInfo form1 = this.update(usCaseInfo);//更新表单
                    if (form1 == null) {
                        jsonResponse = JsonResponse.fail(usCaseInfo, "表单更新失败");
                    } else {
                        //更新流程业务状态
                        updateActBusinessStatus(pmInstence1.getPmInsId(), title);
                    }
                } else {
                    jsonResponse = JsonResponse.fail(usCaseInfo, "主数据标题更新失败");
                }
            } else {
                jsonResponse = JsonResponse.fail(usCaseInfo, "流程相关标题更新失败");
            }
        } catch (Exception e) {
            log.debug("更新退回修改数据-------updateDate" + e.toString());
            jsonResponse = JsonResponse.fail(usCaseInfo, "更新异常");
        } finally {
            return jsonResponse;
        }
    }


    /**
     * 更新附件
     *
     * @param usCaseInfo 表单
     */
    private void updateFileByPmInsId(UsCaseInfo usCaseInfo, String pmInsType) {
        List<SysFile> files = usCaseInfo.getDrawFiles();
        String pmInsId = usCaseInfo.getPmInsId();
        try {
            if (files != null && !files.isEmpty()) {
                for (SysFile file : files) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据主单据id，更新流程业务状态单据标题
     *
     * @param pmInsId 主单据id
     * @param title   单据标题
     */
    private void updateActBusinessStatus(String pmInsId, String title) {
        //根据主单据id和运行工作项状态读取到当前运行的workItemModel
        Specification<WfWorkItemModel> wfSpec = Specifications
                .<WfWorkItemModel>and()
                .eq("receiptCode", pmInsId)
                .build();
        List<WfWorkItemModel> workItemModelList = workItemModelMapper.findAllActive(wfSpec);
        if (null != workItemModelList && workItemModelList.size() > 0) {
            //只需要获取到流程实例id,所以这里直接get(0)
            WfWorkItemModel wfWorkItemModel = workItemModelList.get(0);
            //根据流程实例id读取到流程业务信息actBusinessStatus
            ActBusinessStatus actBusinessStatus = bpsRedisUtil.getFromRedis(bpsRedisUtil.PROCESS_ACT_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), ActBusinessStatus.class);
            if (actBusinessStatus == null) {
                actBusinessStatus = statusService.getByProcessInst(wfWorkItemModel.getProcessInstId());
            }
            if (null != actBusinessStatus) {
                //读取到业务流程数据以后，比较标题字段是否有变化，如果有，则进行变更标题操作
                if (!title.equals(actBusinessStatus.getReceiptTitle())) {
                    //更新act表工单标题
                    actBusinessStatus.setReceiptTitle(title);
                    actBusinessStatusMapper.saveAndFlush(actBusinessStatus);
                    bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_ACT_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), actBusinessStatus);
                    //更新workItemModel表中的工单标题
                    for (WfWorkItemModel workItemModel : workItemModelList) {
                        workItemModel.setReceiptTitle(title);
                        workItemModelMapper.saveAndFlush(workItemModel);
                        bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_INST_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), workItemModel);
                    }
                    //更新wfProcessInstModel
                    Specification<WFProcessInstModel> ProSpec = Specifications
                            .<WFProcessInstModel>and()
                            .eq("processInstId", String.valueOf(wfWorkItemModel.getProcessInstId()))
                            .build();
                    WFProcessInstModel wfProcessInstModel = wfProcessInstModelService.findOne(ProSpec);
                    if (null != wfProcessInstModelService) {
                        wfProcessInstModel.setReceiptTitle(title);
                        wfProcessInstModelMapper.saveAndFlush(wfProcessInstModel);
                        bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_WORK_ITERM.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), wfProcessInstModel);
                    }
                }
            }
        }
    }

    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(nextUserName)) {
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                }
            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }

        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }


    /**
     * 保存业务数据
     *
     * @param usCaseInfo   表单
     * @param usPmInstence 主单据
     * @return
     * @throws Exception
     */
    boolean savePlanTask(UsCaseInfo usCaseInfo, UsPmInstence usPmInstence) throws Exception {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            String planId = usCaseInfo.getId();
            if (StringUtils.isEmpty(planId) || Constants.STR_NULL.equals(planId)) {
                String pmInsId = usPmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                //   String title = usCaseInfo.getTitle();
                usPmInstence.setPmInsId(pmInsId);
                String currentStr = DateUtil.getCurrentStr();
                String count = usPmInstenceService.getCounts(usPmInstence.getPmInsType(), currentStr);
                String workCode = OrderNumberCreate.generateNumber(usPmInstence.getPmInsType(), count);//编号生成
                usPmInstence.setPmInsTitle("优秀案例上报");
                usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                usPmInstenceService.saveData(usPmInstence);
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (StringUtils.isNotEmpty(usPmId)) {
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usCaseInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usCaseInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usCaseInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usCaseInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    usCaseInfo.setBelongCompanyCodeParent(iuser.getBelongCompanyCodeParent());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usCaseInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                    usCaseInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usCaseInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usCaseInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                }
                usCaseInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usCaseInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usCaseInfo.setBelongOrgName(iuser.getBelongOrgName());
                usCaseInfo.setPmInsId(usPmInstence.getPmInsId());
                this.updateFileByPmInsId(usCaseInfo, usPmInstence.getPmInsType());//更新附件
                UsCaseInfo usCaseInfo1 = this.insert(usCaseInfo);
                if (usCaseInfo1 != null) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }


    /**
     * 获取申请表单
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @return
     */
    @Override
    public JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location) {
        UsCaseInfo usCaseInfo = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source +
                ",userCode=" + userCode + ",location=" + location + ",pmInsId=" + pmInsId;
        operateLog.setInterfaceParam(params);
        try {
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**点击办理查看详情**/
            if (null != processInstId) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    String id = actBusinessStatus.getBusinessKey();
                    usCaseInfo = usCaseInfoRepository.getFromDetail(id);
                    if (usCaseInfo != null) {
                        operateLog.setBussinessKey(usCaseInfo.getPmInsId());
                        //获取附件
                        List<SysFile> list = fileExtendService.getPartFile(usCaseInfo.getPmInsId(), "1");
                        usCaseInfo.setDrawFiles(list);
                    }
                }
            } else {
                //草稿箱详情查询
                usCaseInfo = usCaseInfoRepository.getFormDetailByPmInsId(pmInsId);
                //获取附件
                List<SysFile> list = fileExtendService.getPartFile(usCaseInfo.getPmInsId(), "1");
                usCaseInfo.setDrawFiles(list);
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usCaseInfo);
    }


    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(String source, String currentUserCode, UsCaseInfo usCaseInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "carRepair=" + usCaseInfo.toString() + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 保存草稿
            if (StringUtils.isEmpty(usCaseInfo.getId())) {
                // 保存业务单据信息
                this.saveBusinessData(usCaseInfo);
            }
            // 更新草稿
            else {
                //更新业务单据信息
                this.updateBusinessData(usCaseInfo);
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usCaseInfo, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 保存业务单据信息
     *
     * @param usCaseInfo
     * @return
     */
    private Map<String, Object> saveBusinessData(UsCaseInfo usCaseInfo) {
        Map<String, Object> result = Maps.newHashMap();
        IUser iuser = SecurityUtils.getCurrentUser();
        UsPmInstence usPmInstence = new UsPmInstence();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StringUtils.isEmpty(usCaseInfo.getId())) {
                String processName = "com.djfupt.flow.caseReport";
                String processType = "A";
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    String pmInsId = processType + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                    String title = "经验推广填报";
                    usPmInstence.setPmInsId(pmInsId);
                    usPmInstence.setPmInsTitle(title);
                    usPmInstence.setPmInsType(processType);
                    if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                        usPmInstence.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                        usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                        usPmInstence.setBelongDepartmentName(iuser.getBelongCompanyName());
                        usPmInstence.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    }
                    if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                        usPmInstence.setBelongCompanyName(iuser.getBelongCompanyName());
                        usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                        usPmInstence.setBelongDepartmentName(iuser.getBelongDepartmentName());
                        usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    }
                    usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                    usPmInstence.setBelongOrgName(iuser.getBelongOrgName());
                    usPmInstenceService.insert(usPmInstence);
                }
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (StringUtils.isNotEmpty(usPmId)) {
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usCaseInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usCaseInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usCaseInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usCaseInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usCaseInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                    usCaseInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usCaseInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usCaseInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                }
                usCaseInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usCaseInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usCaseInfo.setBelongOrgName(iuser.getBelongOrgName());
                usCaseInfo.setPmInsId(usPmInstence.getPmInsId());
                this.updateFileByPmInsId(usCaseInfo, usPmInstence.getPmInsType());//更新附件
                usCaseInfoService.insert(usCaseInfo);
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return result;
        }
        return result;
    }


    /**
     * 修改业务单据信息
     *
     * @param usCaseInfo
     * @return
     */
    private void updateBusinessData(UsCaseInfo usCaseInfo) {
        UsCaseInfo usCase = this.findById(usCaseInfo.getId());
        CustomBeanUtil.copyPropertiesIgnoreNull(usCaseInfo, usCase);
        Map<String, String> map = commonService.getProcessMap(usCaseInfo.getType());
        String processType = map.get("processType");
        this.updateFileByPmInsId(usCaseInfo, processType);//更新附件
        // 更新表单基础数据
        this.update(usCase);
    }


    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsCaseInfo usCaseInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);
            usPmInstenceService.delete(pmInstence);
            // 删除业务单据数据
            this.delete(usCaseInfo);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }


    /**
     * @param startTime            开始时间
     * @param endTime              结束时间
     * @param problemName          优秀案例名称
     * @param belongDepartmentCode
     * @return
     */

    public Page<List<Map<String, Object>>> conditionQuery(Integer page,
                                                          Integer rows,
                                                          String source,
                                                          String userCodeString,
                                                          String startTime,
                                                          String endTime,
                                                          String problemName,
                                                          String belongDepartmentCode,
                                                          String state
    ) {
        Page<List<Map<String, Object>>> applicationFormPage = null;
        try {

            //获取当前当前月份
            DateFormat df = new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            String treatTime = df.format(calendar.getTime());
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer("   select t.*" +
                    "  from US_CASE_INFO t, act_business_status act" +
                    " where t.enabled = 1" +
                    "   and act.enabled = 1" +
                    "   and t.pm_ins_id = act.receipt_code" +
                    "   and t.is_province='1'");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and t.created_time > :startTime ");
                map.put("startTime", startTime);
                sql.append(" and t.created_time < :endTime ");
                map.put("endTime", endTime);
            }

            if (StringUtils.isNotEmpty(state)) {
                if (state.equals("7")) {
                    sql.append(" and   act.current_state   =:state  ");
                    map.put("state", state);
                } else {
                    sql.append(" and   act.current_state   !=7  ");
                }

            } else {
                sql.append(" and   act.current_state   =7  ");
            }

            if (StringUtils.isNotEmpty(problemName)) {
                sql.append(" and   t.problem_name    like concat( concat('%',:problemName),'%')  ");
                map.put("problemName", problemName);
            }
            if (StringUtils.isNotEmpty(belongDepartmentCode)) {
                //查出当前组织下面所有数据
                List<Object> code = new ArrayList<>();


                Map<String, String> sql1 = new HashMap<>();
                sql1.put("firstParam", belongDepartmentCode);
                List<Map<String, Object>> query = this.uumsSelectBySql(sql1, "djfupt_001");
                if (query.size() > 0) {
                    for (Map<String, Object> map1 : query) {
                        code.add(map1.get("BELONG_DEPARTMENT_CODE"));
                    }
                }

                //     List<String> departmentCode = usCaseInfoRepository.findALlBydepartMentCode(belongDepartmentCode);
                if (code.size() > 0) {
                    sql.append("and   t.belong_department_code  in : departmentCode ");
                    map.put("departmentCodes", code);
                }

            }
            sql.append(" order by t.created_time desc");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            return listPage(page, rows, list);
        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        return applicationFormPage;
    }


    /**
     * @param page     页码
     * @param rows     数量
     * @param source   来源
     * @param userCode 用户
     * @return
     */
    @Override
    public JsonResponse queryApplication(Integer page,
                                         Integer rows,
                                         String source,
                                         String userCode,
                                         String startTime,
                                         String endTime,
                                         String problemName,
                                         String belongDepartmentCode,
                                         String state
    ) {
        Page<List<Map<String, Object>>> forms = null;
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/conditionQuer";
        String params = "pageindex=" + page + ",pagesize=" + rows + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            operateLogTool.operationSource(source, userCode);
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询详单列表**/
            forms = this.conditionQuery(page, rows, source, userCode, startTime, endTime, problemName, belongDepartmentCode, state);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(forms);
    }

    /**
     * 经验推广台账
     *
     * @param resultMap
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllApplication(Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");
        String problemName = cn.hutool.core.map.MapUtil.getStr(resultMap, "problemName");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");
        String state = cn.hutool.core.map.MapUtil.getStr(resultMap, "state");
        String questionMode = cn.hutool.core.map.MapUtil.getStr(resultMap, "questionMode");
        String belongDepartmentCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");
        //获取当前当前月份
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        String treatTime = df.format(calendar.getTime());
        Map<String, Object> map = CollectionUtil.newHashMap();
        StringBuffer sql = new StringBuffer("   select t.*, act.current_state, act.PROCESS_INST_ID" +
                "  from US_CASE_INFO t, act_business_status act" +
                " where t.enabled = 1" +
                "   and act.enabled = 1  " +
                "   and t.pm_ins_id = act.receipt_code   " +

                "   and (select count(0)" +
                "          from wf_workitem_model wks" +
                "         where wks.receipt_code = t.pm_ins_id" +
                "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                "           and wks.enabled = 1) > 0");

        if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
            sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
            map.put("startTime", startTime);
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
            map.put("endTime", endTime);
        }
        if (StringUtils.isNotEmpty(problemName)) {
            sql.append(" and   t.problem_name    like concat( concat('%',:problemName),'%')  ");
            map.put("problemName", problemName);
        }
        if (StringUtils.isNotEmpty(state)) {
            if (state.equals("7")) {
                sql.append(" and   act.current_state   =:state  ");
                map.put("state", state);
            } else {
                sql.append(" and   act.current_state   !=7  ");
                sql.append(" and   act.current_state   !=8  ");
            }

        } else {
            sql.append(" and   act.current_state   =7  ");
        }

        if (StringUtils.isEmpty(questionMode)) {
            questionMode = "0";

        }


        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));


        if (StringUtils.isNotEmpty(belongDepartmentCode)) {

            sql.append(" and (t.belong_org_code = :companyCode or t.belong_company_code = :companyCode   ) ");
            map.put("companyCode", belongDepartmentCode);

        } else if (isRoleAdmin) {

            sql.append(" and t.belong_department_code=:companyCode ");
            map.put("companyCode", SecurityUtils.getCurrentUser().getBelongDepartmentCode());
        } else {


            if (StringUtils.isNotEmpty(questionMode) && questionMode.equals("1")) {
                //如果为省公司管理员  执行一级查询
                List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
                //判断是否省公司管理员
                boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
                // or


                //如果不是省公司管理员执行五级查询，不是的话默认查看全部
                if (!isAdmin) {
                    String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                    switch (queryLevel) {
                        case DataPermissionConstants.QUERY_LEVEL_FIRST:
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_SECOND:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_THIRD:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_FOUR:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
                            break;
                        default:
                            sql.append(" and  t.creator =:username  ");
                            map.put("username", SecurityUtils.getCurrentUser().getUsername());
                            break;
                    }
                }
            }


        }

        sql.append(" and t.question_mode = :questionMode");
        map.put("questionMode", questionMode);

        sql.append(" order by t.created_time desc");
        resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
        if (resultList.size() > 0) {
            for (Map<String, Object> map1 : resultList) {
                map1.put("TITLE", map1.get("PROBLEM_NAME"));
                if (!map1.get("CURRENT_STATE").toString().equals("7")) {
                    Map<String, Object> map2 = usProblemInfoRepository.findAllByReceiptCodeAndCurrentState(map1.get("PM_INS_ID").toString());
                    map1.put("HANDLING_LINK", map2.get("WORK_ITEM_NAME"));
                    map1.put("ACTIVITY_DEF_ID", map2.get("ACTIVITY_DEF_ID"));
                }
            }
        }

        resultList = FormatTool.formatConversion(resultList);//驼峰转换
        return resultList;
    }

    /**
     * @param resultMap
     * @return
     */

    public List<Map<String, Object>> findAllFenInFo(Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        IUser iuser = SecurityUtils.getCurrentUser();
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");
        String problemName = cn.hutool.core.map.MapUtil.getStr(resultMap, "problemName");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");
        String belongDepartmentCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "belongDepartmentCode");
        //获取当前当前月份
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        String treatTime = df.format(calendar.getTime());
        Map<String, Object> map = CollectionUtil.newHashMap();
        StringBuffer sql = new StringBuffer("   select t.*,act.current_state,act.PROCESS_INST_ID" +
                "  from US_CASE_INFO t, act_business_status act" +
                " where t.enabled = 1" +
                "   and act.enabled = 1" +
                "   and t.pm_ins_id = act.receipt_code" +
                "   and t.is_province is null");
        if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
            sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
            map.put("startTime", startTime);
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
            map.put("endTime", endTime);
        } else {
            sql.append(" and  to_char(t.created_time,'yyyy-mm' )=:treatTime ");
            map.put("treatTime", treatTime);
        }
        if (StringUtils.isNotEmpty(problemName)) {
            sql.append(" and   t.problem_name    like concat( concat('%',:problemName),'%')  ");
            map.put("problemName", problemName);
        }
        if (StringUtils.isNotEmpty(belongDepartmentCode)) {
            sql.append(" and t.belong_department_code=:belongDepartmentCode ");
            map.put("belongDepartmentCode", belongDepartmentCode);
        }
        sql.append(" order by t.created_time desc");
        List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
        for (Map<String, Object> stringObjectMap : list) {
            if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                if (null != stringObjectMap.get("belong_company_code")) {
                    String companyCode = stringObjectMap.get("belong_company_code").toString();
                    if (iuser.getBelongCompanyCodeParent().equals(companyCode)) {
                        resultList.add(stringObjectMap);
                    }
                }
            } else {
                if (null != stringObjectMap.get("belong_company_code")) {
                    String companyCode = stringObjectMap.get("belong_company_code").toString();
                    if (iuser.getBelongCompanyCode().equals(companyCode)) {
                        resultList.add(stringObjectMap);
                    }
                }
            }

        }
        return resultList;
    }


    /**
     * 分页方法
     *
     * @param page
     * @param rows
     * @param resultList
     * @return
     */
    public Page<List<Map<String, Object>>> listPage(Integer page, Integer rows, List<Map<String, Object>> resultList) {
        if (resultList != null && resultList.size() > 0) {
            long size = resultList.size();
            Pageable pageable = getPageable(page, rows, null, null);
            List<Map<String, Object>> listPart = PageTool.pagination(resultList, page, rows);
            return new PageImpl(listPart, pageable, size);
        }
        return null;
    }

    @Override
    public List<CaseStatisticsVo> caseStatistics(int page, int rows, Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//
        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//
        if (StringUtils.isNotEmpty(startTime)) {
            startTime = startTime + " 00:00:01";
        }
        if (StringUtils.isNotEmpty(endTime)) {
            endTime = endTime + " 00:00:01";
        }

        //查出18个分公司  按照通讯录排序
        List<SysDictValue> sysDictValues = new ArrayList<>();
        if (StringUtils.isEmpty(companyName)) {
            sysDictValues = dictValueRepository.findDictValue("company");
        } else {
            sysDictValues = dictValueRepository.findAllByNameAndType("company", companyName);
        }

        List<ProblemStatisticsVo> problemStatisticsVos = new ArrayList<>();
        List<CaseStatisticsVo> caseStatisticsVos = new ArrayList<>();
        if (sysDictValues.size() > 0) {
            for (SysDictValue sysDictValue : sysDictValues) {

                //获取当前当前月份
                DateFormat df = new SimpleDateFormat("yyyy-MM");
                Calendar calendar = Calendar.getInstance();
                String treatTime = df.format(calendar.getTime());
                Map<String, Object> map = CollectionUtil.newHashMap();
                StringBuffer sql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                        "  from US_CASE_INFO t, act_business_status act" +
                        " where t.enabled = 1" +
                        "   and act.enabled = 1" +
                        "   and act.current_state = 7  " +
                        "   and t.pm_ins_id = act.receipt_code" +
                        "   and t.question_mode ='0'" +
                        " and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode )" +
                        "   and (select count(0)" +
                        "          from wf_workitem_model wks" +
                        "         where wks.receipt_code = t.pm_ins_id" +
                        "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                        "           and wks.enabled = 1) > 0  ");
                map.put("companyCode", sysDictValue.getValue());

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
                    map.put("startTime", startTime);
                    sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
                    map.put("endTime", endTime);
                }
                sql.append(" order by t.created_time desc");
                List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
                CaseStatisticsVo caseStatisticsVo = new CaseStatisticsVo();
                caseStatisticsVo.setCompanyCode(sysDictValue.getValue());
                caseStatisticsVo.setCompanyName(sysDictValue.getName());
                caseStatisticsVo.setNums(resultList.size());
                caseStatisticsVos.add(caseStatisticsVo);
            }
        }
        return caseStatisticsVos;
    }


    @Override
    public List<CaseStatisticsVo> caseStatisticsOther(int page, int rows, Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//
        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//
        String companyCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");//
        if (StringUtils.isNotEmpty(startTime)) {
            startTime = startTime + " 00:00:01";
        }
        if (StringUtils.isNotEmpty(endTime)) {
            endTime = endTime + " 00:00:01";
        }

        List<SysDictValue> sysDictValues = new ArrayList<>();
        //查出当前下的组织
        List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
        int i = 0;
        if (simpleOrgList.size() > 0) {
            for (SimpleOrg simpleOrg : simpleOrgList) {
                if (i == 0) {
                    simpleOrg.setOrgName(simpleOrg.getDisplayName());

                }
                if (StringUtils.isNotEmpty(companyCode)) {
                    if (!simpleOrg.getOrgCode().equals(companyCode)) {
                        continue;
                    }
                }
                if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                    continue;
                }
                SysDictValue sysDictValue = new SysDictValue();
                sysDictValue.setValue(simpleOrg.getOrgCode());
                sysDictValue.setName(simpleOrg.getOrgName());
                sysDictValues.add(sysDictValue);
                i++;
            }
        }

        List<ProblemStatisticsVo> problemStatisticsVos = new ArrayList<>();
        List<CaseStatisticsVo> caseStatisticsVos = new ArrayList<>();
        if (sysDictValues.size() > 0) {
            int is = 0;
            for (SysDictValue sysDictValue : sysDictValues) {
                if (is == 0) {
                    //获取当前当前月份
                    DateFormat df = new SimpleDateFormat("yyyy-MM");
                    Calendar calendar = Calendar.getInstance();
                    String treatTime = df.format(calendar.getTime());
                    Map<String, Object> map = CollectionUtil.newHashMap();
                    StringBuffer sql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and act.current_state = 7  " +
                            "   and t.pm_ins_id = act.receipt_code" +
                            "   and t.question_mode ='0'" +
                            "  and    t.belong_org_code=:companyCode " +
                            "   and (select count(0)" +
                            "          from wf_workitem_model wks" +
                            "         where wks.receipt_code = t.pm_ins_id" +
                            "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                            "           and wks.enabled = 1) > 0  ");
                    map.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
                        map.put("startTime", startTime);
                        sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
                        map.put("endTime", endTime);
                    }


                    sql.append(" order by t.created_time desc");
                    List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
                    CaseStatisticsVo caseStatisticsVo = new CaseStatisticsVo();
                    caseStatisticsVo.setCompanyCode(sysDictValue.getValue());
                    caseStatisticsVo.setCompanyName(sysDictValue.getName());
                    caseStatisticsVo.setNums(resultList.size());
                    caseStatisticsVos.add(caseStatisticsVo);
                } else {
                    //获取当前当前月份
                    DateFormat df = new SimpleDateFormat("yyyy-MM");
                    Calendar calendar = Calendar.getInstance();
                    String treatTime = df.format(calendar.getTime());
                    Map<String, Object> map = CollectionUtil.newHashMap();
                    StringBuffer sql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and t.pm_ins_id = act.receipt_code" +
                            " and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                    map.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
                        map.put("startTime", startTime);
                        sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
                        map.put("endTime", endTime);
                    }


                    sql.append(" order by t.created_time desc");
                    List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
                    CaseStatisticsVo caseStatisticsVo = new CaseStatisticsVo();
                    caseStatisticsVo.setCompanyCode(sysDictValue.getValue());
                    caseStatisticsVo.setCompanyName(sysDictValue.getName());
                    caseStatisticsVo.setNums(resultList.size());
                    caseStatisticsVos.add(caseStatisticsVo);
                }

                is++;
            }
        }
        return caseStatisticsVos;
    }


    @Override
    public void caseProblemStatistics(Map<String, Object> resultMap, HttpServletResponse response, HttpServletRequest request) {
        Boolean flag = false;
        String fileName = "经验推广数据统计.xls";
        IUser user = SecurityUtils.getCurrentUser();
        List<CaseStatisticsVo> list;
        if (user.getBelongCompanyTypeDictValue().equals("01")) {
            list = caseStatistics(1, 999, resultMap);
        } else {
            list = caseStatisticsOther(1, 999, resultMap);
        }


        try {

            if (list.size() > 0) {
                int i = 1;
                for (CaseStatisticsVo usCaseExcel : list) {
                    usCaseExcel.setNum(i);
                    i++;
                }
            }
            //获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");


            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            // 生成workbook 并导出
            // 创建参数对象（用来设定excel得sheet得内容等信息）
            ExportParams systemCompilation1 = new ExportParams();
            // 设置sheet的名称
            systemCompilation1.setSheetName("sheet1");
            // 创建sheet1使用得map
            Map<String, Object> systemCompilationMap = Maps.newHashMap();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            systemCompilationMap.put("title", systemCompilation1);
            // 模版导出对应得实体类型
            systemCompilationMap.put("entity", CaseStatisticsVo.class);
            // sheet中要填充得数据
            systemCompilationMap.put("data", list);
            // 将sheet1、sheet2.......sheet13使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(systemCompilationMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            // 导出操作
            FileOutputStream fos = new FileOutputStream(targetFileName);
            workbook.write(fos);
            fos.close();
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }

    }

    public void exportParameter(Integer page,
                                Integer rows,
                                String source,
                                String userCode,
                                String startDate,
                                String endDate,
                                String problemName,
                                String companyCode,
                                HttpServletResponse response,
                                HttpServletRequest request,
                                String state) {

        Boolean flag = false;
        String fileName = "经验推广.xls";

        //覆盖文件
        try {
            Map<String, Object> map = CollectionUtil.newHashMap();
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            map.put("problemName", problemName);
            map.put("companyCode", companyCode);
            map.put("state", state);
            List<Map<String, Object>> list = usCaseInfoService.findAllApplication(map);
            //  List<Map<String, Object>> content = FormatConversion.formatConversion(list);
            //获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");
            List<UsCaseExcel> usCaseExcels = new ArrayList<>();
            for (Map<String, Object> systemCompilationMap : list) {
                SimpleDateFormat dateFm = new SimpleDateFormat("yyyy-MM-dd"); //格式化当前系统日期
                String dateTime = dateFm.format(systemCompilationMap.get("createdTime"));
                systemCompilationMap.put("createdTime", dateTime);

                UsCaseExcel usCaseExcel = null;

                Object o = MapUtil.mapToObject(systemCompilationMap, UsCaseExcel.class);
                if (o != null) {
                    String formDataJson = JacksonUtils.obj2json(o);
                    if (!"[]".equals(formDataJson)) {
                        usCaseExcel = JacksonUtils.json2Type(formDataJson, new TypeReference<UsCaseExcel>() {
                        });
                        String belongOrgName = systemCompilationMap.get("belongCompanyName").toString();
                        if (!systemCompilationMap.get("belongCompanyName").toString().equals(systemCompilationMap.get("belongDepartmentName").toString())) {
                            belongOrgName = belongOrgName + "/" + systemCompilationMap.get("belongDepartmentName").toString();
                        }
                        if (!systemCompilationMap.get("belongDepartmentName").toString().equals(systemCompilationMap.get("belongOrgName").toString())) {
                            belongOrgName = belongOrgName + "/" + systemCompilationMap.get("belongOrgName").toString();
                        }
                        usCaseExcel.setBelongOrgName(belongOrgName);
                        usCaseExcels.add(usCaseExcel);
                    }
                }
            }
            if (usCaseExcels.size() > 0) {
                int i = 1;
                for (UsCaseExcel usCaseExcel : usCaseExcels) {

                    usCaseExcel.setRownum(i);
                    i++;
                }
            }
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            // 生成workbook 并导出
            // 创建参数对象（用来设定excel得sheet得内容等信息）
            ExportParams exportParams = new ExportParams();
            // 设置sheet的名称
            exportParams.setSheetName("sheet1");
            // 创建sheet1使用得map
            Map<String, Object> systemCompilationMap = Maps.newHashMap();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            systemCompilationMap.put("title", exportParams);
            // 模版导出对应得实体类型
            systemCompilationMap.put("entity", UsCaseExcel.class);
            // sheet中要填充得数据
            systemCompilationMap.put("data", usCaseExcels);
            // 将sheet1、sheet2.......sheet13使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(systemCompilationMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            // 导出操作
            FileOutputStream fos = new FileOutputStream(targetFileName);
            workbook.write(fos);
            fos.close();
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }
    }


    /**
     * 根据pmINsId查询
     *
     * @param pmInsId
     * @return
     */
    @Override
    public UsCaseInfo getFormDetailByPmInsId(String pmInsId) {
        return usCaseInfoRepository.getFormDetailByPmInsId(pmInsId);
    }


    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 优秀案例数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> caseCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select t.belong_company_name, count(*) as caseCount" +
                "  from us_case_info t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and ac.enabled = 1" +
                "   and t.pm_ins_id = ac.receipt_code"+
                "   and (select count(0)" +
                "          from wf_workitem_model wks" +
                "         where wks.receipt_code = t.pm_ins_id" +
                "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                "           and wks.enabled = 1) > 0  ");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(startTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')>=:startTime ");
            param.put("startTime", startTime);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(endTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')<=:endTime");
            param.put("endTime", endTime);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 优秀案例数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> caseAllCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select count(*) as caseCount" +
                "  from us_case_info t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and ac.enabled = 1" +
                "   and t.pm_ins_id = ac.receipt_code");
        Map<String, Object> param = Maps.newHashMap();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(startTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')>=:startTime ");
            param.put("startTime", startTime);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(endTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')<=:endTime");
            param.put("endTime", endTime);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    public JsonResponse updateCaseInfo(UsCaseInfo caseInfo) {
        if (StringUtils.isEmpty(caseInfo.getId())) {
            return JsonResponse.fail("参数有误");
        }
        this.update(caseInfo);
        updateFileByPmInsId(caseInfo, caseInfo.getType());
        return JsonResponse.success("修改成功");

    }


    public List<Map<String, Object>> uumsSelectBySql(Map<String, String> map, String sqlId) {
        IUser user = SecurityUtils.getCurrentUser();//获取当前人员
        JsonResponse jsonResponse = HttpClient.textBody(config.getUumsAddress() + "/action/uumsSelect/selectBySqlSelectType/sso" + "?loginuser=" + encryptor.encrypt(user.getUsername()) +
                "&appcode=" + Constants.APP_CODE + "&appCode=" + Constants.APP_CODE + "&sqlId=" + sqlId + "&selectType=replacetype")
                .json(JSONUtil.toJsonStr(map)).asBean(JsonResponse.class);
        List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
        return data;
    }

}
