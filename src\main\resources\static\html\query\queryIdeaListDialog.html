<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>思政纪实台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent()
        var abol = false  //纪检身份
        abolFun()
        $(function () {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var yearArr = [year-2,year-1,year]
            var monthArrs = []
            var  titleYearData=[]
            var  titleMonthData=[]

            for(var i=year-2;i<=year;i++){
                titleYearData.push({
                    value:i+'',
                    name:i+''
                })
            }
            $('#year').combobox({ 
                value:'',   
                valueField:'value',    
                textField:'name',
                panelHeight:'auto',
                editable:false,
                onSelect:yearFun,
                data:titleYearData
            });

            function yearFun(data) {
                titleMonthData=[]
                if (data.name == year) {
                    for (var i = 1; i <= month; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }else{
                    for (var i = 1; i <= 12; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }
                $('#month').combobox({ 
                    value:'',   
                    valueField:'value',    
                    textField:'name',
                    panelHeight:'auto',
                    editable:false,
                    data:titleMonthData
                });
                $('#month').combobox('setValue', '01');
            } 

            var strLink = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            if (month >= 1 && month <= 9) { month = "0" + month; }
            if (day >= 1 && day <= 9) { day = "0" + day; }
            var firstDate = year + strLink + month + strLink + '01';
            var sysDate = year + strLink + month + strLink + day;
            var lastDay = getLastDay(year, month);
            var lastDate = year + strLink + month + strLink + lastDay;
            var returnArr = [firstDate, sysDate, lastDate];

            console.log(returnArr,'returnArr');
            var timedata = {year:year+'',month:month+'',endTime:returnArr[2],startTime:returnArr[0]}

            console.log(timedata,'timedata');
            formval(timedata,"taskTableQueryForm");
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usRecordFill/findAllUsRecordFill", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "queryParams":{},
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ 
                            { title: "单位", field: "belongCompanyName", width: 80, tooltip: true, align: "center" },
                            { title: "部门", field: "belongDepartmentName", width: 80, tooltip: true, align: "center" },
                            { title: "党建指导员", field: "trueName", width: 60, tooltip: true, align: "center" },
                            { title: "谈话主题或走访事项", field: "configDeptName", width: 120, tooltip: true, align: "center" },
                            { title: "谈话或走访时间", field: "talkTime", width: 80, tooltip: true, align: "center" },
                            { title: "谈话或走访地点", field: "talkAddress", width: 100, tooltip: true, align: "center" },
                            { title: "谈话或走访对象", field: "configTime", width: 80, tooltip: true, align: "center" },
                            { title: "座谈人数", field: "numberOfPanel", width: 40, tooltip: true, align: "center" },
                            { title: "填报人", field: "applyUser", width: 40, tooltip: true, align: "center" },
                            { title: "填报时间", field: "applyTime", width: 80, tooltip: true, align: "center",
                              /*formatter: function (value, row, index) {
                                if(value){
                                    return  '<span class="titleTooltipA">'+  getTimeDate(row.createdTime, "yyyy-MM-dd hh:mm:ss")+ '</span>'
                                }else {
                                    return value
                                }
                              }*/
                            },
                            {field: "opt", title: "操作", width: 60, rowspan: 1, align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    if(row.configDeptName){
                                        var g = "<a class='check' index='" + index + "'>【查看】</a>";
                                        if(web.currentUser.username=="hadmin"){
                                            g += "<a class='hadminUpdata' index='" + index + "'>【修改】</a>"
                                        }
                                    }else{
                                        var g =""
                                    }
                                    return g
                                }
                            }
                        ]
                    ]
                }
            };
            // if(gps.companyCode){
            //     timedata.belongDepartmentCode=gps.companyCode
            //     pageparam.listtable.queryParams = timedata
            // }
            // loadGrid(pageparam);
            $('#currentUserCode').val(web.currentUser.username)
            $('#belongDepartmentCode').val(gps.companyCode)

            $(document).on("click", ".exporttable", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir + "action/usRecordFill/exportUsRecordFillDate");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            })
            // 修改
            $(document).on("click", "a.hadminUpdata", function () {
                var index = $(this).attr('index');
                var item = $('#taskTable').datagrid('getRows')[index];
                var param = {
                    id: item.id,
                    source: "PC",
                }
                var url = tourl('html/change/changeIdea.html?type=draft&otherType=hadminUpdata&special=special&location=xxxx', param)
                top.dialogP(url, window.name, '思政纪实', 'groupCheck', true, 'maximized', 'maximized')
            })
            // 详情查看
            $(document).on("click", "a.check", function () {
                var index = $(this).attr('index');
                var item = $('#taskTable').datagrid('getRows')[index];
                var param = {
                    id: item.id,
                    source: "PC",
                }
                var url = tourl('html/change/changeIdea.html?workFlag=join&type=join&special=special&location=xxxx', param)
                top.dialogP(url, window.name, '思政纪实', 'groupCheck', true, 'maximized', 'maximized')
            })
            //选择组织
            $(".chooseOrgs").on("click",function(){
                var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'C'};
                if($("#taskTableQueryForm .chooseOrgs").val()!=""){
                    var datas=[];
                    var names=$("#taskTableQueryForm .chooseOrgs").val().split(",");
                    var codes=$("#taskTableQueryForm input[name=belongDepartmentCode]").val().split(",");
                    var comps=$("#taskTableQueryForm input[name=companyTypeDictValue]").val().split(",");
                    for(var i in comps){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseOrgsVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                }
                var url=tourl('html/choose/chooseCompanyII.html',href);
                top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
            });

            // console.log(web.currentUser.belongCompanyTypeDictValue,'web.currentUser.belongCompanyTypeDictValue');
            if(web.currentUser.belongCompanyTypeDictValue == '01'|| abol){
                $('.c02').show()
            }else{
                $('.c01').show()
            }

            //分公司人员新增接口做个判断，控制是否显示c03
            if(web.currentUser.belongCompanyTypeDictValue == '02'&& !abol){
                ajaxgeneral({
                    url: 'action/index/judgeUser',
                    contentType: 'application/json; charset=utf-8',
                    success: function (res) { 
                        if(!res.data.isAdmin){
                            $('.c03').hide()
                        }
                    }
                });
            }

            $(".searchBtn").on("click",function(){
               var data = getFormValue('taskTableQueryForm')
               
               pageparam.listtable.queryParams = data
               loadGrid(pageparam);
            });
            $('.searchBtn').trigger('click')

            $('.queryTable').resize()
        });
        function abolFun(){
            for(var i in web.currentUser.authRoles){
                if(web.currentUser.authRoles[i].authority == 'djfupt_001'){
                    return abol = true
                }
            }
        }

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };
        function getLastDay(year, month) { //获取某年某月最后一天是几号
            var new_year = year;
            var new_month = month++;//取下一个月的第一天，方便计算（最后一天不固定）
            if (month > 12) {//如果当前大于12月，则年份转到下一年
                new_month -= 12;//月份减
                new_year++;//年份增
            }
            var last_date = new Date(new_year, new_month, 0).getDate();
            return last_date;
        }
        function beforeSubmit(data) {
            data.endTime =  getLastDay(data.year, data.month)
            if(data.year){
                data.time = data.year +'-'+ data.month
            }else{
                data.time = ''
            }
            // data.talkTime =data.time +'-01' + '至' + data.time +'-'+ data.endTime

            data.startTime = data.time +'-01'
            data.endTime =  data.time +'-'+ data.endTime
            delete data.chooseOrgs
            delete data.year
            delete data.month
            // delete data.startTime
            // delete data.endTime
            return data
        }

        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [], styles = [], comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
                comps.push(data.data[0].companyTypeDictValue);
            }
            $("#taskTableQueryForm input[name=belongDepartmentCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $("#taskTableQueryForm input[name=pmInsId]").val('C');
            $("#taskTableQueryForm input[name=companyTypeDictValue]").val(comps.join(","));
        };
        function companyFun(data){
            $("#taskTableQueryForm input[name=companyCode]").val(data.value);
        }

        function dialogClosed(){
            top.dialogClose("groupCheck")
        }
    </script>
    <style>
        .date{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm" enctype="multipart/form-data" charset=utf-8" method="post" beforeSubmit="beforeSubmit()">
         <input  id="belongDepartmentCode"  name="belongDepartmentCode" type="hidden"/>
         <input name="companyTypeDictValue" type="hidden"/>
         <input name="companyCode" type="hidden"/>
         <input name="pmInsId" type="hidden"/>
        <input type="hidden" id="currentUserCode" name="currentUserCode">
        <input type="hidden" id="startTime" name="startTime">
        <input type="hidden" id="endTime" name="endTime">
        <table border="0" cellpadding="0" cellspacing="6" width="100%" class="queryTable">
            <tr>
                <td width="18%"></td>
                <td width="15%"></td>
                <td width="10%"></td>
                <td width="15%"></td>
                <td width="8%"></td>
                <td width="15%"></td>
                <td width="8%"></td>
                <td width="10%"></td>
            </tr>
            <tr>
                <td align="right">谈话主题或走访事项</td>
                <td> <input name="configDeptName" type="text" /> </td>
                <!-- <td align="right">谈话或走访时间</td>
                <td>
                    <input id="startTime" name="startTime" type="text" class="easyui-datebox"
                        style="width:40%;height:32px;" validType="startTimeCheck['endTime','startTime']"
                        data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="endTime" name="endTime" type="text" class="easyui-datebox" style="width:40%;height:32px;"
                        validType="endTimeCheck['startTime','endTime']"
                        data-options="panelHeight:'auto', editable:false" />
                </td> -->
                 <!-- test wdy -->
                 <td align="right"  class="c03">上报部门</td>
                 <td class="c01 c03 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" value="" readonly  style="width: 100%; height: 32px;"/>
                </td>
                <td class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>
                <td align="right">完成情况</td>
                <td >
                    <input id="type" name="type" style="width: 100%; height: 32px;"  type="text"
                           class="easyui-combobox" data-options="
                            valueField: 'label',
                            textField: 'value',
                            panelHeight:'auto',
                            editable:false,
                            data: [{ label: '1', value: '已完成' },{ label: '0', value: '未完成' }]",/>
                </td>
            </tr>
            <tr>
                <td align="right">谈话或走访时间</td>
                <td class="date" width="350">
                    <input class="easyui-combobox" id="year" name="year" data-options=" valueField: 'label', textField: 'value', editable:false," style="width:calc(50% - 10px); height: 32px;" /> 年
                    <input class="easyui-combobox" id="month" name="month" data-options=" valueField: 'label', textField: 'value', editable:false," style="width:calc(50% - 10px); height: 32px;" /> 月
                </td>
                <td colspan="6">
                    <a class="btn fr searchBtn">
                        <font>查询</font>
                    </a>
                    <a class="btn fr mr10 exporttable ">
                        <font>导出</font>
                    </a>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>