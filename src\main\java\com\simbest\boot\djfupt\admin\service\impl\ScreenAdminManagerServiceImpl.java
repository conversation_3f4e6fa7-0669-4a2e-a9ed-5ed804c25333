package com.simbest.boot.djfupt.admin.service.impl;


import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.admin.model.ScreenAdminManager;
import com.simbest.boot.djfupt.admin.repository.ScreenAdminManagerRepository;
import com.simbest.boot.djfupt.admin.service.IScreenAdminManagerService;
import com.simbest.boot.djfupt.admin.utils.MyUtils;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ScreenAdminManagerServiceImpl extends LogicService<ScreenAdminManager, String> implements IScreenAdminManagerService {

    private final ScreenAdminManagerRepository repository;

    private final UumsSysUserinfoApi uumsSysUserinfoApi;
    private final UumsSysRoleApi uumsSysRoleApi;


    public ScreenAdminManagerServiceImpl(ScreenAdminManagerRepository screenAdminManagerRepository, UumsSysUserinfoApi uumsSysUserinfoApi, UumsSysRoleApi uumsSysRoleApi) {
        super(screenAdminManagerRepository);
        this.repository = screenAdminManagerRepository;
        this.uumsSysUserinfoApi = uumsSysUserinfoApi;
        this.uumsSysRoleApi = uumsSysRoleApi;
    }

    /**
     * 新增角色
     * <br/> params [o]
     *
     * @return {@link ScreenAdminManager}
     * <AUTHOR>
     * @since 2023/5/23 16:08
     */
    @Transactional(rollbackFor = Exception.class)
    public ScreenAdminManager insert(ScreenAdminManager o) {
        SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(o.getUsername(), Constants.APP_CODE);
        ScreenAdminManager t = super.insert(o.created(user));

        // 更新主数据信息
        String roleCode = Objects.equals(o.getType(), "1") ? Constants.ROLE_SCREEN_S_ADMIN : Constants.ROLE_SCREEN_F_ADMIN;
        roleCode = Objects.equals(o.getType(), "0") ? Constants.ROLE_SCREEN_SS_ADMIN : roleCode;
        String uumsRoleId = uumsSysRoleApi.findRoleByRoleCode(Constants.APP_CODE, roleCode).getId();
        HashMap<String, Object> map = new HashMap<String, Object>() {{
            put("roleId", uumsRoleId);
            put("usernames", o.getUsername());
        }};
        MyUtils.uumsHttpRequest("createRoleUsers", "kv", map);
        return t;
    }

    /**
     * 修改角色
     * <br/> params [o]
     *
     * @return {@link ScreenAdminManager}
     * <AUTHOR>
     * @since 2023/5/23 16:08
     */
    @Transactional(rollbackFor = Exception.class)
    public ScreenAdminManager update(ScreenAdminManager o) {
        String account = this.findById(o.getId()).getUsername();
        SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(o.getUsername(), Constants.APP_CODE);
        ScreenAdminManager t = super.update(o.created(user));

        // 更新主数据信息
        HashMap<String, Object> map = new HashMap<String, Object>() {{
            put("beforeUsername", account);
            put("afterUsername", o.getUsername());
        }};
        MyUtils.uumsHttpRequest("updateRoleUser", "json", map);
        return t;
    }

    /**
     * 删除角色
     * <br/> params [id]
     *
     * @param id 管理员id
     * <AUTHOR>
     * @since 2023/5/23 16:08
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        ScreenAdminManager o = this.findById(id);
        // 删除当前角色
        super.deleteById(id);

        // 更新主数据信息
        String roleCode = Objects.equals(o.getType(), "1") ? Constants.ROLE_SCREEN_S_ADMIN : Constants.ROLE_SCREEN_F_ADMIN;
        roleCode = Objects.equals(o.getType(), "0") ? Constants.ROLE_SCREEN_SS_ADMIN : roleCode;
        String uumsRoleId = uumsSysRoleApi.findRoleByRoleCode(Constants.APP_CODE, roleCode).getId();
        HashMap<String, Object> map = new HashMap<String, Object>() {{
            put("roleId", uumsRoleId);
            put("usernames", o.getUsername());
        }};
        MyUtils.uumsHttpRequest("deleteUsers", "kv", map);
    }

    /**
     * 管理员信息查询 分页
     * <br/>params [o, pageable]
     *
     * @return {@link Page<ScreenAdminManager>}
     * <AUTHOR>
     * @since 2023/5/19 9:46
     */
    @Override
    public Page<ScreenAdminManager> findAllInfo(ScreenAdminManager o, Pageable pageable) {
        // 获取查询条件
        Specification<ScreenAdminManager> or = Specifications.<ScreenAdminManager>or()
                .eq(StringUtils.isNotBlank(o.getCompanyCode()), "companyCode", o.getCompanyCode())
                .eq(StringUtils.isNotBlank(o.getCompanyCode()), "companyParentCode", o.getCompanyCode())
                .build();
        Specification<ScreenAdminManager> and = Specifications.<ScreenAdminManager>and()
                .like(StringUtils.isNotBlank(o.getTruename()), "truename", String.format("%%%s%%", o.getTruename()))
                .eq(StringUtils.isNotBlank(o.getUsername()), "username", o.getUsername())
                .eq(StringUtils.isNotBlank(o.getDeptCode()), "deptCode", o.getDeptCode())
                .build()
                .and(or);
        Page<ScreenAdminManager> page = this.findAll(and, pageable);

        return page;
    }

    /**
     * 新增角色
     * <br/> params [o]
     *
     * @return {@link ScreenAdminManager}
     * <AUTHOR>
     * @since 2023/5/23 16:08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ScreenAdminManager> saveAllInfo(String accounts) {
        String[] split = accounts.split(",");
        List<ScreenAdminManager> list = new ArrayList<>();
        for (String s : split) {
            ScreenAdminManager ScreenAdminManager = new ScreenAdminManager();
            ScreenAdminManager.setUsername(s);
            ScreenAdminManager ScreenAdminManager1 = this.insert(ScreenAdminManager);
            list.add(ScreenAdminManager1);
        }
        return list;
    }

}
