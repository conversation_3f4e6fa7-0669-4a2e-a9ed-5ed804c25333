package com.simbest.boot.djfupt.findfaults.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.findfaults.model.UsAppNameModel;

import java.util.Map;

public interface ISysAppNameService extends ILogicService<UsAppNameModel,String> {
    JsonResponse findAllAppName(Integer page, Integer size, Map<String, String> map);

    JsonResponse getAllAppNameNoPage();
}
