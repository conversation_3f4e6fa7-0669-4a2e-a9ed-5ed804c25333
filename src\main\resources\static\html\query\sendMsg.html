<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>短信催办</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable",//table列表的id名称，需加#
                    "querycmd": "action/smsUrged/queryAllTaskToDo?source=PC",//table列表的查询命令
                    //"contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "fitColumns": true,//自动扩大或缩小列的尺寸以适应表格的宽度并且防止水平滚动，不配置默认false
                    "checkboxall": true,
                    //"sortName":'orgCode',
                    //"sortOrder": 'desc',
                    "idField": 'ID',
                    "remoteSort": false,
                    "frozenColumns": [[
                        { field: "ck", checkbox: true }
                    ]],//固定在左侧的列
                    "columns": [[//列
                        { title: "工单编号", field: "CREATE_ORG_NAME", width: 150, sortable: true, align: "center" },
                        {
                            title: "工单标题", field: "RECEIPT_TITLE", width: 300, rowspan: 1, tooltip: true, sortable: true//align：对齐此列的数据，可以用left、right、center
                        },
                        { title: "部门名称", field: "CREATE_USER_NAME", width: 130, sortable: true, align: "center" },
                        { title: "发起人", field: "CREATE_TIME", width: 150, sortable: true, order: "asc" },//排序sortable: true
                        { title: "发起时间", field: "PARTI_NAME", width: 130, sortable: true, align: "center" },
                        {
                            field: "opt", title: "操作", width: 100, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a class='singleSend' index='" + index + "' title='单条催办'>【催办】</a>";
                                if ("A" == row.PM_INS_TYPE || "B" == row.PM_INS_TYPE || "C" == row.PM_INS_TYPE) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.RECEIPT_TITLE + "' pmInsId='" + row.RECEIPT_CODE + "' " +
                                        "path='html/apply/changeGroup.html?workFlag=join&type=join&location=" + row.ACTIVITYDEFID + "&source=PC&processInstId=" + row.PROCESS_INST_ID + "&pmInsId=" + row.RECEIPT_CODE + "' >【查看】</a>";
                                } else if ("D" == row.PM_INS_TYPE || "E" == row.PM_INS_TYPE || "F" == row.PM_INS_TYPE) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.RECEIPT_TITLE + "' pmInsId='" + row.RECEIPT_CODE + "' " +
                                        "path='html/apply/changeQuestion.html?workFlag=join&type=join&location=" + row.ACTIVITYDEFID + "&source=PC&processInstId=" + row.PROCESS_INST_ID + "&pmInsId=" + row.RECEIPT_CODE + "' >【查看】</a>";
                                } else if ("G" == row.PM_INS_TYPE || "H" == row.PM_INS_TYPE) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.RECEIPT_TITLE + "' pmInsId='" + row.RECEIPT_CODE + "' " +
                                        "path='html/apply/changeScore.html?workFlag=join&type=join&location=" + row.ACTIVITYDEFID + "&source=PC&processInstId=" + row.PROCESS_INST_ID + "&pmInsId=" + row.RECEIPT_CODE + "' >【查看】</a>";
                                }
                                return g
                            }
                        }

                    ]],
                    "rowStyler": function (index, row) {
                    }
                },
            };
            loadGrid(pageparam);

            //全部发送短信催办
            $(document).on("click", ".allSend", function () {
                var datas = $(pageparam.listtable.listname).datagrid("getRows");
                if (datas.length > 0) {
                    $.messager.confirm("批量催办", "请确认是否进行催办？", function (r) {
                        if (r) {
                            var ajaxopts = {
                                url: "action/smsUrged/sendShortMessage?source=PC",
                                contentType: "application/json; charset=utf-8",
                                data: datas,
                                success: function (data) {
                                    top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                }
                            };
                            ajaxgeneral(ajaxopts);
                        }
                    });
                }
                else {
                    top.mesShow("温馨提示", "请选择要发送催办短信的工单！", 2000, 'red');
                }
            });

            //批量发送短信催办
            $(document).on("click", ".branchSend", function () {
                var datas = $(pageparam.listtable.listname).datagrid("getChecked");
                if (datas.length > 0) {
                    $.messager.confirm("批量催办", "请确认是否进行催办？", function (r) {
                        if (r) {
                            var ajaxopts = {
                                url: "action/smsUrged/sendShortMessage?source=PC",
                                contentType: "application/json; charset=utf-8",
                                data: datas,
                                success: function (data) {
                                    top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                }
                            };
                            ajaxgeneral(ajaxopts);
                        }
                    });
                }
                else {
                    top.mesShow("温馨提示", "请选择要发送催办短信的工单！", 2000, 'red');
                }
            });

            //单条发送短信催办
            $(document).on("click", ".singleSend", function () {
                //先清除已经勾选的行的checkbox，防止单条发送短信多选
                $(pageparam.listtable.listname).datagrid("clearChecked");
                //勾选当前行的checkbox并返回当前行
                //$(pageparam.listtable.listname).datagrid("checkRow",$(this).attr('index'));
                //var datas = $(pageparam.listtable.listname).datagrid("getChecked");
                var datas = $(pageparam.listtable.listname).datagrid("checkRow", $(this).attr('index')).datagrid("getChecked");
                //发送单条催办短信
                $.messager.confirm("单条催办", "确定要发送催办短信吗？", function (r) {
                    if (r) {
                        var ajaxopts = {
                            url: "action/smsUrged/sendShortMessage?source=PC",
                            contentType: "application/json; charset=utf-8",
                            data: datas,
                            success: function (data) {
                                top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                //清除已经勾选的行的checkbox，为了页面好看
                                $(pageparam.listtable.listname).datagrid("clearChecked");
                            }
                        };
                        ajaxgeneral(ajaxopts);
                    };
                });
            });

            //详情查看
            $(document).on("click", ".audit", function () {
                var item = $(this);
                var url = item.attr("path");
                top.dialogP(url, window.name, item.attr("ptitle"), 'getCheck', true, 'maximized', 'maximized');
            });
            window.getCheck = function () {

            }
        });
        //初始化界面
        window.initsystem = function () { };
    </script>
</head>

<body class="body_page">
    <form id="taskTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">工单标题：</td>
                <td colspan="2" width="150">
                    <input name="title" type="text" value="" />
                </td>
                <td width="90" align="right">当前办理人：</td>
                <td colspan="2" width="150">
                    <input name="partiName" type="text" value="" />
                </td>
                <td>
                    <div class="w100">
                        <a class="btn fr mr10 mr10 searchtable ">
                            <font>查询</font>
                        </a>
                        <a class="btn fr mr10 mr10 allSend">
                            <font>全部催办</font>
                        </a>
                        <a class="btn fr mr10 mr10 branchSend">
                            <font>催办</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>