<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>
    <title>数智党建</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta charset="UTF-8">
    <meta name="ctx" th:content="${#httpServletRequest.getContextPath()}" />
    <!-- <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=${svn.revision}" rel="stylesheet"/> -->
    <link href="../../fonts/iconfont/iconfont.css?v=${svn.revision}" th:href="@{/fonts/iconfont/iconfont.css?v=${svn.revision}}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=${svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=${svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=${svn.revision}" rel="stylesheet" />
    <link href="../../js/swiper.min.css?v=${svn.revision}" rel="stylesheet"/>
    <link href="./partyBuildScreen.css" rel="stylesheet" />
    <!-- <link href="http://************:8088/simbestui/css/index.css?v=${svn.revision}" rel="stylesheet" /> -->
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=${svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=${svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=${svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.calendar.js?v=${svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=${svn.revision}" th:src="@{/js/jquery.config.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src="../../js/flexible.js?v=${svn.revision}" th:src="@{/js/flexible.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src="../../js/henan.js" type="text/javascript"></script>
    <script src="../../js/echarts.js" type="text/javascript"></script>
    <script src="../../js/swiper.js" type="text/javascript"></script>

    <script src="http://************:8088/simbestui/js/jsencrypt.min.js?v=${svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=${svn.revision}"
            type="text/javascript"></script>

    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <!-- <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/> -->
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>






    <style>
        .hlgjBox {
            width: 100%;
            padding: 0 0.13rem;
        }

        .hlgjBox .successTj {
            height: 46%;
            position: relative;
            background: url(../../images/screen/hlgj/b6.png) no-repeat;
            background-size: 100% 120%;
            padding: 20px 0;
        }

        .hlgjBox .successTj .tjbox2 {
            width: 100%;
            padding: 5px 0;
        }

        .hlgjBox .successTj .tjbox2 .left {
            width: 50%;
            border-right: 1px solid #FCDBD7;
        }

        .hlgjBox .successTj .tjbox2 .left tr {
            height: 0.3rem;
            font-size: 12px;
        }

        .hlgjBox .successTj .tjbox2 .left .th {
            color: #999;
            font-size: 13px;
            margin-bottom: 20px;
            height: 0.28rem;
        }

        .hlgjBox .successTj .tjbox2 .right tr {
            height: 0.3rem;
            font-size: 12px;
        }

        .hlgjBox .successTj .tjbox2 .right .th {
            color: #999;
            font-size: 13px;
            margin-bottom: 20px;
            height: 0.28rem;
        }

        .hlgjBox .successTj .tjbox2 .right {
            width: 50%;
        }

        .hlgjBox .successTj .tjbox1 {
            width: 100%;
            padding: 10px 0;
            display: flex;
        }

        .hlgjBox .successTj .tjbox1 .left {
            width: 50%;
            border-right: 1px solid #FCDBD7;
        }

        .hlgjBox .successTj .tjbox1 .left tr {
            height: 0.4rem;
            font-size: 12px;
        }

        .hlgjBox .successTj .tjbox1 .left .th {
            color: #999;
            font-size: 13px;
            margin-bottom: 20px;
            height: 0.28rem;
        }

        .hlgjBox .successTj .tjbox1 .right tr {
            height: 0.4rem;
            font-size: 12px;
        }

        .hlgjBox .successTj .tjbox1 .right .th {
            color: #999;
            font-size: 13px;
            margin-bottom: 20px;
            height: 0.28rem;
        }

        .hlgjBox .successTj .tjbox1 .right {
            width: 50%;
        }

        .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
        }

        .triangle {
            width: 200px;
            height: 90px;
            padding: 5px;
            position: absolute;
            top: 3.35rem;
            right: 1.75rem;
            /* filter: drop-shadow(0 0 5px #D31805); */
            background-color: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 5px;
            box-shadow: 5px 5px 5px #D31805;
            border: 1px solid #D31805;
        }

        .godjzdy {
            cursor: pointer
        }

        .gohlgj {
            cursor: pointer
        }

        .goadmin {
            position: absolute;
            right: 40px;
            color: #d31805;
            font-weight: 700;
            top: 25px;
            font-size: 13px;
        }

        .panel1 {
            height: 60%;
        }

        .panel4 {
            height: 45%;
            margin-bottom: 0.1rem;
        }

        .panel4 .chart .menuCard {
            width: 25%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
        }

        .panel6 {
            height: 46%;
            padding-bottom: 0.2rem;
            margin-top: 0.1rem;
        }

        .panel6 .chart {
            margin-top: 10px;
        }

        .panel6 .showBox {
            margin-top: 3%;
        }

        .mainbox .column2 {
            height: 90vh;
            position: relative;
        }

        .panel2 .completionBox .right .content {
            font-size: .18rem;
            font-weight: 400;
            line-height: .30rem;
            height: 3rem;
        }

        .panel2 .completionBox {
            border-radius: 20px;
        }

        .listBox .info1 .gohlgjDetail {
            margin-bottom: 0.18rem;
        }

        .panel4 .menutxt {margin: .05rem 0;font-size: .16rem}
        .panel4 .chart .menuCard{
            margin: .1rem 0;
        }
        .completion{
            z-index: 49;
        }
    </style>
    <script type="text/javascript">
        var gps = getQueryString();
        var timers = Date.now()
       if(undefined != gps.ticket && undefined != gps.hqClientId){
                ajaxgeneral({
                    url: "getCurrentUser/hqsso?appcode=djfupt&hqClientId="+gps.hqClientId+"&ticket="+gps.ticket,
                    async: false,
                    success: function (ress) {
                        web.currentUser = ress.data;
                        var url = window.location.origin+window.location.pathname+'?loginuser='+getRsa(ress.data.username);
                        window.history.pushState({},0,url);
                        // console.log("ress.data.authUserOrgs[0].orgCode",ress.data.authUserOrgs[0].orgCode)
                    }
                });

            }else if(undefined != gps.loginuser){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=djfupt&loginuser=" + gps.loginuser,
                    async: false,
                    success: function (ress) {
                        web.currentUser = ress.data;
                        // console.log("ress.data.authUserOrgs[0].orgCode",ress.data.authUserOrgs[0].orgCode)
                    }
                });
            }else {
                getCurrent();
            } 
        var mapDate = '';
        var gridcount = '';
        var policyNum = [];
        var problemNum = [];
        var caseNum = [];
        var huanChart;
        var option1;
        var myChart;
        var optionMap;
        var flag = 0;
        var isAllGridCount = false;
        var returnYear = [];
        var isquanping = false;
        var isquanping2 = false;
        var date = new Date()
        if (gps && gps.uid) {
            window.location.href = web.rootdir + 'html/dataScreen/partyBuildingScreen.html'
        }
        $(function() {
            ajaxgeneral({
                url: 'action/commom/getHeader',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {}
            });

            $('#trueName').text(web.currentUser.truename)
            getDj(), getdyyt(), gethlgj(), getzbjq(), getsjtj(), getVideo() ,//调用函数
            getwenda()
            
            // 设置iframe
            var iframeUrl = '';
            if (window.location.href.indexOf('http://*************:8088') === 0) {
                iframeUrl = 'http://*************:8088/oaWeb/szdjAiWeb/#/?username='+ getRsa(web.currentUser.username) +'&myFrom=OA'
            }else{
                iframeUrl = 'http://************:8088/szdjAiWeb/#/?username='+ getRsa(web.currentUser.username) +'&myFrom=OA'

            }
            $('#iframePlant').attr('src',iframeUrl)

            //#region 获取时间
            var strLink = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (day >= 1 && day <= 9) {
                day = "0" + day;
            }
            var firstDate = year + strLink + month + strLink + '01';
            var sysDate = year + strLink + month + strLink + day;
            var lastDay = getLastDay(year, month);
            var lastDate = year + strLink + month + strLink + lastDay;
            var firstYear = year + strLink + '01' + strLink + '01';
            var lastYear = year + strLink + '12' + strLink + '31';
            var returnArr = [firstDate, sysDate, lastDate]; //以数组形式返回   ['2023-06-01', '2023-06-09', '2023-06-30']
            returnYear = [firstYear, lastYear]; //以数组形式返回 ['2023-01-01', '2023-12-31']

            function getLastDay(year, month) { //获取某年某月最后一天是几号
                var new_year = year;
                var new_month = month++; //取下一个月的第一天，方便计算（最后一天不固定）
                if (month > 12) { //如果当前大于12月，则年份转到下一年
                    new_month -= 12; //月份减
                    new_year++; //年份增
                }
                var last_date = new Date(new_year, new_month, 0).getDate();
                return last_date;
            }
            //#endregion

            //  党建指导员政策宣讲完成情况
            var ajaxopts1 = {
                url: 'action/usPolicyInfo/policyCount',
                data: {
                    startTime: '',
                    endTime: ''
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $.each(res.data, function(i, item) {
                        var str = '<li class="infoli">' + '<a  class="readP" data-belongCompanyName=' + res.data[i].belongCompanyName + '>' + '<span class="infobolck"><image src="../../images/screen/badge.png" ></span>' + '<span class="infos">' + res.data[i].belongCompanyName + '</span>' + '<span class="infospan">' + Number(res.data[i].rate.replace('%', '')).toFixed(2) + '%</span>' + '</a>' + '</li>';
                        if (i < 6) {
                            $(".listOne").append(str)
                        } else if (6 <= i && i < 12) {
                            $(".listTwo").append(str)
                        } else {
                            $(".listThree").append(str)
                        }
                    });
                }
            };
            ajaxgeneral(ajaxopts1);
            //党建指导员当月数据
            var ajaxopts2 = {
                url: 'action/usPolicyInfo/findMapDetail',
                data: {
                    startTime: returnArr[0],
                    endTime: returnArr[2]
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var json = JSON.parse(JSON.stringify(res.data).replace(/belongCompanyName/g, "name"));
                    json = JSON.parse(JSON.stringify(json).replace(/rate/g, "value"));
                    for (var y in json) {
                        json[y].value = parseInt(json[y].value)
                        if (json[y].name.slice(0, -3) == '') {
                            json[y].name = '省公司'
                        } else {
                            json[y].name = json[y].name.slice(0, -3) + '市'
                        }
                    }
                    mapDate = json
                    for (var i = 0; i < mapDate.length; i++) {
                        var item = mapDate[i];
                        if (item.admincount == undefined) {
                            item.admincount = 0
                        }
                        if (item.casecount == undefined) {
                            item.casecount = 0
                        }
                        if (item.dopreach == undefined) {
                            item.dopreach = 0
                        }
                        if (item.filcount == undefined) {
                            item.filcount = 0
                        }
                        if (item.gridcount == undefined) {
                            item.gridcount = 0
                        }
                        if (item.preach == undefined) {
                            item.preach = 0
                        }
                        if (item.problemcount == undefined) {
                            item.problemcount = 0
                        }
                        if (item.value == undefined) {
                            item.value = 0
                        }
                        if (item.name == undefined) {
                            item.name = ''
                        }
                    }
                    if (mapDate.length > 0) {
                        optionMap.series[0].data = mapDate
                    }
                    myChart.setOption(optionMap);
                }

            };
            ajaxgeneral(ajaxopts2);

            //#region   // 环形图表
            huanChart = echarts.init(document.querySelector(".panel6 .chart"));
            option1 = {
                angleAxis: {
                    axisLine: {
                        show: false
                    },
                    axisLabel: {
                        show: false
                    },
                    splitLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    min: 0,
                    max: 160,
                    boundaryGap: ['0', '100'],
                    startAngle: 225
                },
                radiusAxis: {
                    type: 'category',
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: false
                    },
                },
                polar: {
                    radius: '90%'
                },
                series: [],
                legend: { //图例相关配置
                    show: true,
                    bottom: 0,
                    icon: "circle",
                    itemGap: 15,
                    itemWidth: 12,
                    textStyle: { //图例文字的样式
                        fontSize: 12
                    },
                    data: ['开展党的政策宣讲数', '开展思政工作数', '协调解决问题数']
                },
                tooltip: {
                    show: false
                },
            };
            if (document.body.clientWidth <= 1024) {
                option1.legend.textStyle.fontSize = 6
            } else if (document.body.clientWidth <= 1440) {
                option1.legend.textStyle.fontSize = 10
            }
            // 获取网格数量
            getAllGridCount()
                //#endregion

            //#region  //地图配置
            myChart = echarts.init(document.querySelector(".map .chart"));
            echarts.registerMap('henan', henanData)
            optionMap = {
                tooltip: { //数据弹窗
                    trigger: 'item',
                    showDelay: 0,
                    transitionDuration: 0.2,
                    hideDelay: 10,
                    triggerOn: 'mousemove',
                    showDelay: 0,
                    textStyle: {
                        color: '#333',
                        fontSize: 12
                    },
                    transitionDuration: 1,
                    formatter: function(params) {
                        if (params.data && params.data.name)
                            return '<p>' + params.data.name.replace('市', '') + '分公司</p>' +
                                '<p>网格数量' + ":" + params.data.gridcount + '</p>' +
                                '<p>指导员数量' + ":" + params.data.admincount + '</p>' + '</p>' +
                                '<p>政策宣讲数量' + ":" + params.data.contentcount + '</p>' +
                                '<p>开展思政数量' + ":" + params.data.filcount + '</p>' +
                                '<p>问题数量' + ":" + params.data.problemcount + '</p>' +
                                '<p>优秀案例数量' + ":" + params.data.casecount + '</p>'
                    }
                },
                visualMap: { //数据映射
                    type: 'piecewise',
                    roam: false,
                    top: 'bottom',
                    left: 'center',
                    orient: 'horizontal',
                    inverse: true,
                    pieces: [{
                        min: 76,
                        max: 1000,
                        color: '#c7e897',
                        label: '完成率 > 75%'
                    }, {
                        min: 40,
                        max: 75,
                        color: '#fec68d',
                        label: '完成率 40% - 75%'
                    }, {
                        min: 0,
                        max: 39,
                        color: '#ff9e97',
                        label: '完成率 < 40%'
                    }]
                },

                series: [{
                    type: 'map',
                    map: "henan",
                    aspectScale: 0.9, //用于 scale 地图的长宽比，如果设置了projection则无效
                    zoom: 1.1, //当前视角的缩放比例
                    roam: false, //是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
                    // zoomSensitivity: false,
                    // calculable: false,
                    dragEnable: false, // 地图是否可通过鼠标拖拽平移，默认为true
                    zoomEnable: true, //地图是否可缩放，默认值为true
                    label: { //图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称
                        show: true,
                        textStyle: {
                            color: "#db3c17",
                            fontSize: 14
                        },
                    },
                    itemStyle: { //地图区域的多边形 图形样式。
                        normal: {
                            borderColor: "#db3c17",
                            borderWidth: 0.5,
                        },
                        emphasis: {
                            areaColor: '#d31805', //鼠标经过区域颜色
                            label: { //鼠标经过区域内文字样式
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 14,
                                    fontWeight: 700
                                },
                            },
                        },
                    },
                    select: {
                        label: {
                            show: true,
                            color: "#fff",
                            fontSize: 14,
                            fontWeight: 700,
                        },
                        itemStyle: {
                            areaColor: '#d31805',
                        }
                    },
                    data: mapDate
                }],
            };
            reloadPages()
            var pw = $('.panel2').width()
            $('.swiperBox').width(pw * 3)
            var mySwiper = new Swiper('.swiper-container', {
                direction: 'horizontal',
                // loop: true,
                autoplayDisableOnInteraction: true,
                speed: 2000,
                paginationClickable: true,
                effect: 'fade',
                pagination: '.swiper-pagination', // 如果需要分页器
                autoplay: 6000
            })
            $('.triangle').hide()
            $('.swiper-slide').mouseenter(function() { // 鼠标移入停止自动滚动
                mySwiper.stopAutoplay()
            })

            $('.swiper-slide').mouseleave(function() { // 鼠标移出开始自动滚动
                    mySwiper.startAutoplay();
                })
                //获取截止时间，拼接年月日
            var year = date.getFullYear()
            var month = date.getMonth() + 1
            var day = date.getDate()
            $('.deadline').text('(截止' + year + '年' + month + '月' + day + '日)')
                //#endregion

            // 监听视频播放
            setTimeout(function() {
                var videoList = document.getElementsByClassName('videoClass');
                for (var i = 0; i < videoList.length; i++) {
                    video = videoList[i]
                    if (video.addEventListener) {
                        video.addEventListener('play', playHandler, false);
                    } else if (video.attachEvent) {
                        video.attachEvent('play', playHandler);
                    }
                }
            }, 3000)

            function playHandler() { // 处理播放事件
                ajaxgeneral({
                    url: 'action/screenCount/record',
                    data: {
                        name: 'szyg'
                    },
                    success: function(res) {}
                });
            }

            // 初始化AI助手拖拽功能
            initAIDragAndDrop();
        })

        // 获取跳转地址前置url
        function getStartUrl() {
            if (window.location.href.indexOf('http://*************:8088') === 0) {
                return 'http://*************:8088';
            }
            return 'http://************:8088';
        }

        $(document).on("click", ".showTime img", function() { // 开启站内信息面板
            $('.msgbox').show()
        })

        $(document).on("click", ".closeMsg", function() { // 关闭站内信息面板
            $('.msgbox').hide()
        })

        $(document).on("click", '#yqff', function() {
            var str = window.location.href.indexOf('http://*************:8088') === 0 ? '/oaWeb' : '';
            var tourl = getStartUrl() + str + '/yqffWeb/#/mywork/processTask?appcode=yqff&from=oa&uid=' + getRsa(web.currentUser.username)
            window.open(tourl, "_blank");
        })

        $(document).on("click", ".panel4 .chart .menuCard img", function() { // 业务入口点击跳转
            var queryObj = getQueryString(window.location.search)
            var path = $(this).attr('path')
            if (!path) {
                return
            }
            var appCode = $(this).attr('appCode')

            if (path.indexOf('http://iportal.ha.cmcc') === 0) {
                if (window.location.href.indexOf('http://*************:8088') !== 0) {
                    path = "http://************:8088" + path.substring("http://iportal.ha.cmcc".length);
                }
            } else {
                path = getStartUrl() + path;
            }

            var appName = ''
            if (appCode == 'dwdyytxx') {
                appName = 'dyyt'
            }
            if (appCode == 'zbjqxxzd') {
                appName = 'jqxx'
            }
            if (appCode == 'djfupt') {
                appName = 'djfupt'
            }
            if (appCode == 'hlgj') {
                appName = 'hlgj'
            }
            if (appCode == 'xhdj') {
                appName = 'xhdj'
            }
            if (appCode == 'dfglxt') {
                appName = 'dfglxt'
            }
            if (appCode == 'djwh') {
                appName = 'djwh'
            }
            if (appCode == 'tzjyxcxx') {
                appName = 'jyxc'
            }
            if (appCode == 'ycf') {
                appName = 'yxf'
            }
            if (appCode == 'djzs') {
                appName = 'djzs'
            }
            //记录点击数
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: appName
                },
                success: function(res) {}
            });

            if (appCode == 'xhdj') {
                return goToPage(path)
            } //星火党建
            if (appCode == 'dfglxt') {
                return goToPage2(path)
            } //党费管理
            if (appCode == 'yglt') {
                return goyglt(path)
            } //员工论坛
            if (appCode == 'hlgj') {
                return gohlgj()
            } //合力攻坚
            if (appCode == 'hlgj2') {
                return gohlgj2()
            } //党员突击队
            if (appCode == 'fdzl') {
                return gofdzl()
            } //党建资源共享
            if (appCode == 'djwh') { //党建文化
                path += 'iv-user=' + getRsa(web.currentUser.username)
            } else {
                path += 'appcode=' + appCode + '&from=oa&uid=' + getRsa(web.currentUser.username)
            }
            window.open(path)
        })

        function goToPage(path) { //获取个人信息用于进入其他系统
            var tourl = path
            var ajaxopts = {
                url: 'action/usPolicyInfo/getUser',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    tourl = tourl + '?SYS_ID=jt_djypt&iv-user=' + encodeURI(res.data.username) + '&password=' + encodeURI(res.data.employeeNumberCode) + '&a4Switch=false&a4Token=&appAcctId=';
                    window.open(tourl, "_blank");
                }
            };
            ajaxgeneral(ajaxopts);
        }

        function goToPage2(path) { //党费管理
            var tourl = path
            ajaxgeneral({
                url: 'action/usPolicyInfo/getUser',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    tourl = tourl + '?SYS_ID=jt_djypt&iv-user=' + encodeURI(res.data.username) + '&password=' + encodeURI(res.data.employeeNumberCode) + '&a4Switch=false&a4Token=&appAcctId=';
                    window.open(tourl, "_blank");
                }
            });
        }




        $(document).on("click", "#dj .more", function() { // 党建指导员more
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'djzdy'
                },
                success: function(res) {}
            });
            if (!window.location.origin) {
                var url = window.location.protocol + "//" + window.location.hostname + (window.location.port ? ':' + window.location.port : '') + "/djfupt/index";
            } else {
                var url = window.location.origin + "/djfupt/index?tag=queryPolicyList";
            }
            window.open(url, '_blank');
        })

        $(document).on("click", "#dj .info1 p", function() { // 党建指导员台账跳转
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'djzdy'
                },
                success: function(res) {}
            });
            var url = window.location.origin + "/djfupt/index?tag=processJoin&location=" + $(this).attr('location') + "&pmInsId=" +
                $(this).attr('pmInsId') + "&processinstid=" + $(this).attr('processinstid') + "&workItemId=" + $(this).attr('workItemId')
            window.open(url, '_blank');
        })

        var timer;
        var isIE;
        var isFullScreen;

        function reloadPages() { //控制页面刷新
            window.addEventListener("resize", function(event) {
                if (isquanping == '0') {
                    if (timer) clearTimeout(timer);
                    timer = setTimeout(function() {
                        // if (!isquanping2 && isquanping == '0') location.reload();
                    }, 1000)
                    myChart.resize();
                    huanChart.resize();
                }
            })
        }

        $(window).resize(function() { //jQuery监听事件(窗口状态改变)
            if (checkIsFullScreen()) {
                isquanping = true
                isquanping2 = true
            } else {
                isquanping = '0'
            }
            isIEs()
        });

        function checkIsFullScreen() { //?是否全屏
            if (isIE) {
                if (document.msFullscreenElement !== null) {
                    return true
                }
            }
            isFullScreen = document.fullscreen || document.mozFullScreen || document.webkitIsFullScreen;
            if (isFullScreen) {
                ajaxgeneral({
                    url: 'action/screenCount/record',
                    data: {
                        name: 'szyg'
                    },
                    success: function(res) {}
                });
            }
            return isFullScreen == undefined ? false : isFullScreen;
        }

        function isIEs() { //ie?
            if (!!window.ActiveXObject || "ActiveXObject" in window) {
                isIE = true;
            } else {
                isIE = false;
            }
        }

        function getPolicy() { // 获取政策宣讲数
            flag++
            ajaxgeneral({
                url: "action/usPolicyInfo/policyAllCount",
                data: {
                    startTime: returnYear[0],
                    endTime: returnYear[1]
                },
                contentType: "application/json; charset=utf-8",
                success: function(res) {
                    if (res.status == 200) {
                        $($('.numberBox .number')[0]).text(res.data[0].dopreach)
                        policyNum = res.data[0].dopreach
                        var rate = (policyNum / gridcount) * 120 > 120 ? 120 : (policyNum / gridcount) * 120

                        option1.series.push({
                            type: 'bar',
                            name: '开展党的政策宣讲数',
                            color: 'rgba(211, 24, 5, 1)',
                            data: [{
                                name: '开展党的政策宣讲数',
                                value: rate
                            }],
                            coordinateSystem: 'polar',
                            z: 2,
                            roundCap: true,
                            barGap: '-100%'
                        });
                        if (flag == 3) {
                            huanChart.setOption(option1);
                        }
                    }
                }
            });
        }

        function getProblem() { // 获取思政工作
            flag++
            ajaxgeneral({
                url: "action/usRecordFill/findAllRecordCount",
                data: {
                    startTime: returnYear[0],
                    endTime: returnYear[1]
                },
                contentType: "application/json; charset=utf-8",
                success: function(res) {
                    if (res.status == 200) {
                        $($('.numberBox .number')[1]).text(res.data[0].filcount)
                        problemNum = res.data[0].filcount
                        var rate = (problemNum / gridcount) * 120 > 120 ? 120 : (problemNum / gridcount) * 120
                        option1.series.push({
                            type: 'bar',
                            name: '开展思政工作数',
                            color: 'rgba(253, 123, 30, 1)',
                            data: [0, {
                                name: '开展思政工作数',
                                value: rate
                            }],
                            coordinateSystem: 'polar',
                            z: 2,
                            roundCap: true,
                            barGap: '-100%'
                        });
                        if (flag == 3) {
                            huanChart.setOption(option1);
                        }
                    }
                }
            });
        }

        function getCaseAllCount() { // 获取协调解决问题
            flag++
            ajaxgeneral({
                url: "action/usProblemInfo/problemAllCount",
                data: {
                    startTime: returnYear[0],
                    endTime: returnYear[1]
                },
                contentType: "application/json; charset=utf-8",
                success: function(res) {
                    if (res.status == 200) {
                        $($('.numberBox .number')[2]).text(res.data[0].problemcount)
                        caseNum = res.data[0].problemcount
                        var rate = (caseNum / gridcount) * 120 > 120 ? 120 : (caseNum / gridcount) * 120
                        option1.series.push({
                            type: 'bar',
                            name: '协调解决问题数',
                            color: 'rgba(61, 127, 255, 1)',
                            data: [0, 0, {
                                name: '协调解决问题数',
                                value: rate
                            }],
                            coordinateSystem: 'polar',
                            z: 2,
                            roundCap: true,
                            barGap: '-100%'
                        });
                        if (flag == 3) {
                            huanChart.setOption(option1);
                        }
                    }
                }
            });
        }

        function getAllGridCount() { // 获取网格数
            ajaxgeneral({
                url: 'action/usAdminManager/findAllGridCount',
                data: {},
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $('.gridcount').text(res.data[0].gridcount)
                    $('.admincount').text(res.data[0].admincount)
                    gridcount = res.data[0].gridcount * 12
                    option1.series.push({
                        type: 'bar',
                        data: [120, 120, 120],
                        z: 0,
                        silent: true,
                        coordinateSystem: 'polar',
                        roundCap: true,
                        barWidth: '40%',
                        barGap: '-100%',
                        itemStyle: {
                            normal: {
                                color: function(params) {
                                    var colorList = ['rgba(211, 24, 5, 0.25)', 'rgba(253, 123, 30, 0.25)', 'rgba(61, 127, 255, 0.25)'];
                                    return colorList[params.dataIndex]
                                }
                            }
                        }
                    });
                    getPolicy()
                    getProblem()
                    getCaseAllCount()
                }
            });
        }

        function getDj() { // 获取党建指导员数据
            var city = web.currentUser.belongCompanyName
            if (web.currentUser.belongCompanyTypeDictValue == '03') {
                city = web.currentUser.belongCompanyNameParent
            }
            ajaxgeneral({
                url: 'action/usPolicyInfo/findLatest',
                data: {
                    city: city
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var list = res.data.content
                    var str = ''
                    var msg = ''
                    var length = list.length
                    if (list.length > 2) {
                        length = 3
                    }
                    for (var i = 0; i < length; i++) {
                        var item = list[i]
                        if (item.location == '') {
                            item.location = 'djfupt.start'
                        }
                        str += '<div class="smallTag"></div><p pmInsId="' + item.pmInsId + '" location="' + item.location + '" processinstid="' + item.processinstid + '" workItemId="' + item.workItemId + '">' + item.title + '</p>'
                        msg += '<div class="msgTitle"><div class="fl redIcon" ></div><div class="msgItem fr"><p>' + item.title + '</p><span class="fr">' + getNow('yyyy-MM-dd', false, item.createTime) + '</span></div></div > '
                    }
                    $('#dj .info1').append(str)
                    $('.msgbox').append(msg)
                    if (list.length) {
                        $('.msgNum').text(list.length)
                        $('.msgNum').show()
                    }
                }
            });
        }

        function getdyyt() { //第一议题数据
            ajaxgeneral({
                url: 'action/usPolicyInfo/selectAllPro?source=PC',
                data: {
                    currentUserCode: web.currentUser.username
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var list = res.data.content
                    var str = ''
                    var length = list.length
                    if (list.length > 2) {
                        length = 3
                    }
                    for (var i = 0; i < length; i++) {
                        var item = list[i]
                        if (item.location == '') {
                            item.location = 'djfupt.start'
                        }
                        str += '<div class="smallTag"></div><p  class="godwdyytDetail"   pmInsId="' + item.pmInsId + '" location="' + item.location + '" processinstid="' + item.processinstid + '" workItemId="' + item.workItemId + '"   id="' + item.ID + '" >' + item.REMARK + '</p>'
                    }
                    $('#dy .info1').append(str)
                }
            });
        }

        function gethlgj() { //合力攻坚数据
            ajaxgeneral({
                url: 'action/usPolicyInfo/workDynamicsList',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var list = res.data
                    var str = ''
                    var length = list.length
                    if (list.length > 16) {
                        length = 17
                    }
                    for (var i = 0; i < length; i++) {
                        var item = list[i]
                        if (item.location == '') {
                            item.location = 'djfupt.start'
                        }
                        str += '<div class="smallTag"></div><p  class="gohlgjDetail"  id="' + item.ID + '"  type="' + item.TYPE + '" >' + item.TITLE + '</p>'
                    }
                    $('#hlgj .info1').append(str)
                }
            });

            ajaxgeneral({
                url: 'action/usPolicyInfo/overallProgress',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $('.hlgjBox .bg .bbbox .b1 .bj').text(res.data.SJ_BL)
                    $('.hlgjBox .bg .bbbox .b2 .bj').text(res.data.ZD_BL)
                    $('.hlgjBox .bg .bbbox .b3 .bj').text(res.data.YB_BL)
                    $('.hlgjBox .bg .bbbox .b4 .bj').text(res.data.TOTAL_BL)
                    $('.hlgjBox .bg .bbbox .b1 .sl').text('总数量: ' + res.data.SJ_TOTAL + ' 已完成: ' + res.data.SJ_COMPLETE)
                    $('.hlgjBox .bg .bbbox .b2 .sl').text('总数量: ' + res.data.ZD_TOTAL + ' 已完成: ' + res.data.ZD_COMPLETE)
                    $('.hlgjBox .bg .bbbox .b3 .sl').text('总数量: ' + res.data.YB_TOTAL + ' 已完成: ' + res.data.YB_COMPLETE)
                    $('.hlgjBox .bg .bbbox .b4 .sl').text('总数量: ' + res.data.TOTAL + ' 已完成: ' + res.data.COMPLETE)
                }
            });

            ajaxgeneral({
                url: 'action/usPolicyInfo/companyCompletionRate',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var arrl = []
                    var arrr = []

                    for (var i in res.data) {
                        if (i < 9) {
                            arrl.push(res.data[i])
                        } else {
                            arrr.push(res.data[i])
                        }
                    }
                    var strl = '',
                        strr = ''
                    for (var j = 0; j < arrl.length; j++) {
                        var item = arrl[j]
                        strl += '<tr><td align="center">' + item.name + '</td><td align="center">' + item.sj + '</td><td align="center">' + item.yb + '</td><td align="center">' + item.total + '</td></tr>'
                    }
                    var strL = '<table class="w100"><tr style="height: 10px;"><td width="25%"></td><td width="25%"></td><td width="25%"></td><td width="25%"></td></tr><tr class="th"><td align="center">单位名称</td><td align="center">书记项目</td><td align="center">一般项目</td><td align="center">总体</td></tr>' +
                        strl + '</table>'
                    $('.tjbox1 .left').append(strL)

                    for (var v = 0; v < arrl.length; v++) {
                        var item = arrr[v]
                        strr += '<tr><td align="center">' + item.name + '</td><td align="center">' + item.sj + '</td><td align="center">' + item.yb + '</td><td align="center">' + item.total + '</td></tr>'
                    }
                    var strR = '<table class="w100"><tr style="height: 10px;"><td width="25%"></td><td width="25%"></td><td width="25%"></td><td width="25%"></td></tr><tr class="th"><td align="center">单位名称</td><td align="center">书记项目</td><td align="center">一般项目</td><td align="center">总体</td></tr>' +
                        strr + '</table>'
                    $('.tjbox1 .right').append(strR)
                }
            });
            hlsj()
        }

        function hlsj() { //合力攻坚数据
            ajaxgeneral({
                url: 'action/usPolicyInfo/deptCompletionRate',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var arrl = []
                    var arrr = []
                    for (var i in res.data) {
                        if (i < res.data.length / 2) {
                            arrl.push(res.data[i])
                        } else {
                            arrr.push(res.data[i])
                        }
                    }
                    var strl = '',
                        strr = ''
                    for (var j = 0; j < arrl.length; j++) {
                        var item = arrl[j]
                        strl += '<tr><td align="center" class="name">' + item.name + '</td><td align="center">' + item.sj + '</td><td align="center">' + item.yb + '</td><td align="center">' + item.total + '</td></tr>'
                    }
                    var strL = '<table class="w100"><tr style="height: 10px;"><td width="25%"></td><td width="25%"></td><td width="25%"></td><td width="25%"></td></tr><tr class="th"><td align="center">单位名称</td><td align="center">书记项目</td><td align="center">一般项目</td><td align="center">总体</td></tr>' +
                        strl + '</table>'
                    $('.tjbox2 .left').append(strL)

                    for (var v = 0; v < arrl.length; v++) {
                        var item = arrr[v]
                        strr += '<tr><td align="center" class="name">' + item.name + '</td><td align="center">' + item.sj + '</td><td align="center">' + item.yb + '</td><td align="center">' + item.total + '</td></tr>'
                    }
                    var strR = '<table class="w100"><tr style="height: 10px;"><td width="25%"></td><td width="25%"></td><td width="25%"></td><td width="25%"></td></tr><tr class="th"><td align="center">单位名称</td><td align="center">书记项目</td><td align="center">一般项目</td><td align="center">总体</td></tr>' +
                        strr + '</table>'
                    $('.tjbox2 .right').append(strR)
                }
            });
        }

        function getzbjq() { //支部近期数据
            ajaxgeneral({
                url: 'action/usPolicyInfo/selectAll?source=PC',
                data: {
                    currentUserCode: web.currentUser.username
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var list = res.data.content
                    var str = ''
                    var length = list.length
                    if (list.length > 2) {
                        length = 3
                    }
                    for (var i = 0; i < length; i++) {
                        var item = list[i]
                        if (item.location == '') {
                            item.location = 'djfupt.start'
                        }
                        str += '<div class="smallTag"></div><p  class="gozbjqDetail"   pmInsId="' + item.pmInsId + '" location="' + item.location + '" processinstid="' + item.processinstid + '" workItemId="' + item.workItemId + '"   id="' + item.ID + '" >' + item.NAME + '</p>'
                    }
                    $('#zb .info1').append(str)
                }
            });
        }

        function getsjtj() { //获取年度数据统计
            ajaxgeneral({
                url: 'action/usPolicyInfo/selectCountYt?source=PC',
                data: {
                    currentUserCode: web.currentUser.username
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $('#szgz').text(res.data.pro)
                    $('#xtjj').text(res.data.bra)
                }
            })
            ajaxgeneral({
                url: 'action/usPolicyInfo/selectCountZb?source=PC',
                data: {
                    currentUserCode: web.currentUser.username
                },
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $('#zcxj').text(res.data)
                }
            })
        }

        function godwdyyt() { //第一议题more
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'dyyt'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/dwdyytxx/sso'
            tourl = tourl + '?appcode=dwdyytxx&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&tag=pro'
            window.open(tourl, "_blank");
        }

        $(document).on("click", ".godwdyytDetail", function() { //第一议题点击详情
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'dyyt'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/dwdyytxx/sso'
            var id = $(this).attr('id')
            tourl = tourl + '?appcode=dwdyytxx&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&tag=pro' + '&id=' + id
            window.open(tourl, "_blank");
        })

        function gohlgj() { //合力攻坚more
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'hlgj'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/hlgj/action/goUrl/sso/goToDetail'
            var id = '1'
            var type = '3'
            tourl = tourl + '?appcode=hlgj&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&id=' + id + '&type=' + type
            window.open(tourl, "_blank");
        }

        function gohlgj2() { //合力攻坚more
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'yxf'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/hlgj/action/goUrl/sso/goToDetail'
            var id = '1'
            var type = '2'
            tourl = tourl + '?appcode=hlgj&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&id=' + id + '&type=' + type
            window.open(tourl, "_blank");
        }

        function gofdzl() { //党建资源共享
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'fdzl'
                },
                success: function(res) {}
            });

            var tourl = getStartUrl() + '/fdzl/action/goUrl/sso/goToDetail'
                //var tourl = 'http://************:8088/fdzl/action/goUrl/sso/goToDetail'
            tourl = tourl + '?appcode=fdzl&from=oa&loginuser=' + getRsa(web.currentUser.username)
            window.open(tourl, "_blank");
        }


        $(document).on("click", ".gohlgjDetail", function() { //合力攻坚点击详情
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'hlgj'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/hlgj/action/goUrl/sso/goToDetail'
            var id = $(this).attr('ID')
            var type = $(this).attr('TYPE')
            tourl = tourl + '?appcode=hlgj&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&id=' + id + '&type=' + type
            window.open(tourl, "_blank");

        })
        function gopzdy(){ //派驻党员
             ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'hlgj'
                },
                success: function(res) {}
            });
              var tourl = getStartUrl() + '/hlgj/sso'
            tourl = tourl + '?appcode=hlgj&from=oa&target=ledgerIndex&loginuser=' + getRsa(web.currentUser.username)
            window.open(tourl, "_blank");
        }

        function goyglt() { //进入员工论坛
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'yglt'
                },
                success: function(res) {}
            });

            var tourl = getStartUrl() + '/yglt/us/account/goToDetail/sso'
            tourl = tourl + '?appcode=yglt&from=oa&userCode=' + getRsa(web.currentUser.username) + '&loginuser=' + getRsa(web.currentUser.username)
            window.open(tourl, "_blank");
        }

        function gozbjqxxzd() { //支部近期more
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'jqxx'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/zbjqxxzd/sso'
            tourl = tourl + '?appcode=zbjqxxzd&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&tag=serach'
            window.open(tourl, "_blank");
        }

        $(document).on("click", ".gozbjqDetail", function() { //支部近期点击详情
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'jqxx'
                },
                success: function(res) {}
            });
            var tourl = getStartUrl() + '/zbjqxxzd/sso'
            var id = $(this).attr('id')
            tourl = tourl + '?appcode=zbjqxxzd&from=oa&loginuser=' + getRsa(web.currentUser.username) + '&tag=serach' + '&id=' + id
            window.open(tourl, "_blank");
        })

        //领题破题内部完成率切换
        $(document).on("click", ".change0", function() {
            $(this).addClass('active')
            $('.change1').removeClass('active')
            $('.tjbox2').hide()
            $('.tjbox1').show()
        })
        $(document).on("click", ".change1", function() {
            $(this).addClass('active')
            $('.change0').removeClass('active')
            $('.tjbox2').show()
            $('.tjbox1').hide()

            $('.tjbox2').css('display', 'flex')
        })

        $(document).on("click", ".godjzdy", function() { //进入党建指导员
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'djzdy'
                },
                success: function(res) {}
            });
            var path = 'http://10.92.81.163:8089/djfupt/sso?'
            path += 'appcode=djfupt&from=oa&uid=' + getRsa(web.currentUser.username)
            window.open(path)
        })

        $(document).on("click", ".gohlgj", function() { //进入合力攻坚
            ajaxgeneral({
                url: 'action/screenCount/record',
                // contentType: 'application/json; charset=utf-8',
                data: {
                    name: 'hlgj'
                },
                success: function(res) {}
            });
            var path = getStartUrl() + '/hlgj/sso?'
            path += 'appcode=hlgj&from=oa&uid=' + getRsa(web.currentUser.username)
            window.open(path)
        })

        $(document).on("click", "#moremenu", function() { //更多菜单
            $(".triangle").show();
            $(document).one("click", function() {
                $(".triangle").hide();
            });
            if (event.stopPropagation) { //阻止默认行为和冒泡
                event.stopPropagation();
            } else {
                event.cancelBubble = true;
            }
        })

        $(document).on("click", ".djspan", function() { //中间的大屏切换
            $('.djspan').css('color', '#d31805')
            $('.hlgjspan').css('color', '#999')
            $('.djbox').show()
            $('.hlgjBox').hide()
        })
        $(document).on("click", ".hlgjspan", function() {
            $('.hlgjspan').css('color', '#d31805')
            $('.djspan').css('color', '#999')
            $('.hlgjBox').show()
            $('.djbox').hide()
        })

        function toIndex() {
            var url = web.rootdir + 'html/dataIndex/index.html'
            window.open(url, '_blank');
        }

        function getVideo() { //获取企业文化中的视频
            ajaxgeneral({
                url: 'action/commom/visido',
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var list = res.data
                    var str = ''
                    var length = list.length
                    if (list.length > 2) {
                        length = 3
                    }
                    for (var i = 0; i < length; i++) {
                        var item = list[i]
                        str += '<div class="swiper-slide" style="background:#fffdfd;border-radius:0 0 0 50px;"><div class="completionBox">' +
                            '<div class="left"><video width="100%"  height="100%" controls class="videoClass" src="' + item.videoFile[0].anonymousFilePath + '" type="video/mp4"></video><p style="text-align:center;font-size: 0.2rem;font-weight: 700;margin-top: .1rem;">' + item.title + '</p></div>' +
                            '<div class="right"><div class="container"><p class="content">' + item.textDec + '' +
                            '</p></div> </div></div></div>'
                    }

                    var Str = '<div class="swiper-wrapper">' + str + '</div><div class="swiper-pagination"></div>'

                    $('.swiper-container').append(Str)

                    var pw = $('.panel2').width()
                    $('.swiperBox').width(pw * 3)
                    var mySwiper = new Swiper('.swiper-container', {
                        direction: 'horizontal',
                        // loop: true,
                        autoplayDisableOnInteraction: true,
                        speed: 2000,
                        paginationClickable: true,
                        effect: 'fade',
                        pagination: '.swiper-pagination', // 如果需要分页器
                        autoplay: 6000
                    })
                    $('.triangle').hide()
                    $('.swiper-slide').mouseenter(function() { // 鼠标移入停止自动滚动
                        mySwiper.stopAutoplay()
                    })

                    $('.swiper-slide').mouseleave(function() { // 鼠标移出开始自动滚动
                        mySwiper.startAutoplay();
                    })
                }
            });
        }
        // 智能问答模块
        $(document).on("click", ".znwd", function() { //中间的大屏切换
            $('.myplant').show()
        })
        $(document).on("click", ".box1", function() { //中间的大屏切换
            $('.myplant').hide()
        })
        function closeMyplant() {
            $('.myplant').hide()
        }

        // 问答渲染
        $(document).on("click", ".wendaOpen", function () {
            if($('#wdsearch').val()){
            var str = ''
            str =  '<div class="myword">\n' +
            '          <div class="tx" style="text-align: right;"><img  src="../../images/screen/zn6.png"  alt=""/></div>\n' +
            '             <div class="cont" style="margin-left: 29%;">\n' + $('#wdsearch').val() +
            '             </div>\n' +
            '         </div>'

            $('.wd').append(str)
            ajaxgeneral({
                url: 'action/UsTestType/send',
                data:{
                    "content":$('#wdsearch').val(),
                    pmInsId:timers
                },
                loading:true,
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    $('#wdsearch').val('')
                    var str2 = ' <div class="znword">\n' +
                    '         <div class="tx"><img  src="../../images/screen/zn5.png"  style="width: 40px;"  alt=""/></div>\n' +
                    '               <div class="cont">\n' + res.data[0] +
                    '               </div>\n' +
                    '        </div>'

                    $('.wd').append(str2)
                }
            });
            }
        })

        //创建新问答
        $(document).on("click", ".creatwd", function () {
            $('.wd').empty()
            timers = Date.now()
        })

        function getwenda(val){
            $('.wdList').empty()
            // 获取历史问答记录
            ajaxgeneral({
                url: 'action/UsHistoryRecord/getAllNoPage',
                data:{content:val},
                loading:true,
                contentType: 'application/json; charset=utf-8',
                success: function(res) {
                    var str = ''
                    for (var i = 0; i < res.data.length; i++) {
                        str +=  '<li>\n' +
                        '           <a href="#" class="wdinfo" pmInsId='+ res.data[i].pmInsId+'>'+  res.data[i].textContent +'</a>\n' +
                        '           <i class="delwd" pmInsId='+ res.data[i].pmInsId+'><img pmInsId='+ res.data[i].pmInsId+' src="../../images/screen/zn10.png" alt="" ></i>\n' +
                        '       </li>'
                    }

                    $('.wdList').append(str)
                }
            });

        }

        // 问答单个删除
        $(document).on("click", ".delwd", function () {
            var pmInsId = $(this).attr('pmInsId');
            $.messager.confirm('确认', '确认想要删除该记录吗？', function (r) {
                if (r) {
                    ajaxgeneral({
                        url: 'action/UsHistoryRecord/clearByPmInsId?pmInsId=' + pmInsId,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            getwenda($('#search').val())
                        }
                    });
                }
            });
        })

        // 问答列表查询
        $(document).on("click", "#searchBtn", function () {
            getwenda($('#search').val())
        })
        // 问答列表渲染
        $(document).on("click", ".wdinfo", function () {
            var pmInsId = $(this).attr('pmInsId');
            ajaxgeneral({
                url: 'action/UsHistoryRecord/getAllDetailNoPage?pmInsId=' + pmInsId,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    $('.wd').empty()
                    var strs = ''
                    for (var i = 0; i < res.data.length; i++) {
                        if(res.data[i].type ==1){
                            strs +=  '<div class="myword">\n' +
                            '          <div class="tx" style="text-align: right;"><img  src="../../images/screen/zn6.png"  alt=""/></div>\n' +
                            '             <div class="cont" style="margin-left: 29%;">\n' + res.data[i].textContent +
                            '             </div>\n' +
                            '         </div>'

                        }else{
                            strs += ' <div class="znword">\n' +
                            '         <div class="tx"><img  src="../../images/screen/zn5.png"  style="width: 40px;"  alt=""/></div>\n' +
                            '               <div class="cont">\n' + res.data[i].textContent +
                            '               </div>\n' +
                            '        </div>'
                        }
                    }
                    $('.wd').append(strs)
                    timers = pmInsId
                }
            });
        })
        // 全部清除
        $(document).on("click", ".sbtn", function () {
            $.messager.confirm('确认', '确认想要全部删除吗？', function (r) {
                if (r) {
                    ajaxgeneral({
                        url: 'action/UsHistoryRecord/clearAll',
                        data: {},
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            getwenda($('#search').val())
                            $('.creatwd').trigger('click')
                        }
                    });
                }
            });
        })

        $(function(){
            $("#wdsearch").keydown(function(e){
            if(e.keyCode ==13){ // 触发键盘事件enter 防止冒泡产生
                $("#wdsearch").blur()
               $('.wendaOpen').trigger('click')
                return false;
            }
        });
        })


    </script>
</head>

<body>
    <header>
        <!-- 头部公共部分 -->
        <div class="showTime">
            <img src="../../images/screen/msg.png" alt="">
        </div>
        <span id="trueName"></span>
        <div class="msgNum"></div>
        <div class="msgbox">
            <p class="title">站内信<span class="fr"><a class="closeMsg">【关闭】</a></span></p>
        </div>
    </header>
    <div class="mainbox">
        <div class="column column1">
            <!-- 左侧部分 -->
            <div class="panels line panel2" style="margin-top:0;">
                <h2>理论宣讲课堂</h2>
                <!-- 轮播图 -->
                <div class="swiper-container">
                    <div class="swiper-pagination"></div>
                </div>
            </div>
            <div class="panels bar  panel1">
                <h2 style="margin-top: 0.05rem;"> 最新通知 </h2>
                <div class="listBox" id="dy">
                    <div class="listBox-tops">
                        <div class="title">党委第一议题学习</div>
                        <div class="more" onclick="godwdyyt()">更多>></div>
                    </div>
                    <div class="info1"></div>
                </div>
                <div class="listBox" id="zb">
                    <div class="listBox-tops">
                        <div class="title">支部近期学习重点</div>
                        <div class="more" onclick="gozbjqxxzd()">更多>></div>
                    </div>
                    <div class="info1"></div>
                </div>
                <div class="listBox" id="dj" style="border:none;">
                    <div class="listBox-tops">
                        <div class="title">党建指导员</div>
                        <div class="more">更多>></div>
                    </div>
                    <div class="info1"></div>
                </div>
            </div>
        </div>

        <div class="column middle djzdy column2">
            <!-- 中间部分 -->
            <h2 class="" style="position: relative;line-height: 0.3rem;">
                <span class="djspan pointer" style="color: #d31805;">党建指导员</span>
                <span class="hlgjspan pointer" style="margin-left: 38%;color: #999;">领题破题 合力攻坚</span>
            </h2>

            <div class="djbox">
                <div class="map">
                    <div class="chart">
                        <!-- 渲染地图 -->
                    </div>
                </div>
                <p class="completionTitle">党建指导员政策宣讲完成情况</p>
                <div class="completion">
                    <ul class="listOne fl">
                        <!-- 渲染完成率0-6 -->
                    </ul>
                    <ul class="listTwo fl">
                        <!-- 渲染完成率7-12 -->
                    </ul>
                    <ul class="listThree fl">
                        <!-- 渲染完成率13-18 -->
                    </ul>
                </div>
            </div>

            <div class="hlgjBox hide">
                <div class="info listBox" id="hlgj" style="border-bottom: none;margin: 0;width: 100%;">
                    <div class="listBox-tops">
                        <div class="title">工作动态</div>
                        <div class="more" onclick="gohlgj()">更多>></div>
                    </div>
                    <div class="info1">
                        <!-- 渲染工作动态信息 -->
                    </div>
                </div>
                <div class="hide">
                    <div class="bg">
                        <h2>河南公司整体进度 <span style="color: #D31805; font-size: 13px;" class="deadline">(截止0000年0月0日)</span></h2>
                        <div class="bbbox">
                            <div class="bb b4">
                                <div> <img src="../../images/screen/hlgj/a4.png" class="aimg" alt="" /> </div>
                                <div>
                                    <p style="font-weight: 700;">公司项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                                    <div class="sl">总数量:0 已完成:0</div>
                                </div>
                            </div>
                            <div class="bb b1">
                                <div> <img src="../../images/screen/hlgj/a1.png" class="aimg" alt="" /> </div>
                                <div>
                                    <p style="font-weight: 700;">书记项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                                    <div class="sl">总数量:0 已完成:0</div>
                                </div>
                            </div>
                            <div class="bb b2">
                                <div> <img src="../../images/screen/hlgj/a2.png" class="aimg" alt="" /> </div>
                                <div>
                                    <p style="font-weight: 700;">重点项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                                    <div class="sl">总数量:0 已完成:0</div>
                                </div>
                            </div>
                            <div class="bb b3">
                                <div> <img src="../../images/screen/hlgj/a3.png" class="aimg" alt="" /> </div>
                                <div>
                                    <p style="font-weight: 700;">一般项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                                    <div class="sl">总数量:0 已完成:0</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class=" successTj">
                        <!-- 完成率统计 -->
                        <div>
                            <span class="fl pointer ml10" style="font-size: 16px;color:#333;font-weight:700;">
                            <span class="active change0">各分公司完成率</span> | <span class="change1">公司本部完成率</span>
                            </span>
                        </div>
                        <div class="tjbox1">
                            <div class="left">
                                <!-- 各分公司完成率渲染左 -->
                            </div>
                            <div class="right">
                                <!-- 各分公司完成率渲染右 -->
                            </div>
                        </div>
                        <div class="tjbox2 hide">
                            <div class="left">
                                <!-- 公司本部完成率渲染左 -->
                            </div>
                            <div class="right">
                                <!-- 公司本部完成率渲染右 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="column middle column3 hide">
            <!-- 中间部分 -->
            <!-- <div class="column middle hlgjBox column3 hide"> 中间部分 -->
            <div class="info listBox" id="hlgj" style="border-bottom: none;margin: 0;width: 100%;">
                <div class="listBox-tops">
                    <div class="title">工作动态</div>
                    <div class="more" onclick="gohlgj()">更多>></div>
                </div>
                <div class="info1">
                    <!-- 渲染工作动态信息 -->
                </div>
            </div>
            <div class="bg">
                <h2>河南公司整体进度 <span style="color: #D31805; font-size: 13px;" class="deadline">(截止0000年0月0日)</span></h2>
                <div class="bbbox">
                    <div class="bb b4">
                        <div> <img src="../../images/screen/hlgj/a4.png" class="aimg" alt="" /> </div>
                        <div>
                            <p style="font-weight: 700;">公司项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                            <div class="sl">总数量:0 已完成:0</div>
                        </div>
                    </div>
                    <div class="bb b1">
                        <div> <img src="../../images/screen/hlgj/a1.png" class="aimg" alt="" /> </div>
                        <div>
                            <p style="font-weight: 700;">书记项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                            <div class="sl">总数量:0 已完成:0</div>
                        </div>
                    </div>
                    <div class="bb b2">
                        <div> <img src="../../images/screen/hlgj/a2.png" class="aimg" alt="" /> </div>
                        <div>
                            <p style="font-weight: 700;">重点项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                            <div class="sl">总数量:0 已完成:0</div>
                        </div>
                    </div>
                    <div class="bb b3">
                        <div> <img src="../../images/screen/hlgj/a3.png" class="aimg" alt="" /> </div>
                        <div>
                            <p style="font-weight: 700;">一般项目<span style="font-size: 16px;margin-left: 5px;" class="bj">0%</span></p>
                            <div class="sl">总数量:0 已完成:0</div>
                        </div>
                    </div>

                </div>
            </div>

            <div class=" successTj">
                <!-- 完成率统计 -->
                <div>
                    <span class="fl pointer ml10" style="font-size: 14px;color:#333;font-weight:700;"> 
                    <span class="active change0">各分公司完成率</span> | <span class="change1">公司本部完成率</span>
                    </span>
                </div>
                <div class="tjbox1">
                    <div class="left">
                        <!-- 各分公司完成率渲染左 -->
                    </div>
                    <div class="right">
                        <!-- 各分公司完成率渲染右 -->
                    </div>
                </div>
                <div class="tjbox2 hide">
                    <div class="left">
                        <!-- 公司本部完成率渲染左 -->
                    </div>
                    <div class="right">
                        <!-- 公司本部完成率渲染右 -->
                    </div>
                </div>
            </div>
        </div>

        <div class="column column4" style="position: relative;">
            <!-- 最右侧部分 -->
            <div class="panels bar1  panel4">
                <h2>业务入口</h2>
                <div class="chart">
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/dwdyytxx/sso?" appCode="dwdyytxx">
                        <div class="menutxt">党委第一议题</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/zbjqxxzd/sso?" appCode="zbjqxxzd">
                        <div class="menutxt">支部学习重点</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/djfupt/sso?" appCode="djfupt">
                        <div class="menutxt">党建指导员</div>
                    </div>
                    <!-- <div class="menuCard">
                        <img  style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                            path="/hlgj/sso?" appCode="hlgj">
                        <div class="menutxt" style="font-size: 12px;">领题破题</br>合力攻坚</div>
                    </div> -->
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt="" path="http://iportal.ha.cmcc/HaPortalSso/inner_sso_4a/bps_dfglxt.jsp" appCode="dfglxt">
                        <div class="menutxt">党费管理</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/pzdy.png" alt="" onclick="gopzdy()">
                        <div class="menutxt">派驻党员</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt="" path="http://iportal.ha.cmcc/portalweb/portalui/partyc/html/index.html?" appCode="djwh">
                        <div class="menutxt">党建文化模块</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt="" onclick="goyglt()">
                        <div class="menutxt" style="padding: 5px 0">畅所豫言</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/tzjyxcxx/sso?" appCode="tzjyxcxx">
                        <div class="menutxt">建言献策</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/hlgj/sso?" appCode="hlgj2">
                        <div class="menutxt">党员突击队</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt="" onclick="toIndex()">
                        <div class="menutxt">访客分析</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt=""
                             path="/djzs/sso?" appCode="djzs">
                        <div class="menutxt">党建工作助手</div>
                    </div>
                    <div class="menuCard">
                        <img style="width: 0.75rem;height:0.75rem" src="../../images/screen/<EMAIL>" alt="" path="xxx" appCode="fdzl">
                        <div class="menutxt">党建资源共享</div>
                    </div>

                </div>
            </div>
            <div id="yqff" style="width: 100%;cursor: pointer;">
                <img src="../../images/screen/topBanner.png" alt="" style="width: 100%;">
            </div>
            <div class="panels pie1  panel6">
                <!-- 年度数据统计 -->
                <h2 style="margin-top: 0.05rem;">年度数据统计</h2>
                <div class="showBox">
                    <div class="show">
                        <div id="zcxj" class="num">0</div>
                        <div class="text">支部近期学习重点数</div>
                    </div>
                    <div class="show">
                        <div id="szgz" class="num">0</div>
                        <div class="text">省公司第一议题数量</div>
                    </div>
                    <div class="show">
                        <div id="xtjj" class="num">0</div>
                        <div class="text">分公司第一议题数量</div>
                    </div>
                </div>
                <div class="chart"></div>
                <div class="panelBox">
                    <div class="numberBox">
                        <div><span class="number">0</span><span class="times">次</span></div>
                        <div style="border-left: 0.5px solid #bbb;border-right: 0.5px solid #bbb;"><span class="number">0</span><span class="times">次</span>
                        </div>
                        <div><span class="number">0</span><span class="times">个</span></div>
                    </div>
                    <div class="countBox">
                        <div>现有网格数：<span class="gridcount"></span></div>
                        <div>现有指导员人数：<span class="admincount"></span></div>
                    </div>
                </div>
            </div>

            <div class="znwd" id="aiAssistant">
                <div class="ai-close-btn" id="aiCloseBtn">×</div>
                <img src="../../images/screen/ai.gif" alt="AI助手" draggable="false">
            </div>
        </div>

        <div class="myplant"  style="display: none;">
            <div class="box1" style="display: none;">>></div>
            <iframe id="iframePlant" src="" frameborder="0" scrolling="no"  class="myIframe"></iframe>
            <div class="box2" style="display: none;">
                <div class="btn creatwd">新建对话框+</div>
                <div class="seachBox">
                    <input type="text" id="search" name="search" style="width: 100%;border: none;" />
                    <i  id="searchBtn"><img src="../../images/screen/zn7.png" alt="" ></i>
                </div>
                <hr style="margin: 20px 0 0;"  />
                <div class="sbtn" >全部清除</div>
                <ul class="wdList">
                    <!-- <li>
                        <a href="">党建助手系统郑州分公司有多少</a>
                         <i><img src="../../images/screen/zn10.png" alt="" ></i>
                    </li> -->

                </ul>
            </div>
            <div class="box3" style="display: none;">
                <div class="wdArea">
                    <div class="dataShow">
                        <img  src="../../images/screen/zn6.png" alt="" />
                        <div style="font-weight: 700;">你好，我是数智党建，智能问答小助手</div>
                        <div>深海检索党建工作助手和党建资源共享平台数据，轻松获取所需内容</div>
                        <div><span>探索无限知识，只需一键查询！</span>为您揭示数据背后的无线可能。</div>
                    </div>
                    <div class="card_box">
                        <div  class="card" style="margin-bottom: 10px;">
                            <div class="left">
                                <img style="width: 35px;" src="../../images/screen/zn1.png" alt="" />
                            </div>
                            <div class="right">
                                <div>人员数据查询</div>
                                <div>对省、市和县公司不同类型管理员人员数据.....</div>
                            </div>
                        </div>
                        <div  class="card" style="margin-bottom: 10px;">
                            <div class="left">
                                <img style="width: 35px;" src="../../images/screen/zn3.png" alt="" />
                            </div>
                            <div class="right">
                                <div>工单数据查询</div>
                                <div>对省、市和县公司个人权限已办待办数据查询......</div>
                            </div>
                        </div>
                        <div  class="card">
                            <div class="left">
                                <img style="width: 35px;" src="../../images/screen/zn4.png" alt="" />
                            </div>
                            <div class="right">
                                <div>新闻数据查询</div>
                                <div>对省、市和县公司不同类型新闻数据......</div>
                            </div>
                        </div>
                        <div  class="card">
                            <div class="left">
                                <img style="width: 35px;" src="../../images/screen/zn2.png" alt="" />
                            </div>
                            <div class="right">
                                <div>文件数据查询</div>
                                <div>对省、市和县公司工单中所包含的文件数据查询......</div>
                            </div>
                        </div>
                    </div>

                    <div class="wd">

                        <!-- <div class="myword">
                            <div class="tx" style="text-align: end;"><img  src="../../images/screen/zn6.png"  alt=""/></div>
                            <div class="cont" style="margin-left: 29%;">
                                请问对省、市和县公司不同类型管理员人员数据对省、市和县公司不同类型管理员人员数据
                            </div>
                        </div>

                        <div class="znword">
                            <div class="tx"><img  src="../../images/screen/zn5.png"  style="width: 40px;"  alt=""/></div>
                            <div class="cont">
                                请问对省、市和县公司不同类型管理员人员数据对省、市和县公司不同类型管理员人员数据
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="wdBox">
                    <input type="text" id="wdsearch" name="wdsearch" style="width: 100%;border: none;" />
                    <i><img  class="wendaOpen" src="../../images/screen/zn7.png" alt="" ></i>
                </div>
            </div>
        </div>
    </div>
</body>

</html>

<style>
    .wd{
        padding: 0 10px;
    }

   .wd .znword .cont{
        width: 70%;
        border: 1px solid #f8d9d6;
        padding: 10px;
        margin-top: -3%;
        margin-left: 1%;
        border-radius: 20px 5px;
        line-height: 20px;
    }


    .wd .myword .cont{
        width: 70%;
        background: #f8d9d6;
        padding: 10px;
        margin-left: 29%;
        margin-top: -3%;
        border-radius: 5px 20px;
        line-height: 20px;

    }
    .wdArea{
        height: 84vh;
        overflow-y: scroll;
    }
    .wdBox{
        display: flex;
        align-items: center;
        border: 1px solid #c1c1c1;
        padding: 2px 10px;
        border-radius: 30px;
        margin: 0 10px;

        /* position: absolute; */
        /* bottom: 20px; */
    }
    .card_box{
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        margin: 20px 0;
    }
    .card_box  .card{
        width: 45%;
        display: flex;
        align-items: center;
        background-color: #fafafa;

    }
    .card_box  .card .left,
    .card_box  .card .right{
        padding: 10px;
    }


    .znwd{
        position: absolute;
        bottom:1.875rem;
        right: 0;
    }
    .znwd img{
        width: 1.5rem;
    }
    .myplant{
        width: 35vw;
        height: 100vh;
        position: absolute;
        top: 0px;
        right: 0px;
        background: #fff;
        display: flex;
        z-index: 50;

    }

    .myplant .box1{
        cursor: pointer;
        flex: 1;
        border: 1px solid #e8e8e8;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #999;

    }
    .myplant .box2{
        flex: 6;
        border: 1px solid #e8e8e8;
        padding: 10px;
    }

    .myplant .box2 .btn{
        width: 60%;
        padding: 10px;
        font-size: 16px;
        border-radius: 10px;
        background: #d31704;
        color: #fff;
        margin: 10px auto;
    }
    .myplant .box2 .seachBox{
        display: flex;
        align-items: center;
        border: 1px solid #e8e8e8;
        padding: 2px 10px;
        border-radius: 30px;
    }

    .myplant .box2 .sbtn{
        width: 40%;
        padding: 5px;
        font-size: 12px;
        border-radius: 10px;
        text-align: center;
        background: #d31704;
        color: #fff;
        margin: 5px auto;
        cursor: pointer;
    }

    .myplant .box2 ul li {
        width: 100%;
        margin: 8px 0;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .myplant .box2 ul li a{
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        -ms-text-overflow: ellipsis;
    }

     .myplant .box3{
        flex: 12;
        border: 1px solid #e8e8e8;
        /* position: relative; */
    }

    .dataShow{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 14px;
    }
    .dataShow img{
        margin: 20px 0 10px;
    }
    .dataShow div{
        margin: 5px;
    }
    .dataShow span{
        color: #d31704;

    }
    .myIframe{
        flex: 18;
    }
</style>

<script>
// AI助手拖拽功能
function initAIDragAndDrop() {
    const aiAssistant = document.getElementById('aiAssistant');
    const aiCloseBtn = document.getElementById('aiCloseBtn');

    if (!aiAssistant) {
        console.error('AI助手元素未找到');
        return;
    }

    let isDragging = false;
    let startX, startY, startLeft, startTop;
    let savedPosition = localStorage.getItem('aiAssistantPosition');

    // 确保AI助手默认显示
    aiAssistant.classList.remove('hidden');

    // 恢复保存的位置
    if (savedPosition) {
        try {
            const position = JSON.parse(savedPosition);
            aiAssistant.style.right = 'auto';
            aiAssistant.style.left = position.left + 'px';
            aiAssistant.style.top = position.top + 'px';
            aiAssistant.style.transform = 'none';
        } catch (e) {
            console.warn('恢复AI助手位置失败，使用默认位置');
            localStorage.removeItem('aiAssistantPosition');
        }
    }

    // 检查是否之前被隐藏，但提供恢复选项
    if (localStorage.getItem('aiAssistantHidden') === 'true') {
        aiAssistant.classList.add('hidden');
        console.log('AI助手已隐藏，双击页面空白处或按Ctrl+Alt+A可重新显示');
    }

    // 鼠标按下事件
    aiAssistant.addEventListener('mousedown', function(e) {
        // 如果点击的是关闭按钮，不进行拖拽
        if (e.target === aiCloseBtn || e.target.classList.contains('ai-close-btn')) {
            return;
        }

        isDragging = true;
        aiAssistant.classList.add('dragging');

        const rect = aiAssistant.getBoundingClientRect();
        startX = e.clientX;
        startY = e.clientY;
        startLeft = rect.left;
        startTop = rect.top;

        // 禁用文本选择
        document.body.style.userSelect = 'none';

        e.preventDefault();
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        let newLeft = startLeft + deltaX;
        let newTop = startTop + deltaY;

        // 边界检测
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = aiAssistant.offsetWidth;
        const elementHeight = aiAssistant.offsetHeight;

        newLeft = Math.max(0, Math.min(newLeft, windowWidth - elementWidth));
        newTop = Math.max(0, Math.min(newTop, windowHeight - elementHeight));

        aiAssistant.style.right = 'auto';
        aiAssistant.style.left = newLeft + 'px';
        aiAssistant.style.top = newTop + 'px';
        aiAssistant.style.transform = 'none';
    });

    // 鼠标释放事件
    document.addEventListener('mouseup', function(e) {
        if (!isDragging) return;

        isDragging = false;
        aiAssistant.classList.remove('dragging');
        aiAssistant.classList.add('snapping');

        // 恢复文本选择
        document.body.style.userSelect = '';

        // 自动靠边
        const rect = aiAssistant.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const centerX = rect.left + rect.width / 2;

        let finalLeft, finalRight;

        if (centerX < windowWidth / 2) {
            // 靠左边
            finalLeft = 20;
            finalRight = 'auto';
        } else {
            // 靠右边
            finalLeft = 'auto';
            finalRight = 20;
        }

        // 保持当前高度
        const finalTop = rect.top;

        aiAssistant.style.left = finalLeft === 'auto' ? 'auto' : finalLeft + 'px';
        aiAssistant.style.right = finalRight === 'auto' ? 'auto' : finalRight + 'px';
        aiAssistant.style.top = finalTop + 'px';
        aiAssistant.style.transform = 'none';

        // 保存位置到localStorage
        const position = {
            left: finalLeft === 'auto' ? windowWidth - 20 - aiAssistant.offsetWidth : finalLeft,
            top: finalTop,
            side: finalLeft === 'auto' ? 'right' : 'left'
        };
        localStorage.setItem('aiAssistantPosition', JSON.stringify(position));

        // 移除动画类
        setTimeout(() => {
            aiAssistant.classList.remove('snapping');
        }, 300);
    });

    // 关闭按钮事件
    if (aiCloseBtn) {
        aiCloseBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            e.preventDefault();
            aiAssistant.classList.add('hidden');
            localStorage.setItem('aiAssistantHidden', 'true');
            console.log('AI助手已隐藏，双击页面空白处或按Ctrl+Alt+A可重新显示');
        });
    }

    // 双击显示隐藏的AI助手
    document.addEventListener('dblclick', function(e) {
        // 检查点击的不是AI助手本身
        if (!aiAssistant.contains(e.target) && aiAssistant.classList.contains('hidden')) {
            aiAssistant.classList.remove('hidden');
            localStorage.removeItem('aiAssistantHidden');
            console.log('AI助手已重新显示');
        }
    });

    // 添加键盘快捷键恢复AI助手 (Ctrl + Alt + A)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.altKey && e.key === 'a') {
            if (aiAssistant.classList.contains('hidden')) {
                aiAssistant.classList.remove('hidden');
                localStorage.removeItem('aiAssistantHidden');
                console.log('AI助手已通过快捷键恢复显示');
            }
        }
    });

    // 触摸设备支持
    aiAssistant.addEventListener('touchstart', function(e) {
        // 如果点击的是关闭按钮，不进行拖拽
        if (e.target === aiCloseBtn || e.target.classList.contains('ai-close-btn')) {
            return;
        }

        isDragging = true;
        aiAssistant.classList.add('dragging');

        const touch = e.touches[0];
        const rect = aiAssistant.getBoundingClientRect();
        startX = touch.clientX;
        startY = touch.clientY;
        startLeft = rect.left;
        startTop = rect.top;

        e.preventDefault();
    });

    document.addEventListener('touchmove', function(e) {
        if (!isDragging) return;

        const touch = e.touches[0];
        const deltaX = touch.clientX - startX;
        const deltaY = touch.clientY - startY;

        let newLeft = startLeft + deltaX;
        let newTop = startTop + deltaY;

        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = aiAssistant.offsetWidth;
        const elementHeight = aiAssistant.offsetHeight;

        newLeft = Math.max(0, Math.min(newLeft, windowWidth - elementWidth));
        newTop = Math.max(0, Math.min(newTop, windowHeight - elementHeight));

        aiAssistant.style.right = 'auto';
        aiAssistant.style.left = newLeft + 'px';
        aiAssistant.style.top = newTop + 'px';
        aiAssistant.style.transform = 'none';

        e.preventDefault();
    });

    document.addEventListener('touchend', function(e) {
        if (!isDragging) return;

        isDragging = false;
        aiAssistant.classList.remove('dragging');
        aiAssistant.classList.add('snapping');

        const rect = aiAssistant.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const centerX = rect.left + rect.width / 2;

        let finalLeft, finalRight;

        if (centerX < windowWidth / 2) {
            finalLeft = 20;
            finalRight = 'auto';
        } else {
            finalLeft = 'auto';
            finalRight = 20;
        }

        const finalTop = rect.top;

        aiAssistant.style.left = finalLeft === 'auto' ? 'auto' : finalLeft + 'px';
        aiAssistant.style.right = finalRight === 'auto' ? 'auto' : finalRight + 'px';
        aiAssistant.style.top = finalTop + 'px';
        aiAssistant.style.transform = 'none';

        const position = {
            left: finalLeft === 'auto' ? windowWidth - 20 - aiAssistant.offsetWidth : finalLeft,
            top: finalTop,
            side: finalLeft === 'auto' ? 'right' : 'left'
        };
        localStorage.setItem('aiAssistantPosition', JSON.stringify(position));

        setTimeout(() => {
            aiAssistant.classList.remove('snapping');
        }, 300);
    });
}
</script>

</body>
</html>
