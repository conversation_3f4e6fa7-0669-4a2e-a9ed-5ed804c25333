<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>管理员管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
    th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <!-- 覆盖css样式表 -->
    <link href="../../css/easyui.css?v=svn.revision" rel="stylesheet" />
    <link href="../../css/index.css?v=svn.revision" rel="stylesheet" />

    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.aduq.js?v=svn.revision" th:src="@{/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>

    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <!-- <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script> -->
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/screenAdminManager/findAllInfo", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "columns": [
                        [ //列
                            { title: "单位组织", field: "companyName", width: 80, tooltip: true, align: "center" },
                            { title: "姓名", field: "truename", width: 60, tooltip: true, align: "center" },
                            { title: "OA账号", field: "username", width: 80, tooltip: true, align: "center" },
                            { title: "管理员类型", field: "type", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {
                                    if (value == '0') {
                                        return '超级管理员'
                                    }   else if (value == '1') {
                                        return '省公司管理员'
                                    } else if (value == '2') {
                                        return '分公司管理员'
                                    }
                                }
                            },
                            { field: "opt", title: "操作", width: 80, rowspan: 1, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    g += "<a href='#' class='delete'  data-id='" + row.id + "'>【删除】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/screenAdminManager/create",//新增命令
                    "updatacmd": "action/screenAdminManager/update",//修改命令
                    "beforerender": function (data) { },
                    "onSubmit": function (data) {
                        return !flag;
                    }
                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#glyTableReadForm"
                }
            };
            loadGrid(pageparam);

        });

        // 防止数据还没回来用户就再次点击
        var flag = false
        //选择人员
        function chooseuser() {
            if (flag) return
            if (top.chooseWeb && top.chooseWeb.chooseUsersVal) {
                top.chooseWeb.chooseUsersVal = {};
            }
            top.dialogP("html/choose/chooseUsersQuery.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB", false, 900, 500)
        }
        window.chooseUserCB = function (data) {
            flag = true
            ajaxgeneral({
                url: "action/usAdminManager/queryUserOrgInfo?userName=" + data.data[0].userName,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    flag = false
                    var data = res.data
                    // $('#glyTableAddForm #belongDept').val(data.belongDepartmentName);
                    // $('#glyTableAddForm #belongDeptCode').val(data.belongDepartmentCode);
                    $('#glyTableAddForm #companyName').val(data.belongCompanyName);
                    // $('#glyTableAddForm #belongComCode').val(data.belongCompanyCode);
                    $('#glyTableAddForm #username').val(data.username);
                    $('#glyTableAddForm #truename').val(data.truename);
                    $('#glyTableAddForm #type').val(1);
                },
                error: function (res) {
                    flag = false
                }
            });
        }

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('data-id');
            $.messager.confirm('确认', '您确认想要删除吗？', function (r) {
                if (r) {
                    ajaxgeneral({
                        url: 'action/screenAdminManager/deleteById?id=' + id,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $("#glyTable").datagrid("reload");
                        }
                    });
                }
            });
        })

        // //下载模板
        // function downloadFile() {
        //     window.open(web.rootdir + "action/usAdminManager/exportHadmin");
        // }
        // //导入excel
        // function uploadExcel() {
        //     $('#uploadFiles').trigger('click');
        // }
        // //导入excel后
        // function OtherInfo(data) {
        //     $('#glyTable').datagrid('load');
        // }
        // //导出
        // $(document).on('click', 'a.exporttable', function () {
        //     $("#glyTableQueryForm").attr("action", web.rootdir + "action/usAdminManager/exportExcel?type=1");
        //     $("#glyTableQueryForm").attr("method", "post");
        //     $("#glyTableQueryForm").submit();
        // });
    </script>
    <style>
        .uploadImageI{
            padding-top: 32px;
            margin-right: 100px;
        }
        /* a.btn{width: 80px;} */
        .cselectorImageUL{width: 85px;}
        .cselectorImageUL .btn{
            right: 0px;
        }
        .cselectorImageUL input{
            right: 5px;
        }
    </style>
</head>

<body class="body_page">
    <form id="glyTableQueryForm" >
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="40%"></td>
            </tr>
             <tr>
                <td align="right">单位组织</td>
                <td><input name="companyCode" class="easyui-combobox" style="width: 100%; height: 32px;"
                        data-options="
                    valueField: 'orgCode',
                    panelHeight:'200px',
                    ischooseall:true,
                    textField: 'orgName',
                    editable:false,
                    url: 'action/queryDictValue/find18CityOrgAndPOrg'" />
                </td>
                <td align="right">姓名</td>
                <td><input id="truename" name="truename" type="text" /></td>
                <td>
                    <a class="btn fr a_success ml10 showDialog "><span>新增</span></a>
                    <a class="btn fr searchtable"><span>查询</span></a>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="glyTable">
        <table id="glyTable"></table>
    </div>
    <!--新增修改的dialog页面-->
    <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:850px;height:320px;">
        <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()">
            <input id="id" name="id" type="hidden" />
            <!-- <input id="belongComCode" name="belongComCode" type="hidden" /> -->
            <!-- <input id="belongDeptCode" name="belongDeptCode" type="hidden" /> -->
            <input id="type" name="type" type="hidden" />
            <table border="0" cellpadding="0" cellspacing="10">
                <tr>
                    <td width="100" align="right"> 姓名<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="truename" name="truename" type="text" class="easyui-validatebox" readonly required='required' />

                    </td>
                    <td> <a class="btn a_warning ml10" title="人员查询" onclick="chooseuser()"><i class="iconfont">&#xe634;</i></a> </td>
                    <td width="100" align="right"> OA账号<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="username" name="username" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
                <tr>
                    <td width="100" align="right"> 所在单位<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="companyName" name="companyName" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                    <td></td>
                    <!-- <td width="100" align="right"> 管理员类型<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="type" name="type" class="easyui-combobox"
                            style="width: 100%; height: 32px;" data-options="
							valueField: 'value',
							panelHeight:'auto',
							ischooseall:true,
							textField: 'text',
							editable:false,
                            required:true,
                            data:[ {value:'0',text:'超级管理员'}, {value:'1',text:'省公司管理员'}, {value:'2',text:'分公司管理员'} ]" />
                    </td> -->
                </tr>
            </table>
        </form>
    </div>
</body>

</html>