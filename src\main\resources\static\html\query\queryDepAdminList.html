<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>分公司部门管理员管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/usAdminManager/findAllAdmin?roleUserId=djfupt_010", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "所在单位", field: "belongCom", width: 80, tooltip: true, align: "center" },
                            { title: "所在部门", field: "belongDept", width: 100, tooltip: true, align: "center" },
                            { title: "管理员类型", field: "roleUserId", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {

                                        return '分公司部门管理员'

                                }
                            },
                            { title: "姓名", field: "trueName", width: 60, tooltip: true, align: "center" },
                            { title: "OA账号", field: "userName", width: 80, tooltip: true, align: "center" },
                            { title: "最后修改时间", field: "modifiedTime", width: 90, tooltip: true, align: "center",
                                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    return getTimeDate(row.createTime, "yyyy-MM-dd hh:mm:ss");
                                }
                            },
                            { field: "opt", title: "操作", width: 80, rowspan: 1, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    g += "<a href='#' class='delete'  data-id='" + row.id + "'>【删除】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/usAdminManager/insert",//新增命令
                    "updatacmd": "action/usAdminManager/updateInfo",//修改命令
                    "beforerender": function (data) { },
                    "onSubmit": function (data) {
                        data.roleUserId = 'djfupt_010'
                        return !flag;
                    }

                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#glyTableReadForm"
                }
            };
            loadGrid(pageparam);

        });

        // 防止数据还没回来用户就再次点击
        var flag = false
        //选择人员
        function chooseuser() {
            if (flag) return
            if (top.chooseWeb && top.chooseWeb.chooseUsersVal) {
                top.chooseWeb.chooseUsersVal = {};
            }
            top.dialogP("html/choose/chooseUsersQuery.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB", false, 900, 500)
        }
        window.chooseUserCB = function (data) {
            flag = true
            ajaxgeneral({
                url: "action/usAdminManager/queryUserOrgInfo?userName=" + data.data[0].userName,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    flag = false
                    var data = res.data
                    $('#glyTableAddForm #belongDept').val(data.belongDepartmentName);
                    $('#glyTableAddForm #belongDeptCode').val(data.belongDepartmentCode);
                    $('#glyTableAddForm #belongCom').val(data.belongCompanyName);
                    $('#glyTableAddForm #belongComCode').val(data.belongCompanyCode);
                    $('#glyTableAddForm #userName').val(data.username);
                    $('#glyTableAddForm #trueName').val(data.truename);
                    $('#glyTableAddForm #type').val(1);
                },
                error: function (res) {
                    flag = false
                }
            });
        }

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('data-id');
            $.messager.confirm('确认', '您确认想要删除吗？', function (r) {
                if (r) {
                    ajaxgeneral({
                        url: 'action/usAdminManager/delInfo?id=' + id,
                        data: {},
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $("#glyTable").datagrid("reload");
                        }
                    });
                }
            });
        })

        //下载模板
        function downloadFile() {
            window.open(web.rootdir + "action/usAdminManager/exportHadmin");
        }
        //导入excel
        function uploadExcel() {
            $('#uploadFiles').trigger('click');
        }
        //导入excel后
        function OtherInfo(data) {
            $('#glyTable').datagrid('load');
        }
        //导出
        $(document).on('click', 'a.exporttable', function () {
            $("#glyTableQueryForm").attr("action", web.rootdir + "action/usAdminManager/exportExcel?type=1");
            $("#glyTableQueryForm").attr("method", "post");
            $("#glyTableQueryForm").submit();
        });
        //单位选择组织
        $(document).on('click', '.chooseOrgss', function () {
            var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'C'};
            top.chooseWeb.chooseOrgsVal={"data":[]};
            var url=tourl('html/choose/chooseCompanyII.html',href);
            top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
        });
        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [],comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#glyTableQueryForm input[name=belongComCode]").val(codes.join(","));
            $("#glyTableQueryForm .chooseOrgss").val(names.join(","));
            // $('#companyName').combobox('setValue',names.join(","));
        };
    </script>
    <style>
        .uploadImageI{
            padding-top: 32px;
            margin-right: 100px;
        }
        a.btn{width: 80px;}
        .cselectorImageUL{width: 85px;}
        .cselectorImageUL .btn{
            right: 0px;
        }
        .cselectorImageUL input{
            right: 5px;
        }
    </style>
</head>

<body class="body_page">
    <form id="glyTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <input name="belongComCode" type="hidden"/>
            <tr>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="11.1%"></td>
                <td width="15.2%"></td>
                <td width="20%"></td>
                <td width="1%"></td>
            </tr>
             <tr>
                <td align="right">所在单位</td>
                <td>
<!--                    <input name="belongComCode" class="easyui-combobox" style="width: 100%; height: 32px;"-->
<!--                        data-options="-->
<!--                    valueField: 'ORG_CODE',-->
<!--                    panelHeight:'200px',-->
<!--                    ischooseall:true,-->
<!--                    textField: 'ORG_NAME',-->
<!--                    editable:false,-->
<!--                    url: 'action/commom/queryOrg'" />-->
                    <input class="chooseOrgss" name="chooseOrgss"  type="text" style="width: 100%;" readonly />
                </td>
                <td align="right">管理员姓名</td>
                <td><input id="trueName" name="trueName" type="text" /></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td align="right">OA账号</td>
                <td><input id="userName" name="userName" type="text" /></td>
                <td colspan="5">
                    
                    <a class="btn fr a_primary ml10" onclick="downloadFile()" style="width: 100px;"><span>模板下载</span></a>
                    <a class="btn fr ml10 a_success exporttable "> <span>导出</span> </a>
                    <a class="btn fr a_success ml10 showDialog "><span>添加</span></a>
                    <a class="btn fr a_primary searchtable"><span>查询</span></a>

                </td>
                <td align="center">
                    <input id="uploadFiles" name="uploadFiles" type="text" file="true" mulaccept="true"
                    class="cselectorImageUpload" btnmsg="<span class='iconfont' title='添加' style='font-size:14px'>导入</span>"
                    href="action/usAdminManager/importInfoHadmin" OtherInfo="OtherInfo" />
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="glyTable">
        <table id="glyTable"></table>
    </div>
    <!--新增修改的dialog页面-->
    <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:950px;height:320px;">
        <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()">
            <input id="id" name="id" type="hidden" />
            <input id="belongComCode" name="belongComCode" type="hidden" />
            <input id="belongDeptCode" name="belongDeptCode" type="hidden" />
            <input id="type" name="type" type="hidden" />
            <table border="0" cellpadding="0" cellspacing="10">
                <tr>
                    <td width="100" align="right"> 姓名<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="trueName" name="trueName" type="text" class="easyui-validatebox" readonly required='required' />

                    </td>
                    <td> <a class="btn a_warning ml10" title="人员查询" onclick="chooseuser()"><i class="iconfont">&#xe634;</i></a> </td>
                    <td width="100" align="right"> OA账号<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="userName" name="userName" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
                <tr>
                    <td width="100" align="right"> 所在单位<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="belongCom" name="belongCom" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                    <td></td>
                    <td width="100" align="right"> 所在部门<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="belongDept" name="belongDept" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
<!--                <tr>-->
<!--                    <td width="100" align="right"> 管理员类型<font class="col_r">*</font> </td>-->
<!--                    <td width="200">-->
<!--                        <input id="roleUserId" name="roleUserId" class="easyui-combobox"-->
<!--                            style="width: 100%; height: 32px;" data-options="-->
<!--							valueField: 'value',-->
<!--							panelHeight:'auto',-->
<!--							ischooseall:true,-->
<!--							textField: 'text',-->
<!--							editable:false,-->
<!--                            required:true,-->
<!--                            data:[ {value:'djfupt_001',text:'省公司党办管理员'}, {value:'djfupt_003',text:'分公司党办管理员'}, {value:'djfupt_004',text:'县公司党办管理员'}, {value:'djfupt_005',text:'省公司观察员'}, {value:'djfupt_006',text:'分公司观察员'}, {value:'djfupt_007',text:'县公司观察员'} ]" />-->
<!--                    </td>-->
<!--                    <td></td>-->
<!--                    <td></td>-->
<!--                </tr>-->
            </table>
        </form>
    </div>
</body>

</html>