package com.simbest.boot.djfupt.wfquey.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DictValueRepository extends LogicRepository<SysDictValue, String> {

    @Query(
            value = " sELECT dv.*" +
                    "  from sys_dict d, sys_dict_value dv" +
                    " where d.dict_type = dv.dict_type" +
                    "   and d.enabled = 1" +
                    "   and dv.enabled = 1" +
                    "   and d.dict_type =:type" +
                    "   and dv.name=:name" +
                    " order by dv.display_order asc" +
                    "          " +
                    "      ",
            nativeQuery = true
    )
    List<SysDictValue> findAllByNameAndType(@Param("type") String type,
                                            @Param("name") String name);

    @Query(
            value = " SELECT dv.* from sys_dict d,sys_dict_value dv where d.dict_type=dv.dict_type and d.enabled=1 and dv.enabled=1 and d.dict_type=:dictType order by dv.display_order asc ",
            nativeQuery = true
    )
    List<SysDictValue> findDictValue(@Param("dictType") String var1);
}
