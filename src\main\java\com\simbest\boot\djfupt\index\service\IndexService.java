package com.simbest.boot.djfupt.index.service;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IndexService {

    /**
     * 待办事项通知
     *
     * @return
     */
    JsonResponse todoItems();

    /**
     * 导出
     *
     * @param startTime
     * @param endTime
     * @return
     */
    ResponseEntity<?> importExcel(String startTime, String endTime,
                                  HttpServletResponse response,
                                  HttpServletRequest request);

    /**
     * 党的政策要求
     *
     * @param resultMap
     * @return
     */
    JsonResponse queryPolicyInfo(Map<String, Object> resultMap);


    /**
     * 工单查询
     *
     * @param page
     * @param size
     * @param title
     * @param pmInsType
     * @return
     */
    JsonResponse workOrder(int page,
                           int size,
                           String title,
                           String pmInsType,
                           String state,
                           String startTime,
                           String endTime);

    /**
     * 工单导出
     */
    void exportParameter(
            String title,
            String pmInsType,
            String state,
            String startTime,
            String endTime,
            HttpServletRequest request,
            HttpServletResponse response);


    List<DataScreeningVo> dataScreening(Map<String, Object> resultMap);
    DataScreeningVo dataScreeningTotal(Map<String, Object> resultMap);


    List<DataScreeningVo> dataScreeningBranch( Map<String, Object> resultMap);


    void exportDataScreening( HttpServletRequest request,
                    HttpServletResponse response,
                    Map<String, Object> resultMap

    );

    JsonResponse judgeUser();

    DataScreeningVo dataScreeningBranchTotal(Map<String, Object> resultMap);
}
