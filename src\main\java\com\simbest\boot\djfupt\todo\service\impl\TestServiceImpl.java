package com.simbest.boot.djfupt.todo.service.impl;

import com.simbest.boot.djfupt.todo.service.ITestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.LocalTime;

/**
 * <strong>Title : TestServiceImpl</strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/5/24</strong><br>
 * <strong>Modify on : 2022/5/24</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> <EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class TestServiceImpl implements ITestService {


    @Override
    @Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 1000,multiplier = 1.5))
    public String test() throws Exception{
        log.debug("开始执行代码："+ LocalTime.now());
        throw new Exception("【cmss】-->openTodo推送待办异常!");
        //return "22";
    }

    /**
     * 最终重试失败处理
     * @param e
     * @return
     */
    @Recover
    public String recover(Exception e){
        log.debug("代码执行重试后依旧失败");
        return null;
    }
}
