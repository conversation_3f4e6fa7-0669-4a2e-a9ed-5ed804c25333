package com.simbest.boot.djfupt.record.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface UsRecordFillRepository extends LogicRepository<UsRecordFill, String> {
    /**
     * 查询思政纪实草稿状态工单
     *
     * @param creator
     * @return
     */
    @Transactional
    @Query(
            value = "select t.*" +
                    "  from US_RECORD_FILL t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.is_draft = '2'" +
                    "   and t.creator =:creator order by t.created_time desc",
            nativeQuery = true
    )
    List<UsRecordFill> getFromDetail(@Param("creator") String creator);


    //查询月份下当前人有没有起草工单
    @Query(
            value = " select t.* " +
                    "  from US_RECORD_FILL t " +
                    " where t.enabled = 1 " +
                    "   and t.removed_time is null" +
                    "   and t.creator =:creator " +
                    "   and to_CHAR(t.created_time, 'MM') = :month   ",
            nativeQuery = true
    )
    List<UsRecordFill> findAllByCreatorAndYear(@Param("month") int month,
                                               @Param("creator") String creator);


    @Query(
            value = "select d.name,d.value," +
                    "            (select count(ad.id)" +
                    "    from us_admin_manager ad" +
                    "    where ad.enabled = 1   and ad.role_user_id='djfupt_002'    " +
                    "    and ad.belong_company_name = d.name) as flag" +
                    "    from sys_dict_value d" +
                    "    where d.enabled = 1      " +
                    "    and d.name = :companyName " +
                    "    and d.dict_type = :dictType ",
            nativeQuery = true
    )
    List<Map<String, Object>> findAllByCompanyAndName(@Param("dictType") String dictType, @Param("companyName") String companyName);


    @Query(
            value = "select d.name,d.value," +
                    "            (select count(ad.id)" +
                    "    from us_admin_manager ad" +
                    "    where ad.enabled = 1 and ad.role_user_id='djfupt_002'   " +
                    "    and ad.belong_company_name = d.name) as flag       " +
                    "    from sys_dict_value d" +
                    "    where d.enabled = 1     " +
                    "    and d.dict_type = :dictType ",
            nativeQuery = true
    )
    List<Map<String, Object>> findAllByCompany(@Param("dictType") String dictType);


    //@Query(
    //        value = "  select count(t.id)" +
    //                "    from US_RECORD_FILL t" +
    //                "    where t.enabled = 1" +
    //                "    and SUBSTR(nvl(t.apply_time, '1950-01'), 0, 7) = :applyTime" +
    //                "    and t.belong_company_name = :companyName  and t.is_draft = 1 ",
    //        nativeQuery = true
    //)
    //int countByCompany(@Param("applyTime") String applyTime,
    //                   @Param("companyName") String companyName);
    @Query(
            value = "  select count(distinct t.creator)" +
                    "    from US_RECORD_FILL t" +
                    "    where t.enabled = 1" +
                    "    and SUBSTR(nvl(t.apply_time, '1950-01'), 0, 7) = :applyTime" +
                    "    and t.belong_company_name = :companyName  and t.is_draft = 1 ",
            nativeQuery = true
    )
    int countByCompany(@Param("applyTime") String applyTime,
                       @Param("companyName") String companyName);
    List<UsRecordFill> findAllByEnabledOrderByCreatedTimeDesc(Boolean enabled);



    @Query(
            value = "  select count(distinct t.creator)" +
                    "    from US_RECORD_FILL t" +
                    "    where t.enabled = 1" +
                    "    and SUBSTR(nvl(t.apply_time, '1950-01'), 0, 7) = :applyTime " +
                    "    and t.creator = :name  and t.is_draft = 1 ",
            nativeQuery = true
    )
    int countByName(@Param("applyTime") String applyTime,
                       @Param("name") String name);


}
