package com.simbest.boot.djfupt.problem.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.common.BpsRedisUtil;
import com.simbest.boot.bps.process.bussiness.mapper.ActBusinessStatusMapper;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.mapper.WfProcessInstModelMapper;
import com.simbest.boot.bps.process.listener.mapper.WfWorkItemModelMapper;
import com.simbest.boot.bps.process.listener.model.WFProcessInstModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfProcessInstModelService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.common.repository.ActBusinessStatusRepository;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.model.UsproblemIbnfoExcel;
import com.simbest.boot.djfupt.problem.repository.UsProblemInfoRepository;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.djfupt.record.model.RecordVo;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.record.repository.UsRecordFillRepository;
import com.simbest.boot.djfupt.util.*;
import com.simbest.boot.djfupt.wfquey.repository.DictValueRepository;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.CustomBeanUtil;
import com.simbest.boot.util.MapUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.login.WorkFlowBpsLoginService;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("ALL")
@Transactional(rollbackFor = Exception.class)
@Service(value = "usProblemInfoService")
public class UsProblemInfoServiceImpl extends LogicService<UsProblemInfo, String> implements IUsProblemInfoService {

    private UsProblemInfoRepository usProblemInfoRepository;

    @Autowired
    public UsProblemInfoServiceImpl(UsProblemInfoRepository usProblemInfoRepository) {
        super(usProblemInfoRepository);
        this.usProblemInfoRepository = usProblemInfoRepository;
    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private WfWorkItemModelMapper workItemModelMapper;

    @Autowired
    private BpsRedisUtil bpsRedisUtil;

    @Autowired
    private ActBusinessStatusMapper actBusinessStatusMapper;

    @Autowired
    private IWfProcessInstModelService wfProcessInstModelService;

    @Autowired
    private WfProcessInstModelMapper wfProcessInstModelMapper;

    @Autowired
    private IUsProblemInfoService usProblemInfoService;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private UsCaseInfoRepository usCaseInfoRepository;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;


    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    String param1 = "/action/usProblemInfo";

    @Autowired
    private DictValueRepository dictValueRepository;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;


    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private ActBusinessStatusRepository actBusinessStatusRepository;

    @Autowired
    private UsRecordFillRepository usRecordFillRepository;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;


    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param copyLocation    抄送环节
     * @param bodyParam
     * @param formId          表单id
     * @return
     */
    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId) {
        JsonResponse jsonResponse = new JsonResponse();
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                UsProblemInfo form = null;
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    form = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), UsProblemInfo.class);
                } else {
                    if (StringUtils.isNotEmpty(formId) && "MOBILE".equals(source)) {
                        form = this.findById(formId);
                    }
                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }
                tempList = (List<Map<String, String>>) map.get("copyNextUserNames");
                String copyNextUserNames = "";
                if (null != tempList && !tempList.isEmpty()) {
                    for (Map<String, String> mapObj : tempList) {
                        String copyName = mapObj.get("value");
                        if (!org.springframework.util.StringUtils.isEmpty(copyName)) {
                            copyNextUserNames = copyName + "," + copyNextUserNames;
                        }
                    }
                }
                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;
                String copyMessage = map.get("copyNextUserName") != null ? map.get("message").toString() : null;

                /**如果表单的id不为空，则不是起草环节，走审批流程**/
                if (form != null && form.getId() != null && StringUtils.isNotEmpty(workItemId)) {
                    //起草环节
                    jsonResponse = saveSubmitTask(form, workItemId, outcome, message, nextUserName, location, copyLocation, copyMessage, copyNextUserNames, notificationId, source, currentUserCode);
                } else {
                    //审批流程环节
                    jsonResponse = startProcess(form, nextUserName, outcome, message, source, currentUserCode);//创建提交
                }
            }
        }
        return jsonResponse;
    }


    /**
     * 审批提交
     *
     * @param usProblemInfo     表单
     * @param workItemId        活动实例id
     * @param outcome           连线规则
     * @param message           审批意见
     * @param nextUserName      审批人
     * @param location          当前环节
     * @param copyLocation      抄送下一环节
     * @param copyMessage       抄送意见
     * @param copyNextUserNames 抄送人员
     * @param notificationId    待阅id
     * @param source            来源
     * @param userCode          当前用户OA账号
     * @return
     */

    public JsonResponse saveSubmitTask(UsProblemInfo usProblemInfo, String workItemId, String outcome, String message, String nextUserName, String location, String copyLocation, String copyMessage, String copyNextUserNames, String notificationId, String source, String userCode) {
        log.debug("起草接口----------saveSubmitTask---------->" + usProblemInfo.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "applicationForm=" + usProblemInfo.toString() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + copyLocation + ",copyMessage"
                + copyMessage + ",copyNextUserNames=" + copyNextUserNames + ",notificationId=" + notificationId + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            String pmInsId = usProblemInfo.getPmInsId();
            operateLog.setBussinessKey(pmInsId);
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**相关流转审批操作**/
            if (pmInstence != null) {

                /**获取用户**/
                IUser user = SecurityUtils.getCurrentUser();
                /**审批流转**/
                if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                    if ((!"end".equals(outcome) && StringUtils.isNotEmpty(nextUserName)) || "end".equals(outcome)) {
                        ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence);
                    } else {
                        ret = 0;
                        operateLog.setErrorMsg("审批人不能为空");
                        return JsonResponse.fail("审批人不能为空");
                    }
                }

//                if (!"end".equals(outcome)) {  //通讯录反馈
//                    usProblemInfoService.update(usProblemInfo);
//                }

                if ((location.equals("djfupt.braAdminFeedback") && outcome.equals("djfupt.braAdminToAsk")) //分公司党办反馈归档
                        || (location.equals("djfupt.proAdminFeedback") && outcome.equals("djfupt.proAdminToAsk"))//省公司党办反馈归档
                        || (location.equals("djfupt.addressBookFeedback") && outcome.equals("djfupt.proAdminToAsk"))) {  //通讯录反馈
                    usProblemInfo.setState(0);

                }
                this.updateFileByPmInsId(usProblemInfo, usProblemInfo.getType());//更新附件


                if (location.equals("djfupt.proAdminFeedbackAsk") && !outcome.equals("end")) //重新反馈
                {  //通讯录反馈
                    usProblemInfo.setState(1);

                }

                if(outcome.equals("end")&& location.equals("djfupt.start")&&usProblemInfo.getQuestionMode().equals("0")){
                    actBusinessStatusRepository.updateCurrDate(usProblemInfo.getPmInsId());

                }

                usProblemInfoService.update(usProblemInfo);
            } else {
                operateLog.setErrorMsg("请联系管理员  ，主数据查找异常！pmInsId = " + pmInsId);
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
            /**提醒流转下一步信息**/
            String showMessage = this.getTemplate(nextUserName);
            return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        }
    }


    /**
     * 提交起草流程
     *
     * @param usProblemInfo 割接计划表单
     * @param nextUserName  审批人
     * @param outcome       连线规则
     * @param message       审批意见
     * @param source        来源
     * @param userCode      当前用户
     * @return
     */
    public JsonResponse startProcess(UsProblemInfo usProblemInfo, String nextUserName, String outcome, String message, String source, String userCode) {
        log.debug("起草接口----------startProcess---------->" + usProblemInfo.toString());
        long ret = 0;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",userCode=" + userCode + ",applicationForm=" + usProblemInfo.toString() + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser iuser = SecurityUtils.getCurrentUser();

            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                /**校验表单和下一步审批人是否为空**/
                if (StringUtils.isNotEmpty(nextUserName)) {
                    /**获取登录人所在公司应启动的流程**/

                    Map<String, String> map = commonService.getProcessMap(usProblemInfo.getType());
                    String processDefId = map.get("processName");
                    String processType = map.get("processType");
                    if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                        boolean flag = false;
                        UsPmInstence usPmInstence = new UsPmInstence();
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        if (StringUtils.isEmpty(usProblemInfo.getId())) {
                            flag = this.savePlanTask(usProblemInfo, usPmInstence);
                        } else {
                            usPmInstence = usPmInstenceService.findByPmInsId(usProblemInfo.getPmInsId());
                            flag = true;
                        }
                        /**启动发起流程**/
                        if (flag) {
                            Map<String, Object> variables = Maps.newHashMap();
                            String currentUserCode = iuser.getUsername();
                            String currentUserName = iuser.getTruename();
                            variables.put("inputUserId", currentUserCode);
                            variables.put("receiptId", usPmInstence.getId());
                            variables.put("title", usPmInstence.getPmInsTitle());
                            variables.put("code", usPmInstence.getPmInsId());
                            variables.put("currentUserCode", currentUserCode);
                            variables.put("activityDefID", Constants.ACTIVITY_START);
                            variables.put("appCode", Constants.APP_CODE);
                            //第一个参数为流程定义名称
                            Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                            if (workItemId != 0) {
                                /**提交表单审批处理**/
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                operateLog.setErrorMsg("启动流程失败");
                                JsonResponse.fail(null, "启动流程失败");
                            }
                        } else {
                            operateLog.setErrorMsg("保存割接计划失败");
                            JsonResponse.fail(null, "保存割接计划失败");
                        }
                    } else {
                        operateLog.setErrorMsg("获取流程失败");
                        JsonResponse.fail(null, "操作失败，获取流程失败!");
                    }
                } else {
                    operateLog.setErrorMsg("表单为空或审批人为空");
                    JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
                }
            } else {
                Map<String, String> map = commonService.getProcessMap(usProblemInfo.getType());
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                    boolean flag = false;
                    UsPmInstence usPmInstence = new UsPmInstence();
                    usPmInstence.setPmInsType(processType);
                    /**保存业务数据**/
                    if (StringUtils.isEmpty(usProblemInfo.getId())) {
                        flag = this.savePlanTask(usProblemInfo, usPmInstence);
                    } else {
                        usPmInstence = usPmInstenceService.findByPmInsId(usProblemInfo.getPmInsId());
                        flag = true;
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);
                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);

                            }

                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存割接计划失败");
                        JsonResponse.fail(null, "保存割接计划失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(nextUserName)) {
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                }
            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }

        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }


    /**
     * 流转下一步
     *
     * @param workItemID      活动实例id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人姓名
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param location        当前所处环节
     * @param message         审批意见
     * @param pmInstence      主单据
     * @return
     */
    private long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence) {
        long ret;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            workItemService.submitApprovalMsg(workItemID, message);
            //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
            ret = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }


    /**
     * 保存业务数据
     *
     * @param usProblemInfo 表单
     * @param usPmInstence  主单据
     * @return
     * @throws Exception
     */
    boolean savePlanTask(UsProblemInfo usProblemInfo, UsPmInstence usPmInstence) throws Exception {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            String planId = usProblemInfo.getId();
            if (StringUtils.isEmpty(planId) || Constants.STR_NULL.equals(planId)) {
                String pmInsId = usPmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                //   String title = usCaseInfo.getTitle();
                usPmInstence.setPmInsId(pmInsId);
                String currentStr = DateUtil.getCurrentStr();
                String count = usPmInstenceService.getCounts(usPmInstence.getPmInsType(), currentStr);
                String workCode = OrderNumberCreate.generateNumber(usPmInstence.getPmInsType(), count);//编号生成
                usPmInstence.setPmInsTitle("问题上报");
                usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                usPmInstenceService.saveData(usPmInstence);
//                usPmInstenceService.insert(usPmInstence);
//                usPmInstenceRepository.flush();
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (StringUtils.isNotEmpty(usPmId)) {

                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usProblemInfo.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    usProblemInfo.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    usProblemInfo.setBelongDepartmentName(iuser.getBelongCompanyName());
                    usProblemInfo.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    usProblemInfo.setBelongCompanyCodeParent(iuser.getBelongCompanyCodeParent());
                }
                if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                    usProblemInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                    usProblemInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usProblemInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    usProblemInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                }

                usProblemInfo.setState(1);
                usProblemInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usProblemInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usProblemInfo.setBelongOrgName(iuser.getBelongOrgName());
                usProblemInfo.setPmInsId(usPmInstence.getPmInsId());
                this.updateFileByPmInsId(usProblemInfo, usPmInstence.getPmInsType());//更新附件
                UsProblemInfo usProblemInfo1 = this.insert(usProblemInfo);
                if (usProblemInfo1 != null) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }

    /**
     * 更新附件
     *
     * @param usProblemInfo 表单
     */
    public void updateFileByPmInsId(UsProblemInfo usProblemInfo, String pmInsType) {
        List<SysFile> files = usProblemInfo.getDrawFiles();
        List<SysFile> feedBackFiles = usProblemInfo.getFeedBackFiles();
        String pmInsId = usProblemInfo.getPmInsId();
        try {
            if (files != null && !files.isEmpty()) {
                for (SysFile file : files) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId());
                }
            }
            if (feedBackFiles != null && !feedBackFiles.isEmpty()) {
                for (SysFile file : feedBackFiles) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public JsonResponse getCurrentProblemInfo() { //默认查询个人草稿
        UsProblemInfo form = null;
        IUser currentUser = SecurityUtils.getCurrentUser();
        List<UsProblemInfo> fromDetailList = usProblemInfoRepository.getFromDetailByUserName(currentUser.getUsername());
        if (CollectionUtil.isNotEmpty(fromDetailList)) {
            form = fromDetailList.get(0);
            List<SysFile> list = fileExtendService.getPartFile(form.getPmInsId(), "1");
            form.setDrawFiles(list);
        }
        return JsonResponse.success(form,null);
    }

    /**
     * 更新表单、主数据以及流程操作
     *
     * @param pmInstence    主数据
     * @param usProblemInfo 表单
     * @return
     */
    private JsonResponse updateDate(UsPmInstence pmInstence, UsProblemInfo usProblemInfo) {
        String title = "经验推广上报";
        JsonResponse jsonResponse = null;
        try {
            int result = processInstanceService.updateTitleByBusinessKey(pmInstence.getId(), title);
            if (result > 0) {
                pmInstence.setPmInsTitle(title);
                UsPmInstence pmInstence1 = usPmInstenceService.update(pmInstence);//更新主数据标题
                if (pmInstence1 != null) {
                    this.updateFileByPmInsId(usProblemInfo, pmInstence.getPmInsType());//更新附件
                    UsProblemInfo form1 = this.update(usProblemInfo);//更新表单
                    if (form1 == null) {
                        jsonResponse = JsonResponse.fail(usProblemInfo, "表单更新失败");
                    } else {
                        //更新流程业务状态
                        updateActBusinessStatus(pmInstence1.getPmInsId(), title);
                    }
                } else {
                    jsonResponse = JsonResponse.fail(usProblemInfo, "主数据标题更新失败");
                }
            } else {
                jsonResponse = JsonResponse.fail(usProblemInfo, "流程相关标题更新失败");
            }
        } catch (Exception e) {
            log.debug("更新退回修改数据-------updateDate" + e.toString());
            jsonResponse = JsonResponse.fail(usProblemInfo, "更新异常");
        } finally {
            return jsonResponse;
        }
    }


    /**
     * 根据主单据id，更新流程业务状态单据标题
     *
     * @param pmInsId 主单据id
     * @param title   单据标题
     */
    private void updateActBusinessStatus(String pmInsId, String title) {
        //根据主单据id和运行工作项状态读取到当前运行的workItemModel
        Specification<WfWorkItemModel> wfSpec = Specifications
                .<WfWorkItemModel>and()
                .eq("receiptCode", pmInsId)
                .build();
        List<WfWorkItemModel> workItemModelList = workItemModelMapper.findAllActive(wfSpec);
        if (null != workItemModelList && workItemModelList.size() > 0) {
            //只需要获取到流程实例id,所以这里直接get(0)
            WfWorkItemModel wfWorkItemModel = workItemModelList.get(0);
            //根据流程实例id读取到流程业务信息actBusinessStatus
            ActBusinessStatus actBusinessStatus = bpsRedisUtil.getFromRedis(bpsRedisUtil.PROCESS_ACT_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), ActBusinessStatus.class);
            if (actBusinessStatus == null) {
                actBusinessStatus = statusService.getByProcessInst(wfWorkItemModel.getProcessInstId());
            }
            if (null != actBusinessStatus) {
                //读取到业务流程数据以后，比较标题字段是否有变化，如果有，则进行变更标题操作
                if (!title.equals(actBusinessStatus.getReceiptTitle())) {
                    //更新act表工单标题
                    actBusinessStatus.setReceiptTitle(title);
                    actBusinessStatusMapper.saveAndFlush(actBusinessStatus);
                    bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_ACT_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), actBusinessStatus);
                    //更新workItemModel表中的工单标题
                    for (WfWorkItemModel workItemModel : workItemModelList) {
                        workItemModel.setReceiptTitle(title);
                        workItemModelMapper.saveAndFlush(workItemModel);
                        bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_INST_REDIS_KEY.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), workItemModel);
                    }
                    //更新wfProcessInstModel
                    Specification<WFProcessInstModel> ProSpec = Specifications
                            .<WFProcessInstModel>and()
                            .eq("processInstId", String.valueOf(wfWorkItemModel.getProcessInstId()))
                            .build();
                    WFProcessInstModel wfProcessInstModel = wfProcessInstModelService.findOne(ProSpec);
                    if (null != wfProcessInstModelService) {
                        wfProcessInstModel.setReceiptTitle(title);
                        wfProcessInstModelMapper.saveAndFlush(wfProcessInstModel);
                        bpsRedisUtil.addToRedis(bpsRedisUtil.PROCESS_WORK_ITERM.concat(String.valueOf(wfWorkItemModel.getProcessInstId())), wfProcessInstModel);
                    }
                }
            }
        }
    }


    /**
     * 获取申请表单
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @return
     */
    @Override
    public JsonResponse getFormDetail(String processInstId, String workFlag, String source, String userCode, String pmInsId, String location,String type) {
        UsProblemInfo usProblemInfo = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source +
                ",userCode=" + userCode + ",location=" + location + ",pmInsId=" + pmInsId;
        operateLog.setInterfaceParam(params);
        try {
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser currentUser = SecurityUtils.getCurrentUser();


            //草稿箱详情查询
            usProblemInfo = usProblemInfoRepository.getFormDetailByPmInsId(pmInsId);
            if(usProblemInfo!= null){
                //获取附件
                List<SysFile> list = fileExtendService.getPartFile(usProblemInfo.getPmInsId(), "1");
                usProblemInfo.setDrawFiles(list);
                //获取附件
                List<SysFile> feedBackFiles = fileExtendService.getPartFile(usProblemInfo.getPmInsId(), "2");
                usProblemInfo.setFeedBackFiles(feedBackFiles);
            }
            ///**点击办理查看详情**/
            //if (null != processInstId) {
            //    ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
            //    if (actBusinessStatus != null) {
            //        String id = actBusinessStatus.getBusinessKey();
            //        usProblemInfo = usProblemInfoRepository.getFromDetail(id);
            //        if (usProblemInfo != null) {
            //            operateLog.setBussinessKey(usProblemInfo.getPmInsId());
            //            //获取附件
            //            List<SysFile> list = fileExtendService.getPartFile(usProblemInfo.getPmInsId(), "1");
            //            usProblemInfo.setDrawFiles(list);
            //            //获取附件
            //            List<SysFile> feedBackFiles = fileExtendService.getPartFile(usProblemInfo.getPmInsId(), "2");
            //            usProblemInfo.setFeedBackFiles(feedBackFiles);
            //        }
            //    }
            //} else {
            //    //草稿箱详情查询
            //    usProblemInfo = usProblemInfoRepository.getFormDetailByPmInsId(pmInsId);
            //    //获取附件
            //    List<SysFile> list = fileExtendService.getPartFile(usProblemInfo.getPmInsId(), "1");
            //    usProblemInfo.setDrawFiles(list);
            //}
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usProblemInfo);
    }


    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(String source, String currentUserCode, UsProblemInfo usProblemInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "carRepair=" + usProblemInfo.toString() + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 保存草稿
            if (StringUtils.isEmpty(usProblemInfo.getId())) {
                // 保存业务单据信息
                this.saveBusinessData(usProblemInfo);
            }
            // 更新草稿
            else {
                //更新业务单据信息
                this.updateBusinessData(usProblemInfo);
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usProblemInfo, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 保存业务单据信息
     *
     * @param usProblemInfo
     * @return
     */
    private Map<String, Object> saveBusinessData(UsProblemInfo usProblemInfo) {
        Map<String, Object> result = Maps.newHashMap();
        IUser iuser = SecurityUtils.getCurrentUser();
        UsPmInstence usPmInstence = new UsPmInstence();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StringUtils.isEmpty(usProblemInfo.getId())) {
                Map<String, String> map = commonService.getProcessMap("B");
                String processName = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    String pmInsId = processType + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                    String title = "问题上报";
                    usPmInstence.setPmInsId(pmInsId);
                    usPmInstence.setPmInsTitle(title);
                    usPmInstence.setPmInsType(processType);
                    usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                    usPmInstenceService.insert(usPmInstence);
                }
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (StringUtils.isNotEmpty(usPmId)) {
                usProblemInfo.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usProblemInfo.setBelongOrgCode(iuser.getBelongOrgCode());
                usProblemInfo.setBelongOrgName(iuser.getBelongOrgName());
                usProblemInfo.setBelongCompanyCode(iuser.getBelongCompanyCode());
                usProblemInfo.setBelongCompanyName(iuser.getBelongCompanyName());
                usProblemInfo.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                usProblemInfo.setBelongDepartmentName(iuser.getBelongDepartmentName());
                usProblemInfo.setPmInsId(usPmInstence.getPmInsId());
                UsProblemInfo newUsProblemInfo = usProblemInfoService.insert(usProblemInfo);
                if (StringUtils.isNotEmpty(newUsProblemInfo.getId())) {
                    this.updateFileByPmInsId(newUsProblemInfo, usPmInstence.getPmInsType());//更新附件
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return result;
        }
        return result;
    }


    /**
     * 修改业务单据信息
     *
     * @param usProblemInfo
     * @return
     */
    private void updateBusinessData(UsProblemInfo usProblemInfo) {
        UsProblemInfo usCase = this.findById(usProblemInfo.getId());
        CustomBeanUtil.copyPropertiesIgnoreNull(usProblemInfo, usCase);
        Map<String, String> map = commonService.getProcessMap("B");
        String processType = map.get("processType");
        this.updateFileByPmInsId(usProblemInfo, processType);//更新附件
        // 更新表单基础数据
        this.update(usCase);
    }


    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsProblemInfo usProblemInfo) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);
            usPmInstenceService.delete(pmInstence);
            // 删除业务单据数据
            usProblemInfoService.delete(usProblemInfo);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }

    public List<Map<String, Object>> conditionQuerys(Integer page,
                                                     Integer rows,
                                                     String source,
                                                     String userCodeString,
                                                     String startTime,
                                                     String endTime,
                                                     String problemName,
                                                     String belongDepartmentCode,
                                                     String state
    ) {
        List<Map<String, Object>> applicationFormPage = null;
        try {
            //获取当前当前月份
            DateFormat df = new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            String treatTime = df.format(calendar.getTime());
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer(" select t.*\n" +
                    "  from us_problem_info t\n" +
                    " where t.enabled = 1\n" +
                    "   and t.is_draft = '1' ");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            }
            if (StringUtils.isNotEmpty(problemName)) {
                sql.append(" and   t.problem_name    like concat( concat('%',:problemName),'%')  ");
                map.put("problemName", problemName);
            }

            //获取角色、判断是否为部门管理员
            List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
            boolean isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));

            if (StringUtils.isNotEmpty(belongDepartmentCode)) {
                sql.append(" and (t.belong_org_code = :companyCode or t.belong_company_code = :companyCode  or t.belong_department_code = :companyCode   ) ");
                map.put("companyCode", belongDepartmentCode);
            } else if (isRoleAdmin) {
                sql.append(" and  t.belong_department_code = :belongDepartmentCode   ");
                map.put("belongDepartmentCode", SecurityUtils.getCurrentUser().getBelongDepartmentCode());
            } else {
                //获取群组、判断是否省公司管理员
                List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
                boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
                //如果不是省公司管理员执行五级查询，不是的话默认查看全部
                if (!isAdmin) {
                    String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                    switch (queryLevel) {
                        case DataPermissionConstants.QUERY_LEVEL_FIRST:
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_SECOND:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_THIRD:
                            sql.append(" and (t.belong_company_code =:belong_company_code  or t.belong_department_code =:belong_department_code )");
                            map.put("belong_company_code", BelongInfoTool.getBelongCompanyCode());
                            map.put("belong_department_code", BelongInfoTool.getBelongDepartmentCode());
                            break;
                        default:
                            sql.append(" and  t.creator =:username  ");
                            map.put("username", SecurityUtils.getCurrentUser().getUsername());
                            break;
                    }
                }
            }
            sql.append(" order by t.created_time desc");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            if (list.size() > 0) {
                for (Map<String, Object> map1 : list) {
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    map1.put("CREATED_TIME", sdf2.format(map1.get("CREATED_TIME")));
                    map1.put("TITLE", map1.get("PROBLEM_NAME"));
                }
            }
           list = FormatTool.formatConversion(list);//驼峰转换
            return list;
        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        return applicationFormPage;
    }

    /**
     * 问题上报数据统计
     *
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    @Override
    public List<ProblemStatisticsVo> problemStatistics(int page, int rows, Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//
        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//
        if (StringUtils.isNotEmpty(startTime)) {
            startTime = startTime + " 00:00:01";
        }
        if (StringUtils.isNotEmpty(endTime)) {
            endTime = endTime + " 23:59:59";
        }
        List<Map<String, Object>> sysDictValues = new ArrayList<>();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(companyName)) {
            sysDictValues = usRecordFillRepository.findAllByCompanyAndName("company", companyName);
        } else {
            sysDictValues = usRecordFillRepository.findAllByCompany("company");
        }

        List<ProblemStatisticsVo> problemStatisticsVos = new ArrayList<>();
        if (sysDictValues.size() > 0) {
            for (Map<String, Object> dictValue : sysDictValues) {
                ProblemStatisticsVo problemStatisticsVo = new ProblemStatisticsVo();
                problemStatisticsVo.setCompanyCode(String.valueOf(dictValue.get("VALUE")));
                problemStatisticsVo.setCompanyName(String.valueOf(dictValue.get("NAME")));

                Map<String, Object> map = CollectionUtil.newHashMap();
                //已解决问题数
                StringBuffer sql = new StringBuffer(" select t.*\n" +
                        "  from us_problem_info t\n" +
                        " where t.enabled = 1\n" +
                        "   and t.is_draft = 1\n" +
                        "   and (t.belong_company_code = :companyCode or\n" +
                        "       t.belong_company_code_parent = :companyCode)\n ");
                map.put("companyCode", String.valueOf(dictValue.get("VALUE")));
                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                    map.put("startTime", startTime);
                    sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                    map.put("endTime", endTime);
                }
                String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                switch (queryLevel) {
                    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                        break;
                    default:
                        sql.append(" and  t.creator =:username  ");
                        map.put("username", SecurityUtils.getCurrentUser().getUsername());
                        break;
                }
                List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

                List<Map<String, Object>> list1 = list.stream()
                        .collect(Collectors.toMap(
                                m -> m.get("CREATOR"),
                                m -> m,
                                (m1, m2) -> m1))
                        .values()
                        .stream()
                        .collect(Collectors.toList());

                //Map<String, Object> map1 = CollectionUtil.newHashMap();
                ////根据creator、已解决问题数
                //StringBuffer sql1 = new StringBuffer(" select distinct t.creator \n" +
                //        "  from us_problem_info t\n" +
                //        " where t.enabled = 1\n" +
                //        "   and t.is_draft = 1\n" +
                //        "   and (t.belong_company_code = :companyCode or\n" +
                //        "       t.belong_company_code_parent = :companyCode)\n ");
                //map1.put("companyCode", String.valueOf(dictValue.get("VALUE")));
                //if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                //    sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                //    map1.put("startTime", startTime);
                //    sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                //    map1.put("endTime", endTime);
                //}
                //String queryLevel1 = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                //switch (queryLevel1) {
                //    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                //        break;
                //    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                //        DataPermissionTool.handleSql(sql1, map1, DataPermissionConstants.QUERY_LEVEL_SECOND);
                //        break;
                //    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                //        DataPermissionTool.handleSql(sql1, map1, DataPermissionConstants.QUERY_LEVEL_THIRD);
                //        break;
                //    default:
                //        sql1.append(" and  t.creator =:username  ");
                //        map1.put("username", SecurityUtils.getCurrentUser().getUsername());
                //        break;
                //}
                //List<Map<String, Object>> list1 = customDynamicWhere.queryNamedParameterForList(sql1.toString(), map1);

                //问题数量
                problemStatisticsVo.setProblemNums(Integer.valueOf(String.valueOf(dictValue.get("FLAG"))));
                //已解决数量
                problemStatisticsVo.setSolveNums(list.size());

                String completionRate = "";
                float f1 = Float.parseFloat(String.valueOf(dictValue.get("FLAG")));
                float f2 = Float.parseFloat(String.valueOf(list1.size()));
                if (f1 == 0 || f2 == 0) {
                    completionRate = "0";
                } else {
                    float f3 = (f2 / f1) * 100;
                    BigDecimal b = new BigDecimal(f3);
                    f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                    if(Float.compare(f3, 100.0f)>0){
                        f3=100;
                    }
                    completionRate = f3 + "%";

                }
                problemStatisticsVo.setCompletionRate(completionRate);
                problemStatisticsVos.add(problemStatisticsVo);
            }
        }
        return problemStatisticsVos;
    }

    /**
     * 问题上报数据统计
     *
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    @Override
    public List<ProblemStatisticsVo> problemStatisticsOther(int page, int rows, Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//
        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//
        String companyCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyCode");//
        if (StringUtils.isNotEmpty(startTime)) {
            startTime = startTime + " 00:00:01";
        }
        if (StringUtils.isNotEmpty(endTime)) {
            endTime = endTime + " 23:59:59";
        }
        IUser user=SecurityUtils.getCurrentUser();
        List<ProblemStatisticsVo> problemStatisticsVos = new ArrayList<>();


        //如果为省公司管理员  执行一级查询
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        //判断是否为部门管理员
        boolean isAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.DEP_OBSERVER, simpleRoless.getRoleCode()));

        if (isAdmin) {
            ProblemStatisticsVo problemStatisticsVo = new ProblemStatisticsVo();
            problemStatisticsVo.setCompanyCode(user.getBelongDepartmentCode());
            problemStatisticsVo.setCompanyName(user.getBelongDepartmentName());
            //问题数量
            int num = adminManagerRepository.findByDepCodeAndEnabledAndRoleUserIdss(user.getBelongDepartmentCode(), Constants.FJFUPT_BRO);
            //已解决问题数
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer(" select t.*\n" +
                    "   from us_problem_info t\n" +
                    "  where t.enabled = 1\n" +
                    "    and t.is_draft = 1 ");
            map.put("companyCode", user.getBelongDepartmentCode());
            sql.append(" and t.belong_department_code = :companyCode ");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            }
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

            ////根据创建人去重、已解决问题数
            //Map<String, Object> map1 = CollectionUtil.newHashMap();
            //StringBuffer sql1 = new StringBuffer(" select distinct t.creator\n" +
            //        "   from us_problem_info t\n" +
            //        "  where t.enabled = 1\n" +
            //        "    and t.is_draft = 1 ");
            //map1.put("companyCode", user.getBelongDepartmentCode());
            //sql1.append(" and t.belong_department_code = :companyCode ");
            //if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
            //    sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
            //    map1.put("startTime", startTime);
            //    sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
            //    map1.put("endTime", endTime);
            //}
            //List<Map<String, Object>> list1 = customDynamicWhere.queryNamedParameterForList(sql1.toString(), map1);

            List<Map<String, Object>> list1 = list.stream()
                    .collect(Collectors.toMap(
                            m -> m.get("CREATOR"),
                            m -> m,
                            (m1, m2) -> m1))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            problemStatisticsVo.setProblemNums(num);
            problemStatisticsVo.setSolveNums(list.size());
            String completionRate = "";
            float f1 = Float.parseFloat(String.valueOf(num));
            float f2 = Float.parseFloat(String.valueOf(list1.size()));
            if (num == 0 || list1.size() == 0) {
                completionRate = "0";
            } else {
                float f3 = (f2 / f1) * 100;
                BigDecimal b = new BigDecimal(f3);
                f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                if(Float.compare(f3, 100.0f)>0){
                    f3=100;
                }
                completionRate = f3 + "%";
            }
            problemStatisticsVo.setCompletionRate(completionRate);
            problemStatisticsVos.add(problemStatisticsVo);
        } else {
            List<SysDictValue> sysDictValues = new ArrayList<>();
            //查出当前下的组织
            List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
            int i = 0;
            if (simpleOrgList.size() > 0) {
                for (SimpleOrg simpleOrg : simpleOrgList) {
                    if (i == 0) {
                        simpleOrg.setOrgName(simpleOrg.getDisplayName());
                    }
                    if (StringUtils.isNotEmpty(companyCode)) {
                        if (!simpleOrg.getOrgCode().equals(companyCode)) {
                            continue;
                        }
                    }
                    if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                        continue;
                    }
                    SysDictValue sysDictValue = new SysDictValue();
                    sysDictValue.setValue(simpleOrg.getOrgCode());
                    sysDictValue.setName(simpleOrg.getOrgName());
                    sysDictValues.add(sysDictValue);
                    i++;
                }
            }

            if (sysDictValues.size() > 0) {
                int is = 0;
                for (SysDictValue sysDictValue : sysDictValues) {
                    int num=0;
                    if(is==0) {
                        num= adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                    }else {
                        num= adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIds(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                    }
                    ProblemStatisticsVo problemStatisticsVo = new ProblemStatisticsVo();
                    problemStatisticsVo.setCompanyCode(sysDictValue.getValue());
                    problemStatisticsVo.setCompanyName(sysDictValue.getName());

                    //解决数量
                    Map<String, Object> map = CollectionUtil.newHashMap();
                    StringBuffer sql = new StringBuffer("  select t.*\n" +
                            "   from us_problem_info t\n" +
                            "  where t.enabled = 1\n" +
                            "    and t.is_draft = 1 ");
                    map.put("companyCode", sysDictValue.getValue());
                    if (is == 0) {
                        sql.append(" and t.belong_org_code = :companyCode ");
                    } else {
                        sql.append("and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                    }
                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                        map.put("startTime", startTime);
                        sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                        map.put("endTime", endTime);
                    }
                    List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

/*                    //根据creator去重，问题解决数量
                    Map<String, Object> map1 = CollectionUtil.newHashMap();
                    StringBuffer sql1 = new StringBuffer("  select distinct t.creator\n" +
                            "   from us_problem_info t\n" +
                            "  where t.enabled = 1\n" +
                            "    and t.is_draft = 1 ");
                    map1.put("companyCode", sysDictValue.getValue());
                    if (is == 0) {
                        sql1.append(" and t.belong_org_code = :companyCode ");
                    } else {
                        sql1.append("and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                    }
                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                        map1.put("startTime", startTime);
                        sql1.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                        map1.put("endTime", endTime);
                    }
                    List<Map<String, Object>> list1 = customDynamicWhere.queryNamedParameterForList(sql1.toString(), map1);*/

                    List<Map<String, Object>> list1 = list.stream()
                            .collect(Collectors.toMap(
                                    m -> m.get("CREATOR"),
                                    m -> m,
                                    (m1, m2) -> m1))
                            .values()
                            .stream()
                            .collect(Collectors.toList());

                    problemStatisticsVo.setProblemNums(num);
                    problemStatisticsVo.setSolveNums(list.size());
                    String completionRate = "";
                    float f1 = Float.parseFloat(String.valueOf(num));
                    float f2 = Float.parseFloat(String.valueOf(list1.size()));
                    if (num == 0 || list1.size() == 0) {
                        completionRate = "0";
                    } else {
                        float f3 = (f2 / f1) * 100;
                        BigDecimal b = new BigDecimal(f3);
                        f3 = b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                        completionRate = f3 + "%";
                    }
                    problemStatisticsVo.setCompletionRate(completionRate);
                    problemStatisticsVos.add(problemStatisticsVo);
                    is++;
                }
            }
        }
        return problemStatisticsVos;
    }

    @Override
    public void exportProblemStatistics(Map<String, Object> resultMap, HttpServletResponse response, HttpServletRequest request) {
        Boolean flag = false;
        String fileName = "问题上报数据统计.xls";
        IUser user = SecurityUtils.getCurrentUser();
        List<ProblemStatisticsVo> list;
        if (user.getBelongCompanyTypeDictValue().equals("01")) {
            list = usProblemInfoService.problemStatistics(1, 999, resultMap);
        } else {
            list = usProblemInfoService.problemStatisticsOther(1, 999, resultMap);
        }


        try {

            if (list.size() > 0) {
                int i = 1;
                for (ProblemStatisticsVo usCaseExcel : list) {
                    usCaseExcel.setNum(i);
                    //  usCaseExcel.setCompletionRate(usCaseExcel.getCompletionRate()+"%");
                    i++;
                }
            }
            //获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");


            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            // 生成workbook 并导出
            // 创建参数对象（用来设定excel得sheet得内容等信息）
            ExportParams systemCompilation1 = new ExportParams();
            // 设置sheet的名称
            systemCompilation1.setSheetName("sheet1");
            // 创建sheet1使用得map
            Map<String, Object> systemCompilationMap = Maps.newHashMap();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            systemCompilationMap.put("title", systemCompilation1);
            // 模版导出对应得实体类型
            systemCompilationMap.put("entity", ProblemStatisticsVo.class);
            // sheet中要填充得数据
            systemCompilationMap.put("data", list);
            // 将sheet1、sheet2.......sheet13使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(systemCompilationMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            // 导出操作
            FileOutputStream fos = new FileOutputStream(targetFileName);
            workbook.write(fos);
            fos.close();
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }

    }

    /**
     * @param startTime            开始时间
     * @param endTime              结束时间
     * @param problemName          优秀案例名称
     * @param belongDepartmentCode
     * @return
     */

    public Page<List<Map<String, Object>>> conditionQuery(Integer page,
                                                          Integer rows,
                                                          String source,
                                                          String userCodeString,
                                                          String startTime,
                                                          String endTime,
                                                          String problemName,
                                                          String belongDepartmentCode,
                                                          String state
    ) {
        return listPage(page, rows, conditionQuerys(page, rows, source, userCodeString, startTime, endTime, problemName, belongDepartmentCode, state));
    }


    /**
     * @param page     页码
     * @param rows     数量
     * @param source   来源
     * @param userCode 用户
     * @return
     */
    @Override
    public JsonResponse queryApplication(Integer page,
                                         Integer rows,
                                         String source,
                                         String userCode,
                                         String startTime,
                                         String endTime,
                                         String problemName,
                                         String belongDepartmentCode,
                                         String state
    ) {
        Page<List<Map<String, Object>>> forms = null;
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/conditionQuer";
        String params = "pageindex=" + page + ",pagesize=" + rows + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询详单列表**/
            forms = this.conditionQuery(page, rows, source, userCode, startTime, endTime, problemName, belongDepartmentCode, state);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(forms);
    }

    /**
     * 分页方法
     *
     * @param page
     * @param rows
     * @param resultList
     * @return
     */
    public Page<List<Map<String, Object>>> listPage(Integer page, Integer rows, List<Map<String, Object>> resultList) {
        if (resultList != null && resultList.size() > 0) {
            long size = resultList.size();
            Pageable pageable = getPageable(page, rows, null, null);
            List<Map<String, Object>> listPart = PageTool.pagination(resultList, page, rows);
            return new PageImpl(listPart, pageable, size);
        }
        return null;
    }


    public void exportParameter(Integer page,
                                Integer rows,
                                String source,
                                String userCode,
                                String startTime,
                                String endTime,
                                String problemName,
                                String belongDepartmentCode,
                                String state,
                                HttpServletResponse response,
                                HttpServletRequest request) {

        Boolean flag = false;
        String fileName = "问题上报.xls";

        try {

            //获取当前当前月份
            DateFormat df = new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            String treatTime = df.format(calendar.getTime());
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer(" select t.*\n" +
                    "  from us_problem_info t\n" +
                    " where t.enabled = 1\n" +
                    "   and t.is_draft = '1'\n ");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            }
            if (StringUtils.isNotEmpty(state)) {
                if (state.equals("7")) {
                    sql.append(" and   act.current_state   =:state  ");
                    map.put("state", state);
                } else {
                    sql.append(" and   act.current_state   !=7  ");
                    sql.append(" and   act.current_state   !=8  ");
                }

            }
            if (StringUtils.isNotEmpty(problemName)) {
                sql.append(" and   t.problem_name    like concat( concat('%',:problemName),'%')  ");
                map.put("problemName", problemName);
            }
            if (StringUtils.isNotEmpty(belongDepartmentCode)) {

                sql.append(" and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode   or t.belong_department_code=:companyCode ) ");
                map.put("companyCode", belongDepartmentCode);


            } else {

                //如果为省公司管理员  执行一级查询
                List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
                //判断是否省公司管理员
                boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
                // or


                //如果不是省公司管理员执行五级查询，不是的话默认查看全部
                if (!isAdmin) {
                    String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                    switch (queryLevel) {
                        case DataPermissionConstants.QUERY_LEVEL_FIRST:
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_SECOND:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                            break;
                        case DataPermissionConstants.QUERY_LEVEL_THIRD:
                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                            break;
//                        case DataPermissionConstants.QUERY_LEVEL_FOUR:
//                            DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
//                            break;
                        default:
                            sql.append(" and  t.creator =:username  ");
                            map.put("username", SecurityUtils.getCurrentUser().getUsername());
                            break;
                    }
                }
            }

            sql.append(" order by t.created_time desc");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            if (list.size() > 0) {
                for (Map<String, Object> map1 : list) {
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    map1.put("CREATED_TIME", sdf2.format(map1.get("CREATED_TIME")));
                    map1.put("TITLE", map1.get("PROBLEM_NAME"));
                    //if (!map1.get("CURRENT_STATE").equals("7")) {
                    //    Map<String, Object> map2 = usProblemInfoRepository.findAllByReceiptCodeAndCurrentState(map1.get("PM_INS_ID").toString());
                    //    map1.put("HANDLING_LINK", map2.get("WORK_ITEM_NAME"));
                    //    map1.put("ACTIVITY_DEF_ID", map2.get("ACTIVITY_DEF_ID"));
                    //
                    //}
                }
            }
            list = FormatTool.formatConversion(list);//驼峰转换

            //获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");

            List<UsproblemIbnfoExcel> usproblemIbnfoExcels = new ArrayList<>();
            for (Map<String, Object> systemCompilationMap : list) {
                SimpleDateFormat dateFm = new SimpleDateFormat("yyyy-MM-dd"); //格式化当前系统日期
                String dateTime = dateFm.format(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(systemCompilationMap.get("createdTime").toString()));
                UsproblemIbnfoExcel usproblemIbnfoExcel = null;
                systemCompilationMap.put("createdTime", dateTime);
                Object o = MapUtil.mapToObject(systemCompilationMap, UsproblemIbnfoExcel.class);
                if (o != null) {
                    String formDataJson = JacksonUtils.obj2json(o);
                    if (!"[]".equals(formDataJson)) {
                        usproblemIbnfoExcel = JacksonUtils.json2Type(formDataJson, new TypeReference<UsproblemIbnfoExcel>() {
                        });
                        String belongOrgName = systemCompilationMap.get("belongCompanyName").toString();
                        if (!systemCompilationMap.get("belongCompanyName").toString().equals(systemCompilationMap.get("belongDepartmentName").toString())) {
                            belongOrgName = belongOrgName + "/" + systemCompilationMap.get("belongDepartmentName").toString();
                        }
                        if (!systemCompilationMap.get("belongDepartmentName").toString().equals(systemCompilationMap.get("belongOrgName").toString())) {
                            belongOrgName = belongOrgName + "/" + systemCompilationMap.get("belongOrgName").toString();
                        }
                        usproblemIbnfoExcel.setBelongOrgName(belongOrgName);
                        usproblemIbnfoExcels.add(usproblemIbnfoExcel);
                    }
                }
            }
            if (usproblemIbnfoExcels.size() > 0) {
                int i = 1;
                for (UsproblemIbnfoExcel usproblemIbnfoExcel : usproblemIbnfoExcels) {
                    usproblemIbnfoExcel.setRownum(i);

                    i++;
                }
            }
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            // 生成workbook 并导出
            // 创建参数对象（用来设定excel得sheet得内容等信息）
            ExportParams systemCompilation1 = new ExportParams();
            // 设置sheet的名称
            systemCompilation1.setSheetName("sheet1");
            // 创建sheet1使用得map
            Map<String, Object> systemCompilationMap = Maps.newHashMap();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            systemCompilationMap.put("title", systemCompilation1);
            // 模版导出对应得实体类型
            systemCompilationMap.put("entity", UsproblemIbnfoExcel.class);
            // sheet中要填充得数据
            systemCompilationMap.put("data", usproblemIbnfoExcels);
            // 将sheet1、sheet2.......sheet13使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(systemCompilationMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            // 导出操作
            FileOutputStream fos = new FileOutputStream(targetFileName);
            workbook.write(fos);
            fos.close();
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }

    }


    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 问题数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> problemCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select t.belong_company_name, count(*) as problemCount" +
                "  from US_PROBLEM_INFO t, act_business_status ac" +
                " where t.enabled = 1" +
                "   and t.removed_time is null" +
                "   and ac.enabled = 1" +
                "   and t.pm_ins_id = ac.receipt_code");
        String sql2 = " group by t.belong_company_name";
        Map<String, Object> param = Maps.newHashMap();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(startTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')>=:startTime");
            param.put("startTime", startTime);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(endTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')<=:endTime");
            param.put("endTime", endTime);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.append(sql2).toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }


    /**
     * 问题数量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> problemAllCount(Map<String, Object> resultMap) {
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");//talkTime--开始时间
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");//talkTime--结束时间
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select count(*) as problemCount" +
                "  from US_PROBLEM_INFO t" +
                " where t.enabled = 1" +
                "   and t.removed_time is null");
        Map<String, Object> param = Maps.newHashMap();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(startTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')>=:startTime");
            param.put("startTime", startTime);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(endTime)) {
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd')<=:endTime");
            param.put("endTime", endTime);
        }
        list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    @Override
    public JsonResponse test() {
        JsonResponse jsonResponse = null;
        Date day = new Date();
        day = DateUtil.subDays(3);

        //查询所有未归档工单
        List<UsProblemInfo> problemInfos = usProblemInfoRepository.findAllByStateAndTimes();
        if (problemInfos.size() > 0) {
            for (UsProblemInfo problemInfo : problemInfos) {
                if (!problemInfo.getProblemName().equals("测试工单请忽略")) {
                    continue;
                }
                //模拟登陆起草人
                List<Map<String, Object>> wfWorkItemModels = usProblemInfoRepository.findAllByReceiptCode(problemInfo.getPmInsId());
                if (wfWorkItemModels.size() == 0) {
                    continue;
                }
                String userName = wfWorkItemModels.get(0).get("participant").toString();
                String workItemId = wfWorkItemModels.get(0).get("work_item_id").toString();
                String activityDefId = wfWorkItemModels.get(0).get("activity_def_id").toString();
                String username = rsaEncryptor.encrypt(userName);
                String createdTime = wfWorkItemModels.get(0).get("created_time").toString();
                Date createDate = null;
                DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    createDate = fmt.parse(createdTime);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                //判断事是否实是在分公司党办管理员这一环节djfupt.braAdminFeedback  判断创建时间是否已经超过三天，超过三天需要手动推送到 分公司党委主任
                if (activityDefId.equals(Constants.BRAADM_INFEED_BACK) && day.compareTo(createDate) > 0) {
                    loginUtils.manualLogin(username, Constants.APP_CODE);
                    doFilter(userName);
                    //根据字典表获取下一步出人
                    IUser user = SecurityUtils.getCurrentUser();
                    List<SysDictValue> dictValues = dictValueRepository.findAllByNameAndType(Constants.DIRECTOR, user.getBelongCompanyName());
                    if (dictValues.size() > 0) {
                        jsonResponse = saveSubmitTask(problemInfo,
                                workItemId,
                                Constants.OVERTIME_RETURN,
                                "超时三天，请分公司党委主任处理",
                                dictValues.get(0).getValue(), activityDefId, null,
                                null, null, null, "PC", userName);
                        log.debug(jsonResponse.getMessage());
                    }

                }
                //判断事是否实是在分公司党办管理员这一环节djfupt.braAdminFeedback  判断创建时间是否已经超过三天，超过三天需要手动推送到 分公司党委书记
                if (activityDefId.equals(Constants.OVERTIME_APPROVAL) && day.compareTo(createDate) > 0) {
                    log.debug(problemInfo.toString());
                    loginUtils.manualLogin(username, Constants.APP_CODE);
                    doFilter(userName);
                    //根据字典表获取下一步出人
                    IUser user = SecurityUtils.getCurrentUser();
                    List<SysDictValue> dictValues = dictValueRepository.findAllByNameAndType(Constants.SECRETARY, user.getBelongCompanyName());
                    if (dictValues.size() > 0) {
                        jsonResponse = saveSubmitTask(problemInfo,
                                workItemId,
                                Constants.OVERTIME_APPROVAL_PASS,
                                "超时三天，请分公司党委书记处理",
                                dictValues.get(0).getValue(), activityDefId, null,
                                null, null, null, "PC", userName);
                        log.debug(jsonResponse.getMessage());
                    }

                }

            }
        }
        return null;
    }

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private WorkFlowBpsLoginService workFlowBpsLoginService;

    public void doFilter(String userName) {
        boolean bpsTenant = Boolean.valueOf(bpsConfig.bpsTenant);
        String bpsTenantId = bpsConfig.bpsTenantId;
        Map<String, Object> map = Maps.newConcurrentMap();
        map.put("tenant", bpsTenant);
        map.put("userName", userName);
        map.put("tenantId", bpsTenantId);
        workFlowBpsLoginService.bpsLogin(map);
    }


    public JsonResponse updateProblemInfo(UsProblemInfo problemInfo) {
        if (StringUtils.isEmpty(problemInfo.getId())) {
            return JsonResponse.fail("参数有误");
        }
        this.update(problemInfo);
        Map<String, String> map = commonService.getProcessMap("B");
        String processType = map.get("processType");
        this.updateFileByPmInsId(problemInfo, processType);//更新附件
        return JsonResponse.success("修改成功");
    }


}
