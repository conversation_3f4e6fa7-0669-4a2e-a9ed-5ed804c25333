﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>流程下一步</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            var param = {};
            if (gps.processType == 'quetsion') {
                param = {
                    "getDecisionsCmd": "action/usProblemInfo/getDecisions?processType=B&questionMode=" + gps.questionMode,
                    "getOrgAndUserCmd": "action/usProblemInfo/getOrgAndUser"
                };
            } else if (gps.processType == 'experience') {
                param = {
                    "getDecisionsCmd": "action/usCaseInfo/getDecisions?processType=A&questionMode=" + gps.questionMode,
                    "getOrgAndUserCmd": "action/usCaseInfo/getOrgAndUser"
                };
            }else if(gps.processType == 'policy'){
                param = {
                    "getDecisionsCmd": "action/usPolicyInfo/getDecisions?processType=C&source=PC",
                    "getOrgAndUserCmd": "action/usPolicyInfo/getOrgAndUser"
                };
            } else if (gps.processType == 'faults') {
                param = {
                    "getDecisionsCmd": "action/findFaults/getDecisions?processType=D&source=PC",
                    "getOrgAndUserCmd": "action/findFaults/getOrgAndUser"
                };
            } else if (gps.processType == 'faults3') {
                param = {
                    "getDecisionsCmd": "action/findFaults/getDecisions?processType=E&source=PC",
                    "getOrgAndUserCmd": "action/findFaults/getOrgAndUser?faultsAppName="+ encodeURIComponent(gps.faultsAppName)
                };
            }
            else {
                param = {
                    "getDecisionsCmd": "action/usMyGroup/getDecisions",
                    "getOrgAndUserCmd": "action/usMyGroup/getOrgAndUser"
                };
            }
            loadProcessNext(param);
        });
    </script>
</head>

<body>
    <div id="processNext" class="easyui-resizable" data-options="handles:'s',onStopResize:onStopResize"
        style="width:942px;height:287px;">
        <input id="copyLocation" name="copyLocation" type="hidden" />
        <div style="width:220px;float:left;">
            <h6 class="proNxtTit">填写意见</h6>
            <div style="padding:0 20px 0 0;">
                <fieldset class="title titleA mt10 p10 proNetOptF" style="height:240px;">
                    <legend>
                        <font class="f14">决策项</font>
                    </legend>
                    <div class="decList" style="padding:8px 10px 0px 20px;"></div>
                </fieldset>
                <fieldset class="title titleA mt10 p10 proNetMesF" style="height:150px;">
                    <legend>
                        <font class="f14">意见内容</font>
                    </legend>
                    <textarea id="message" name="message" style="width: 100%; height: 98px; resize: none;padding:8px 10px 0px 20px;"></textarea>
                </fieldset>
            </div>
        </div>
        <div class="resizableR" style="width:682px;float:left;">
            <h6 class="proNxtTit toDoTitle">办理人</h6>
            <div class="proNetTreeF titleA" style="margin-top:20px;">
                <div class="proNetTreeFT"></div>
                <div class="proNetTreeFD"></div>
            </div>
        </div>
    </div>
    <!--<div class="copyD" style="width:942px;height:175px;overflow:hidden;">
	<div style="width:260px;float:left;">
		<h6 class="proNxtTit copyH">抄送</h6>
		<div class="copyH" style="padding:0 20px 0 0;">
			<fieldset class="title titleA mt10 p10 copyPh" style="height:124px;">
				<legend><font class="f14">抄送意见内容</font></legend>
				<textarea id="copyMessage" name="copyMessage" style="width: 100%; height: 80px; resize: none;"></textarea>
			</fieldset>
		</div>
	</div>
	<div style="width:682px;float:left;">
		<h6 class="proNxtTit copyH">抄送人</h6>
		<div class="copyPhF titleA" style="margin-top:20px;">
			<div class="copyPhT"></div>
			<div class="copyPhD"></div>
		</div>
	</div>
</div>-->
</body>

</html>