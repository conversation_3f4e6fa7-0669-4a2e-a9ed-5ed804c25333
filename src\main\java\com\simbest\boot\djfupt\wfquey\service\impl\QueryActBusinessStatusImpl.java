package com.simbest.boot.djfupt.wfquey.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.GenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.exceptions.BpsWorkFlowBusinessException;
import com.simbest.boot.bps.process.bussiness.mapper.ActBusinessStatusMapper;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfWorkItemModelService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.wfquey.service.IQueryActBusinessStatusService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用途：查询待办已办
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryActBusinessStatus")
public class QueryActBusinessStatusImpl extends GenericService<ActBusinessStatus, Long> implements IQueryActBusinessStatusService {


    @Autowired
    private IProcessTodoDataService processTodoDataService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ISysOperateLogService operateLogService;


    @Autowired
    private ActBusinessStatusMapper actBusinessStatusMapper;

    @Autowired
    private IWfWorkItemModelService wfWorkItemModelService;

    @Autowired
    private CustomDynamicWhere dynamicRepository;

    String param1 = "/action/queryActBusinessStatus";

    /**
     * 我的待办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myTaskToDo(Integer pageindex, Integer pagesize, String title, String workCode, String source, String userCode) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> resultPage = null;
        /*准备操作日志参数*/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myTaskToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            boolean isSms = Boolean.FALSE;
            if (StrUtil.equals(source, "SMS")) {
                source = Constants.MOBILE;
                isSms = Boolean.TRUE;
            }
            /*判断是否是从手机端 true是，false否*/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            /*查询待办*/
            paramMap.put("title", title);
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H,I,J,K");
            paramMap.put("participant", SecurityUtils.getCurrentUserName());
            paramMap.put("workCode", workCode);
            Pageable page = getPageable(pageindex, pagesize, null, null);
            resultPage = getTodoByUserNamePage(paramMap, pageindex, pagesize);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /*操作日志记录*/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(resultPage, (resultPage != null && resultPage.getContent().size() > 0) ? "操作成功！" : "暂无待办");
    }



    /**
     * 我的待办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myTaskToDoDj(Integer pageindex, Integer pagesize, String title, String workCode, String source, String userCode) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> resultPage = null;
        /*准备操作日志参数*/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myTaskToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            boolean isSms = Boolean.FALSE;
            if (StrUtil.equals(source, "SMS")) {
                source = Constants.MOBILE;
                isSms = Boolean.TRUE;
            }
            /*判断是否是从手机端 true是，false否*/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /*查询待办*/
            paramMap.put("title", title);
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H,I,J,K");
            paramMap.put("participant", SecurityUtils.getCurrentUserName());
            paramMap.put("workCode", workCode);
            Pageable page = getPageable(pageindex, pagesize, null, null);
            resultPage = getTodoByUserNamePage(paramMap, pageindex, pagesize);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /*操作日志记录*/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(resultPage, (resultPage != null && resultPage.getContent().size() > 0) ? "" : "");
    }

    /**
     * 分页获取指定 userName 下面所有的待办数据
     *
     * @param todoUserParam 查询待办参数
     * @return
     */
    public Page getTodoByUserNamePage(Map<?, ?> todoUserParam, Integer pageindex, Integer pagesize) {
        Page<Map<String, Object>> myTodoPage = null;
        String participant = (String) todoUserParam.get("participant");
        String dynamicWhere = (String) todoUserParam.get("title");
        String pmInsType = (String) todoUserParam.get("pmInsType");
        String workCode = (String) todoUserParam.get("workCode");
        StringBuilder inWhere = new StringBuilder();
        try {
            List<String> inWheres = new ArrayList<String>();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pmInsType)) {
                inWheres.addAll(Arrays.asList(pmInsType.split(",")));
            }
            List<Map<String, Object>> actBusinessStatusList = this.getTodoByUserNamePage(participant, dynamicWhere, workCode, inWheres);
            //转驼峰式
            List<Map<String, Object>> list = FormatTool.formatConversion(actBusinessStatusList);
            //构建分页对象
            List<Map<String, Object>> pagination = PageTool.pagination(list, pageindex, pagesize);
            Pageable pageable = this.getPageable(pageindex, pagesize);
            //分页处理
            myTodoPage = new PageImpl<>(pagination, pageable, list.size());

            for (Map<String, Object> map : myTodoPage.getContent()) {
                Long parentProcessInstId = ((BigDecimal) map.get("parentProcId")).longValue();
                Long processInstId = ((BigDecimal) map.get("processInstId")).longValue();
                if (!parentProcessInstId.equals(0)) {
                    processInstId = ((BigDecimal) map.get("processInstId")).longValue();
                    //获取最新的流程实例id
                    List<ActBusinessStatus> statusList = actBusinessStatusMapper.getByProcessInstIdDoneNew(processInstId);
                    if (!statusList.isEmpty() && statusList.size() > 0) {
                        processInstId = statusList.get(0).getProcessInstId();
                    }
                }
                //查询最新的工作项实例信息
                WfWorkItemModel wfWorkItemModel = wfWorkItemModelService.queryTodoByParticipantAndProInsId(processInstId, participant);
                map.put("workItemId", wfWorkItemModel.getWorkItemId());
                map.put("activityDefId", wfWorkItemModel.getActivityDefId());
                map.put("activityInstName", wfWorkItemModel.getActivityInstName());
                map.put("participant", participant);
                map.put("assistant", wfWorkItemModel.getAssistant());
                map.put("workItemStartTime", wfWorkItemModel.getStartTime());
                map.put("workItemEndTime", wfWorkItemModel.getEndTime());
                String processType = ((String) map.get("receiptCode")).replaceAll("[^a-z^A-Z]", "");
                map.put("pmInsType", processType);
            }
        } catch (Exception e) {
            BpsWorkFlowBusinessException.printException(e);
        }
        return myTodoPage;
    }

    /**
     * 查询我的待办
     *
     * @param participant
     * @param dynamicWhere
     * @param workCode
     * @param pmInsType
     * @return
     */
    List<Map<String, Object>> getTodoByUserNamePage(String participant, String dynamicWhere, String workCode, List<String> pmInsType) {
        Map<String, Object> queryMap = Maps.newHashMap();
        String dataQuerySQL = " SELECT act.id, act.business_key, act.create_org_code, act.create_org_name, act.create_time, act.create_user_code, act.create_user_id, act.create_user_name, " +
                " act.current_state, act.duration, act.enabled, act.end_time, act.parent_proc_id, act.previous_assistant, act.previous_assistant_date, act.previous_assistant_name, " +
                " act.previous_assistant_org_code, act.previous_assistant_org_name, act.process_ch_name, act.process_def_id, act.process_def_name, act.process_inst_id, " +
                " act.receipt_code, act.receipt_title, act.removed, act.start_time, act.update_time," +
                " us.WORK_CODE," +
                " wk.WORK_ITEM_ID as workItemId," +
                " wk.ACTIVITY_DEF_ID as activityDefId," +
                " wk.ACTIVITY_INST_NAME as activityInstName," +
                " wk.participant as participant," +
                " wk.assistant as assistant," +
                " wk.current_State  as wkCurState," +
                " wk.start_Time as workItemStartTime" +
                " FROM act_business_status act," +
                " us_pm_instence us," +
                " wf_workitem_model wk" +
                " WHERE act.PROCESS_INST_ID = wk.PROCESS_INST_ID" +
                " and act.BUSINESS_KEY = us.id" +
                " and us.pm_ins_type in (:pmInsType)" +
                " and wk.participant = :participant" +
                " and wk.current_State = 10" +
                " and act.enabled = 1" +
                " and wk.enabled = 1" +
                " and us.enabled = 1";
        queryMap.put("participant", participant);
        queryMap.put("pmInsType", pmInsType);
        if (dynamicWhere != null) {
            dataQuerySQL += " and act.RECEIPT_TITLE like concat(concat('%', :dynamicWhere), '%')";
            queryMap.put("dynamicWhere", dynamicWhere);
        }
        if (workCode != null) {
            dataQuerySQL += " and us.WORK_CODE like concat(concat('%', :workCode), '%')";
            queryMap.put("workCode", workCode);
        }
        String orderSQL = " order by wk.START_TIME desc";
        return dynamicRepository.queryNamedParameterForList(dataQuerySQL + orderSQL, queryMap);
    }





    /**
     * 我的已办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyJoin(Integer pageindex, Integer pagesize, String title,String workCode,  String source, String userCode) {
        Page<ActBusinessStatus> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyJoin";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的已办**/
            paramMap.put("title", title);
            paramMap.put("pmInsType", "A,B,C,D,E");
            paramMap.put("assistant", SecurityUtils.getCurrentUserName());
            Pageable page = getPageable(pageindex, pagesize, null, null);
            actBusinessStatuses = (Page<ActBusinessStatus>) processTodoDataService.getAreadyDoneByUserIdSubFlowPage(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 获取指定 userName 下面所有的已办数据 存在子流程(目前在用)
     *
     * @param doneUserParam 查询已办参数
     * @return
     */
    public Page getAreadyDoneByUserIdSubFlowPage(Map<?, ?> doneUserParam, Integer pageindex, Integer pagesize) {
        long begin;
        long end;
        Page<Map<String, Object>> myJoinPage = null;
        String assistant = (String) doneUserParam.get("assistant");
        String dynamicWhere = (String) doneUserParam.get("title");
        String pmInsType = (String) doneUserParam.get("pmInsType");
        String workCode = (String) doneUserParam.get("workCode");
        String providerName = (String) doneUserParam.get("providerName");
        StringBuilder inWhere = new StringBuilder();
        try {
     /*       if (org.apache.commons.lang3.StringUtils.isEmpty(dynamicWhere)) {
                dynamicWhere = "";
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(workCode)) {
                workCode = "";
            }*/
            List<String> inWheres = new ArrayList<String>();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pmInsType)) {
                inWheres.addAll(Arrays.asList(pmInsType.split(",")));
            }
            begin = System.currentTimeMillis();
            System.out.println("============================" + begin + "========================");
            List<Map<String, Object>> actBusinessStatusList = this.getByAreadyDoneAssistantSubFlowPage(assistant,dynamicWhere, workCode,
                    inWheres,providerName);
            end = System.currentTimeMillis();
            System.out.println("============================" + end + "============================");
            System.out.println("============================SQL查询时间：" + (end - begin) + "============================");
            log.debug("============================SQL查询时间：【{}】====================================================", (end - begin));
            //转驼峰式
            List<Map<String, Object>> list = FormatTool.formatConversion(actBusinessStatusList);
            //构建分页对象
            List<Map<String, Object>> pagination = PageTool.pagination(list, pageindex, pagesize);
            Pageable pageable = this.getPageable(pageindex, pagesize);
            //分页处理
            myJoinPage = new PageImpl<>(pagination, pageable, list.size());

            begin = System.currentTimeMillis();
            System.out.println("============================" + begin + "========================");
            for (Map<String, Object> map : myJoinPage.getContent()) {
                Long parentProcessInstId = ((BigDecimal) map.get("parentProcId")).longValue();
                Long processInstId = ((BigDecimal) map.get("processInstId")).longValue();
                if (!parentProcessInstId.equals(0)) {
                    processInstId = ((BigDecimal) map.get("processInstId")).longValue();
                    //获取最新的流程实例id
                    List<ActBusinessStatus> statusList = actBusinessStatusMapper.getByProcessInstIdDoneNew(processInstId);
                    if (!statusList.isEmpty() && statusList.size() > 0) {
                        processInstId = statusList.get(0).getProcessInstId();
                    }
                }
                //查询最新的工作项实例信息
                WfWorkItemModel wfWorkItemModel = wfWorkItemModelService.queryAredayTodoByParticipantAndProInsId(processInstId, assistant).get(0);
                map.put("workItemId", wfWorkItemModel.getWorkItemId());
                map.put("activityDefId", wfWorkItemModel.getActivityDefId());
                map.put("activityInstName", wfWorkItemModel.getActivityInstName());
                map.put("participant", assistant);
                map.put("assistant", wfWorkItemModel.getAssistant());
                map.put("workItemStartTime", wfWorkItemModel.getStartTime());
                map.put("workItemEndTime", wfWorkItemModel.getEndTime());
                map.put("previousAssistant", wfWorkItemModel.getParticipant());
                map.put("previousAssistantName", wfWorkItemModel.getPartiName());
                String processType = ((String) map.get("receiptCode")).replaceAll("[^a-z^A-Z]", "");
                map.put("pmInsType", processType);
            }
            end = System.currentTimeMillis();
            System.out.println("============================" + end + "============================");
            System.out.println("============================map封装时间：" + (end - begin) + "============================");
            log.debug("============================map封装时间：【{}】====================================================", (end - begin));
        } catch (
                Exception e) {
            BpsWorkFlowBusinessException.printException(e);
        }
        return myJoinPage;
    }



    /**
     * 查询我的已办
     *
     * @param assistant
     * @param dynamicWhere
     * @param workCode
     * @param pmInsType
     * @return
     */
    List<Map<String, Object>> getByAreadyDoneAssistantSubFlowPage(String assistant, String dynamicWhere, String workCode, List<String> pmInsType,String providerName) {
        Map<String, Object> queryMap = Maps.newHashMap();
        String dataQuerySQL = "select act.*,us.WORK_CODE,us.provider_name" +
                " from ACT_BUSINESS_STATUS act," +
                " US_PM_INSTENCE us," +
                " (select * from wf_workitem_model where WORK_ITEM_ID in (select max(WORK_ITEM_ID) from wf_workitem_model B  where B.enabled=1 and B.assistant = :assistant group by B.Root_Proc_Inst_Id)) workItem" +
                " where act.process_inst_id = workItem.process_inst_id" +
                " and act.BUSINESS_KEY = us.id" +
                " and us.pm_ins_type in (:pmInsType)" +
                " and act.enabled = 1" +
                " and workItem.enabled = 1" +
                " and us.enabled = 1";
        queryMap.put("assistant", assistant);
        queryMap.put("pmInsType", pmInsType);
        if (dynamicWhere != null) {
            dataQuerySQL += " and act.RECEIPT_TITLE like concat(concat('%', :dynamicWhere), '%')";
            queryMap.put("dynamicWhere", dynamicWhere);
        }
        if (workCode != null) {
            dataQuerySQL += " and us.WORK_CODE like concat(concat('%', :workCode), '%')";
            queryMap.put("workCode", workCode);
        }
        if (providerName != null && !"".equals(providerName)) {
            dataQuerySQL += " and us.provider_name like concat(concat('%', :providerName), '%')";
            queryMap.put("providerName", providerName);
        }

        String orderSQL = " order by workItem.END_TIME desc";
        return dynamicRepository.queryNamedParameterForList(dataQuerySQL + orderSQL, queryMap);
    }


    /**
     * 我的申请
     *
     * @param pageindex
     * @param pagesize
     * @param title
     * @return
     */
    @Override
    public Page<ActBusinessStatus> queryMyApply(Integer pageindex, Integer pagesize, String title) {
        Map<String, String> paramMap = Maps.newHashMap();
        IUser iUser = SecurityUtils.getCurrentUser();
        paramMap.put("participant", iUser.getUsername());
        paramMap.put("title", title);
        paramMap.put("pmInsType", "A,B");
        return (Page<ActBusinessStatus>) processTodoDataService.getMyCreateDataPage(paramMap, PageRequest.of(pageindex, pagesize, Sort.by(Sort.Direction.DESC, "created_time")));
    }

    /**
     * 我的待阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyPending(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Page<Map<String, Object>> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyPending";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的待阅**/
            paramMap.put("title", title);
            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            Pageable page = getPageable(pageindex, pagesize, null, null);
            actBusinessStatuses = processTodoDataService.getMyTodoReadByUserNamePageMap(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 我的已阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyRead(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Page<Map<String, Object>> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();

        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyRead";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的已阅**/
            paramMap.put("title", title);
            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            Pageable page = getPageable(pageindex, pagesize, null, null);
            actBusinessStatuses = processTodoDataService.getMyAreadyReadByUserNamePageMap(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 查询草稿
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    来源
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myDraftToDo(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> draftTodo = null;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myDraftToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询草稿**/
            if (!StringUtils.isEmpty(title)) {
                paramMap.put("title", title);
            }
            paramMap.put("createUser", SecurityUtils.getCurrentUserName());
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H,I,J,K");
            Pageable page = getPageable(pageindex, pagesize, null, null);
            draftTodo = processTodoDataService.getMyApplyPage(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(draftTodo);
    }

}
