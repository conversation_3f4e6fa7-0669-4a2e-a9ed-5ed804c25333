package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_admin_manager")
@ApiModel(value = "问题上报")
public class UsAdminManager extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;



    @Column(length = 100)
    @ApiModelProperty(value = "网格名称", required = true)
    protected String gridName;               //网格名称


    @Column(length = 100)
    @ApiModelProperty(value = "type", required = true)
    @ExcelVOAttribute(name = "管理员类型", column = "C")
    protected String type;               //管理员类型    0党建指导员  1管理员   2 部门管理员

    @Column(length = 100)
    @ApiModelProperty(value = "部门")
    @ExcelVOAttribute(name = "所在部门", column = "B")
    private String belongDept;

    @Column(length = 100)
    @ApiModelProperty(value = "部门编码")
    private String belongDeptCode   ;


    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    @ExcelVOAttribute(name = "所在单位", column = "A")
    private String belongCom;

    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String belongComCode   ;


    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String parnetBelongComCode   ;



    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String parnetBelongComName   ;

    @Column(length = 20)
    @ApiModelProperty(value = "OA账号")
    @ExcelVOAttribute(name = "OA账号", column = "E")
    private String userName;

    @Column(length = 20)
    @ApiModelProperty(value = "管理员姓名")
    @ExcelVOAttribute(name = "管理员姓名", column = "D")
    private String trueName;

    @Column(length = 20)
    @ApiModelProperty(value = "roleUSerId")
    private String roleUserId;

    @Column(length = 20)
    @ExcelVOAttribute(name = "党建指导员手机号", column = "G")
    @ApiModelProperty(value = "phone")
    private String phone;



    @Transient
    @ExcelVOAttribute(name = "党建指导员所在组织", column = "F")
    private String orgName;

}
