package com.simbest.boot.djfupt.xtgj.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.xtgj.model.UsLhgzhdInfo;
import com.simbest.boot.djfupt.xtgj.service.IUsLhgzhdInfoService;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Objects;


@Api("联合跟装活动")
@Slf4j
@RestController
@RequestMapping(value = "/action/usLhgzhdInfo")
public class UsLhgzhdInfoController extends LogicController<UsLhgzhdInfo, String> {

    private final IUsLhgzhdInfoService service;
    private final LoginUtils loginUtils;

    public UsLhgzhdInfoController(IUsLhgzhdInfoService service, LoginUtils loginUtils) {
        super(service);
        this.service = service;
        this.loginUtils = loginUtils;
    }

    /**
     * 新增
     **/
    @PostMapping({"/insertInfo", "/insertInfo/sso", "/insertInfo/api"})
    public JsonResponse insertInfo(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                   @RequestParam(required = false) String currentUserCode,
                                   @RequestBody UsLhgzhdInfo o) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.insertInfo(o));
    }

    /**
     * 编辑
     **/
    @PostMapping({"/updateInfo", "/updateInfo/sso", "/updateInfo/api"})
    public JsonResponse updateInfo(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                   @RequestParam(required = false) String currentUserCode,
                                   @RequestBody UsLhgzhdInfo o) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.updateInfo(o));
    }

    /**
     * 查询详情
     **/
    @PostMapping({"/findByIdInfo", "/findByIdInfo/sso", "/findByIdInfo/api"})
    public JsonResponse findByIdInfo(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                     @RequestParam(required = false) String currentUserCode,
                                     @RequestParam String id) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.findByIdInfo(id));
    }

    /**
     * 删除
     **/
    @PostMapping({"/deleteByIdInfo", "/deleteByIdInfo/sso", "/deleteByIdInfo/api"})
    public JsonResponse deleteByIdInfo(@RequestParam(required = false, defaultValue = Constants.PC) String source,
                                       @RequestParam(required = false) String currentUserCode,
                                       @RequestParam String id) {
        mobileLogin(source, currentUserCode);
        service.deleteById(id);
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 通用查询列表
     **/
    @PostMapping({"/findAllInfo", "/findAllInfo/sso", "/findAllInfo/api"})
    public JsonResponse findAllInfo(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false, defaultValue = "desc") String direction,
                                    @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                    @RequestParam(required = false, defaultValue = Constants.PC) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestBody(required = false) UsLhgzhdInfo o) {
        mobileLogin(source, currentUserCode);
        return JsonResponse.success(service.findAllInfo(o, service.getPageable(page, size, direction, properties)));
    }

    /**
     * 通用导出
     */
    @PostMapping(value = {"/exportInfo", "/exportInfo/sso", "/exportInfo/api"})
    public void exportInfo(HttpServletRequest request,
                           HttpServletResponse response,
                           @RequestParam(required = false, defaultValue = Constants.PC) String source,
                           @RequestParam(required = false) String currentUserCode,
                           @RequestParam(required = false) String gridName,
                           @RequestParam(required = false) String belongCompanyCode,
                           @RequestParam(required = false) String belongDepartmentCode,
                           @RequestParam(required = false) String truename,
                           @RequestParam(required = false) LocalDateTime sdate,
                           @RequestParam(required = false) LocalDateTime edate) {
        mobileLogin(source, currentUserCode);
        UsLhgzhdInfo o = new UsLhgzhdInfo();
        o.setGridName(gridName);
        o.setBelongCompanyCode(belongCompanyCode);
        o.setBelongDepartmentCode(belongDepartmentCode);
        o.setTruename(truename);
        o.setSdate(sdate);
        o.setEdate(edate);
        service.exportInfo(request, response, o);
    }

    /**
     * 手机端 模拟登录
     *
     * @param source          手机端还是PC端
     * @param currentUserCode 当前用户code
     */
    public void mobileLogin(String source, String currentUserCode) {
        if (!Objects.equals(Constants.MOBILE, source)) return;
        Assert.state(StringUtils.isNotBlank(currentUserCode), "未登录状态,OA账户不能为空!");
        loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
    }

}
