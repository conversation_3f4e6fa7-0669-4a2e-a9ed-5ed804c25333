package com.simbest.boot.djfupt.screenCount.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.utils.MyUtils;
import com.simbest.boot.djfupt.screenCount.model.ScreenCount;
import com.simbest.boot.djfupt.screenCount.service.IScreenCountService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;


@Api(description = "访问量相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/screenCount")
public class ScreenCountController extends LogicController<ScreenCount, String> {

    private final IScreenCountService service;

    public ScreenCountController(IScreenCountService ScreenCountService) {
        super(ScreenCountService);
        this.service = ScreenCountService;
    }

    /**
     * 分页
     * <br/>params [o]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/5/18 18:22
     */
    @PostMapping(value = {"/findAllInfo", "/findAllInfo/sso", "/api/findAllInfo"})
    public JsonResponse findAllInfo(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false) String direction,
                                    @RequestParam(required = false) String properties,
                                    @RequestBody ScreenCount o) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, direction, properties);
        return JsonResponse.success(service.findAllInfo(o, pageable));
    }

    /**
     * 记录点击量
     * <br/>params [o]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/5/18 18:22
     */
    @PostMapping(value = {"/record", "/record/sso", "/api/record"})
    public JsonResponse record(@RequestParam(required = false) String name,
                               @RequestParam(required = false) String source,
                               @RequestParam(required = false) String currentUserCode) {
        MyUtils.manualLogin(source, currentUserCode);
        service.record(name);
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 导出
     * <br/>params [o]
     *
     * <AUTHOR>
     * @since 2023/5/18 18:22
     */
    @PostMapping(value = {"/export", "/export/sso", "/api/export"})
    public void export(HttpServletResponse response, HttpServletRequest request, String sdate, String edate) {
        ScreenCount o = new ScreenCount();
        o.setSdate(this.formatDate(sdate));
        o.setEdate(this.formatDate(edate));
        service.export(response, request, o);
    }

    private LocalDate formatDate(String date) {
        if (StringUtils.isBlank(date)) return null;
        date = date.replaceAll("/", "-");
        try {
            Date parse = DateFormat.getDateInstance().parse(date);
            return parse.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

}
