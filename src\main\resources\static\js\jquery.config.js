﻿//声明字典命名空间
if(!window.web){ web={}; }
if(!window.dictionary){ dictionary={}; }
if(!window.dictionarys){ dictionarys={}; }
web.appCode="djfupt";
//ABC代表的是流程类型,后边的是对应的需要跳转的页面路径
web.appHtml = {
    "A": "html/change/changeExperience.html",
    "B": "html/change/changeQuestion.html",
    "C": "html/change/changePolicy.html",
    "D": "html/query/faultsApply.html",
    "E": "html/query/faultsApplyThree.html"
};
//ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部
web.appName = {
    "A": "优秀案例上报",
    "B": "问题上报",
    "C": "政策宣讲",
    "D": "找茬上报",
    "E": "找茬上报三期"
};
web.processType = "pmInsType-流程类型";
