<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
  <title>管理员管理</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
    rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
    </script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
    </script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
    </script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
    </script>
  <script type="text/javascript">
    var myinfo = {};
    $(function () {
      //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
      var pageparam = {
        "listtable": {
          "listname": "#glyTable", //table列表的id名称，需加#
          "querycmd": "action/usGidSupervision/findListByPage?type=1", //table列表的查询命令
          "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
          "styleClass": "noScroll",
          "nowrap": true, //把数据显示在一行里,默认true
          "frozenColumns": [], //固定在左侧的列
          "columns": [
            [ //列
              { title: "所在单位", field: "belongCompanyName", width: 80, tooltip: true, align: "center" },
              { title: "所在部门", field: "county", width: 100, tooltip: true, align: "center" },
              { title: "网格名称", field: "gridName", width: 100, tooltip: true, align: "center" },
              { title: "网格长名称", field: "gridTrueName", width: 100, tooltip: true, align: "center" },
              { title: "监督员名称", field: "supervisionTrueName", width: 100, tooltip: true, align: "center" },
              {
                title: "最后修改时间", field: "modifiedTime", width: 90, tooltip: true, align: "center",
                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                  return getTimeDate(row.createTime, "yyyy-MM-dd hh:mm:ss");
                }
              },
              {
                field: "opt", title: "操作", width: 80, rowspan: 1, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                  var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                  g += "<a href='#' class='delete'  data-id='" + row.id + "'>【删除】</a>";
                  return g
                }
              }
            ]
          ]
        },
        "dialogform": {
          "dialogid": "#buttons",//对话框的id
          "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
          "insertcmd": "action/usGidSupervision/insert",//新增命令
          "updatacmd": "action/usGidSupervision/updateInfo",//修改命令
          "beforerender": function (data) {
           },
          "onSubmit": function (data) {
            return !flag;
          }
        },
        "readDialog": {//查看
          "dialogid": "#readDag",
          "dialogedit": false,//查看对话框底部要不要编辑按钮
          "formname": "#glyTableReadForm"
        }
      };
      loadGrid(pageparam);

    });

    // 防止数据还没回来用户就再次点击
    var flag = false
    //选择人员
    function chooseuser() {
      if (flag) return
      if (top.chooseWeb && top.chooseWeb.chooseUsersVal) {
        top.chooseWeb.chooseUsersVal = {};
      }
      top.dialogP("html/choose/chooseUsersQuery.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB", false, 900, 500)
    }
    function chooseuser2() {
      if (flag) return
      if (top.chooseWeb && top.chooseWeb.chooseUsersVal) {
        top.chooseWeb.chooseUsersVal = {};
      }
      top.dialogP("html/choose/chooseUsersQuery.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB2", false, 900, 500)
    }
    window.chooseUserCB = function (data) {
      flag = true
      ajaxgeneral({
        url: "action/usAdminManager/queryUserOrgInfo?userName=" + data.data[0].userName,
        contentType: "application/json; charset=utf-8",
        success: function (res) {
          flag = false
          var data = res.data
          $('#glyTableAddForm #gridTrueName').val(data.truename);
          $('#glyTableAddForm #gridUserName').val(data.username);
          $('#glyTableAddForm #gridPhone').val(data.preferredMobile);
        },
        error: function (res) {
          flag = false
        }
      });
    }

    window.chooseUserCB2 = function (data) {
      flag = true
      ajaxgeneral({
        url: "action/usAdminManager/queryUserOrgInfo?userName=" + data.data[0].userName,
        contentType: "application/json; charset=utf-8",
        success: function (res) {
          flag = false
          var data = res.data
          $('#glyTableAddForm #supervisionTrueName').val(data.truename);
          $('#glyTableAddForm #supervisionName').val(data.username);
          $('#glyTableAddForm #supervisionPhone').val(data.preferredMobile);
        },
        error: function (res) {
          flag = false
        }
      });
    }

    $(document).on('click', 'a.delete', function () {
      var id = $(this).attr('data-id');
      $.messager.confirm('确认', '您确认想要删除吗？', function (r) {
        if (r) {
          ajaxgeneral({
            url: 'action/usGidSupervision/delInfo?id=' + id,
            data: {},
            contentType: "application/json; charset=utf-8",
            success: function (data) {
              $("#glyTable").datagrid("reload");
            }
          });
        }
      });
    })

    //下载模板
    function downloadFile() {
      window.open(web.rootdir + "action/usGidSupervision/dolwnLoad");
    }
    //导入excel
    function uploadExcel() {
      $('#uploadFiles').trigger('click');
    }
    //导入excel后
    function OtherInfo(data) {
      $('#glyTable').datagrid('load');
    }
    //导出
    $(document).on('click', 'a.exporttable', function () {
      $("#glyTableQueryForm").attr("action", web.rootdir + "action/usAdminManager/exportExcel?type=1");
      $("#glyTableQueryForm").attr("method", "post");
      $("#glyTableQueryForm").submit();
    });

    function belongCompanyNameFun(data){
      $('#county').combobox({ 
          valueField:'orgName',    
          textField:'orgName',
          panelHeight:'auto',
          editable:false,
          url: 'action/usGidSupervision/getCountyByCityName?city=' + data.ORG_NAME,
          onSelect:countyFun,
      });
    }

    function countyFun(data){
      // console.log(data);

    }
  </script>
  <style>
    .formTable {
      width: 100%;
      margin-top: 0px;
      border-spacing: 0;
      border-top: 1px solid #e8e8e8;
      border-left: 1px solid #e8e8e8;
    }

    .formTable>tbody>tr>td {
      border-right: 1px solid #e8e8e8;
      border-bottom: 1px solid #e8e8e8;
      font-size: 13px;
      color: #356885;
      font-weight: bold;
    }

    .formTable>tbody>tr>td input,
    .formTable>tbody>tr>td span,
    .formTable>tbody>tr>td textarea,
    .formTable>tbody>tr>td .textbox .textbox-text {
      border: none;
      font-size: 13px;
    }

    .formTable td.lable {
      background-color: #ddf1fe;
      padding: 5px;
      text-align: left;
      max-width: 100px;
    }

    .formTable td .textAndInput_readonly,
    .formTable td .textAndInput_readonly .validatebox-readonly {
      background-color: #fff;
    }

    input:read-only {
      background-color: #fff;
    }

    textarea {
      line-height: 20px;
      letter-spacing: 1px;
    }

    .queSave {
      display: none;
    }

    .uploadImageI {
      padding-top: 32px;
      margin-right: 100px;
    }

    a.btn {
      width: 95px;
    }

    .cselectorImageUL {
      width: 85px;
      float: right;
      margin-right: 10px;
    }

    .cselectorImageUL .btn {
      right: 0px;
    }

    .cselectorImageUL input {
      right: 5px;
    }

    .dialog-button {
      text-align: center;
    }

    .dialog-button .l-btn {
      margin-left: 30px;
    }
  </style>
</head>

<body class="body_page">
  <form id="glyTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
      <tr>
        <td width="70"></td>
        <td width="200"></td>
        <td width="70"></td>
        <td width="200"></td>
        <td width="70"></td>
        <td width="200"></td>
        <td width="70"></td>
        <td width="200"></td>
      </tr>
      <tr>
        <td align="right">所在单位</td>
        <td><input name="belongCompanyName" class="easyui-combobox" style="max-width: 100%; height: 32px;" data-options="
                    valueField: 'ORG_NAME',
                    panelHeight:'200px',
                    ischooseall:true,
                    textField: 'ORG_NAME',
                    editable:false,
                    url: 'action/commom/queryOrg'" />
        </td>
        <td align="right">网格名称</td>
        <td><input id="gridName" name="gridName" type="text" /></td>

        <td align="right">网格长姓名</td>
        <td><input id="gridTrueName" name="gridTrueName" type="text" /></td>
        <td align="right">监督员姓名</td>
        <td><input id="supervisionTrueName" name="supervisionTrueName" type="text" /></td>
      </tr>
      <tr>
        <td colspan="8">
          <a class="btn fr ml10" onclick="downloadFile()"><span>模板下载</span></a>
          <!-- <a class="btn fr ml10 exporttable "> <span>导出</span> </a> -->
          <a class="btn fr ml10 showDialog "><span>添加</span></a>
          <a class="btn fr searchtable"><span>查询</span></a>

          <input id="uploadFiles" name="uploadFiles" type="text" file="true" mulaccept="true"
            class="cselectorImageUpload fr" btnmsg="<span class='iconfont' title='添加' style='font-size:14px'>导入</span>"
            href="action/usGidSupervision/importGidSupervision" OtherInfo="OtherInfo" />
        </td>

      </tr>
    </table>
  </form>
  <!--table-->
  <div class="glyTable">
    <table id="glyTable"></table>
  </div>
  <!--新增修改的dialog页面-->
  <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:750px;height:520px;
     margin-top: 15px">
    <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8" onSubmit="onSubmit()">
      <input id="id" name="id" type="hidden" />
      <table border="0" cellpadding="0" cellspacing="0" class="formTable">
        <tr>
          <td width="25%"></td>
          <td width="75%"></td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 地市<font class="col_r">*</font> </td>
          <td width="200">
            <input id="city"  name="city" class="easyui-combobox" style="max-width: 100%; height: 32px;" required data-options="
                        valueField: 'ORG_NAME',
                        panelHeight:'200px',
                        ischooseall:true,
                        textField: 'ORG_NAME',
                        editable:false,
                        url: 'action/commom/queryOrg',
                        onSelect:belongCompanyNameFun" />
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 地市编码<font class="col_r">*</font> </td>
          <td width="200">
            <input id="cityCode" name="cityCode" type="text" class="easyui-validatebox"  required='required' />
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 区县<font class="col_r">*</font> </td>
          <td width="200">
            <input id="county" name="county" class="easyui-combobox" style="width: 100%; height: 32px;" required />
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 区县编码<font class="col_r">*</font> </td>
          <td width="200">
            <input id="countyCode" name="countyCode" type="text" class="easyui-validatebox" 
              required='required' />
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 网格编码<font class="col_r">*</font> </td>
          <td width="200">
            <input id="gridCode" name="gridCode" type="text" class="easyui-validatebox" 
              required='required' />
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 网格名称<font class="col_r">*</font> </td>
          <td width="200">
            <input id="gridName" name="gridName" type="text" class="easyui-validatebox" 
              required='required' />
          </td>
        </tr>
        <tr style="height: 35px">
          <td width="100" align="right" class="lable"> 网格长姓名<font class="col_r">*</font> </td>
          <td width="200">
            <input id="gridTrueName" name="gridTrueName" type="text" class="easyui-validatebox" readonly
              required='required' style="width: 88%" />
            <input id="gridUserName" name="gridUserName" type="hidden" />

            <a class="btn ml10 fr" title="人员查询" onclick="chooseuser()" style="width: 50px; padding-right:7px"><i
                class="iconfont">&#xe634;</i></a>
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 网格长手机号<font class="col_r">*</font> </td>
          <td width="200">
            <input id="gridPhone" name="gridPhone" type="text" class="easyui-validatebox" readonly
              required='required' />
          </td>
        </tr>
        <tr style="height: 35px">
          <td width="100" align="right" class="lable"> 监督员姓名<font class="col_r">*</font> </td>
          <td width="200">
            <input id="supervisionTrueName" name="supervisionTrueName" type="text" class="easyui-validatebox" readonly
              required='required' style="width: 88%" />
            <input id="supervisionName" name="supervisionName" type="hidden" />

            <a class="btn ml10 fr" title="人员查询" onclick="chooseuser2()" style="width: 50px; padding-right:7px"><i
                class="iconfont">&#xe634;</i></a>
          </td>
        </tr>
        <tr>
          <td width="100" align="right" class="lable"> 监督员手机号<font class="col_r">*</font> </td>
          <td width="200">
            <input id="supervisionPhone" name="supervisionPhone" type="text" class="easyui-validatebox" readonly
              required='required' />
          </td>
        </tr>
      </table>
    </form>
  </div>
</body>

</html>