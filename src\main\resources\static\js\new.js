// 初始化设置多选下拉框
function initMultipleCombobox(eleId, url, prop, onLoadSuccess, onSelect) {
    $(eleId).combobox({
        url: web.rootdir + url,
        contentType: 'application/json; charset=utf-8',
        valueField: prop?prop.valueField:'value',//绑定字段ID
        textField: prop?prop.textField:'name',//绑定字段Name
        editable: false,
        panelHeight: '200',
        multiple: true,
        formatter: function (row) {
            var opts = $(this).combobox('options');
            return '<input type="checkbox"  class="combobox-checkbox wauto" id="' + row[opts.valueField] + '">' + row[opts.textField]
        },
        onShowPanel: function () {
            // 先清空下拉面板中已选中的值
            $(this).combobox("panel").find(".wauto").each(function(i,v){
                $(v).attr("checked",false);
            });

            var opts = $(this).combobox('options');
            var target = this;
            var values = $(target).combobox('getValues');
            $.map(values, function (value) {
                var el = opts.finder.getEl(target, value);
                el.find('input.combobox-checkbox')._propAttr('checked', true);
            })
        },
        onLoadSuccess: function (data) {
            var opts = $(this).combobox('options');
            var target = this;
            var values = $(target).combobox('getValues');
            $.map(values, function (value) {
                var el = opts.finder.getEl(target, value);
                el.find('input.combobox-checkbox')._propAttr('checked', true);
            })

            if(onLoadSuccess){
                eval(onLoadSuccess+"(data)");
            }
        },
        onSelect: function (row) {
            if(row){
                var opts = $(this).combobox('options');
                var el = opts.finder.getEl(this, row[opts.valueField]);
                el.find('input.combobox-checkbox')._propAttr('checked', true);
                // 去掉显示值前面的,
                var inputVal = $(this).combobox("textbox").val();
                var newVal = arryFormat(inputVal.split(","));//选择的数据，对应textField
                $(this).combobox("textbox").val(newVal.join(","));

                if(onSelect){
                    var result = [];
                    var values = $(this).combobox("getValues");//选择的数据，对应valueField
                    for(var i=0;i<values.length;i++){
                        var item = {};
                        item[prop?prop.valueField:'value'] = values[i];
                        item[prop?prop.textField:'name'] = newVal[i];
                        result.push(item);
                    }
                    eval(onSelect+"(result)");
                }
            }
        },
        onUnselect: function (row) {
            var opts = $(this).combobox('options');
            var el = opts.finder.getEl(this, row[opts.valueField]);
            el.find('input.combobox-checkbox')._propAttr('checked', false);
            // 去掉显示值前面的,
            var inputVal = $(this).combobox("textbox").val();
            var newVal = arryFormat(inputVal.split(","));//选择的数据，对应textField
            $(this).combobox("textbox").val(newVal.join(","));

            if(onSelect){
                var result = [];
                var values = $(this).combobox("getValues");//选择的数据，对应valueField
                for(var i=0;i<values.length;i++){
                    var item = {};
                    item[prop?prop.valueField:'value'] = values[i];
                    item[prop?prop.textField:'name'] = newVal[i];
                    result.push(item);
                }
                eval(onSelect+"(result)");
            }
        }
    });
}

// 去掉数组中的空字符
function arryFormat(arry){
    var newArry = [];
    if(arry){
        for(var i=0;i<arry.length;i++){
            if(arry[i]!="" && arry[i]!=" "){
                newArry.push(arry[i]);
            }
        }
    }
    return newArry;
}