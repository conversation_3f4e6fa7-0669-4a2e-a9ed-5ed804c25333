package com.simbest.boot.djfupt.findfaults.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ExportFaultsInfo {

    @ExcelVOAttribute(name = "归属单位",column = "A")
    @ApiModelProperty(value = "归属单位")
    private String companyName;

    @ExcelVOAttribute(name = "归属部门",column = "B")
    @ApiModelProperty(value = "归属部门")
    private String departmentName;

    @ExcelVOAttribute(name = "网格名称",column = "C")
    @ApiModelProperty(value = "网格名称")
    private String gridName;


    @ExcelVOAttribute(name = "上报人姓名",column = "D")
    @ApiModelProperty(value = "上报人姓名")
    private String applyUser;


    @ExcelVOAttribute(name = "联系电话",column = "E")
    @ApiModelProperty(value = "联系电话")
    private String applyUserPhone;


    @ExcelVOAttribute(name = "意见或建议归属系统",column = "F")
    @ApiModelProperty(value = "意见或建议归属系统")
    private String appName;


    @ExcelVOAttribute(name = "上报时间",column = "G")
    @ApiModelProperty(value = "上报时间")
    private String createdTime;


    @ExcelVOAttribute(name = "解决状态",column = "H")
    @ApiModelProperty(value = "解决状态")
    private String status;

    @ExcelVOAttribute(name = "意见建议描述",column = "I")
    @ApiModelProperty(value = "意见建议描述")
    private String ideaDes;

    @ExcelVOAttribute(name = "反馈意见",column = "J")
    @ApiModelProperty(value = "反馈意见")
    private String feedBackIdea;

}
