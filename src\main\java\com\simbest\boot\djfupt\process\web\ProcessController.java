package com.simbest.boot.djfupt.process.web;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <strong>Title :ProcessController </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/21</strong><br>
 * <strong>Modify on : 2022/6/21</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

@RequestMapping(value = {"/action/process"})
@RestController
@Slf4j
public class ProcessController {


}
