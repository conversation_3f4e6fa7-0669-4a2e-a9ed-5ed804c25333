<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>党建指导员管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/usAdminManager/findAllAdmin?type=0&roleUserId=" + encodeURI('djfupt_002'), //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "所在单位", field: "belongCom", width: 80, tooltip: true, align: "center" },
                            { title: "所在部门", field: "belongDept", width: 100, tooltip: true, align: "center" },
                            { title: "网格名称", field: "gridName", width: 80, tooltip: true, align: "center" },
                            { title: "党建指导员", field: "trueName", width: 60, tooltip: true, align: "center" },
                            { title: "OA账号", field: "userName", width: 80, tooltip: true, align: "center" },
                            { title: "手机号", field: "phone", width: 80, tooltip: true, align: "center" },
                            { title: "最后修改时间", field: "modifiedTime", width: 90, tooltip: true, align: "center",
                                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    return getTimeDate(row.modifiedTime, "yyyy-MM-dd hh:mm:ss");
                                }
                            },
                            { field: "opt", title: "操作", width: 80, rowspan: 1, align: "center", formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引    
                                    var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    g += "<a href='#' class='delete'  data-id='" + row.id + "'>【删除】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#glyTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/usAdminManager/insert",//新增命令
                    "updatacmd": "action/usAdminManager/updateInfo",//修改命令
                    "beforerender": function () {
                    },
                    "onSubmit": function (data) {
                        data.roleUserId = 'djfupt_002'
                        return !flag;
                    },
                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#glyTableReadForm"
                }
            };
            loadGrid(pageparam);

            $('#glyTableQueryForm').resize()
        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
            $('#glyTableQueryForm').resize()
        };

        // 防止数据还没回来用户就再次点击
        var flag = false
        //选择人员
        function chooseuser() {
            if (flag) return
            if (top.chooseWeb && top.chooseWeb.chooseUsersVal) {
                top.chooseWeb.chooseUsersVal = {};
            }
            // top.dialogP("html/choose/chooseUsersEU.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB", false, 900, 500)
            top.dialogP("html/choose/chooseUsersQuery.html?multi=0&name=chooseUsersVal", window.name, "选择人员", "chooseUserCB", false, 900, 500)
        }
        window.chooseUserCB = function (data) {
            flag = true
            ajaxgeneral({
                url: "action/usAdminManager/queryUserOrgInfo?userName=" + data.data[0].userName,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    flag = false
                    var data = res.data
                    if (data.belongCompanyTypeDictValue == "03") {
                        $('#glyTableAddForm #belongCom').val(data.belongCompanyNameParent);
                        $('#glyTableAddForm #belongComCode').val(data.belongCompanyCodeParent);
                        $('#glyTableAddForm #belongDept').val(data.belongCompanyName);
                        $('#glyTableAddForm #belongDeptCode').val(data.belongCompanyCode);

                    }
                    if (data.belongCompanyTypeDictValue == "02" || data.belongCompanyTypeDictValue == "01") {
                        $('#glyTableAddForm #belongDept').val(data.belongDepartmentName);
                        $('#glyTableAddForm #belongDeptCode').val(data.belongDepartmentCode);
                        $('#glyTableAddForm #belongCom').val(data.belongCompanyName);
                        $('#glyTableAddForm #belongComCode').val(data.belongCompanyCode);
                    }
                    $('#glyTableAddForm #userName').val(data.username);
                    $('#glyTableAddForm #trueName').val(data.truename);
                    $('#glyTableAddForm #type').val(0);
                },
                error: function (res) {
                    flag = false
                }
            });
        }

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {

        };

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('data-id');
            $.messager.confirm('确认', '您确认想要删除吗？', function (r) {
                if (r) {
                    ajaxgeneral({
                        url: 'action/usAdminManager/delInfo?id=' + id,
                        data: {},
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $("#glyTable").datagrid("reload");
                        }
                    });
                }
            });
        })


        //下载模板
        function downloadFile() {
            window.open(web.rootdir + "action/usAdminManager/exportPartyBuilding");
        }
        //导入excel
        function uploadExcel() {
            $('#uploadFiles').trigger('click');
        }
        //导入excel后
        function OtherInfo(data) {
            $('#glyTable').datagrid('load');
        }

        //导出
        $(document).on('click', 'a.exporttable', function () {
            $("#glyTableQueryForm").attr("action", web.rootdir + "action/usAdminManager/exportExcel?type=0");
            $("#glyTableQueryForm").attr("method", "post");
            $("#glyTableQueryForm").submit();
        });
    </script>

    <style>
        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }
        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }
        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text { border: none; font-size: 13px; }
        .formTable td.lable { background-color: #ddf1fe; padding: 5px; text-align: left; max-width: 100px; }
        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly { background-color: #fff; }
        input:read-only { background-color: #fff; }
        textarea { line-height: 20px; letter-spacing: 1px; }
        .queSave { display: none; }
        .uploadImageI{
            padding-top: 32px;
            margin-right: 100px;
        }
        a.btn{width: 80px;}
        .cselectorImageUL{width: 85px;}
        .cselectorImageUL .btn{
            right: 0px;
        }
        .cselectorImageUL input{
            right: 5px;
        }
        .dialog-button {
            text-align: center;
        }
        .dialog-button .l-btn {
            margin-left: 30px;
        }
        .cselectorImageUL {
            display: inline-block;
        }
    </style>
</head>

<body class="body_page"> 
    <form id="glyTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="60"></td>
                <td width="150"></td>
                <td width="60"></td>
                <td width="250"></td>
                <td width="70"></td>
                <td width="230"></td>
                <td width="50"></td>
                <td width="230"></td>
            </tr>
             <tr>
                <td align="right">所在单位</td>
                <td>
                    <input name="belongComCode" class="easyui-combobox" style="width: 100% !important; height: 32px;"
                    data-options="
                    valueField: 'ORG_CODE',
                    panelHeight:'200px',
                    ischooseall:true,
                    textField: 'ORG_NAME',
                    editable:false,
                    url: 'action/commom/queryOrg'" />
                </td>
                <td align="right">网格名称</td>
                <td><input id="gridName" name="gridName" type="text" /></td>
                 <td align="right">管理员姓名</td>
                 <td><input id="trueName" name="trueName" type="text" /></td>
                 <td align="right">OA账号</td>
                 <td><input id="userName" name="userName" type="text" /></td>
            </tr>
            <tr>
                <td colspan="8" align="right">



                    <a class="btn fr ml10 searchtable"><span>查询</span></a>
                    <a class="btn fr ml10 showDialog "><span>添加</span></a>
                    <input id="uploadFiles" name="uploadFiles" type="text" file="true" mulaccept="true" class="cselectorImageUpload fr" btnmsg="<span class='iconfont' title='添加' style='font-size:14px'>导入</span>" href="action/usAdminManager/importInfoPartyBuilding" OtherInfo="OtherInfo" />
                    <a class="btn fr ml10" onclick="downloadFile()"><span>模板下载</span></a>
                    <a class="btn fr ml10 exporttable "> <span>导出</span></a>

                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="glyTable">
        <table id="glyTable"></table>
    </div>
    <!--新增修改的dialog页面-->
    <div id="buttons" title="新增或修改" class="easyui-dialog" data-options="closed:true" style="width:700px;height:320px;margin-top: 15px">
        <form id="glyTableAddForm" method="post" contentType="application/json; charset=utf-8"
            beforerender="beforerender" onSubmit="onSubmit()">
            <input id="id" name="id" type="hidden" />
            <input id="belongComCode" name="belongComCode" type="hidden" />
            <input id="belongDeptCode" name="belongDeptCode" type="hidden" />
            <input id="type" name="type" type="hidden" />
            <input id="roleUserid" name="roleUserid" value="djfupt_002" type="hidden" />
            <table border="0" cellpadding="0" cellspacing="10" class="formTable">
                <tr style="height: 35px">
                    <td width="80" align="right" class="lable"> 姓名<font class="col_r">*</font> </td>
                    <td width="200" style="padding-right:7px">
                        <input id="trueName" name="trueName" type="text" class="easyui-validatebox" readonly required='required' style="width: 80%"/>
                        <a class="btn ml10 fr" title="人员查询" onclick="chooseuser()" style="width: 50px; padding-right:7px"><i class="iconfont">&#xe634;</i></a>
                    </td>
                </tr>
                <tr>
                    <td width="80" align="right" class="lable"> 所在单位<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="belongCom" name="belongCom" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
                <tr>
                    <td width="80" align="right" class="lable"> 所在部门<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="belongDept" name="belongDept" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
                <tr>
                    <td width="80" align="right" class="lable">网格名称</td>
                    <td width="200">
                        <input id="gridName" name="gridName" type="text" class="easyui-validatebox" validType="maxLength[20]" />
                    </td>
                </tr>
                <tr>
                    <td width="80" align="right" class="lable"> OA账号<font class="col_r">*</font> </td>
                    <td width="200">
                        <input id="userName" name="userName" type="text" class="easyui-validatebox" readonly required='required' />
                    </td>
                </tr>
            </table>
        </form>
    </div>
</body>

</html>