package com.simbest.boot.djfupt.assiatant.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.service.IUsAdminManagerService;
import com.simbest.boot.djfupt.assiatant.model.UsTestType;
import com.simbest.boot.djfupt.assiatant.service.IUsTestTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 思政纪实配置相关接口
 */
@Api(description = "智能助手问题类型")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/UsTestType")
public class UsTestTypeController extends LogicController<UsTestType, String> {

    private IUsTestTypeService service;

    @Autowired
    public UsTestTypeController(IUsTestTypeService service) {
        super(service);
        this.service = service;
    }
    @Autowired
    private IUsTestTypeService iUsTestTypeService;

    @ApiOperation(value = "发送", notes = "发送")
    @PostMapping(value = {"/send", "/api/send", "/send/sso","/anonymous/send"})
    public JsonResponse send(@RequestBody Map<String,Object> paramMap){
        return iUsTestTypeService.send(paramMap);
    }

    @ApiOperation(value = "分词测试", notes = "分词测试")
    @PostMapping(value = {"/test01", "/api/test01", "/test01/sso","/anonymous/test01"})
    public JsonResponse test01(@RequestParam String content){
        return iUsTestTypeService.test01(content);
    }

}
