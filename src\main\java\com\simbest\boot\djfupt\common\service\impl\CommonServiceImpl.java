package com.simbest.boot.djfupt.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cmcc.mss.importsrvresponse.ImportSrvResponse;
import com.cmcc.mss.sb_oa_oa_importtodocloselistinfosrv.SBOAOAImportToDoCloseListInfoSrvInputItem;
import com.cmcc.mss.sb_oa_oa_importtodoopenlistinfosrv.SBOAOAImportToDoOpenListInfoSrvInputItem;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.enums.ToDoEnum;
import com.simbest.boot.bps.exceptions.BpsWorkFlowBusinessException;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.operate.WorkItemManager;
import com.simbest.boot.bps.task.model.TaskCallbackLog;
import com.simbest.boot.bps.task.service.ITaskCallBackLogService;
import com.simbest.boot.cmcc.hq.clients.HqTodoRestClient;
import com.simbest.boot.cmcc.hq.model.*;
import com.simbest.boot.cmcc.hq.service.IUsernameAccountService;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.cmcc.ntodo.CloseTodoSrvClient;
import com.simbest.boot.cmcc.ntodo.OpenTodoSrvClient;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.repository.WfWorkItemRepository;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.common.repository.ActBusinessStatusRepository;
import com.simbest.boot.djfupt.common.repository.IQueryDictValueRepository;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.repository.UsPantchDetailRepository;
import com.simbest.boot.djfupt.policy.repository.UsPolicyInfoRepository;
import com.simbest.boot.djfupt.todo.model.UsTodoModel;
import com.simbest.boot.djfupt.todo.service.IUsTodoModelService;
import com.simbest.boot.djfupt.util.*;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.SpringContextUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2022/11/23 0023 10:45
 */
@Slf4j
@Service
@SuppressWarnings("ALL")
public class CommonServiceImpl implements ICommonService {


    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private AppConfig config;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;
    @Autowired
    private UumsSysAppApi uumsSysAppApi;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;
    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;


    @Autowired
    private OperateLogTool operateLogTool;


    @Autowired
    private IFileExtendService iFileExtendService;

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private UsCaseInfoRepository usCaseInfoRepository;

    @Autowired
    private ITaskCallBackLogService taskCallBackLogService;

    @Autowired
    private IQueryDictValueRepository iQueryDictValueRepository;

    @Autowired
    private ActBusinessStatusRepository actBusinessStatusRepository;

    @Autowired
    private IUsTodoModelService usTodoModelService;


    @Autowired
    private SMSTool smsTool;


    @Autowired
    private PaginationHelps paginationHelp;

    @Autowired
    private UsPolicyInfoRepository usPolicyInfoRepository;

    @Autowired
    private UsPantchDetailRepository pantchDetailRepository;

    @Autowired
    private WorkItemManager workItemManagers;


    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private BpsLoginUtil bpsLoginUtil;

    @Autowired
    private SpringContextUtil springContextUtil;

    String param1 = "/action/commom";

    private static final String todoHtml = "/html/change/changePolicy.html";


    private static final String USER_MAPPING = "/action/videoForm/";
    private static final String SSO = "/sso";

    @Value("${uni.todo.mobile.address}")
    private String mobileTodoAddress;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Autowired
    private IUsernameAccountService usernameAccountService;

    private ZoneId zone = ZoneId.of("Asia/Shanghai");

    @Autowired
    private HqTodoRestClient todoRestClient;





    /**
     * 获取决策项审批人员
     *
     * @param processInstId  流程实例id
     * @param source         来源
     * @param userCode       用户OA账户
     * @param sysAppDecision 决策对象
     * @return
     */
    @Override
    public JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision, String param1,String faultsAppName) {
        JsonResponse userList = new JsonResponse();

        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getOrgAndUser";
        String params = "processInstId=" + processInstId + ",source=" + source + ",userCode=" + userCode + ",SimpleAppDecision=" + sysAppDecision.toString();
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);

        try {
            /**判断来源记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            if ("djfupt.start.to.provincialManager_3qi".equals(sysAppDecision.getDecisionId())
                    && Constants.FIND_FAULTS_THREE.equals(sysAppDecision.getProcessDefId())
                    &&  Constants.ACTIVITY_START.equals(sysAppDecision.getActivityDefId())
               ) {
                //根据表单内容进行出人
                String groupId = "";
                if("政企运营管理系统管理大屏（电脑端）".equals(faultsAppName)){
                        groupId = Constants.GROUP_FAULTS002;
                } else {
                    groupId = Constants.GROUP_FAULTS003;

                }
                JsonResponse response = uumsSysUserinfoApi.findUserByGroupSort(groupId, Constants.APP_CODE);
                String zcAdmin = Optional.ofNullable(response).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
                userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, zcAdmin);
                return userList;
            }
            /**请起草人确认决策处理**/
            if (Constants.DEC_CONFIRM.equals(sysAppDecision.getDecisionId())) {
                if (StringUtils.isNotEmpty(processInstId)) {
                    //查询工单
                    ActBusinessStatus act = statusService.getByProcessInst(Long.parseLong(processInstId));
                    if (act != null) {
                        String userName = act.getCreateUserCode();
                        userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                    }
                }
            } else {
                /**根据根据flowType处理查询组织人员**/
                IUser currentUser = SecurityUtils.getCurrentUser();
                String defaultValue = sysAppDecision.getDecisionConfig();
                if (defaultValue != null) {
                    String newDefault = JacksonUtils.unescapeString(defaultValue).replace(("\'"), "\"");
                    List<HashMap<String, String>> mapLists = JacksonUtils.json2Type(newDefault, new TypeReference<List<HashMap<String, String>>>() {
                    });
                    if (mapLists != null && mapLists.size() > 0) {
                        Map<String, String> hashMap = mapLists.get(0);
                        String isNextStep = null;// hashMap.get("typeValue");
                        String nextStepValue = null;// hashMap.get("includeValue");
                        String filterValue = null;// hashMap.get("includeValue");
                        String groupType = null;// hashMap.get("includeValue");
                        for (Map<String, String> map : mapLists) {
                            String type = map.get("type");
                            if ("flowType".equals(type)) {
                                isNextStep = map.get("typeValue");
                                nextStepValue = map.get("includeValue");
                            }
                            if ("filterUserType".equals(type)) {
                                filterValue = map.get("typeValue");
                            }
                            if ("pageType".equals(type)) {
                                groupType = map.get("groupType");
                            }
                        }

                        if ("normalStep".equals(isNextStep)) {
                            Map<String, String> map = new HashMap<>();
                            map.put("appCode", Constants.APP_CODE);
                            map.put("processDefId", sysAppDecision.getProcessDefId());
                            map.put("activityDefId", sysAppDecision.getActivityDefId());
                            map.put("decisionId", sysAppDecision.getDecisionId());
                            map.put("groupId", sysAppDecision.getGroupId());
                            map.put("decisionConfig", sysAppDecision.getDecisionConfig());
                            // 如果有过滤规则 orgRule
                            if (("orgRule".equals(filterValue))) {
                                // 获取当前登录人
                                // 如果当前登录人所属公司为省公司或分公司,orgCode传递departmentCode
                                if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {
                                    map.put("orgCode", currentUser.getBelongDepartmentCode());
                                }
                                // 如果当前登录人所属公司为县公司，orgCode传递companyCode
                                else {
                                    map.put("orgCode", currentUser.getBelongCompanyCode());
                                }
                            }
                            //如果有过滤规则 companyCodeRule
                            if (("companyRule".equals(filterValue))) {
                                // 获取当前登录人
                                // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                                if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {

                                    map.put("orgCode", currentUser.getBelongCompanyCode());
                                }
                                // 如果当前登录人所属公司为县公司，orgCode传递companyCodeParent
                                else {
                                    map.put("orgCode", currentUser.getBelongCompanyCodeParent());
                                }
                            }
                            if (("departmentRule".equals(filterValue))) {
                                // 获取当前登录人
                                // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                                if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH)) {

                                    map.put("orgCode", currentUser.getBelongDepartmentCode());
                                }
                                // 如果当前登录人所属公司为县公司，orgCode传递companyCodeParent
                                else {
                                    map.put("orgCode", currentUser.getBelongDepartmentCode());
                                }
                            }
                            // 如果有过滤规则 companyCodeRule
                            if (!StringUtils.isEmpty(filterValue) && ("companyRule".equals(filterValue))) {
                                // 获取当前登录人

                                // 如果当前登录人所属公司为省公司或分公司,orgCode传递companyCode
                                if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_BRANCH) || currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {
                                    map.put("orgCode", currentUser.getBelongCompanyCode());
                                }
                                // 如果当前登录人所属公司为县公司，orgCode传递上级公司组织code
                                else {
                                    SimpleOrg simpleOrg = uumsSysOrgApi.findParentBySon(Constants.APP_CODE, currentUser.getBelongCompanyCode());
                                    map.put("orgCode", simpleOrg.getOrgCode());
                                }
                            }
                            userList = uumsSysUserinfoApi.findUserByDecisionNoPage(Constants.APP_CODE, map);

                        }


                        if ("previousStep".equals(isNextStep) && !StringUtils.isEmpty(nextStepValue)) { //退回上一步处理,获取上一步审批人
                            if (processInstId != null) {
                                WfWorkItemModel wfWorkItemModel = (WfWorkItemModel) workItemManager.getByProInstIdAAndAInstId(Long.parseLong(processInstId), nextStepValue);
                                if (wfWorkItemModel != null) {
                                    String userName = wfWorkItemModel.getParticipant();
                                    userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                                }
                            }
                        }



                    }
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return userList;
    }


    /**
     * 获取决策项
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @param processType    流程类型
     * @return
     */
    @Override
    public JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String processType, String param1, String questionMode) {
        /**处理操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getDecisions";
        String params = "processInstId=" + processInstId + ",processDefName=" + processDefName + ",location=" + location + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        List<SimpleAppDecision> decisions = new ArrayList<>();
        if (null != processDefName && processDefName.equals("undefined")) {
            processDefName = null;
        }
        try {

            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            /**开始阶段没有流程定义id**/
            if (Constants.ACTIVITY_START.equals(location) && StringUtils.isEmpty(processDefName)) {
                Map<String, String> map = this.getProcessMap(processType);
                processDefName = map.get("processName");
                if (StringUtils.isEmpty(processDefName)) {
                    return JsonResponse.fail(null, "获取流程失败");
                }
            }
            if (StringUtils.isNotEmpty(processInstId)) {
                if ("A".equals(processType)) {
                    List<WfWorkItemModel> wfWorkItem = wfWorkItemRepository.findWfWorkItem(processInstId);
                    if (CollectionUtil.isNotEmpty(wfWorkItem)) {
                        location = wfWorkItem.get(0).getActivityDefId();
                    }
                }
            }
            if (StringUtils.isEmpty(processDefName)) {
                Map<String, String> map = this.getProcessMap(processType);
                processDefName = map.get("processName");
                if (StringUtils.isEmpty(processDefName)) {
                    return JsonResponse.fail(null, "获取流程失败");
                }
            }
            /**当前环节下所有决策**/
            Map<String, String> map = new HashMap<>();
            map.put("appCode", Constants.APP_CODE);
            map.put("processDefId", processDefName);
            map.put("activityDefId", location);
            decisions = uumsSysAppDecisionApi.findDecisions(Constants.APP_CODE, map);
            //查询工单
            // ActBusinessStatus act = actBusinessStatusService.getByProcessInst(Long.parseLong(processInstId));
            // 获取起草人所在部门
            // String createOrgName = act.getCreateOrgName();
            /**过滤决策**/

            if (Constants.ACTIVITY_START.equals(location)
                    && StringUtils.isEmpty(processInstId)
                    && processDefName.equals(Constants.FIND_FAULTS_THREE)) {
                for (int i = 0; i < decisions.size(); i++) {
                    if ("end2".equals(decisions.get(i).getDecisionId()) || "end".equals(decisions.get(i).getDecisionId())) {
                        decisions.remove(i);
                    }
                }
            }


            if (processType.equals(Constants.PM_INS_TYPE_B) && location.equals("djfupt.start")) {
                List<SimpleAppDecision> decisionList = new ArrayList<>();
                if (questionMode.equals("1")) {
                    for (SimpleAppDecision decision : decisions) {
                        if (decision.getId().equals("djfuptProblem02")) {
                            decisionList.add(decision);
                        }
                    }

                } else {
                    for (SimpleAppDecision decision : decisions) {
                        if (!decision.getId().equals("djfuptProblem02")) {
                            decisionList.add(decision);
                        }
                    }
                }
                decisions = decisionList;

            }

            if (processType.equals(Constants.PM_INS_TYPE_A) && location.equals("djfupt.start")) {
                List<SimpleAppDecision> decisionList = new ArrayList<>();
                if (questionMode.equals("1")) {
                    for (SimpleAppDecision decision : decisions) {
                        if (decision.getId().equals("djfuptCase02")) {
                            decisionList.add(decision);
                        }
                    }

                } else {
                    for (SimpleAppDecision decision : decisions) {
                        if (!decision.getId().equals("djfuptCase02")) {
                            decisionList.add(decision);
                        }
                    }
                }
                decisions = decisionList;

            }
            if (processType.equals(Constants.PM_INS_TYPE_B)) {
                List<SimpleAppDecision> decisionList = new ArrayList<>();
                if (decisions.size() > 0) {
                    for (SimpleAppDecision decision : decisions) {
                        //不返回自动退回这个决策项  在定时任务里定时推送
                        if (!(decision.getDecisionId().equals(Constants.OVERTIME_RETURN) || decision.getDecisionId().equals(Constants.OVERTIME_APPROVAL_PASS))) {
                            decisionList.add(decision);
                        }
                    }
                }
                decisions = decisionList;
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(decisions);
    }

    /**
     * 获取使用的流程名称
     *
     * @return
     */
    public Map<String, String> getProcessMap(String type) {
        Map<String, String> map = Maps.newHashMap();
        String processType = null;
        String processName = null;
        switch (type) {
            case Constants.PM_INS_TYPE_A:
                processType = "A";
                processName = Constants.CASE_REPORT;
                break;
            case Constants.PM_INS_TYPE_B:
                processType = "B";
                processName = Constants.PROBLEM_REPOR;
                break;
            case Constants.PM_INS_TYPE_C:
                processType = "C";
                processName = Constants.POLICY_PROPAGANDA;
                break;
            case Constants.PM_INS_TYPE_D:
                processType = "D";
                processName = Constants.FIND_FAULTS;
            case Constants.PM_INS_TYPE_E:
                processType = "E";
                processName = Constants.FIND_FAULTS_THREE;
                break;
            default:
                break;
        }
        map.put("processName", processName);
        map.put("processType", processType);
        return map;
    }

    public JsonResponse queryFile(String pmInsId) {
        List<SysFile> sysFiles = iFileExtendService.queryFile(pmInsId);
        return JsonResponse.success(sysFiles);

    }


    public JsonResponse queryOrg() {
        Map<String, String> sql1 = new HashMap<>();

        List<Map<String, Object>> query = this.uumsSelectBySql(sql1, "djfupt_002");
        return JsonResponse.success(query);

    }



    public List<Map<String, Object>> uumsSelectBySql(Map<String, String> map,String sqlId) {
        IUser user = SecurityUtils.getCurrentUser();//获取当前人员
        JsonResponse jsonResponse = com.mzlion.easyokhttp.HttpClient.textBody(config.getUumsAddress() + "/action/uumsSelect/selectBySqlSelectType/sso" + "?loginuser=" + encryptor.encrypt(user.getUsername()) +
                "&appcode=" + Constants.APP_CODE + "&appCode=" + Constants.APP_CODE + "&sqlId="+sqlId+"&selectType=replacetype")
                .json(JSONUtil.toJsonStr(map)).asBean(JsonResponse.class);
        List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
        return data;
    }

    /**
     * 待办短信触发
     *
     * @param businessStatus
     * @param sendUser
     * @return
     */
    @Override
    public JsonResponse sendCarShortMessage(ActBusinessStatus businessStatus, String sendUser) {
        try {
            LocalDateTime callbackStartDate = LocalDateTime.now();
            Boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, sendUser);
            if (simpleApp != null) {
                isPostMsg = simpleApp.getIsSendMsg();
            }
            Boolean isPostMsgOK = false;
            /**短信相关操作**/
            if (isPostMsg && StringUtils.isNotEmpty(businessStatus.getPreviousAssistant())) {
                /**准备发送短息**/
                if (StringUtils.isNotEmpty(sendUser)) {
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", businessStatus.getCreateUserName());
                    paramMap.put("itemSubject", businessStatus.getReceiptTitle());
                    String msg = MessageEnum.MT000001.getMessage(paramMap);
                    isPostMsgOK = msgPostOperatorService.postMsg(this.readyParams(sendUser, msg));
                }
            }
            /**记录发送短信日志操作**/
            if (!isPostMsgOK) {
                TaskCallbackLog taskCallbackLog = new TaskCallbackLog();
                taskCallbackLog.setActBusinessStatusId(businessStatus.getId());
                taskCallbackLog.setCallbackType("MSG");
                taskCallbackLog.setCallbackStartDate(callbackStartDate);
                taskCallbackLog.setCallbackEndDate(LocalDateTime.now());
                Duration duration = Duration.between(taskCallbackLog.getCallbackEndDate(), callbackStartDate);
                taskCallbackLog.setCallbackDuration(duration.toMillis());
                taskCallbackLog.setCallbackResult(isPostMsgOK);
                taskCallbackLog.setCallbackError(String.valueOf(isPostMsgOK));
                log.debug("TodoOpenServiceImpl execution MSG Fialure>>>>>taskCallBackLogService>>>>" + taskCallBackLogService);
                taskCallbackLog = taskCallBackLogService.insert(taskCallbackLog);
                log.debug("TodoOpenServiceImpl execution MSG Fialure>>>>" + taskCallbackLog.toString());
            }
        } catch (Exception e) {
            log.error("TodoOpenServiceImpl execution Error>>>>>>>" + e.getMessage());
            Exceptions.printException(e);
        }
        return null;
    }


    /**
     * 准备短信对象
     *
     * @param sendUser 发送人
     * @param msg      短信内容
     * @return
     */
    private static ShrotMsg readyParams(String sendUser, String msg) {
        ShrotMsg shrotMsg = new ShrotMsg();
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode(Constants.APP_CODE);
        content.setUsername(sendUser);
        content.setMsgContent(msg);
        content.setImmediately(true);
        content.setSmsPriority(1);
        contentSet.add(content);
        shrotMsg.setContents(contentSet);
        return shrotMsg;
    }


    /**
     * 短信催办获取流程
     *
     * @return
     */
    public JsonResponse queryProcessType() {
        List<SysDictValue> sysDictValues = iQueryDictValueRepository.findAllByDictTypeProcessType();
        return JsonResponse.success(sysDictValues);
    }


    /**
     * 查询自己发起的未办工单
     *
     * @param pageIndex   页码
     * @param pageSize    数量
     * @param title       标题
     * @param source      是否是从手机端调用
     * @param userCode    OA账号
     * @param processType 流程类型
     * @return
     */
    @Override
    public JsonResponse queryMySelfCreate(Integer pageIndex, Integer pageSize, String title, String source, String userCode, String processType, String partiName) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> allTodoPage = null;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMySelfCreate";
        String params = "pageIndex=" + pageIndex.toString() + ",pageSize=" + pageSize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode + ",processType" + processType;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**根据条件查询**/
            paramMap.put("title", title);
            paramMap.put("createUser", SecurityUtils.getCurrentUserName());
            // todo 查询自己发起的待办
            if (StringUtils.isNotEmpty(processType)) {
                paramMap.put("pmInsType", processType);
            } else {
                paramMap.put("pmInsType", "A,B");
            }
            paramMap.put("partiName", partiName);
            Pageable pageable = paginationHelp.getPageable(pageIndex, pageSize, null, null);
            allTodoPage = getMyCreateAllTodoPage(paramMap, pageable);
        } catch (Exception e) {
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(allTodoPage);
    }


    public Page<Map<String, Object>> getMyCreateAllTodoPage(Map<?, ?> todoParam, Pageable pageable) {
        Page<Map<String, Object>> pages = null;
        String dynamicWhere = (String) todoParam.get("title");
        String pmInsType = (String) todoParam.get("pmInsType");
        String createUser = (String) todoParam.get("createUser");
        String partiName = (String) todoParam.get("partiName");
        new StringBuilder();

        try {
            if (StringUtils.isEmpty(dynamicWhere)) {
                dynamicWhere = "";
            }

            List<String> inWheres = new ArrayList();
            if (StringUtils.isNotEmpty(pmInsType)) {
                inWheres.addAll(Arrays.asList(pmInsType.split(",")));
            }

            pages = actBusinessStatusRepository.getMyCreateAllTodoPage(dynamicWhere, inWheres, createUser, partiName, pageable);
        } catch (Exception var9) {
            BpsWorkFlowBusinessException.printException(var9);
        }

        return pages;
    }


    /**
     * 待办短信催办
     *
     * @param list 待办列表
     * @return
     */
    @Override
    public JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list) {
        Boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
        Boolean isOk = false;//记录是否发送成功
        int faCount = 0;//失败记录
        String strName = "";
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/sendShortMessage";
        String params = "source=" + source + ",currentUserCode" + currentUserCode + ",list" + list.toString();
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**设置当前要发送短信的人**/
            IUser user = SecurityUtils.getCurrentUser();
            String fromUserCode = user.getUsername();
            String fromUserName = user.getTruename();
            /**查询发送短信开关是否开始**/
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, fromUserCode);
            if (simpleApp != null) {
                isPostMsg = simpleApp.getIsSendMsg();
            }
            /**如果开关开启且待发列表不为空则发短信**/
            if (isPostMsg && !list.isEmpty()) {
                /**准备发送的参数**/
                for (Map<String, Object> map : list) {
                    Map<String, Object> mapParam = Maps.newHashMap();
                    String sendUser = map.get("PARTICIPANT") != null ? map.get("PARTICIPANT").toString() : "";
                    String sendName = map.get("PARTI_NAME") != null ? map.get("PARTI_NAME").toString() : "";
                    String itemSubject = map.get("RECEIPT_TITLE") != null ? map.get("RECEIPT_TITLE").toString() : "";
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("fromUser", fromUserName);
                    mapParam.put("itemSubject", itemSubject);
                    isOk = smsTool.sendSMS(mapParam);
                    /**发送失败记录**/
                    if (!isOk) {
                        faCount++;
                        strName = sendName + ",";
                    }
                }
                strName = strName + "等" + faCount + "人发送失败";
            } else {
                strName = "短信开关未开启";
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(isOk, StringUtils.isEmpty(strName) ? "发送成功" : strName);
    }

    /**
     * 根据当前管理员不同，可以在查询时选择不同的组织树
     *
     * @return
     */
    @Override
    public JsonResponse queryOrgTreeBySearch(String pmInsId) {
        IUser user = SecurityUtils.getCurrentUser();
        int flag = 2;

        if (Constants.PROVINCIAL_CODE.equals(user.getBelongCompanyTypeDictValue())) {
            flag = 1;

        }
//                if(StringUtils.isNotEmpty(pmInsId)&&pmInsId.equals(Constants.PM_INS_TYPE_A)){
//                    flag = 1;
//                }


        List<SimpleOrg> simpleOrgList = new ArrayList<>();
        if (flag == 1) {
            simpleOrgList = uumsSysOrgApi.findPOrgAndCityOrg(Constants.APP_CODE);
        }
        if (flag == 2) {
            simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
        }

        return JsonResponse.success(simpleOrgList, "获取选择用组织树成功！");
    }


    /**
     * 根据当前管理员不同，可以在查询时选择不同的组织树
     *
     * @return
     */
    @Override
    public JsonResponse queryOrgTreeBySearchTwo(String pmInsId) {
        JsonResponse response = (JsonResponse)HttpClient.post(this.config.getUumsAddress() + "/action/org/org/" + "findPOrgAndCityOrg" + "/sso").param("loginuser", this.encryptor.encrypt("hadmin")).param("appcode", "djfupt").param("appCode", "djfupt").asBean(JsonResponse.class);
        String json = JacksonUtils.obj2json(response.getData());
        List<SimpleOrg> orgList = (List)JacksonUtils.json2Type(json, new TypeReference<List<SimpleOrg>>() {
        });
        return JsonResponse.success(orgList, "获取选择用组织树成功！");
    }


    @Override
    public void update(UsAdminManager usAdminManagerOld, UsAdminManager usAdminManager) {

        //在查询wordModel状态是10的并核销统一待办并重新推送
        List<WfWorkItemModel> workItemModels = wfWorkItemRepository.findAllByCreatorAndParticipant(usAdminManagerOld.getUserName());
        if (workItemModels.size() > 0) {
            for (WfWorkItemModel workItemModel : workItemModels) {
                //核销统一待办
                cancelToDo(workItemModel);
                //先查询主数据表
                List<UsPolicyInfo> policyInfos = usPolicyInfoRepository.findAllByCreator(usAdminManagerOld.getUserName());
                //替换主数据的创建人
                if (policyInfos.size() > 0) {
                    for (UsPolicyInfo policyInfo : policyInfos) {
                        usPolicyInfoRepository.updateCreatorById(usAdminManager.getUserName(), policyInfo.getId());
                    }
                }
                //修改宣讲内同
                pantchDetailRepository.updateCaterByPmInsID(workItemModel.getReceiptCode(), usAdminManagerOld.getCreator(), usAdminManager.getUserName());

                //替换workModel办理人
                wfWorkItemRepository.updateParticpantAndPartNamebyId(usAdminManager.getUserName(), usAdminManager.getTrueName(), workItemModel.getId());
                WfWorkItemModel wfWorkItemModel = wfWorkItemRepository.findByWorkItemId(StrUtil.toString(workItemModel.getWorkItemId()));
                //转派bps
//                bpsLoginUtil.bpsLogin(usAdminManager.getUserName());
//                List<Object> list=new ArrayList<>();
//                list.add(usAdminManager.getUserName());
//                workItemManagers.reassignWorkItem(workItemModel.getWorkItemId(),list);
                //推送新的统一待办
                workItemModel.setParticipant(usAdminManager.getUserName());
                workItemModel.setPartiName(usAdminManager.getTrueName());
                sendToDo(workItemModel, usAdminManager);
            }
        }
    }


    @Override
    public JsonResponse visido() {
        String username = SecurityUtils.getCurrentUserName();
        String url ="";
        String profiles = springContextUtil.getActiveProfile() ;
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************:8088";
        } else {
            url = "http://************:8088";
        }

        String requestUrl = url + "/fdzl"+USER_MAPPING + "findAllVideoSso" + SSO;
        Map<String, Object> downloadParamMap = Dict.create()
                .set("isShow", 1)
                .set("belongModule", "智慧党建");
        String downloadInfojson = JacksonUtils.obj2json(downloadParamMap);
        requestUrl = requestUrl.concat("?appcode=").concat(Constants.APP_CODE).concat("&loginuser=").concat(encryptor.encrypt("hadmin"));
        JsonResponse jsonResponse = HttpClient.textBody(requestUrl)
                .json(downloadInfojson)
                .asBean(JsonResponse.class);



        if (jsonResponse == null) {
            log.debug("--response对象为空!--");
            return null;
        } else {
            List<Map<String,Object>> map= (List<Map<String, Object>>) jsonResponse.getData();
            return jsonResponse;
        }
    }


    /**
     * 核销统一待办
     *
     * @param businessStatus
     * @param userName
     */
    public void cancelToDo(WfWorkItemModel wfWorkItemModel) {
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办开始");
        Boolean sendFalg = false;
        log.warn("businessStatus>>>>>>>>【{}】",JacksonUtils.obj2json(wfWorkItemModel));
        try {
            ActBusinessStatus actBusinessStatus = actBusinessStatusRepository.findByOldPmInsIdOne(wfWorkItemModel.getReceiptCode());
            //pc办理页地址
            String oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml;
            //手机办公办理页地址
            String mobileUrl = mobileTodoAddress + "/hamoa/djfupt/#/mainview" ;
            //代办回调路径后面带的参数，即url ?后面的数据
            String urlParams = "?type=join&location="
                    + wfWorkItemModel.getActivityDefId()
                    + "&processInstId=" + wfWorkItemModel.getProcessInstId()
                    + "&pmInsId=" + actBusinessStatus.getReceiptCode()
                    + "&processDefName=" + wfWorkItemModel.getProcessDefName()
                    + "&workItemId=" + wfWorkItemModel.getWorkItemId()
                    + "&name=auditVal&appcode=djfupt&from=oa"
                    + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=2";
            urlParams = urlParams + "&showFlag=true";
            Set<String> userSet = Sets.newHashSet();
            userSet.add(SecurityUtils.getCurrentUserName());
            Map<String, HqUserInfo> userInfoMap = this.findByUsernames(userSet);
            HqUserInfo userInfo = userInfoMap.get(SecurityUtils.getCurrentUserName());
            Assert.notNull(userInfo , "获取办理人信息失败！");
            SimpleHqTodo hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .appName(Constants.APP_NAME)
                    .itemUrl(oaHtmlUrl + urlParams)
                    .itemMOAUrl(mobileUrl + urlParams)
                    .itemId(String.valueOf(wfWorkItemModel.getWorkItemId()))
                    .itemType("2")//0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .receiverUserId(userInfo.getUserId())
                    .receiver(userInfo)
                    .build();
            JsonResponse jsonResponse = todoRestClient.updateStatusTodo(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                throw new Exception("推送统一待办待办失败！");
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用核销统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(wfWorkItemModel.getProcessInstName()).concat("】").concat("，待办人：【").concat(SecurityUtils.getCurrentUserName()).concat("】").concat("，调用接口平台核销统一代办异常");;
            smsTool.sendMsgUtil(msgContent);
        }
        closeTodoModel(wfWorkItemModel, wfWorkItemModel.getParticipant(), sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办结束");
    }

    @SuppressWarnings("unused")
    private UsTodoModel closeTodoModel(WfWorkItemModel wfWorkItemModel, String todoUser, Boolean sendFalg) {
        UsTodoModel usTodoModel = new UsTodoModel();
        try {

            //UsPolicyInfo usPolicyInfo = usPolicyInfoRepository.findUsPolicyInfoInfo(wfWorkItemModel.getReceiptCode(), wfWorkItemModel.getParticipant());
            ActBusinessStatus actBusinessStatus = actBusinessStatusRepository.findByOldPmInsIdOne(wfWorkItemModel.getReceiptCode());
            SBOAOAImportToDoCloseListInfoSrvInputItem inputItem = new SBOAOAImportToDoCloseListInfoSrvInputItem();
            usTodoModel.setBusinessKey(actBusinessStatus.getId());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(StrUtil.toString(wfWorkItemModel.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)) {
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>close>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    /**
     * 推送统一待办
     *
     * @param businessStatus
     * @param userName
     */


    public void sendToDo(WfWorkItemModel wfWorkItemModel, UsAdminManager adminManager) {
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办开始");
        String oaHtmlUrl = null;
        String urlParams = null;
        String mobileUrl = null;
        Boolean sendFalg = false;
        log.debug( "businessStatus>>>>>>>> {}" , JacksonUtils.obj2json(wfWorkItemModel));
        ActBusinessStatus businessStatus = actBusinessStatusRepository.findByOldPmInsIdOne(wfWorkItemModel.getReceiptCode());
        try {
            //截取到数字，代表流程类型
            String pmInsType = businessStatus.getReceiptCode().substring(0,1);
            //pc办理页地址
            oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml;
            //手机办公办理页地址
            mobileUrl = mobileTodoAddress + "/hamoa/djfupt/#/mainview" ;
            //pc路径后面带的参数，即url ?后面的数据
            urlParams = "?type=task&location="
                    + wfWorkItemModel.getActivityDefId()
                    + "&processInstId=" + wfWorkItemModel.getProcessInstId()
                    + "&pmInsId=" + businessStatus.getReceiptCode()
                    + "&processDefName=" + wfWorkItemModel.getProcessDefName()
                    + "&workItemId=" + wfWorkItemModel.getWorkItemId()
                    + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=0";
            urlParams = urlParams + "&showFlag=true";

            SimpleHqTodo hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .itemCreateTime(System.currentTimeMillis())
                    .appName(Constants.APP_NAME)
                    .itemId(String.valueOf(wfWorkItemModel.getWorkItemId()))
                    .itemType("0") //0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .itemUrl(oaHtmlUrl + urlParams)
                    .itemMOAUrl(mobileUrl + urlParams)
                    .processInstanceId(String.valueOf(wfWorkItemModel.getProcessInstId()))
                    .build();
            //获取办理人信息
            Set<String> usernameSet = Sets.newHashSet();
            Map<String, String> userMap = Maps.newHashMap();
            //办理人
            usernameSet.add(adminManager.getUserName());
            //起草人
            usernameSet.add(businessStatus.getCreateUserCode());
            //上一步办理人
            WfWorkItemModel workItem = (WfWorkItemModel) workItemManagers.getWorkItemByWorkItemId(businessStatus.getWorkItemId());
            String activityDefId = "approval";
            String activityDefName = "业务审批";
            if (null != workItem) {
                usernameSet.add(workItem.getCreator());
                activityDefId = workItem.getActivityDefId();
                activityDefName = workItem.getActivityInstName();
            }
            Map<String, HqUserInfo> userInfoMap = findByUsernames(usernameSet);
            Assert.notEmpty(userInfoMap , "查询人员信息失败！");
            HqUserInfo receiver = userInfoMap.get(adminManager.getUserName());
            Assert.notNull(receiver , "当前办理人信息查询失败！");
            hqTodo.setReceiver(receiver);
            hqTodo.setReceiverUserId(receiver.getUserId());

            HqUserInfo drafter = userInfoMap.get(businessStatus.getCreateUserCode());
            Assert.notNull(drafter , "获取起草人失败！");

            Boolean isStart = StringUtils.isNotEmpty(businessStatus.getPreviousAssistant()) ;

            //获取流程实例信息
            ProcessDef processDef = ProcessDef.builder()
                    .activityId(activityDefId)
                    .activityName(activityDefName)
                    .processTypeId(businessStatus.getProcessDefName())
                    .processTypeName(Constants.APP_NAME)
                    .definitionId(String.valueOf(businessStatus.getProcessInstId()))
                    .definitionName(Constants.APP_NAME)
                    .instanceStartTime(businessStatus.getCreateTime().atZone(zone).toInstant().toEpochMilli())
                    .instanceCreateUser(drafter.getUserId())
                    .startFlag( isStart ? "false" : "true")
                    .endFlag("false")
                    .build();
            hqTodo.setProcessDef(processDef);

            //封装上一办理人信息
            HqUserInfo lastHandleuser = null;
            if (null != workItem ) {
                lastHandleuser = userInfoMap.get(workItem.getCreator());
            }
            if (null == lastHandleuser) {
                lastHandleuser = HqUserInfo.builder().userId("-").userName("-").idType("0").build();
            }
            hqTodo.setLastHandler(Collections.singletonList(lastHandleuser));

            //封装文种信息
            DocInfo docInfo = DocInfo.builder()
                    .docInsId(String.valueOf(businessStatus.getWorkItemId()))
                    .docState("1")
                    .itemTitle(businessStatus.getReceiptTitle())
                    .createDept(businessStatus.getCreateOrgName())
                    .docCreateTime(System.currentTimeMillis())
                    .docTypeId(Constants.APP_ID)
                    .docTypeName(Constants.APP_NAME)
                    .isMyDoc(StrUtil.equals(adminManager.getUserName(), businessStatus.getCreateUserCode()) ? "Y" : "N")
                    .drafter(drafter)
                    .build();
            hqTodo.setDocInfo(docInfo);
            log.warn("推送统一待办数据：{}" , JacksonUtils.obj2json(hqTodo));
            JsonResponse jsonResponse = todoRestClient.pushHqTodoSimple(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                throw new Exception("推送统一待办待办失败！");
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用推送统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(adminManager.getUserName()).concat("】").concat("，调用接口平台推送统一代办异常");
            smsTool.sendMsgUtil(msgContent);
        }
        saveTodoModel(wfWorkItemModel, oaHtmlUrl, urlParams, sendFalg, Constants.APP_SYS_ID, Constants.APP_NAME, adminManager);
        log.warn("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办结束");
    }

    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModel(WfWorkItemModel wfWorkItemModel, String oaHtmlUrl, String urlParams, Boolean sendFalg, String sysId, String sysName, UsAdminManager adminManager) {
        UsTodoModel usTodoModel = new UsTodoModel();
        UsPolicyInfo usPolicyInfo = usPolicyInfoRepository.findUsPolicyInfoInfo(wfWorkItemModel.getReceiptCode(), adminManager.getUserName());
        ActBusinessStatus businessStatus = actBusinessStatusRepository.findByOldPmInsIdOne(wfWorkItemModel.getReceiptCode());
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(StrUtil.toString(wfWorkItemModel.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(StrUtil.toString(wfWorkItemModel.getWorkItemId()));
                usTodoModel.setActivityDefId(wfWorkItemModel.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(wfWorkItemModel.getProcessInstId()));
                usTodoModel.setBusinessKey(usPolicyInfo.getId());
                usTodoModel.setBusinessStatusId(usPolicyInfo.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(wfWorkItemModel.getProcessDefId()));
                usTodoModel.setCreator(wfWorkItemModel.getCreator());
                usTodoModel.setCreatedTime(wfWorkItemModel.getCreatedTime());
                usTodoModel.setModifiedTime(wfWorkItemModel.getModifiedTime());
                usTodoModel.setModifier(wfWorkItemModel.getParticipant());
                usTodoModel.setUserName(wfWorkItemModel.getParticipant());
                usTodoModel.setSender(wfWorkItemModel.getParticipant());
                usTodoModel.setTitle(wfWorkItemModel.getProcessInstName());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(wfWorkItemModel.getCreatedTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>insert>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }


    @Override
    public JsonResponse getHeader() {
        IUser currentUser = SecurityUtils.getCurrentUser();
        //正式环境地址
        String url = "";
        String profiles = springContextUtil.getActiveProfile();
        if (Constants.PRD.equalsIgnoreCase(profiles)) {
            url = "http://**************";
        } else {
            url = "http://************:8088";
        }

       // String url = bpsConfig.fdzlPost+"fdzl";
        JsonResponse jsonResponse = HttpClient.textBody(url + "/iportal/common/sso/add/header?loginuser=" +
                        //  JsonResponse jsonResponse = HttpClient.textBody("http://iportal.ha.cmcc/iportal/common/sso/add/header?loginuser=" +
                        encryptor.encrypt(currentUser.getUsername()) + "&appcode=" + "fdzl")
                .asBean(JsonResponse.class);
//        JsonResponse jsonResponse = HttpClient.textBody(url + "/common/sso/add/header?loginuser=" +
//                //  JsonResponse jsonResponse = HttpClient.textBody("http://iportal.ha.cmcc/iportal/common/sso/add/header?loginuser=" +
//                encryptor.encrypt(currentUser.getUsername()) + "&appcode=" + "fdzl")
//                .asBean(JsonResponse.class);
        return jsonResponse;
    }

    //根据账号查询账号信息
    private Map<String, HqUserInfo> findByUsernames(Set<String> usernameSet) {
        Map<String, HqUserInfo> userMap = Maps.newHashMap();
        Assert.notEmpty(usernameSet , "人员信息不能为空！");
        String usernames = usernameSet.stream().collect(Collectors.joining(","));
        Map<String, String> userAccountMap = Maps.newHashMap();
        if (!StrUtil.equals(profilesActive , "prd")){
            List<String> userAllList = Lists.newArrayList();
            usernameSet.stream().forEach(username -> userAllList.add(username + "@ha.cmcc"));
            Specification<UsernameAccount> build = Specifications.<UsernameAccount>and()
                    .in("username", userAllList)
                    .build();
            Iterable<UsernameAccount> usernameAccounts = usernameAccountService.findAllNoPage(build);
            for (UsernameAccount usernameAccount : usernameAccounts) {
                userAccountMap.put(usernameAccount.getUsername().replace("@ha.cmcc", ""), usernameAccount.getAccount().replace("@ha.cmcc", ""));
            }
        }
        JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPageNoSession(Constants.APP_CODE, Constants.USER_ADMIN, usernames);
        if (jsonResponse.getErrcode() == 0) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
            if (CollectionUtil.isNotEmpty(data)) {
                Map<String, Object> map = data.get(0);
                List<Map<String, Object>> userList = MapUtil.get(map, "user", new cn.hutool.core.lang.TypeReference<List<Map<String, Object>>>() {});
                if (CollectionUtil.isNotEmpty(userList)) {
                    userList.stream().forEach(user -> {
                        if (StrUtil.equals(MapUtil.getStr(user, "treeType"), "user")) {
                            String id = MapUtil.getStr(user, "id");
                            HqUserInfo userInfo = HqUserInfo.builder().userId(id).userName(MapUtil.getStr(user, "name")).idType("0").build();
                            if (!StrUtil.equals(profilesActive , "prd")) {
                                userInfo.setUserId(MapUtil.getStr(userAccountMap , id));
                                userInfo.setUserName( "xx" + userInfo.getUserName().substring(userInfo.getUserName().length() -1 , userInfo.getUserName().length()));
                            }
                            userMap.put( id , userInfo);
                        }
                    });
                }
            }
        }
        log.warn("查询人员数据信息：{}" , JacksonUtils.obj2json(userMap));
        return userMap;
    }
}
