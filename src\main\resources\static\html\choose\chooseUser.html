﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<ul id="orgTree"></ul> 
<div class="role orgC" style="position:fixed;"></div>
<script type="text/javascript">
	var gps=getQueryString();
	$(function(){
		treeLoadSuccess();
		$("#orgTree").tree({
			url:web.rootdir+"sys/uums/userinfo/findAllInfosUnderOrgTogether?appCode="+web.appCode+"&orgCode="+gps.inCompanyCode,
			//checkbox:true,//是否在每一个借点之前都显示复选框
			lines:true,//是否显示树控件上的虚线
            treeId:'ID',
            treePid:'PARENTORGCODE',
			contentType: "application/json; charset=utf-8",
            fileds:'ID,NAME|text,PARENTORGCODE,DISPLAYNAME,STYLETYPE',
			animate:true,//节点在展开或折叠的时候是否显示动画效果
			onClick:function(node){
				if(node.STYLETYPE=="org"){
					if(node.children){
						if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
					}else{
						ajaxgeneral({
							url:"sys/uums/userinfo/findAllInfosUnderOrg?appCode="+web.appCode+"&orgCode="+node.ID,
							//data:{"appCode":web.appCode,"orgCode":node.orgCode},
							contentType: "application/json; charset=utf-8",
							success:function(data){
								if(data.data.length==0){
									top.mesAlert("提示信息","该组织无下级数据！", 'info');
								}else{
                                    for(var j in data.data[1]) {
                                        data.data[1][j].ID = data.data[1][j].USERNAME;
                                        data.data[1][j].text = data.data[1][j].TRUENAME;
                                    }
                                    $("#orgTree").tree("append", {
                                        parent : node.target,
                                        data : data.data[1]
                                    });
                                    for(var j in data.data[0]) {
                                        data.data[0][j].ID = data.data[0][j].ORGCODE;
                                        data.data[0][j].text = data.data[0][j].ORGNAME;
                                    }
                                    $("#orgTree").tree("append", {
                                        parent : node.target,
                                        data : data.data[0]
                                    });
								}
							}
						});
					}
				}else{
                    if(gps.multi=="0"){
                        $(".role").html("");
                    }
                    if($(".role a#"+node.ID).length==0) $(".role").append("<a id='"+node.ID+"' displayname='"+node.DISPLAYNAME+"'><font>"+node.text+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                }
			}
		});
		$(document).on("click",".role a i",function(){
			$(this).parent("a").remove();
		});
	});
	//数据加载成功
	function treeLoadSuccess(){
		var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
		for(var i in chooseRow){
            if($(".role a#"+chooseRow[i].id).length==0) $(".role").append("<a id='"+chooseRow[i].username+" displayname='"+chooseRow[i].outCompany+"'><font>"+chooseRow[i].trueName+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
		}
	};
	window.getchoosedata=function(){
		var datas=[];
		$(".role a").each(function(i,v){
			var data={};
            data.username=$(v).attr("id");
            data.trueName=$(v).find("font").text();
            data.outCompany=$(v).attr("displayname");
			datas.push(data);
		});
		return {"data":datas,"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
	};
</script>
</body>
</html>
