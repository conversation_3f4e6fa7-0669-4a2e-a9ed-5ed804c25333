package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_gid_supervision_manager")
@ApiModel(value = "网格监督员实体")
public class UsGidSupervisionManager extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UGSM")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 200)
    @ApiModelProperty(value = "地市编码")
    protected String cityCode;

    @Column(length = 500)
    @ApiModelProperty(value = "地市")
    protected String city;

    @Column(length = 200)
    @ApiModelProperty(value = "区县编码")
    protected String countyCode;

    @Column(length = 500)
    @ApiModelProperty(value = "区县")
    protected String county;

    @Column(length = 200)
    @ApiModelProperty(value = "网格编码")
    protected String gridCode;

    @Column(length = 100)
    @ApiModelProperty(value = "网格名称")
    protected String gridName;

    @Column(length = 200)
    @ApiModelProperty(value = "网格长姓名")
    protected String gridTrueName;

    @Column(length = 100)
    @ApiModelProperty(value = "网格长OA账号")
    protected String gridUserName;

    @Column(length = 40)
    @ApiModelProperty(value = "网格长手机号")
    protected String gridPhone;

    @Column(length = 200)
    @ApiModelProperty(value = "监督员姓名")
    protected String supervisionTrueName;

    @Column(length = 100)
    @ApiModelProperty(value = "监督员OA账号")
    protected String supervisionName;

    @Column(length = 40)
    @ApiModelProperty(value = "监督员手机号")
    protected String supervisionPhone;

}
