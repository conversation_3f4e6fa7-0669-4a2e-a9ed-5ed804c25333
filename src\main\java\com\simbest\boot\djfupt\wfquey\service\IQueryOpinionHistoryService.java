package com.simbest.boot.djfupt.wfquey.service;

import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2018/07/04
 * @Description 流程意见service层
 */
public interface IQueryOpinionHistoryService  {

    /**
     * 查询工单审批意见
     * @param processInstId 流程实例id
     * @param currentUserCode 当前人
     * @return
     */
    //List<WfOptMsgModel> getWfOptMags ( Long processInstId );
    List<Map<String,Object>> getWfOptMags (Long processInstId,String currentUserCode );


}
