package com.simbest.boot.djfupt.common.service;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.security.SimpleAppDecision;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2022/11/23 0023 10:43
 */

public interface ICommonService {

    /**
     * 根据决策查找人员
     *
     * @param processInstId  流程实例id
     * @param sysAppDecision 决策对象
     * @param source         来源
     * @param userCode       用户OA账户
     * @return
     */
    JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision,String param1,String faultsAppName);


    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @param processType    流程类型
     * @return
     */
    JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String processType,String param1,String questionMode);


     Map<String, String> getProcessMap(String type) ;

     JsonResponse queryFile(String pmInsId);

    JsonResponse queryOrg();




    /**
     * 政策宣讲短信
     * @return
     */
    JsonResponse sendCarShortMessage(ActBusinessStatus businessStatus, String sendUser );


    JsonResponse queryProcessType();

    JsonResponse queryMySelfCreate(Integer page, Integer rows, String title, String source, String userCode, String processType,String partiName);


    /**
     * 短信催办
     * @param source 来源
     * @param currentUserCode 当前人
     * @param list 待催列表
     * @return
     */
    JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list);


    /**
     * 根据当前管理员不同，可以在查询时选择不同的组织树
     * @return
     */
    JsonResponse queryOrgTreeBySearch(String pmInsId);

    /**
     * 根据当前管理员不同，可以在查询时选择不同的组织树
     * @return
     */
    JsonResponse queryOrgTreeBySearchTwo(String pmInsId);


    void update(UsAdminManager usAdminManagerOld,UsAdminManager usAdminManager);

    JsonResponse visido();

    JsonResponse getHeader();

}
