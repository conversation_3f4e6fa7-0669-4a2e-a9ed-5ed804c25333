<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
	<title>查询级别管理</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
		<style>
			.tips p {
				font-size: 12px;
				color: #333;
			}
	
			.tips {
				display: none;
				margin-bottom: 30px;
			}
	
			.formTable {
				width: 100%;
				margin-top: 30px;
				border-spacing: 0;
				border-top: 1px solid #e8e8e8;
				border-left: 1px solid #e8e8e8;
			}
	
			.formTable>tbody>tr>td {
				border-right: 1px solid #e8e8e8;
				border-bottom: 1px solid #e8e8e8;
				font-size: 15px;
			}
	
			.formTable>tbody>tr>td input,
			.formTable>tbody>tr>td span,
			.formTable>tbody>tr>td textarea,
			.formTable>tbody>tr>td .textbox .textbox-text {
				border: none;
				font-size: 14px;
			}
	
			.formTable td.lable {
				background-color: #ddf1fe;
				padding: 5px;
				text-align: center;
				min-width: 100px;
			}
	
			.formTable td .textAndInput_readonly,
			.formTable td .textAndInput_readonly .validatebox-readonly {
				background-color: #f7f7f7;
			}
	
			.cselectorImageUL .btn,
			.cselectorImageUL input[type='file'] {
				right: 3px;
				top: 3px;
			}
	
			textarea {
				line-height: 20px;
				letter-spacing: 1px;
			}
	
			.datagrid-header {
				background: #ddf1fe;
			}
		</style>
	<script type="text/javascript">
		$(function () {
			$('.formTable td .combo,.formTable td .textAndInput_readonly').css('width', '100%');
			$('.formTable td .combo input').css('width', "100%");
			$(".body_page").css("min-width", "100px");
			$(".body_page").css("white-space", "nowrap");
			loadForm("userTableQueryForm");
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam = {
				"listtable": {
					"listname": "#userTable",//table列表的id名称，需加#
					"querycmd": "action/queryLevelConfig/findAllQueryLevelInfo",//table列表的查询命令
					"contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"styleClass": "noScroll",
					"checkboxall": true,
					//"queryParams":{"accountNonExpired":"true","accountNonLocked":"true","credentialsNonExpired":"true","status":"0","enabled":1},
					/*"frozenColumns":[[
							{ field: "ck",checkbox:false}
					]],//固定在左侧的列*/
					"columns": [[//列
						{ title: "公司", field: "belongCompanyName", width: 150, sortable: true, tooltip: true },
						{ title: "部门", field: "belongDepartmentName", width: 150, sortable: true, tooltip: true },
						{ title: "姓名", field: "truename", width: 150, sortable: true, tooltip: true },
						{ title: "OA账号", field: "username", width: 150, sortable: true, tooltip: true },
						{ title: "查询模块", field: "queryModuleName", width: 150, sortable: true, tooltip: true },
						{ title: "查询级别", field: "queryLevelName", width: 150, sortable: true, tooltip: true },
						{ title: "备注", field: "remarks", width: 150, sortable: true, tooltip: true },
						{
							title: "操作", field: "opt", align: "center", width: 200, rowspan: 1, tooltip: true,
							formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a href='#' class='readDialog' readDialogindex='" + index + "'>【查看】</a>" +
									"<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>" +
									"<a href='#' contentType='application/x-www-form-urlencoded; charset=utf-8' delete='action/queryLevelConfig/deleteById' deleteid='" + row.id + "'>【删除】</a>";
								return g;
							}
						}
					]],
					"onBeforeLoad": function (params) {
						//params.aa="112";
					},
					/*"pagerbar": [{
							id:"deleteall",
							iconCls: 'icon-remove',
							text:"批量删除&nbsp;"
					}],*/
					"deleteall": {//批量删除deleteall.id要与pagerbar.id相同
						"id": "deleteall",
						"url": "action/queryLevelConfig/deleteAll"
					}
				},
				"dialogform": {
					"dialogid": "#buttons",//对话框的id
					"ctable": "usFamily",
					"formname": "#userTableAddForm",//新增或修改对话框的formid需加#
					"onSubmit": function (param) {//在请求加载数据之前触发。返回false可以停止该动作
						//param.sysOrg={"id":$("#oid").combogrid("getValue"),"orgName":$("#orgName").val()};
						return true;
					},
					"insertcmd": "action/queryLevelConfig/insertQueryLevel",//新增命令
					"updatacmd": "action/queryLevelConfig/update"//修改命令
				},
				"readDialog": {//查看
					"dialogid": "#readDag",
					//"dialogedit": true,//查看对话框底部要不要编辑按钮
					"formname": "#userTableReadForm"
				}
			};
			loadGrid(pageparam);
			$(window).trigger("resize");
			setInterval(function () {
				$(".datagrid-wrap").css("height", "auto")
			}, 100)
		});

		function getcallback(data) {
			if (data != null) {
				idReadonly("username");
				idReadonly("truename");
			}
		};

		//查询模块
		//onSelect 选中之后的操作,record表示选中项
		function queryModuleV(record) {
			if (record.name) {
				$("#queryModuleName").val(record.name);
			}
		};

		//查询级别
		//onSelect 选中之后的操作,record表示选中项
		function queryLevelV(record) {
			if (record.name) {
				$("#queryLevelName").val(record.name);
			}
		};

		// 公司修改触发部门函数--用于搜索
		function belongCompanyNameOnChange(nv, ov) {
			// 部门
			$("#belongDepartmentNameSearch").combobox({
				valueField: 'BELONG_DEPARTMENT_NAME',
				ischooseall: true,
				panelHeight: 'auto',
				editable: false,
				textField: 'BELONG_DEPARTMENT_NAME',
				queryParams: { 'belongCompanyName': nv },
				url: web.rootdir + 'action/queryLevelConfig/findAllDepartmentInfoByCompany'
			});
		};
	</script>
</head>

<body class="body_page">
	<!--searchform-->
	<form id="userTableQueryForm">
		<table border="0" cellpadding="0" cellspacing="6" width="100%">
			<tr>
				<td width="50" align="right">公司：</td>
				<td width="160">
					<input name="belongCompanyName" class="easyui-combobox" style="width:100%;height: 32px;" data-options="
					valueField: 'BELONG_COMPANY_NAME',
					panelHeight:'auto',
					ischooseall:true,
					textField: 'BELONG_COMPANY_NAME',
					editable:false,
					url: web.rootdir+'action/queryLevelConfig/findAllCompanyInfo',
					onChange:belongCompanyNameOnChange">
				</td>
				<td width="50" align="right">部门：</td>
				<td width="200">
					<input id="belongDepartmentNameSearch" name="belongDepartmentName" class="easyui-combobox"
						style="width:100%;height: 32px;" data-options="
						valueField: 'belongDepartmentName',
						ischooseall:true,
						panelHeight:'auto',
						textField: 'belongDepartmentName',
						editable:false,
						queryParams:{'belongCompanyName': 'BELONG_COMPANY_NAME'},
						url: web.rootdir+'action/queryLevelConfig/findAllDepartmentInfoByCompany'" />
				</td>
				<td width="50" align="right">查询模块：</td>
				<td width="120">
					<input th:class="queryModuleCode" name="queryModuleCode" class="easyui-combobox"
						style="width: 100%; height: 32px;" data-options="
						valueField: 'value',
						editable:false,
						panelHeight:'auto',
						ischooseall:true,
						textField: 'name',
						queryParams:{'dictType':'findType'},
						url: web.rootdir+'action/queryDictValue/queryByType'" />
				</td>
				<td width="50" align="right">查询级别：</td>
				<td width="120">
					<input th:class="queryLevelCode" name="queryLevelCode" class="easyui-combobox"
						style="width: 100%; height: 32px;" data-options="
						valueField: 'value',
						editable:false,
						panelHeight:'auto',
						ischooseall:true,
						textField: 'name',
						queryParams:{'dictType':'queryLevelType'},
						url: web.rootdir+'action/queryDictValue/queryByType'" />
				</td>
				<td width="50" align="right">OA账号：</td>
				<td width="120">
					<input id="usernameSearch" name="username" class="easyui-validatebox" type="text" />
				</td>
				<td width="50"></td>
				<td>
					<div class="w10">
						<a class="btn fl searchtable">
							<font>查询</font>
						</a>
						<a class="btn showDialog fr"><span>新增</span></a>
					</div>
				</td>

			</tr>
		</table>
	</form>
	<!--table-->
	<div class="userTable">
		<table id="userTable"></table>
	</div>
	<!--dialog-->
	<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:750px;height:550px;">
		<form id="userTableAddForm" method="post" contentType="application/json; charset=utf-8" getcallback="getcallback()">
			<table class="formTable" border="0" cellpadding="0" cellspacing="6" width="100%">
				<tr></tr>
				<input id="id" name="id" type="hidden" />
				<input id="queryModuleName" name="queryModuleName" type="hidden" />
				<input id="queryLevelName" name="queryLevelName" type="hidden" />
				<tr>
					<td class="lable" width="10%" align="right">
						OA账号<font class="col_r">*</font>
					</td>
					<td>
						<input id="username" name="username" class="easyui-validatebox" type="text" required='required' />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						OA姓名<font class="col_r">*</font>
					</td>
					<td>
						<input id="truename" name="truename" class="easyui-validatebox" type="text" required='required' />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						查询模块<font class="col_r">*</font>
					</td>
					<td width="400">
						<input id="queryModuleCode" name="queryModuleCode" class="easyui-combobox" required='required'
							style="width: 100%; height: 32px;" data-options="
							valueField: 'value',
							panelHeight:'auto',
							ischooseall:true,
							textField: 'name',
							editable:false,
							queryParams:{'dictType':'findType'},
							url: web.rootdir+'action/queryDictValue/queryByType',
							onSelect:queryModuleV" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						查询级别<font class="col_r">*</font>
					</td>
					<td>
						<input id="queryLevelCode" name="queryLevelCode" class="easyui-combobox" required='required'
							style="width: 100%; height: 32px;" data-options="
							valueField: 'value',
							panelHeight:'auto',
							ischooseall:true,
							textField: 'name',
							editable:false,
							queryParams:{'dictType':'queryLevelType'},
							url: web.rootdir+'action/queryDictValue/queryByType',
							onSelect:queryLevelV" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注<font class="col_r">*</font>
					</td>
					<td>
						<textarea id="remarks" name="remarks" class="easyui-validatebox" style="min-height:120px;"
							required="true"></textarea>
					</td>
				</tr>
			</table>
		</form>
	</div>
	<!--dialog-->
	<div id="readDag" title="节假日详情查看" class="easyui-dialog" style="width:800px;height:450px;">
		<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取-->
		<form id="userTableReadForm" method="post" style="padding-bottom:5px;" cmd-select="action/queryLevelConfig/findById"
			getcallback="getcallback()">
			<table class="formTable" border="0" cellpadding="0" cellspacing="15" width="100%">
				<tr>
					<td class="lable" width="10%" align="right">
						OA账号<font class="col_r">*</font>
					</td>
					<td>
						<input class="username" name="username" class="username easyui-validatebox" type="text" noReset="true" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						OA姓名<font class="col_r">*</font>
					</td>
					<td>
						<input class="truename" name="truename" class="easyui-validatebox" type="text" noReset="true" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						查询模块<font class="col_r">*</font>
					</td>
					<td width="400">
						<input class="queryModuleCode" name="queryModuleCode" class="easyui-combobox" required='required'
							style="width: 100%; height: 32px;" data-options="
							valueField: 'value',
							panelHeight:'auto',
							ischooseall:true,
							textField: 'name',
							editable:false,
							queryParams:{'dictType':'findType'},
							url: web.rootdir+'action/queryDictValue/queryByType',
							onSelect:queryModuleV" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						查询级别<font class="col_r">*</font>
					</td>
					<td>
						<input class="queryLevelCode" name="queryLevelCode" class="easyui-combobox" required='required'
							style="width: 100%; height: 32px;" data-options="
							valueField: 'value',
							panelHeight:'auto',
							ischooseall:true,
							textField: 'name',
							editable:false,
							queryParams:{'dictType':'archiveStatus'},
							url: web.rootdir+'action/queryDictValue/queryByType',
							onSelect:queryLevelV" />
					</td>
				</tr>
				<tr>
					<td class="lable" width="10%" align="right">
						备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注<font class="col_r">*</font>
					</td>
					<td>
						<textarea class="remarks" name="remarks" class="easyui-validatebox" style="min-height:120px;"
							required="true"></textarea>
					</td>
				</tr>
			</table>
		</form>
	</div>
</body>

</html>