package com.simbest.boot.djfupt.findfaults.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_Find_Faults_Model")
@ApiModel(value = "找茬上报实体类")
public class UsFindFaultsModel extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UFFM") //主键前缀，此为可选项注解
    private String id;

    @Column(name = "apply_User_name",  length = 100)
    @ApiModelProperty(value = "上报人OA账号")
    @Getter
    @Setter
    private String applyUserName;

    @Column(name = "pm_ins_id",  length = 100)
    @ApiModelProperty(value = "主单据ID")
    @Getter
    @Setter
    private String pmInsId;

    @Column(name = "apply_User",  length = 100)
    @ApiModelProperty(value = "上报人姓名")
    @Getter
    @Setter
    private String applyUser;

    @Column(name = "apply_User_Phone",  length = 100)
    @ApiModelProperty(value = "上报人电话")
    @Getter
    @Setter
    private String applyUserPhone;

    @Column(name = "display_name",  length = 200)
    @ApiModelProperty(value = "上报人组织")
    @Getter
    @Setter
    private String displayName;

    @Column(name = "grid_name",  length = 200)
    @ApiModelProperty(value = "网格名称")
    @Getter
    @Setter
    private String gridName;

    @Column(name = "app_name",  length = 200)
    @ApiModelProperty(value = "归属系统名称")
    @Getter
    @Setter
    private String appName;

    @Column(name = "company_name",  length = 200)
    @ApiModelProperty(value = "公司名称")
    @Getter
    @Setter
    private String companyName;

    @Column(name = "department_name",  length = 200)
    @ApiModelProperty(value = "部门名称")
    @Getter
    @Setter
    private String departmentName;

    @Column(name = "org_name",  length = 200)
    @ApiModelProperty(value = "组织名称")
    @Getter
    @Setter
    private String orgName;

    @Column(name = "company_code",  length = 200)
    @ApiModelProperty(value = "公司编码")
    @Getter
    @Setter
    private String companyCode;

    @Column(name = "department_code",  length = 200)
    @ApiModelProperty(value = "部门编码")
    @Getter
    @Setter
    private String departmentCode;

    @Column(name = "org_code",  length = 200)
    @ApiModelProperty(value = "组织编码")
    @Getter
    @Setter
    private String orgCode;

    @Column(name = "status",  length =1 )
    @ApiModelProperty(value = "解决状态 0未处理，1解决中，2已解决未归档，3已解决归档")
    @Getter
    @Setter
    private Integer status;

    @Column(name = "belong_Company_Type_Dict_Value",  length = 200)
    @ApiModelProperty(value = "公司形态")
    @Getter
    @Setter
    private String belongCompanyTypeDictValue;

    @Transient
    List<UsFindFaultsIdea> usFindFaultsIdea;

    @Column(name = "feed_back_idea",  length = 1000)
    @ApiModelProperty(value = "反馈意见")
    @Getter
    @Setter
    private String feedBackIdea;

    @Column(name = "is_Optimize",  length = 1)
    @ApiModelProperty(value = "是否涉及优化系统")
    @Getter
    @Setter
    private Integer isOptimize;

    @Column(name = "Optimize_end_time",  length = 100)
    @ApiModelProperty(value = "计划优化完成时间")
    @Getter
    @Setter
    private String optimizeEndTime;


    //todo 找茬活动类型临时设计为2期
    @Column(name = "faults_type",  length = 200)
    @ApiModelProperty(value = "期数（一期、二期、三期）")
    @Getter
    @Setter
    private String faultsType="2";

}
