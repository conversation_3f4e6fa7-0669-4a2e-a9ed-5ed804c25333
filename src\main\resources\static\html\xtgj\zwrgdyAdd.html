<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>装维入格调研台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent()
        var gps = getQueryString()
        $(function () {
            if (gps.type == 'add') {
                ajaxgeneral({
                    url: 'action/usZwrgdyInfo/getGridName',
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        formval({
                            gridName: data.data.gridName,
                            belongDepartmentName: web.currentUser.belongDepartmentName,
                            belongCompanyName: web.currentUser.belongCompanyName,
                            truename: web.currentUser.truename,
                            phone: web.currentUser.preferredMobile,
                            createdTime: getNow('yyyy-MM-dd hh:mm:ss')
                        }, '#glyTableAddForm')
                    }
                });

                ajaxgeneral({
                    url: 'sys/dictValue/findDictValue',
                    data: {
                        "dictType": "XTGJ_ZWRGDY_TYPE"
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        var list = data.data
                        for (let i = 0; i < list.length; i++) {
                            var $tr = $(".ctable").find("thead.trow tr").clone();
                            var len = $(".ctable").find("tbody").find("tr[path]").length + 1;
                            $tr.attr("path", len);
                            $tr.find("td[path=xh]").text(len);
                            $tr.find("td[path=item]").text(list[i].name);
                            $tr.find("td[path=zhidaoyuan]").text(list[i].value);
                            $tr.find('#dictId').val(list[i].id)
                            $tr.find('#dictValue').val(list[i].value)
                            $tr.find('#dictName').val(list[i].dictName)
                            $tr.find("textarea,input,select").each(function (x, y) {
                                tdPathI($(y), $tr);
                            });
                            $tr.appendTo($(".ctable").find("tbody"));
                        }
                    }
                });
            } else {
                ajaxgeneral({
                    url: 'action/usZwrgdyInfo/findByIdInfo?source=PC&id=' + gps.id,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        formval(data.data, '#glyTableAddForm')
                        var xh = 1;
                        var arr = data.data.operationList
                        for (let i = 0; i < arr.length; i++) {
                            appTr(xh, arr[i], i)
                            xh++
                        }
                        formReadonly("glyTableAddForm")
                    }
                });
            }
        });

        function tdPathI($id, $tr, val) {
            if (val) {
                tdPath($id, val);
            } else {
                tdPath($id);
            }
        };

        function appTr(xh, data, i) {
            var $tr = $(".ctable").find("thead.trow tr").clone();
            $tr.attr("path", xh);
            $tr.find("td[path=xh]").text(xh)
            $tr.find("td[path=item]").text(data.dictName);
            $tr.find("td[path=zhidaoyuan]").text(data.dictValue);
            $tr.find('#dictId').val(data.dictId)
            $tr.find('#dictValue').val(data.dictValue)
            $tr.find('#dictName').val(data.dictName)
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPath($(y), data[$(y).attr("name")]);
            });
            $tr.appendTo($(".ctable").find("tbody"));
        };

        window.getchoosedata = function () {
            var operationList = [];
            $("table.ctable tbody").find("tr").each(function (x, y) {
                var listi = {};
                $(y).find("input,textarea").each(function (a, b) {
                    if ($(b).attr("name")) {
                        listi[b.name] = b.value || "";
                    }
                });
                operationList.push(listi);
            });

            var flag = true
            for (var i = 0; i < operationList.length; i++) {
                if (!operationList[i].operationInfo) {
                    flag = false
                    break;
                }
            }
            if (!flag) {
                top.mesShow("温馨提示", "请选择是或否", 1000, 'red');
                return false
            }

            ajaxgeneral({
                url: 'action/usZwrgdyInfo/insertInfo',
                data: {
                    "id": '',
                    "operationList": operationList,
                    "gridName": $('#gridName').val()
                },
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    top.mesShow("温馨提示", "新增成功", 1000, 'red');
                }
            });

            return { 'state': 1 };
        };

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('id');
            top.mesConfirm("温馨提示", "请确认是否删除该思政纪实工作配置，删除后不可恢复！", function () {
                ajaxgeneral({
                    url: 'action/usRecordConfig/deleteUsRecordConfig',
                    data: { id: id },
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        $("#glyTable").datagrid("reload");
                    }
                });
            });
        })

    </script>
    <style>
        textarea { white-space: normal !important; }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }
        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }
        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text { border: none; font-size: 13px; }
        .formTable td.lable { background-color: #ddf1fe; padding: 5px;  max-width: 100px; }
        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly { background-color: #fff; }
        /* input:read-only { background-color: #f7f7f7; } */

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] { right: 3px; top: -15px; }
        .cselectorImageUL input[type='file'] {display: inline-block;width: 60px !important;}
        textarea { line-height: 20px; letter-spacing: 1px; }
        .cselectorImageUL{width: 100%;}

        .dialog-button {
            text-align: center;
        }
        .dialog-button .l-btn {
            margin-left: 30px;
        }
        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }
        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .input-select-td .textbox {
            width: 100% !important;
        }

        .blockTitle {
            border-left: 5px solid #f79e40;
            padding: 5px 0px 5px 10px;
            margin: 10px 0px;
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
        }

        .radio-td .cselectorRadioUL {
            text-align: center;
        }

        .radio-td .cselectorRadioUL a {
            font-size: 17px;
            margin: 0 10px;
        }

        .body_page {
            padding: 15px 0 0;
        }
    </style>
</head>

<body class="body_page">
    <form id="glyTableAddForm">
        <table border="0" cellpadding="0" cellspacing="10" class="formTable">
            <tr>
                <td colspan="6" style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding:
                10px;">
                    装维入格调研填报
                </td>
            </tr>
            <tr>
                <td width="10%" class="lable">
                    装维人姓名
                </td>
                <td width="23%">
                    <input id="truename" name="truename" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    联系电话
                </td>
                <td width="23%">
                    <input id="phone" name="phone" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    归属单位
                </td>
                <td width="23%">
                    <input id="belongCompanyName" name="belongCompanyName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
            </tr>
            <tr>
                <td width="10%" class="lable">
                    归属本部门
                </td>
                <td width="23%">
                    <input id="belongDepartmentName" name="belongDepartmentName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    归属网格
                </td>
                <td width="23%">
                    <input id="gridName" name="gridName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    上报时间
                </td>
                <td width="23%">
                    <input id="createdTime" name="createdTime" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
            </tr>
        </table>
        <div class="block" style="width: 100%;">
            <span class="blockTitle">省专协同任务1-装维入格调研</span>
            <table border="0" cellpadding="0" cellspacing="0" class="ctable w100">
                <thead>
                <tr>
                    <td align="center" width="30">序号</td>
                    <td align="center" width="100">项目</td>
                    <td align="center" width="150">移动党建指导员</td>
                    <td align="center" width="100">操作</td>
                    <td align="center" width="300">填写备注说明(如需填写打钩后可填)</td>
                </tr>
                </thead>
                <thead class="trow hide">
                <tr path="0">
                    <input id="dictId" name="dictId" type="hidden"/>
                    <input id="dictValue" name="dictValue" type="hidden"/>
                    <input id="dictName" name="dictName" type="hidden"/>
                    <td align="center" width="30" path="xh"></td>
                    <td align="center" width="100" style="border-left: 1px solid #e6e6e6;" path="item"></td>
                    <td align="center" width="150" style="border-left: 1px solid #e6e6e6;border-right: 1px solid #e6e6e6;" path="zhidaoyuan"></td>
                    <td class="radio-td" width="100" style="border-left: 1px solid #e6e6e6;border-right: 1px solid
                    #e6e6e6;">
                        <input name="operationInfo" classpath="cselectorRadio" required="required"
                               values="1|是,2|否" style="height: 32px;"/>
                    </td>
                    <td width="300" style="border-left: 1px solid #e6e6e6;">
                        <textarea name="remarkInfo" class="remark" classpath="validatebox"></textarea>
                    </td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </form>
</body>

</html>