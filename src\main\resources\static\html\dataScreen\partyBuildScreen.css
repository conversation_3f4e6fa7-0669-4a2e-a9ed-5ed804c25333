* { margin: 0; padding: 0; box-sizing: border-box; }
li { list-style: none; }
a:hover { text-decoration: none; }
body { margin: 0; line-height: 1.15; overflow: hidden; }
header { position: relative; height: 80px; background: url(../../images/screen/top_bgNew.png) no-repeat top center; background-size: 100% 100%; }
header h1 { font-size: 0.475rem; color: #fff; text-align: center; line-height: 1rem; }
header .showTime { position: absolute; top: -0.2rem; right: 1.5rem; line-height: 0.9375rem; font-size: 0.25rem; color: #333; }
header .showTime img { width: 0.4rem; height: 0.4rem; }
header .showTime img:hover { cursor: pointer; }
header .msgNum { position: absolute; right: 1.3rem; top: 0.1rem; background: red; color: #fff; padding: 2px 5px; border-radius: 10px; font-size: .1rem; display: none; }
#trueName { position: absolute; right: 0.6rem; top: 0.2rem; font-size: .2rem; }
.mainbox { padding: 0 0.125rem 0; display: flex; height: calc(100% - 80px); }
.mainbox .column1 { float: right; width: 30%; }
.mainbox .column2 { float: right; width: 42%;background: url(../../images/screen/<EMAIL>) no-repeat; background-size: 100% 100%;}
.mainbox .column3 { float: right; width: 42%; }
.mainbox .column4 { float: right; width: 28%; }
.mainbox .column:nth-child(2) { flex: 5; margin: 0 0.125rem 0.16rem; overflow: hidden;position: relative; }
.mainbox .column:nth-child(3) { flex: 5; margin: 0 0.125rem 0.1rem; overflow: hidden;position: relative; }
.panels { position: relative; background: rgba(255, 255, 255, 0.04) url(../../images/screen/kj.png) no-repeat; padding: 0 0.1875rem 0.4rem; margin-bottom: 0.1875rem; background-size: 100% 100%; }
.panel1 { height: 60%; }
.panel4 { height: 35%; }
.panel5 { height: 1.375rem; background: rgba(255, 255, 255, 0.04) url(../../images/screen/<EMAIL>) no-repeat; background-size: 100% 100%; }
.panel6 { height: 61.6%; }
.panel5 .title { width: 100%; height: 1.375rem; font-weight: bold; font-size: 0.25rem; margin-left: 20%; margin-top: .1rem; color: #333 !important; line-height: 1.375rem !important; }
.panels .panel-footers { position: absolute; left: 0; bottom: 0; width: 100%; }
/* .panels h2,.middle h2 { height: 0.6rem; color: #333; font-size: 0.25rem;padding-left: 15%; font-family: YouSheBiaoTiHei;} */
.panels h2,.middle h2 { height: 0.6rem; color: #333; font-size: 0.2rem;padding-left: 16%;font-weight: bold; }
.panel2 { height: 35%; }
.panel2 h2 { line-height: 0.1rem; }
.panel4 h2 { line-height: 0.1rem; }
/* .middle h2 { line-height: 0.1rem; padding-left: 11%; font-family: YouSheBiaoTiHei;} */
.middle h2 { line-height: 0.1rem; padding-left: 11%;}
.hlgjBox h2 { line-height: 0.1rem; padding-left: 15%; }
.panels h2 a { margin: 0 0.1875rem; color: #fff; text-decoration: underline; }
.panel6 .chart { height: 45%; margin-top: 30px; }
.djbox{width: 100%;height: 100%;}
.map { position: relative; height: 62%; }
.completion { position: relative; height: 27%; display: flex; justify-content: space-evenly; padding: 0 0.1rem 0.3rem; }
.completion ul { flex: 1; height: 100%; display: flex; flex-direction: column; justify-content: space-around; }
.completion li { margin-left: 0.15rem; }
.listTwo { border-left: 1px #D31805 dashed; border-right: 1px #D31805 dashed; }
.map .chart { position: absolute; top: 0; left: 0; z-index: 5; height: 100%; width: 100%; }
.middle .djzdy { position: relative; background: url(../../images/screen/<EMAIL>) no-repeat; background-size: 100% 100%; }
/* .hlgjBox .info{height: 35%;position: relative; background: url(../../images/screen/kj.png) no-repeat; background-size: 100% 100%;padding: 0 0.1875rem 0.4rem;} */
.hlgjBox .bg{height: 15%;position: relative; background: url(../../images/screen/hlgj/b7.png) no-repeat; background-size: 100% 100%; margin: 0.1875rem 0 ;}

.hlgjBox .bg h2{ padding: 15px; }
.hlgjBox .bg .bbbox{ display: flex; justify-content: space-around; }
.hlgjBox .bb { width: 25%; height: 72px; display: flex; justify-content: center; align-items: center; font-size: 12px; padding-bottom: 12px; }
.hlgjBox .b1{ background: url(../../images/screen/hlgj/b1.png) no-repeat; background-size: 100% 100%; }
.hlgjBox .b2{ background: url(../../images/screen/hlgj/b2.png) no-repeat; background-size: 100% 100%; }
.hlgjBox .b3{ background: url(../../images/screen/hlgj/b3.png) no-repeat; background-size: 100% 100%; }
.hlgjBox .b4{ background: url(../../images/screen/hlgj/b4.png) no-repeat; background-size: 100% 100%; }
.pointer{cursor: pointer;}
.active{color: #D31805;font-weight: bold;}
@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}
@keyframes rotate1 {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(-360deg); }
}
@media screen and (max-width: 1024px) {
    html { font-size: 42px !important; }
}
@media screen and (min-width: 1920px) {
    html { font-size: 80px !important; }
}
.listBox { width: 98%; height: 28%; position: relative; border-bottom: 1px #D31805 dashed; margin: 10px 0; }
.listBox-tops { width: 100%; height: 0.3rem; background: url(../../images/screen/<EMAIL>) no-repeat; background-size: 100% 100%; display: flex; justify-content: space-between; align-items: center; }
.listBox-tops .title { margin-left: 10px; font-size: .2rem; color: #D31805; font-weight: 700; }
.listBox-tops .more { cursor: pointer; margin-right: 5px; font-size: .12rem; }
.listBox .info1 { padding: 0 .2rem; }
.listBox .info1 p { height: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; position: relative; margin-left: .2rem; font-size: 0.2rem; margin-bottom: 0.10rem; }
.listBox .info1 p:hover { cursor: pointer; }
.listBox .smallTag { width: 0.1rem; height: 0.1rem; background-color: #e53935; border-radius: 50%; position: relative; top: 0.2rem; }
#bots li{ width: 0.1rem; height: 0.1rem; margin: 0 5px; background-color: #c6cacd; border-radius: 50%; position: relative; top: 0.2rem; }
#bots .active{ background-color: #e53935; }
#bots{ width: 100%; display: flex; justify-content: center; }
.smallTagBox { width: 100%; display: flex; justify-content: center; }
.smallTag2{ width: 0.1rem; height: 0.1rem; background-color: #c6cacd; border-radius: 50%; position: relative; top: 0.2rem; margin: 0 0.05rem; }
.smallTag{ width: 0.1rem; height: 0.1rem; background-color: #e53935; border-radius: 50%; position: relative; top: 0.2rem; margin: 0 0.05rem; }
.listBox p { line-height: normal; }
.listBox-footers { position: absolute; left: 0; bottom: 0; width: 100%; }
.panel2 .completionBox { width: 100%; display: flex; overflow: hidden;padding-bottom: 0.1rem;}
.panel2 .completionBox .left { width: 50%; height: 100%; padding: 4% 3% 0 2%; }
.panel2 .completionBox .right { width: 50%; height: 100%; padding: 0 1% 0 0; }
.completionTitle { font-size: 0.2rem; font-weight: 700; margin: 0rem 0 0rem 0.4rem; }
.panel2 .completionBox .right .title { font-size: 0.2rem; font-weight: 700;}
.panel2 .completionBox .right .content { font-size: .18rem; font-weight: 400; line-height: .30rem;}
.panel4 .chart { display: flex; justify-content: space-around; flex-wrap: wrap; height: 3.5rem; }
.panel4 .chart .menuCard { width: 25%; display: flex; flex-direction: column; align-items: center; justify-content: center; }
.panel4 .chart .menuCard img:hover { cursor: pointer; }
.panel4 .menutxt { color: #333; font-size: 14px; margin: 5px 0; }
.panel6 .showBox { width: 100%; height: 10%; display: flex; justify-content: space-around; align-items: center; font-size: .1rem; margin-top: 5%; }
.panel6 .showBox .show { width: 30%; height: .7rem; background: url(../../images/screen/<EMAIL>) no-repeat; background-size: 100% 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; /* padding: 0.3rem 0; */ }
.panel6 .showBox .show .num { font-weight: bold; color: #D31805; font-size: .3rem; }
.panel6 .showBox .show .text { margin-top: 10px; font-weight: bold; color: #333; font-size: 0.16rem; }
.infobolck img { width: .2rem; height: .2rem; margin-right: 10px; }
.infos { width: 1.3rem; display: inline-block; color: #333; font-size: 0.2rem; }
.infospan { /* width: 48.75px; */ color: #D31805; font-size: 0.2rem; }
/* 站内信 */
.msgbox { width: 5rem; height: 6rem; background: #fff; border: 1px solid #FEF6F6; position: absolute; top: 1rem; z-index: 99; right: 1rem; display: none; overflow: hidden; font-size: .2rem; }
.msgbox .title { text-align: center; border-bottom: 1px solid #FEF6F6; }
.msgbox .msgTitle { position: relative; padding: .2rem; overflow: hidden; }
.msgbox .msgItem { width: 90%; padding: 2px; border-bottom: 1px solid #eee; overflow: hidden; }
.msgbox .msgItem p { font-size: .25rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.msgbox .msgItem span { color: #ccc; font-size: .1rem; }
.msgbox .redIcon { width: 10%; background: red; width: 5px; height: 5px; border-radius: 50%; position: absolute; top: .4rem; }
.panel6 .numberBox { display: flex; }
.panel6 .numberBox div { flex: 1; text-align: center; line-height: .3rem; }
.panel6 .numberBox .number { font-size: .3rem; }
.panel6 .numberBox .times { color: #bbb }
.panel6 .countBox { display: flex; width: 80%; margin: 0.2rem auto; border-radius: 20px; background: #efefef; line-height: .5rem; height: 0.5rem; font-size: .2rem; }
.panel6 .countBox div { flex: 1; width: 50%; text-align: center; }
.swiperBox{display: flex;position: relative;}
.swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction{ bottom: 0px; }
.swiper-pagination-bullet-active{background: #e53935;}
.c1{ width: 55%; margin: 0 auto 10px; display: flex; flex-direction: column;align-items: center; }
.swiper-slide-active{position: relative;}

.aimg{width:20px;height:20px;margin-right: 10px;}

/* 点击切换 */
.grey{
    filter: grayscale(10%);
    -webkit-filter: grayscale(10%); /* Safari 6.0 - 9.0 */
    -moz-filter: grayscale(10%); /* Firefox 35 - 50 */
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(grayscale=0.1)"; /* IE 6 - 9 */
    -o-filter: grayscale(10%); /* Opera 15 - 16, 19 */
}
.triangle::after{
    content: "";
    position: absolute;
    right: -10px;
    top: 20px;
    width: 20px;
    height: 20px;
    background-color: #fff;
    transform: rotate(45deg);
}
.triangle .menuCard{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.triangle .menuCard .menutxt{
    margin: 5px 0;
}