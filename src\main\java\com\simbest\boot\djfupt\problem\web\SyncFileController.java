package com.simbest.boot.djfupt.problem.web;

import cn.hutool.core.io.FileUtil;
import com.simbest.boot.base.enums.StoreLocation;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.http.client.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <strong>Title :SyncFileController </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/11/7</strong><br>
 * <strong>Modify on : 2022/11/7</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

@RestController
@RequestMapping(value = "/sync/file")
@Slf4j
public class SyncFileController {

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private AppConfig appConfig;


    @PostMapping(value = "/syncFile")
    public JsonResponse syncFile() {
        int i = 1 ;
        Pageable pageable = fileService.getPageable(i, 100, Sort.Direction.ASC.toString(), "createdTime");
        Page<SysFile> all = fileService.findAll(pageable);
        List<SysFile> files = all.getContent();
        int count = files.size();
        while (files.size() > 0) {
            for (SysFile file : files) {
                try {
                    File downloadFile = HttpClient.get(file.getAnonymousFilePath()).asFile(HttpMethod.GET, file.getFileName());
                    String newFilePath = appConfig.getUploadPath() + "/sync/" + UUID.randomUUID().toString() + "." + file.getFileType();
                    //String newFilePath = "F:\\cmcc\\apps\\andstock" + "\\sync\\" + UUID.randomUUID().toString() + "." + file.getFileType();
                    FileUtil.copyFile(downloadFile , new File(newFilePath));
                    file.setFilePath(newFilePath);
                    file.setAnonymousFilePath("http://10.92.81.163:8089/djfupt/sys/file/download/anonymous?id=" + file.getId());
                    file.setApiFilePath("http://10.92.81.163:8089/djfupt/sys/file/download/api?id=" + file.getId());
                    file.setMobileFilePath("http://10.92.81.163:8089/djfupt/sys/file/download?id=" + file.getId());
                    file.setStoreLocation(StoreLocation.disk);
                    fileService.update(file);
                } catch (Exception e ) {
                    Exceptions.printException(e);
                }
            }
            i++;
            pageable = fileService.getPageable(i, 100, Sort.Direction.ASC.toString(), "createdTime");
            all = fileService.findAll(pageable);
            files = all.getContent();
            count += files.size();
        }
        log.warn("同步完成！一同同步【{}】个文件" , count);
        return JsonResponse.defaultSuccessResponse();
    }


}
