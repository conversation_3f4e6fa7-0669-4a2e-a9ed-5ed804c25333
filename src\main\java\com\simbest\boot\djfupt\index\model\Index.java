package com.simbest.boot.djfupt.index.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.util.List;

@Data
public class Index {

    @ExcelVOAttribute(name = "序号", column = "A")
    @Excel(name = "序号")
    private String id;//序号

    @ExcelVOAttribute(name = "标题", column = "B")
    @Excel(name = "标题")
    private String title;//标题

    @ExcelVOAttribute(name = "创建部门", column = "C")
    @Excel(name = "创建部门")
    private String belongCompanyName;//创建部门

    @ExcelVOAttribute(name = "创建人", column = "D")
    @Excel(name = "创建人")
    private String applyUser;//创建人

    @ExcelVOAttribute(name = "创建时间", column = "E")
    @Excel(name = "创建时间")
    private String createdTime;//创建时间

    @ExcelVOAttribute(name = "当前办理环节", column = "F")
    @Excel(name = "当前办理环节")
    private String handlingLink;//当前办理环节



}
