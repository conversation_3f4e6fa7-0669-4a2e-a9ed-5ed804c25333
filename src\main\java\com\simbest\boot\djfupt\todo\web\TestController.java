package com.simbest.boot.djfupt.todo.web;

import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.djfupt.todo.TodoBusOperatorService;
import com.simbest.boot.djfupt.todo.service.ITestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <strong>Title : TestController</strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/5/24</strong><br>
 * <strong>Modify on : 2022/5/24</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    @Autowired
    private ITestService testService;

    @PostMapping(value = {"/demo1", "/api/demo1", "/demo1/sso", "/anonymous/demo1"})
    public String test() throws Exception {
        testService.test();

        return "1111";
    }

    @PostMapping(value = {"/testTodo", "/api/testTodo", "/testTodo/sso", "/anonymous/testTodo"})
    public String testTodo() throws Exception {
        String sendUser = "chenhaiwei";
        ActBusinessStatus actBusinessStatus = new ActBusinessStatus();
        actBusinessStatus.setBusinessKey("UMF471277779121451008");
        actBusinessStatus.setProcessInstId(100789L);
        actBusinessStatus.setWorkItemId(230389L);
        actBusinessStatus.setReceiptTitle("公文管理-公文管理公文管理公文管理公文管理公文管理");
        actBusinessStatus.setProcessDefId(751L);
        actBusinessStatus.setCreateUserId("hadmin3");
        actBusinessStatus.setCreateUserCode("hadmin3");
        actBusinessStatus.setEndTime(LocalDateTime.now());
        actBusinessStatus.setCreateTime(LocalDateTime.now());
        actBusinessStatus.setActivityDefId("djfupt.third_level_manager");
        actBusinessStatus.setId("WFW471277796385206272");
        todoBusOperatorService.openTodo(actBusinessStatus,sendUser);
        return "1111";
    }
}
