package com.simbest.boot.djfupt.admin.utils;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * MyUtils
 *
 * <AUTHOR>
 * @since 2023/5/19 16:16
 */
@Slf4j
@Component
public class MyUtils {

    private static final String SSO = "/sso";
    private static final String USER_ROLE_MAPPING = "/action/user/role/";
    private static AppConfig config;
    private static RsaEncryptor encryptor;
    private static LoginUtils loginUtils;

    public MyUtils(AppConfig config, RsaEncryptor encryptor, LoginUtils loginUtils) {
        MyUtils.config = config;
        MyUtils.encryptor = encryptor;
        MyUtils.loginUtils = loginUtils;
    }

    /**
     * 模拟登录 JsonResponse
     *
     * @param source          手机端还是PC端
     * @param currentUserCode 当前用户code
     * @return 登录成功返回null   返回非null时，说明登录出错
     */
    public static JsonResponse manualLogin(String source, String currentUserCode) {
        String username = SecurityUtils.getCurrentUserName();
        if (username != null) return null;
        if (!"MOBILE".equals(source))
            return JsonResponse.fail(String.format("source: %s, currentUserCode: %s, 未登录状态！", source, currentUserCode));
        if (StringUtils.isBlank(currentUserCode))
            return JsonResponse.fail(String.format("source: %s, currentUserCode: %s, 未登录状态,OA账户不能为空!", source, currentUserCode));
        loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        return null;
    }

    /**
     * 获取远程uums请求链接
     * <br/> params [uri]
     *
     * @return {@link String}
     * <AUTHOR>
     * @since 2023/6/25 11:24
     */
    public static String getUUMSUrl(String uri) {
        String loginUser = SecurityUtils.getCurrentUserName();
        String username = encryptor.encrypt(loginUser).replace("+", "%2B");
        String url = String.format(
                MyUtils.config.getUumsAddress() + uri + SSO + "?loginuser=%s&appcode=%s",
                username, Constants.APP_CODE
        );
        log.debug("Http remote request: user: {}, url: {}", loginUser, url);
        return url;
    }

    /**
     * 获取单据唯一id
     * <br/> params []
     *
     * @return {@link String}
     * <AUTHOR>
     * @since 2023/5/24 18:20
     */
    public static String getUniqueId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 格式化时间
     * <br/> params []
     *
     * @return {@link String}
     * <AUTHOR>
     * @since 2023/5/24 18:20
     */
    public static String formatDate(String date) {
        date = date.replaceAll("/", "-");
        try {
            Date parse = DateFormat.getDateInstance().parse(date);
            LocalDate localDate = parse.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return localDate.toString();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送远程http请求 uums 处理用户角色关联信息
     *
     * @param uri  uri
     * @param type 请求参数类型
     * @param map  参数
     */
    public static void uumsHttpRequest(String uri, String type, Map<String, Object> map) {
        String url = MyUtils.getUUMSUrl(USER_ROLE_MAPPING + uri);

        JsonResponse response;
        if (type.equals("json")) {
            response = HttpClient.textBody(url).json(JacksonUtils.obj2json(map)).asBean(JsonResponse.class);
        } else {
            for (Map.Entry<String, Object> e : map.entrySet()) {
                String key = e.getKey();
                String value = e.getValue().toString();
                url += String.format("&%s=%s", key, value);
            }
            response = HttpClient.textBody(url).asBean(JsonResponse.class);
        }

        if (response == null || response.getErrcode() != 0) {
            log.error("远程请求uums失败, 参数: uri: {}, type: {}, map: {}", uri, type, map);
            throw new IllegalStateException("远程请求操作失败");
        }
    }

}
