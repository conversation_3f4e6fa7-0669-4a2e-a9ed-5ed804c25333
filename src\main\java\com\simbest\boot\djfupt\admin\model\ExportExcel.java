package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;

@Data
public class ExportExcel {

    @ExcelVOAttribute(name = "所在单位", column = "A")
    private String belongCom;

    @ExcelVOAttribute(name = "所在部门", column = "B")
    private String belongDept;

    @ExcelVOAttribute(name = "网格名称", column = "C")
    protected String gridName;               //网格名称

    @Column(length = 20)
    @ApiModelProperty(value = "党建指导员姓名")
    @ExcelVOAttribute(name = "党建指导员姓名", column = "D")
    private String trueName;

    @Column(length = 20)
    @ApiModelProperty(value = "OA账号")
    @ExcelVOAttribute(name = "OA账号", column = "E")
    private String userName;



    @ExcelVOAttribute(name = "党建指导员手机号", column = "G")
    @Transient
    private String phone;



    @Transient
    @ExcelVOAttribute(name = "党建指导员所在组织", column = "F")
    private String orgName;



    private String id;

}
