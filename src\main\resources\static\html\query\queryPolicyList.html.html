<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>政策宣讲台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usCaseInfo/queryAllUsCaseInfo", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {
                                title: "单位", field: "GROUP_NUMBER", width: 120, tooltip: true, align: "center"
                            },
                            {
                                title: "应宣讲次数", field: "GROUP_NAME", width: 80, tooltip: true, align: "center"
                            },
                            {
                                title: "已完成宣讲次数", field: "NUMBER_PEOPLE", width: 80, tooltip: true, align: "center"
                            },
                            {
                                title: "完成率", field: "GROUP_APPLY_USER", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {
                                    return '<a class="check">' + value + '</a>'
                                }
                            },
                            {
                                title: "宣讲内容数量", field: "GROUP_BELONG_COMPANY_NAME", width: 80, tooltip: true, align: "center"
                            },
                        ]
                    ]
                }
            };
            loadGrid(pageparam);

            // 查看宣讲完成详情
            $(document).on('click', 'a.check', function () {
                var index = $(this).attr('index');
                var item = $('#taskTable').datagrid('getRows')[index];
                var param = {
                }
                var url = tourl('html/query/policyRate.html', param)
                top.dialogP(url, window.name, '宣讲完成情况', 'groupCheck', true, 'maximized', 'maximized')
            })


            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir +
                    "action/statisticalReport/exportGroupInfo");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });
        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };
    </script>
    <style>
        .date{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">单位</td>
                <td width="200"><input name="belongDepartmentCode" class="easyui-combobox"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'ORG_CODE',
						panelHeight:'200px',
						ischooseall:true,
						textField: 'ORG_NAME',
						editable:false,
						url: 'action/commom/queryOrg'
                        " />
                </td>
                <td width=" 90" align="right">宣讲时间</td>
                <td width="400" class="date">
                    <input id="startDate" name="startDate" type="text" class="easyui-datebox"
                        style="width:calc(50% - 10px);height:32px;" validType="startDateCheck['endDate','startDate']"
                        data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="endDate" name="endDate" type="text" class="easyui-datebox" style="width:calc(50% - 10px);height:32px;"
                        validType="endDateCheck['startDate','endDate']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>
                <td>
                    <a class="btn fl searchtable">
                        <font>查询</font>
                    </a>
                    <a class="btn fl ml10 a_success exporttable ">
                        <font>导出</font>
                    </a>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>