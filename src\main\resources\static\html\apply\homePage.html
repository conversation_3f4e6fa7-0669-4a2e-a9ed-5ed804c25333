<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>省公司管理员首页</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent()
        var now = new Date()
        var startTime = getNow('yyyy-MM-dd', false, now.setDate(1))
        now = now.setMonth(now.getMonth() + 1)
        var endTime = getNow('yyyy-MM-dd', false, new Date(now).setDate(0))
        $(function () {
            $('#startTime').datebox('setValue', startTime)
            $('#endTime').datebox('setValue', endTime)
            var flag  = false
            for (var i = 0; i < web.currentUser.authRoles.length; i++) {
                var item = web.currentUser.authRoles[i];
                // if (item.id == 'djfupt_002') {
                //     $('#superAdmin').show();
                //     $('#wddb').hide();
                //     $('#dbtz').show();
                //     $('#kjrk').width('70%')
                //     getTable()
                //     getInfo()
                //     flag = true
                // } else {
                //     $('#admin').show();
                //     $('#wddb').show();
                //     $('#dbtz').hide();
                //     $('#gzxs').hide();
                //     // processTask(web.appName, web.appHtml);
                // }

                if (item.id == 'djfupt_002') {
                    flag = true
                   
                }
            }

            if(flag){
                $('#superAdmin').show();
                $('#wddb').hide();
                $('#dbtz').show();
                $('#kjrk').width('70%')
                getTable()
                getInfo()
            }else{
                $('#admin').show();
                $('#wddb').show();
                $('#dbtz').hide();
                $('#gzxs').hide();
            }

            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/queryActBusinessStatus/myTaskToDo?source=PC", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [[//列
                        {
                            title: "工单标题", field: "receiptTitle", width: 200, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var th = appNameTH(web.appName, web.appHtml, row.pmInsType);
                                var g = "<span class='audit col_b titleTooltipA' index='" + index + "' ptitle='" + th.type + "' path='" + th.html + "'>" + value + "</span>";
                                return g;
                            }
                        },
                        { title: "创建部门", field: "createOrgName", width: 140, tooltip: true },
                        { title: "创建人", field: "createUserName", width: 100 },
                        {
                            title: "创建时间", field: "createTime", width: 150,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getTimeDate(row.createTime, "yyyy-MM-dd hh:mm:ss");
                            }
                        },//排序sortable: true
                        { title: "已办理人", field: "previousAssistantName", width: 100 },
                        {
                            title: "办理时间", field: "previousAssistantDate", width: 150,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getTimeDate(row.previousAssistantDate, "yyyy-MM-dd hh:mm:ss");
                            }
                        },
                        {
                            title: "当前办理环节", field: "activityInstName", width: 160, tooltip: true,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return row.currentState == "7" ? "已归档" : value;
                            }
                        }
                        //{ title: "当前状态", field: "currentState", width: 100},

                    ]],
                }
            };
            pageparam.listtable.columns[0].push({
                field: "opt", title: "操作", width: 160, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                    var th = appNameTH(web.appName, web.appHtml, row.pmInsType);
                    var g = "<a class='audit' index='" + index + "' title='办理' index='" + index + "' ptitle='" + th.type + "' path='" + th.html + "'>【办理】</a>";
                    return g;
                }
            });
            if(!flag){
                loadGrid(pageparam);
            }

            $(document).on("click", "a.audit,span.audit", function () {
                var $t = $(this);
                var index = $t.attr("index");
                $("#taskTable").datagrid("clearSelections");//取消选择所有当前页中所有的行
                $("#taskTable").datagrid("clearChecked");//取消选择所有当前页中所有的行
                $("#taskTable").datagrid("selectRow", index);//选择一行，行索引从0开始
                var row = $("#taskTable").datagrid("getSelected");//返回第一个被选中的行或如果没有选中的行则返回null
                var url = ($t.attr("path")) + "?type=task&location=" + row.activityDefId + "&processInstId=" + (row.processInstId || '') + "&processDefName=" + row.processDefName + "&workItemId=" + row.workItemId + "&currentState=" + row.currentState + "&pmInsId=" + row.receiptCode + "&usPmInstenceId=" + row.businessKey;
                var ptitle = ($t.attr("ptitle")) + "-" + row.receiptTitle + "-审批";
                top.dialogP(url, 'processTask', ptitle, 'audit', true, "maximized", "maximized", taskListLoad);
            });





            $(document).on('click', 'a.formreset', function () {
                formreset('taskTableQueryForm');
            })
            //导出
            $(".exporttable").on("click", function () {
                $("#searchtableForm").attr("action", web.rootdir + "action/index/importExcel");
                $("#searchtableForm").attr("method", "post");
                $("#searchtableForm").submit();
            });

            $(document).on('click', 'li.cont_item', function () {
                var url = $(this).attr('url');
                top.tabClick(url);
            })

            // 下载
            $(document).on('click', 'a.downloadFile', function () {
                var index = $(this).attr('index');
                var row = $("#taskPolicy").datagrid("getRows")[index];//返回第一个被选中的行或如果没有选中的行则返回null
                var list = []
                $.each(row.usPantchDetailList, function (idx, item) {
                    if (item.fileIds) {
                        list.push(item.fileIds)
                    }
                })
                list = list.join(',')
                if (!list) {
                    top.mesShow("温馨提示", "暂无附件", 2000);
                    return
                }
                var fileId = $(this).attr('fileId');
                $("#applicationForm").attr("action", web.rootdir + "action/index/downLoadZip?fileIds=" + list);
                $("#applicationForm").attr("method", "post");
                $("#applicationForm").submit();
            })

            //详情查看
            $(document).on("click", ".checkAudit", function () {
                var item = $(this);
                var url = item.attr("path");
                top.dialogP(url, window.name, item.attr("ptitle"), 'getCheck', true, 'maximized', 'maximized');
            });

            $(document).on("click", ".doAudit", function () {
                var $t = $(this);
                var index = $t.attr("index");
                $("#taskPolicy").datagrid("clearSelections");//取消选择所有当前页中所有的行
                $("#taskPolicy").datagrid("clearChecked");//取消选择所有当前页中所有的行
                $("#taskPolicy").datagrid("selectRow", index);//选择一行，行索引从0开始
                var row = $("#taskPolicy").datagrid("getSelected").maps;//返回第一个被选中的行或如果没有选中的行则返回null
                var url = ($t.attr("path")) + "?type=task&location=" + row.ACTIVITYDEFID + "&processInstId=" + (row.PROCESS_INST_ID || '') + "&processDefName=" + row.PROCESS_DEF_NAME + "&workItemId=" + row.WORKITEMID + "&currentState=" + row.CURRENT_STATE + "&pmInsId=" + row.RECEIPT_CODE + "&usPmInstenceId=" + row.BUSINESS_KEY;
                var ptitle = ($t.attr("ptitle")) + "-" + row.receiptTitle + "-审批";
                top.dialogP(url, 'processTask', ptitle, 'audit', true, "maximized", "maximized", taskListLoad);
            });
            //刷新页面
            function taskListLoad() {
                $('#taskPolicy').datagrid("reload");
                $('#taskIdea').datagrid("reload");
                $('#taskQuestion').datagrid("reload");
                $('#taskExperience').datagrid("reload");
            };
        });
        //获取四个表格
        function getTable() {
            var taskPolicy = {
                "listtable": {
                    "listname": "#taskPolicy",//table列表的id名称，需加#
                    "querycmd": "action/index/queryPolicyInfo",//table列表的查询命令
                    "queryParams": { startTime: startTime, endTime: endTime },
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns": [],//固定在左侧的列
                    "pagination": false,
                    "columns": [[//列
                        {
                            title: "宣讲时间", field: "policyTime", width: 60, tooltip: true, align: "center", formatter: function (value, row, index) {
                                if (value) {
                                    return value
                                } else {
                                    return '暂无'
                                }
                            }
                        },
                        {
                            title: "宣讲地点", field: "policyAddress", width: 60, tooltip: true, align: "center", formatter: function (value, row, index) {
                                if (value) {
                                    return value
                                } else {
                                    return '暂无'
                                }
                            }
                        },
                        {
                            title: "宣讲清单", field: "usPantchDetailList", width: 120, tooltip: true, align: "center", formatter: function (value, row, index) {
                                var g = ''
                                $.each(row.usPantchDetailList, function (idx, item) {
                                    g += '<p>' + item.lectureItems + '</p>'
                                })
                                return g
                            }
                        },
                        {
                            title: "支撑材料附件", field: "usPantchDetailList2", width: 40, tooltip: true, align: "center", formatter: function (value, row, index) {
                                var g = "<a href='#' class='downloadFile' index=" + index + ">【一键下载】</a>"
                                return g
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 40, rowspan: 1, align: "center", formatter: function (value, row, index) {
                                var g = ''
                                //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                if (row.maps.WKCURSTATE == 12) {
                                    var row = row.maps
                                    g = "<a class='checkAudit' index='" + index + "' title='查看' ptitle='" + row.RECEIPT_TITLE + "' pmInsId='" + row.RECEIPT_CODE + "' " +
                                        "path='html/change/changePolicy.html?workFlag=join&type=join&location=" + row.ACTIVITYDEFID + "&source=PC&processInstId=" +
                                        row.PROCESS_INST_ID + "&pmInsId=" + row.RECEIPT_CODE + "&previousAssistant='" + row.PREVIOUS_ASSISTANT + " >【查看】</a>";
                                } else if (row.maps.WKCURSTATE == 10) {
                                    var th = appNameTH(web.appName, web.appHtml, 'C');
                                    g = "<a class='doAudit' index='" + index + "' title='办理' index='" + index + "' ptitle='" + th.type + "' path='" + th.html + "'>【办理】</a>";
                                }
                                return g
                            }
                        }
                    ]]
                }
            };

            var taskIdea = {
                "listtable": {
                    "listname": "#taskIdea",//table列表的id名称，需加#
                    "querycmd": "action/usRecordFill/findAllUsRecordFill", //table列表的查询命令
                    "queryParams": { page: 1, rows: 999, startTime: startTime, endTime: endTime, talkTime: startTime + '至' + endTime },
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "pagination": false,
                    "columns": [[//列
                        { title: "谈话或走访时间", field: "talkTime", width: 80, tooltip: true, align: "center" },
                        { title: "谈话或走访地点", field: "talkAddress", width: 80, tooltip: true, align: "center" },
                        { title: "谈话或走访对象", field: "configTime", width: 120, tooltip: true, align: "center" },
                        { title: "谈话主题或走访事项", field: "configDeptName", width: 200, tooltip: true, align: "center" },
                        { title: "座谈人数", field: "numberOfPanel", width: 50, tooltip: true, align: "center" },
                    ]]
                }
            };

            var taskQuestion = {
                "listtable": {
                    "listname": "#taskQuestion",//table列表的id名称，需加#
                    "querycmd": "action/usProblemInfo/queryAllUsCaseInfo",//table列表的查询命令
                    "queryParams": { page: 1, rows: 999, startDate: startTime, endDate: endTime },
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns": [],//固定在左侧的列
                    "pagination": false,
                    "columns": [[//列
                        {
                            title: "是否已解决", field: "currentState", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {
                                if (value == '7') {
                                    return '是'
                                } else {
                                    return '否'
                                }
                            }
                        },
                        { title: "反馈时间", field: "createdTime", width: 80, tooltip: true, align: "center" },
                        { title: "问题名称", field: "problemName", width: 120, tooltip: true, align: "center" },
                        { title: "问题具体描述", field: "problemDescribe", width: 120, tooltip: true, align: "center" },
                        { title: "协调推动情况", field: "problemFeedback", width: 120, tooltip: true, align: "center" },
                    ]]
                }
            };

            var taskExperience = {
                "listtable": {
                    "listname": "#taskExperience",//table列表的id名称，需加#
                    "querycmd": "action/usCaseInfo/findAllApplication",//table列表的查询命令
                    "queryParams": { page: 1, rows: 999, startDate: startTime, endDate: endTime },
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns": [],//固定在左侧的列
                    "pagination": false,
                    "columns": [[//列
                        {
                            title: "上报时间", field: "createdTime", width: 80, tooltip: true, align: "center",
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getNow("yyyy-MM-dd", false, value);
                            }
                        },
                        { title: "优秀案例名称", field: "problemName", width: 200, tooltip: true, align: "center" },
                        { title: "优秀案例简述", field: "problemDescribe", width: 200, tooltip: true, align: "center" },
                    ]]
                }
            };

            loadGrid(taskPolicy);
            loadGrid(taskIdea);
            loadGrid(taskQuestion);
            loadGrid(taskExperience);
        }
        // 获取待办通知
        function getInfo() {
            ajaxgeneral({
                url: "action/index/todoItems",
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    var data = res.data.slice(0, 11)
                    $.each(data, function (index, item) {
                        $('#dbtz ul').append('<li>' + item + '</li>')
                    })
                }
            });
        }
        //政策宣讲历史
        $(document).on('click', 'a.zcxjHis', function () {
            var param = {
                fromIfr: 'policHis',
            }
            var url = tourl('html/query/queryPolicyList.html', param)
            var title = '政策宣讲台账'
            top.dialogP(url, window.name, title, 'policHis', true, 'maximized', 'maximized')
        });
        //思政纪实历史
        $(document).on('click', 'a.szjstbHis', function () {
            var param = {
                fromIfr: 'ideaHis',
            }
            var url = tourl('html/query/queryIdeaList.html', param)
            var title = '思政纪实台账'
            top.dialogP(url, window.name, title, 'ideaHis', true, 'maximized', 'maximized')
        });
        //思政纪实填报
        $(document).on('click', 'a.szjstb', function () {
            var param = {
                fromIfr: 'idea',
            }
            var url = tourl('html/change/changeIdea.html', param)
            var title = '思政纪实填报'
            top.dialogP(url, window.name, title, 'idea', true, 'maximized', 'maximized')
        });
        //问题上报历史
        $(document).on('click', 'a.wtsbHis', function () {
            var param = {
                fromIfr: 'questionHis',
            }
            var url = tourl('html/query/queryQuestionList.html', param)
            var title = '问题协调台账'
            top.dialogP(url, window.name, title, 'questionHis', true, 'maximized', 'maximized')
        });
        //问题上报
        $(document).on('click', 'a.wtsb', function () {
            var param = {
                fromIfr: 'question',
            }
            var url = tourl('html/change/changeQuestion.html', param)
            var title = '问题上报'
            top.dialogP(url, window.name, title, 'question', true, 'maximized', 'maximized')
        });
        //优秀案例上报历史
        $(document).on('click', 'a.yxansbHis', function () {
            var param = {
                fromIfr: 'experienceHis',
            }
            var url = tourl('html/query/queryExperienceList.html', param)
            var title = '经验推广台账'
            top.dialogP(url, window.name, title, 'experienceHis', true, 'maximized', 'maximized')
        });
        //优秀案例上报
        $(document).on('click', 'a.yxansb', function () {
            var param = {
                fromIfr: 'experience',
            }
            var url = tourl('html/change/changeExperience.html', param)
            var title = '优秀案例上报'
            top.dialogP(url, window.name, title, 'experience', true, 'maximized', 'maximized')
        });
        //条件搜索
        $(document).on("click", ".searchBtn", function () {
            if (!formValidate("searchtableForm")) {
                return
            }
            var data = getFormValue('searchtableForm')
            startTime = data.startTime
            endTime = data.endTime
            $("#taskPolicy").datagrid("options").queryParams.startTime = startTime;
            $("#taskPolicy").datagrid("options").queryParams.endTime = endTime;
            $("#taskIdea").datagrid("options").queryParams.startTime = startTime;
            $("#taskIdea").datagrid("options").queryParams.endTime = endTime;
            $("#taskIdea").datagrid("options").queryParams.talkTime = startTime + '至' + endTime;
            $("#taskQuestion").datagrid("options").queryParams.startDate = startTime;
            $("#taskQuestion").datagrid("options").queryParams.endDate = endTime;
            $("#taskExperience").datagrid("options").queryParams.startDate = startTime;
            $("#taskExperience").datagrid("options").queryParams.endDate = endTime;
            $('#taskPolicy').datagrid("reload");
            $('#taskIdea').datagrid("reload");
            $('#taskQuestion').datagrid("reload");
            $('#taskExperience').datagrid("reload");
        });
        
        //重置
        $(document).on("click", ".formreset", function () {
            formreset('taskTableQueryForm')
            $('.searchtable').trigger('click')
        });
    </script>
    <style>
        .panel_title {
            font-weight: 700;
            font-size: 16px;
            height: 52px;
            padding: 8px 14px;
            border: 1px solid rgba(228, 228, 228, 1);
            border-left: 6px solid rgba(60, 185, 252, 1);
            background-color: rgba(243, 243, 243, 1);
        }

        .panel_content {
            padding: 20px;
        }

        .flex {
            padding: 0 3%;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .panel_box {
            width: 20%;
            min-width: 120px;
            padding: 30px 0px;
        }

        .panel_box_two {
            width: 25%;
            min-width: 120px;
            padding: 30px 0px;
        }

        .cont_item {
            width: 112px;
            margin: auto;
        }

        .cont_item:hover {
            cursor: pointer;
        }

        .cont_icon {
            width: 102px;
            height: 102px;
            border-radius: 10px;
            line-height: 102px;
            text-align: center;
        }

        .cont_img {
            width: 60px;
            height: 60px;
        }

        .cont_text {
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
        }

        #dbtz ul {
            padding: 20px;
        }

        #dbtz li {
            line-height: 30px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        #gzxs .fTitle {
            height: 40px;
            line-height: 40px;
            margin-top: 10px;
        }

        #gzxs .fTitle div:nth-of-type(1) {
            width: 300px;
            height: 30px;
            line-height: 30px;
            border-left: 4px solid rgba(60, 185, 252, 1);
            font-size: 16px;
            font-weight: 700;
            padding-left: 5px;
        }

        .datagrid .panel-body {
            width: 100% !important;
        }

        .datagrid-view2 .datagrid-body .datagrid-btable{
            width: 100%;
        }


    </style>
</head>

<body class="body_page">
    <div class="panel fl mr5" id="dbtz" style="width:calc(30% - 39px);">
        <p class="panel_title">
            <img src="../../images/homePage/u199.png" alt="" width="36px" height="36px">
            <font class="ml10">待办事项通知</font>
        </p>
        <ul>
        </ul>
    </div>
    <div class="panel" id="kjrk">
        <p class="panel_title">
            <img src="../../images/homePage/u199.png" alt="" width="36px" height="36px">
            <font class="ml10">快捷入口</font>
        </p>
        <ul class="flex" id="superAdmin" style="display:none">
            <div class="panel_box_two">
                <li class="cont_item" url="processTask">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(0, 232, 190, 1) 0%, rgba(0, 188, 148, 1) 100%);">
                        <img src="../../images/homePage/wddb.png" class="cont_img">
                    </div>
                    <p class="cont_text">我的待办</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="changeIdea">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(250, 213, 38, 1) 0%, rgba(251, 175, 5, 1) 100%);">
                        <img src="../../images/homePage/u344.png" class="cont_img">
                    </div>
                    <p class="cont_text">思政纪实填报</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="changeQuestion">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(93, 194, 247, 1) 0%, rgba(68, 135, 235, 1) 100%);">
                        <img src="../../images/homePage/wtsb.png" class="cont_img">
                    </div>
                    <p class="cont_text">问题上报</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="changeExperience">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(249, 160, 88, 1) 0%, rgba(253, 106, 45, 1) 100%);">
                        <img src="../../images/homePage/yxalsb.png" class="cont_img">
                    </div>
                    <p class="cont_text">优秀案例上报</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="queryPolicyList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(93, 195, 247, 1) 0%, rgba(68, 135, 235, 1) 100%);">
                        <img src="../../images/homePage/zcxjtz.png" class="cont_img">
                    </div>
                    <p class="cont_text">政策宣讲台账</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="queryIdeaList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(249, 158, 87, 1) 0%, rgba(254, 110, 49, 1) 100%);">
                        <img src="../../images/homePage/szjstz.png" class="cont_img">
                    </div>
                    <p class="cont_text">思政纪实台账</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="queryQuestionList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(250, 208, 34, 1) 0%, rgba(251, 180, 9, 1) 100%);">
                        <img src="../../images/homePage/u347.png" class="cont_img">
                    </div>
                    <p class="cont_text">问题协调台账</p>
                </li>
            </div>
            <div class="panel_box_two">
                <li class="cont_item" url="queryExperienceList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(93, 193, 247, 1) 0%, rgba(67, 133, 235, 1) 100%);">
                        <img src="../../images/homePage/u348.png" class="cont_img">
                    </div>
                    <p class="cont_text">优秀案例台账</p>
                </li>
            </div>
        </ul>
        <ul class="flex" id="admin" style="display:none">
            <div class="panel_box">
                <li class="cont_item" url="changePolicy">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(0, 232, 190, 1) 0%, rgba(0, 188, 148, 1) 100%);">
                        <img src="../../images/homePage/u323.png" class="cont_img">
                    </div>
                    <p class="cont_text">政策宣讲下达</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryIdeaConfig">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(250, 219, 43, 1) 0%, rgba(251, 171, 1, 1) 100%);;">
                        <img src="../../images/homePage/u343.png" class="cont_img">
                    </div>
                    <p class="cont_text">思政纪实配置</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="changeIdea">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(96, 201, 248, 1) 0%, rgba(65, 128, 234, 1) 100%);">
                        <img src="../../images/homePage/u344.png" class="cont_img">
                    </div>
                    <p class="cont_text">思政纪实填报</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryPolicyList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(249, 165, 92, 1) 0%, rgba(254, 99, 40, 1) 100%)">
                        <img src="../../images/homePage/u345.png" class="cont_img">
                    </div>
                    <p class="cont_text">政策宣讲台账</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryIdeaList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(96, 198, 249, 1) 0%, rgba(66, 131, 233, 1) 100%);">
                        <img src="../../images/homePage/u346.png" class="cont_img">
                    </div>
                    <p class="cont_text">政策纪实台账</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryIdeaList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(250, 156, 85, 1) 0%, rgba(254, 105, 45, 1) 100%);">
                        <img src="../../images/homePage/u347.png" class="cont_img">
                    </div>
                    <p class="cont_text">问题协调台账</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryExperienceList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(93, 193, 247, 1) 0%, rgba(67, 133, 235, 1) 100%);">
                        <img src="../../images/homePage/u348.png" class="cont_img">
                    </div>
                    <p class="cont_text">优秀案例台账</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryAdminList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(250, 213, 38, 1) 0%, rgba(251, 175, 5, 1) 100%);">
                        <img src="../../images/homePage/u349.png" class="cont_img">
                    </div>
                    <p class="cont_text">管理员管理</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="queryInstructorList">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(92, 192, 246, 1) 0%, rgba(68, 136, 235, 1) 100%);">
                        <img src="../../images/homePage/u350.png" class="cont_img">
                    </div>
                    <p class="cont_text">党建指导员管理</p>
                </li>
            </div>
            <div class="panel_box">
                <li class="cont_item" url="sendMsg">
                    <div class="cont_icon"
                        style="background: linear-gradient(135deg, rgba(0, 232, 190, 1) 0%, rgba(0, 188, 148, 1) 100%);">
                        <img src="../../images/homePage/u351.png" class="cont_img">
                    </div>
                    <p class="cont_text">短信催办</p>
                </li>
            </div>
        </ul>
    </div>
    <div class="panel" id="wddb">
        <p class="panel_title">
            <img src="../../images/homePage/u199.png" alt="" width="36px" height="36px">
            <font class="ml10">我的待办</font>
        </p>
        <div class="panel_content">
            <form id="taskTableQueryForm">
                <table border="0" cellpadding="0" cellspacing="6" width="100%">
                    <tr>
                        <td width="90" align="right">工单编号：</td>
                        <td width="150"><input name="title" type="text" value="" /></td>
                        <td width="90" align="right">工单标题：</td>
                        <td width="150"><input name="title" type="text" value="" /></td>
                        <td>
                            <div class="w100">
                                <a class="btn ml10 searchtable">
                                    <font>查询</font>
                                </a>
                                <a class="btn ml10 formreset">
                                    <font>重置</font>
                                </a>
                            </div>
                        </td>
                    </tr>
                </table>
            </form>
            <!--table-->
            <div class="taskTable">
                <table id="taskTable"></table>
            </div>
        </div>
    </div>
    <div id="gzxs">
        <p class="panel_title">
            <img src="../../images/homePage/u199.png" alt="" width="36px" height="36px">
            <font class="ml10">党建指导员工作写实</font>
        </p>
        <div class="panel_content">
            <form id="searchtableForm">
                <table border="0" cellpadding="0" cellspacing="6" width="100%">
                    <tr>
                        <td width="90" align="right">时间段：</td>
                        <td width="600">
                            <input id="startTime" name="startTime" type="text" class="easyui-datebox"
                                validType="startDateCheck['endTime','startTime']"
                                data-options="editable:false,required:true" style="width:45%;height:32px;" />
                            至
                            <input id="endTime" name="endTime" type="text" class="easyui-datebox"
                                validType="endDateCheck['startTime','endTime']"
                                data-options="editable:false,required:true" style="width:45%;height:32px;" />
                        </td>
                        <td>
                            <div class="w100">
                                <a class="btn ml10 searchBtn">
                                    <font>查询</font>
                                </a>
                                <a class="btn fl ml10 a_success exporttable ">
                                    <font>导出</font>
                                </a>
                            </div>
                        </td>
                    </tr>
                </table>
            </form>
            <div class="fTitle">
                <div class="fl">传递党的政策要求</div>
                <div>
                    <a class="btn fl mr10 zcxjHis">
                        <font>历史查询</font>
                    </a>
                </div>
            </div>
            <div class="taskPolicy">
                <table id="taskPolicy"></table>
            </div>
            <div class="fTitle">
                <div class="fl">扎实开展思政工作</div>
                <div>
                    <a class="btn fl mr10 szjstbHis">
                        <font>历史查询</font>
                    </a>
                    <a class="btn ml10 szjstb">
                        <font>思政纪实填报</font>
                    </a>
                </div>
            </div>
            <div class="taskIdea">
                <table id="taskIdea"></table>
            </div>
            <div class="fTitle">
                <div class="fl">协调推动问题解决</div>
                <div>
                    <a class="btn fl mr10 wtsbHis">
                        <font>历史查询</font>
                    </a>
                    <a class="btn ml10 wtsb">
                        <font>问题上报</font>
                    </a>
                </div>
            </div>
            <div class="taskQuestion">
                <table id="taskQuestion"></table>
            </div>
            <div class="fTitle">
                <div class="fl">积极推广经验做法</div>
                <div>
                    <a class="btn fl mr10 yxansbHis">
                        <font>历史查询</font>
                    </a>
                    <a class="btn ml10 yxansb ">
                        <font>优秀案例上报</font>
                    </a>
                </div>
            </div>
            <div class="taskExperience">
                <table id="taskExperience"></table>
            </div>
        </div>
        <form id="applicationForm"></form>
    </div>
</body>

</html>