package com.simbest.boot.djfupt.assiatant.service.impl;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.assiatant.model.UsHistoryRecord;
import com.simbest.boot.djfupt.assiatant.repository.UsHistoryRecordRepository;
import com.simbest.boot.djfupt.assiatant.service.IUsHistoryRecordService;

import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FormatTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class UsHistoryRecordServiceImpl extends LogicService<UsHistoryRecord, String> implements IUsHistoryRecordService {
    private UsHistoryRecordRepository repository;

    @Autowired
    public UsHistoryRecordServiceImpl(UsHistoryRecordRepository repository) {
        super(repository);
        this.repository = repository;
    }
    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Override
    public List<Map<String, Object>> getAllNoPage(String source, String currentUserCode, Map<String, String> map) {
        if (StrUtil.equals(source, Constants.MOBILE) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        String userName = SecurityUtils.getCurrentUserName();
        Map<String, Object> sqlMap = Maps.newHashMap();
        String content = map.get("content");
        try {
            StringBuffer allSql = new StringBuffer(" select tt.*\n" +
                    "  from us_history_record tt\n" +
                    " where tt.id in (select min(t.id)\n" +
                    "                   from us_history_record t\n" +
                    "                  where t.enabled = 1\n" +
                    "                    and t.type = '1'\n" +
                    "                    and t.user_name = :userName ");
            sqlMap.put("userName",userName);
            if (StrUtil.isNotEmpty(content)) {
                allSql.append(" and t.text_content like concat(concat('%', :content), '%') ");
                sqlMap.put("content", content);
            }
            allSql.append(" group by t.pm_ins_id) order by tt.created_time asc ");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(allSql.toString(), sqlMap);
            list = FormatTool.formatConversion(list);
            return list;
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getAllDetailNoPage(String source, String currentUserCode, String pmInsId) {
        if (StrUtil.equals(source, Constants.MOBILE) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        String userName = SecurityUtils.getCurrentUserName();
        Map<String, Object> sqlMap = Maps.newHashMap();
        try {
            StringBuffer selectSql = new StringBuffer(" select t.* " +
                    "  from us_history_record t where t.enabled = 1 ");
            StringBuffer orderBySql = new StringBuffer(" order by t.created_time asc ");
            selectSql.append(" and t.pm_ins_id = :pmInsId ");
            sqlMap.put("pmInsId", pmInsId);
            selectSql.append(" and t.user_name = :userName ");
            sqlMap.put("userName", userName);
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(selectSql.append(orderBySql).toString(), sqlMap);
            list = FormatTool.formatConversion(list);
            return list;
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return null;
    }

    @Override
    public JsonResponse clearByPmInsId(String pmInsId) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        repository.clearByPmInsId(pmInsId,currentUserName);
        return JsonResponse.defaultSuccessResponse();
    }

    @Override
    public JsonResponse clearAll() {
        String currentUserName = SecurityUtils.getCurrentUserName();
        repository.clearAll(currentUserName);
        return JsonResponse.defaultSuccessResponse();
    }
}

