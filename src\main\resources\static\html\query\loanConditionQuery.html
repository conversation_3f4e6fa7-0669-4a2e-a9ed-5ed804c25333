<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>工单查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        dictionary.applyType="E|员工借调申请,F|员工续借申请";
        var myinfo={};
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#taskTable",//table列表的id名称，需加#
                    "querycmd":"action/loanUser/conditionQuery?source=PC",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "申请部门", field: "belongDepartmentName", width: 30,tooltip:true },
                        { title: "借调人员", field: "trueName", width: 20,tooltip:true},
                        { title: "被借调部门", field: "inCompany", width: 30,tooltip:true },
                        { title: "借调类型", field: "applyType", width: 30 ,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getvals("applyType",value);
                            }},
                        { title: "借调开始时间", field: "startTime", width: 30 },
                        { title: "借调结束时间", field: "endTime", width: 30 },
                        { title: "附件", field: "userAttachment", width: 40,
                            formatter: function (value, row,index){
                                if ( value != null && value[0] ){
                                        var g = "<p> <a target='_blank' title='单击预览' style='float:left' href='/" +web.appCode+ value[0].openUrl + "'>" + value[0].fileName + "</a> " +
                                            "<a target='_blank' title='下载' class='col_r fr ml15' style='display: block;float:right;width:40px' href='/" +web.appCode+ value[0].downLoadUrl + "'><i class='iconfont'>&#xe64b;</i></a> </p>";
                                        return g;
                                }
                            }
                        }
                    ] ]
                }
            };
            loadGrid(pageparam);
            $(document).on("click","a.audit",function(){
                var $t=$(this);
                var index=$t.attr("index");
                var path=$t.attr("path");
                top.dialogP(path, 'comprehensive', $t.attr("ptitle") + '查看', 'audit', true, $(window).width() + 200, $(window).height() + 80, listLoad);
            });
            //选择组织
            $(".chooseOrgs").on("click",function(){
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                var href={"multi":"0","name":"chooseCompanyVal"};//chooseOrgsVal
                if($("#taskTableQueryForm .chooseOrgs").val()!=""){
                    var datas=[];
                    var names=$("#taskTableQueryForm .chooseOrgs").val().split(",");
                    var codes=$("#taskTableQueryForm input[name=belongOrgCode]").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseOrgsVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                }
                /*var url=tourl('html/choose/chooseOrgs.html',href);*/
                var url = tourl('html/choose/chooseCompany.html', href);
                top.dialogP(url,'loanConditionQuery','选择组织','chooseOrgs',false,'800');//chooseOrgs
            });
        });
        //刷新页面
        function listLoad(){
            $("#taskTable").datagrid("reload");
        };


        //选择调入单位回调
        window.chooseCompany = function (data) {
            alert();
            var name = [],ids = [],vals=[];
            for (var i in data.data) {
                name.push(data.data[i].text);
                ids.push(data.data[i].orgCode);
            }
            $("#belongDepartmentName").val(name.join(","));
        };

        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $("#taskTableQueryForm input[name=belongOrgCode]").val(codes.join(","));
        };
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="taskTableQueryForm" onsubmit="">
    <input name="belongOrgCode" type="hidden"/>
    <input name="styleDictValue" type="hidden"/>
    <input name="companyTypeDictValue" type="hidden"/>
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="100" align="right">申请部门：</td>
            <td width="150">
                <input class="chooseOrgs" id ="belongDepartmentName" name="belongDepartmentName" type="text" value="" />
            </td>
            <td width="100" align="right">借调人员：</td>
            <td width="150">
                <input  name="trueName" type="text" value="" />
            </td>

        </tr>
        <tr>
            <td width="100" align="right">调动类型：</td>
            <td width="150">
                <input th:class="applyType" name="applyType" class="easyui-combobox"  style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       queryParams:{'dictType':'processType1'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            </td>
            <td ></td>
            <td >
            </td>

            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="taskTable"><table id="taskTable"></table></div>
</body>
</html>
