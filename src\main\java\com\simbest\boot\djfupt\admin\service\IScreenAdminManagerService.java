package com.simbest.boot.djfupt.admin.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.admin.model.ScreenAdminManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface IScreenAdminManagerService extends ILogicService<ScreenAdminManager, String> {


    Page<ScreenAdminManager> findAllInfo(ScreenAdminManager o, Pageable pageable);

    @Transactional(rollbackFor = Exception.class)
    List<ScreenAdminManager> saveAllInfo(String accounts);
}
