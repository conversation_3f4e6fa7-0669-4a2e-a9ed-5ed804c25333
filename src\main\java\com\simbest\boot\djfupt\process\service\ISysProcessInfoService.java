package com.simbest.boot.djfupt.process.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.process.model.SysProcessInfo;

/**
 * <strong>Title :ISysProcessInfoService </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/22</strong><br>
 * <strong>Modify on : 2022/6/22</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

public interface ISysProcessInfoService extends ILogicService<SysProcessInfo, String> {

    /**
     * 根据连线获取目标环节信息
     * @param processDefId          流程编码
     * @param fromActivityId      来源环节
     * @param outcome               决策项条件
     * @return
     */
    SysProcessInfo findByOutcome(String processDefId , String fromActivityId, String outcome);

    /**
     *
     * 根据连线获取目标环节信息
     * @param processDefId          流程编码
     * @param fromActivityId      来源环节
     * @param outcome               决策项条件
     * @return
     */
    SysProcessInfo findSysProcessInfo(String processDefId, String fromActivityId, String outcome);
}
