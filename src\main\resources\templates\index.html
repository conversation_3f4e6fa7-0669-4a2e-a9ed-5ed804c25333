<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>
	<title>党建指导员支撑服务平台</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css}" rel="stylesheet"/>
    -->
	<link href="../fonts/iconfont/iconfont.css" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css"
		th:href="@{http://************:8088/simbestui/css/public.css}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/index.css"
		th:href="@{http://************:8088/simbestui/css/index.css}" rel="stylesheet" />
	<script src="http://************:8088/simbestui/js/jquery.min.js"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.calendar.js"
		th:src="@{http://************:8088/simbestui/js/jquery.calendar.js}" type="text/javascript"></script>
	<script src="../js/jquery.config.js" th:src="@{/js/jquery.config.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js}" type="text/javascript"></script>
	<style>
		/* ul.nav1 li a font {
			display: block;
		} */
		.dialog-button {
			text-align: center !important;
		}
		.dialog-button .l-btn {
			margin-left: 30px !important;
		}

		ul.nav1 li ul.nav2 li ul.nav3{
			background-color: #fff;
		}
		ul.nav1 li ul.nav2 li ul.nav3 li a{
			padding: 0px 0px 0px 8px;
		}
	</style>
</head>

<body>
	<!--top-->
	<div class="top">
		<div class="top_left">
			<div class="top_left_top"><a href=""><img class="fl"
						src="http://************:8088/simbestui/images/logo_all.png"
						th:src="@{http://************:8088/simbestui/images/logo_all.png}" /></a>
				<h1 class="fl">党建指导员支撑服务平台</h1>
			</div>
		</div>
		<div class="top_right">
			<!-- <img id="logo" src="${ctx}/images/users_images.png" />-->
			<strong>您好：<font class="nickname" th:text="${indexModel.iuser.nickname}">aa</font>
				<font class="indexDate"></font>
			</strong>
			<input id="userName" th:value="${indexModel.iuser.username}" type="hidden" /><input id="phone"
				th:value="${indexModel.iuser.preferredMobile}" type="hidden" />
			<!--  <i class="iconfont" id="setup" path="setup.html" title="设置">&#xe62f;</i><span></span>-->
			<i class="iconfont" id="home" th:attr="path=@{/html/process/processTask.html}"
				title="首页">&#xe691;</i><span></span>
			<!--<i class="iconfont" id="passwordMod" title="修改密码">&#xe682;</i><span></span>-->
			<i class="iconfont" id="needHelpD" title="帮助文档">&#xe60f;</i><span></span>
			<!--<i class="iconfont" id="password" th:attr="path=@{/html/updatepassword.html}" title="修改密码">&#xe682;</i><span></span>
        <i class="iconfont" id="help" th:attr="path=@{/html/help/help.html}" title="帮助文档">&#xe60f;</i><span></span>-->
			<a th:href="@{/logout}" title="退出"><i class="iconfont" id="exit">&#xe68a;</i></a>
		</div>
	</div>
	<!--center-->
	<div class="center">
		<!--left-->
		<div class="left">
			<div class="left_menu">
			</div>
		</div>
		<!--right-->
		<div class="right">
			<div class="right_tab"><a id="li_home" class="a_hover"><i class="fl iconfont">&#xe691;</i>
					<font>首页</font>
				</a></div>
			<div>
				<iframe id="li_home_if" name="welcome" class="iframe_first" src="/html/process/processTask.html"
					th:src="@{/html/process/processTask.html}" frameborder="0" scrolling="auto"></iframe>
			</div>
			<!-- <div>
			<iframe id="li_home_if" name="welcome" class="iframe_first" src="/html/process/processTask.html" th:src="@{/html/process/processTask.html}" frameborder="0" scrolling="auto"></iframe>
		</div> -->
		</div>
	</div>
	<script th:inline="javascript">
		var ih;
		$(function () {
			$(".indexDate").html(getNow("&nbsp;&nbsp;yyyy年MM月dd日&nbsp;&nbsp;星期weekday", true) + "&nbsp;&nbsp;农历&nbsp;&nbsp;" + showCal() + "&nbsp;&nbsp;");
			ajaxgeneral({
				url: "uums/sys/userinfo/findPermissionByAppUser?appcode=" + web.appCode + "&username=" + [[${ indexModel.iuser.username }]],
				success: function (data) {
					if (data.data) {
						var datai = data.data.sort(function (a, b) {
							return (a.displayOrder > b.displayOrder) ? 1 : -1;
						});
						var datas = toTreeData(datai, "id", "parentId", "id,parentId,description,url,icon");

						var dataNew = []
						for(var i in datas){
							if(datas[i].url == 'html/dataIndex/VisitorAnalysis.html'  || datas[i].url == 'html/dataIndex/adminManagement.html'){

							}else{
								dataNew.push(datas[i])
							}
						}

						var htmls = menuH(dataNew, 1);
						$(".left_menu").html(htmls);
					}
				}
			});
			//帮助文档
			$("#needHelpD").on("click", function () {
				var url = "html/help/help.html";
				top.dialogP(url, 'help', "帮助文档", 'helpD', true, 900, 500);
			});
			//修改密码
			// $("#passwordMod").on("click",function(){
			//     var url="html/updatepassword.html";
			//     top.dialogP(url,'updatepassword',"修改密码",'password',false,600,350);
			// });
			//打开首页计算高度
			$(".right").css("width", $(window).width() - 218);
			calculateHeight();
			$(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));

			// $(document).on("click", ".nav1 > li > a", function (e) {
			// 	var $ul = $(this).siblings();
			// 	if ($(this).hasClass('a_active')) {
			// 		$ul.slideUp(600, function () {
			// 			$ul.hide();
			// 		});
			// 		$(this).removeClass("a_hover");
			// 		$(this).removeClass("a_active");
			// 		$(this).children("i.down").html("&#xe673;");
			// 	} else {
			// 		$ul.slideDown(600, function () {
			// 			$ul.show();
			// 		});
			// 		$(this).addClass("a_hover a_active");
			// 		$(this).children("i.down").html("&#xe674;");
			// 	}
			// })

			//菜单事件
			$(document).on("click", ".nav1 a", function (e) {
				// 重置布局位置，防止偏移
				resetLayout();

				var path = $(this).attr("path");
				//为空表示有多级菜单
				var $sib = $(this).parent().siblings();
				$(".nav1 a").removeClass("a_active");
				$sib.children("a").removeClass("a_hover").children("i.down").html("&#xe673;");
				$sib.children("ul").hide();
				$(this).addClass("a_active a_hover");
				if (path != "") {
					$(this).parents("ul").prev("a").addClass("a_hover");
					if ($("#li_" + $(this).attr("id")).length > 0) {
						$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
						$("#li_" + $(this).attr("id") + "_if").show().siblings().hide();
						$(".right_tab a i.fr").removeClass("i_color");
						$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");

						$("#li_" + $(this).attr("id")).insertAfter("#li_home");
						// 只有当iframe不存在或src为空时才重新加载
						var $iframe = $("#li_" + $(this).attr("id") + "_if");
						if ($iframe.length === 0 || !$iframe.attr("src")) {
							var tourl = path + (path.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
							$iframe.insertAfter("#li_home_if").attr("src", tourl);
						} else {
							$iframe.insertAfter("#li_home_if");
						}
					}
					else {
						$(".right iframe").hide();
						//IE6、IE7样式
						if ($.support.msie) {//($.browser.msie && ($.browser.version == "6.0") && !$.support.style) || ($.browser.msie && ($.browser.version == "7.0"))
							$("<a id='li_" + $(this).attr("id") + "' class='a_hover'><i class='iconfont fr i_color'>&#xe63e;</i>" + $(this).html() + "</a>").insertAfter("#li_home");
							$("#li_" + $(this).attr("id")).css("width", ($(this).children("font").text().length) * 18 + 15);
						} else {
							$("<a id='li_" + $(this).attr("id") + "' class='a_hover'>" + $(this).html() + "<i class='iconfont fr i_color'>&#xe63e;</i></a>").insertAfter("#li_home");
						}
						$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
						$(".right_tab a i.fr").removeClass("i_color");
						$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");
						var url = $(this).attr("path");
						var tourl = url + (url.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
						$("<iframe name='" + $(this).attr("id") + "' id='li_" + $(this).attr("id") + "_if' style='height:" + ih + "px;' src='" + tourl + "' frameborder='0'></iframe>").insertAfter("#li_home_if");
						//$("#rightiframe").attr("src","${ctx}/" + tourl);
					}
				} else {
					$(this).children("i.down").html("&#xe674;");
					var $nextli = $(this).next("ul").children("li");
					$nextli.children("ul").hide();
					$nextli.children("a").removeClass("a_hover").children("i.down").html("&#xe674;");
					$next = $(this).next("ul");
					$next.slideDown(600, function () {
						$next.show();
					});
				}
				e.stopPropagation();
			});



			//点击设置和修改密码
			$(document).on("click", ".top_right i", function (e) {
				if ($(this).attr("path") != null) {
					if ($("#li_" + $(this).attr("id")).length > 0) {
						$("#li_" + $(this).attr("id")).trigger("click");
						$("#li_" + $(this).attr("id")).insertAfter("#li_home");

						// 只有当iframe不存在或src为空时才重新加载
						var $iframe = $("#li_" + $(this).attr("id") + "_if");
						if ($iframe.length === 0 || !$iframe.attr("src")) {
							var path = $(this).attr("path");
							var tourl = path + (path.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
							$iframe.insertAfter("#li_home_if").attr("src", tourl);
						} else {
							$iframe.insertAfter("#li_home_if");
						}
					} else {
						$(".right_nr iframe").hide();
						//IE6、IE7样式
						if ($.support.msie) {//($.browser.msie && ($.browser.version == "6.0") && !$.support.style) || ($.browser.msie && ($.browser.version == "7.0"))
							$("<a id='li_" + $(this).attr("id") + "' class='a_hover'><i class='iconfont fr i_color'>&#xe63e;</i>" + $(this).attr("title") + "</a>").insertAfter("#li_home");
							$("#li_" + $(this).attr("id")).css("width", ($(this).attr("title").length) * 18 + 15);
						} else {
							$("<a id='li_" + $(this).attr("id") + "' class='a_hover'>" + $(this).attr("title") + "<i class='iconfont fr i_color'>&#xe63e;</i></a>").insertAfter("#li_home");
						}
						$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
						$(".right_tab a i.fr").removeClass("i_color");
						$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");
						$(".right iframe").hide();
						var url = $(this).attr("path");
						var tourl = url + (url.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
						$("<iframe name='" + $(this).attr("id") + "' id='li_" + $(this).attr("id") + "_if' style='height:" + ih + "px;' src='" + tourl + "' frameborder='0'></iframe>").insertAfter("#li_home_if");
					}
					e.stopPropagation();
				}
			});
			//单击右侧顶部菜单选项
			$(document).on("click", ".right_tab a", function () {
				// 重置布局位置，防止偏移
				resetLayout();

				$(".nav1 a").removeClass("a_hover");
				$(".nav1 a#" + $(this).attr("id").substr(3)).addClass("a_hover");
				$(".nav1 li ul").hide();
				$(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").show();
				$(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").prev().addClass("a_hover");
				$(this).addClass("a_hover").siblings().removeClass("a_hover");
				$(".right_tab a i.fr").removeClass("i_color");
				$("#" + $(this).attr("id") + " i.fr").addClass("i_color");
				$("#" + $(this).attr("id") + "_if").show().siblings().hide();
				//$(".nav1 a#" + $(this).attr("id").substr(3)).trigger("click");
			});

			//右键菜单强制刷新页面
			$(document).on("contextmenu", ".right_tab a", function (e) {
				e.preventDefault();
				var tabId = $(this).attr("id");
				if (tabId && tabId !== "li_home") {
					if (confirm("是否要强制刷新此页面？这将丢失页面上未保存的数据。")) {
						var $iframe = $("#" + tabId + "_if");
						if ($iframe.length > 0) {
							var currentSrc = $iframe.attr("src");
							if (currentSrc) {
								// 移除旧的时间戳并添加新的时间戳
								var baseSrc = currentSrc.split("?")[0];
								var newSrc = baseSrc + "?tm=" + (new Date()).getTime();
								$iframe.attr("src", newSrc);
							}
						}
					}
				}
				return false;
			});
			//单击右侧顶部菜单关闭按钮
			$(document).on("click", ".right_tab a i.fr", function (e) {
				if ($(".right iframe:visible").attr("id") == $(this).parent().attr("id") + "_if") {
					if ($(this).parent().next().length > 0) {
						$(this).parent().next().trigger("click");
					}
					else if ($(this).parent().prev().length > 0) {
						$(this).parent().prev().trigger("click");
					}
				}
				$(this).parent().remove();
				$("#" + $(this).parent().attr("id") + "_if").remove();
				e.stopPropagation();
			});
			var search = getQueryString(window.location.search)

			if (search.tag) {
				if (search.tag == 'queryPolicyList')
					top.tabOpen("html/query/queryPolicyList.html", "数据统计");
				else if (search.tag == 'processTask')
					top.tabOpen("html/process/processTask.html", "我的待办")
				else if (search.tag == 'processJoin') {
					top.joinSearch = search
					top.tabOpen("html/process/processJoin.html", "我的已办")
				}
			}
		});
		//重置布局位置
		function resetLayout() {
			// 确保右侧内容区域位置正确
			$(".right").css({
				"left": "218px",
				"width": $(window).width() - 218
			});
			// 确保左侧菜单位置正确
			$(".left").css({
				"left": "0px",
				"width": "218px"
			});
		}

		//保存页面状态
		function savePageState() {
			var pageState = {
				activeTab: $(".right_tab a.a_hover").attr("id"),
				openTabs: []
			};
			$(".right_tab a").each(function() {
				var tabId = $(this).attr("id");
				var $iframe = $("#" + tabId + "_if");
				if (tabId && $iframe.length > 0) {
					pageState.openTabs.push({
						id: tabId,
						title: $(this).text(),
						src: $iframe.attr("src")
					});
				}
			});
			sessionStorage.setItem('pageState', JSON.stringify(pageState));
		}

		//恢复页面状态
		function restorePageState() {
			var pageStateStr = sessionStorage.getItem('pageState');
			if (pageStateStr) {
				try {
					var pageState = JSON.parse(pageStateStr);
					// 这里可以根据需要实现状态恢复逻辑
					console.log('页面状态已保存:', pageState);
				} catch (e) {
					console.error('恢复页面状态失败:', e);
				}
			}
		}

		//页面卸载时保存状态
		$(window).on('beforeunload', function() {
			savePageState();
		});

		//计算高度
		function calculateHeight() {
			// 先重置布局位置
			resetLayout();

			//var a = $(".left").height();
			var a = 335;
			var b = $(".right").height();
			var c = $(window).height() - 60;
			if (c < a || c < b) {
				if (a < b) {
					$(".center").css("height", b);
					$(".left").css("height", b);
					$(".left_menu").css("height", b);
					$(".right").css("height", b);
					$(".right iframe").css("height", b - 47);
					ih = b - 47;
				} else {
					$(".center").css("height", a);
					$(".left").css("height", a);
					$(".left_menu").css("height", a);
					$(".right").css("height", a);
					$(".right iframe").css("height", a - 47);
					ih = a - 47;
				}
			} else {
				$(".center").css("height", c);
				$(".left").css("height", c);
				$(".left_menu").css("height", c);
				$(".right").css("height", c);
				$(".right iframe").css("height", c - 47);
				ih = c - 47;
			}
		};
		$(window).resize(function () {
			$(".right").css("width", $(window).width() - 218);
			calculateHeight();
			$(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));
		});
	</script>
</body>

</html>