package com.simbest.boot.djfupt.findfaults.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsIdea;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsModel;
import com.simbest.boot.djfupt.findfaults.repository.UsFindFaultsIdeaRepository;
import com.simbest.boot.djfupt.findfaults.repository.UsFindFaultsRepository;
import com.simbest.boot.djfupt.findfaults.service.ISysFindFaultsIdeaService;
import com.simbest.boot.djfupt.findfaults.service.ISysFindFaultsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SysFindFaultsIdeaServiceImpl extends LogicService<UsFindFaultsIdea,String> implements ISysFindFaultsIdeaService {

    private UsFindFaultsIdeaRepository repository;

    @Autowired
    public SysFindFaultsIdeaServiceImpl(UsFindFaultsIdeaRepository repository) {
        super(repository);
        this.repository=repository;
    }
}
