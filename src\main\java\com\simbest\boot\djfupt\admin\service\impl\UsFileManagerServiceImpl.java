package com.simbest.boot.djfupt.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsFileManager;
import com.simbest.boot.djfupt.admin.repository.UsFileManagerRepository;
import com.simbest.boot.djfupt.admin.service.IUsFileManagerService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service(value = "usFileManagerService")
@SuppressWarnings("ALL")
public class UsFileManagerServiceImpl extends LogicService<UsFileManager, String> implements IUsFileManagerService {

    private UsFileManagerRepository usFileManagerRepository;

    @Autowired
    UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private SysFileService fileService;

    @Autowired
    LoginUtils loginUtils;
    @Autowired
    PaginationHelp paginationHelp;

    @Autowired
    public UsFileManagerServiceImpl(UsFileManagerRepository usFileManagerRepository) {
        super(usFileManagerRepository);
        this.usFileManagerRepository = usFileManagerRepository;
    }

    @Override
    public JsonResponse insertInfo(String source, String currentUserCode, UsFileManager usFileManager) {
        Assert.notNull(usFileManager.getTitle(), "标题不能为空！");
        Assert.notNull(usFileManager.getDescribe(), "文件描述不能为空！");

        if (Constants.MOBILE.equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser simpleUser = SecurityUtils.getCurrentUser();
        usFileManager.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode());
        usFileManager.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
        usFileManager.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
        usFileManager.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
        usFileManager.setBelongOrgCode(simpleUser.getBelongOrgCode());
        usFileManager.setBelongOrgName(simpleUser.getAuthOrgs().iterator().next().getDisplayName());
        usFileManager.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
        this.insert(usFileManager);
        return JsonResponse.success("操作成功！");
    }

    @Override
    public UsFileManager getById(String id) {
        UsFileManager usFileManager = this.findById(id);
        if (usFileManager != null && StrUtil.isNotEmpty(usFileManager.getFileIds())) {
            String[] split = usFileManager.getFileIds().split(",");
            List<SysFile> sysFileList = fileService.findAllByIDs(Arrays.asList(split));
            usFileManager.setSysFileList(sysFileList);
        }
        return usFileManager;
    }

    @Override
    public JsonResponse updateInfo(String source, String currentUserCode, UsFileManager usFileManager) {
        Assert.notNull(usFileManager.getTitle(), "标题不能为空！");
        Assert.notNull(usFileManager.getDescribe(), "文件描述不能为空！");

        if (Constants.MOBILE.equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }

        IUser simpleUser = SecurityUtils.getCurrentUser();
        usFileManager.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode());
        usFileManager.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
        usFileManager.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
        usFileManager.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
        usFileManager.setBelongOrgCode(simpleUser.getBelongOrgCode());
        usFileManager.setBelongOrgName(simpleUser.getAuthOrgs().iterator().next().getDisplayName());
        usFileManager.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
        this.update(usFileManager);
        return JsonResponse.success("操作成功！");
    }

    @Override
    public JsonResponse delInfo(String id) {
        try {
            this.deleteById(id);
            return JsonResponse.success("操作成功！");
        } catch (Exception e) {
            log.error("--->>> usFileManagerService.delInfo 删除失败：id{},e:{}", id, e.toString());
            return JsonResponse.fail("删除失败！");
        }
    }

    @Override
    public Page<UsFileManager> findListByPage(int page, int size, String direction, String properties, String title) {
        Pageable pageable = paginationHelp.getPageable(page, size, direction, properties);
        Specification<UsFileManager> specification = Specifications.<UsFileManager>and()
                .like(StringUtils.isNotEmpty(title), "title", "%" + title + "%")
                .build();
        Page<UsFileManager> usFileManagerPage = this.findAll(specification, pageable);
        for (UsFileManager usFileManager : usFileManagerPage.getContent()) {
            String fileIds = usFileManager.getFileIds();
            if (StrUtil.isNotEmpty(fileIds)) {
                String[] split = fileIds.split(",");
                List<SysFile> sysFileList = fileService.findAllByIDs(Arrays.asList(split));
                usFileManager.setSysFileList(sysFileList);
            }
        }
        return usFileManagerPage;
    }
}

