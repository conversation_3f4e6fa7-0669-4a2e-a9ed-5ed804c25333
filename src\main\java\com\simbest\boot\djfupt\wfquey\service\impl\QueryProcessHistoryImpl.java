package com.simbest.boot.djfupt.wfquey.service.impl;

import com.google.common.collect.Maps;
import com.simbest.boot.djfupt.wfquey.service.IQueryProcessHistoryService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用途：查询审批流程
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryProcessHistory")
public class QueryProcessHistoryImpl implements IQueryProcessHistoryService {
    @Autowired
    private IWorkItemService workItemManager;


    /**
     * 查询流转过的工作项
     *
     * @param processInstId   流程实例id
     * @param currentUserCode 当前人
     * @return
     */
    @Override
    public List<Map<String, Object>> getWorkItems(Long processInstId, String currentUserCode) {
        /**查询审批流程**/
        Map<String, Object> mapParam = Maps.newHashMap();
        mapParam.put("processInsId", processInstId);
        if (StringUtils.isEmpty(currentUserCode)) {
            mapParam.put("currentUser", SecurityUtils.getCurrentUserName());
        } else {
            mapParam.put("currentUser", currentUserCode);
        }
        return workItemManager.queryWorkITtemDataMap(mapParam);
    }
}
