package com.simbest.boot.djfupt.policy.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_policy_info")
@ApiModel(value = "政策宣讲明细表")
public class UsPolicyInfo extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID", required = true)
    protected String pmInsId;               //主单据ID  关联us_pm_instence表中的单据ID

    @Column(nullable = false, length = 40)
    @ApiModelProperty(value = "政策宣讲时限类型", required = true)
    private String policyType;               //下拉选项为：周、月、季度、年


    @Column(length = 30)
    @ApiModelProperty(value = "政策宣讲-年度", required = true)

    private String year;

    @Column(length = 30)
    @ApiModelProperty(value = "政策宣讲-季度", required = true)

    private String quarterly;

    @Column(length = 30)
    @ApiModelProperty(value = "政策宣讲月份", required = true)

    private String month;


    @Column(length = 50)
    @ApiModelProperty(value = "政策宣讲开始时间", required = true)
    private String policyStartTime;                    //政策宣讲开始时间-下拉类型为周时

    @Column(length = 40)
    @ApiModelProperty(value = "政策宣讲结束时间", required = true)
    private String policyEndTime;                    //政策宣讲结束时间-下拉类型为周时

    @Column(length = 40)
    @ApiModelProperty(value = "发起人", required = true)
    private String applyUser;                   //发起人

    @Column(length = 500)
    @ApiModelProperty(value = "发起人组织", required = true)
    private String belongOrgName;                    //发起人组织


    @Column(length = 500)
    @ApiModelProperty(value = "网格", required = true)
    @ExcelVOAttribute(name = "网格", column = "C")
    private String gridName;                    //网格名称


    @Column(length = 400)
    @ApiModelProperty(value = "标题", required = true)
    @ExcelVOAttribute(name = "工单标题", column = "E")
    private String title;                    //标题


    @Column(length = 400)
    @ApiModelProperty(value = "工单编号", required = true)
    private String workCode;                    //工单编号



    @Column(length = 400)
    @ApiModelProperty(value = "宣讲完成状态", required = true)
    @ExcelVOAttribute(name = "宣讲完成状态", column = "I")
    private String policyStatue;                    //0:初始状态，刚推送出去，未到达宣讲员处
                                                    //1：已到达党建指导员处可以进行宣讲
                                                    //2：已完成宣讲



    @ApiModelProperty(value = "父公司名称")
    @Column(length = 200)

    private String parentCompanyName;

    @ApiModelProperty(value = "省/市公司编码")
    @Column(length = 200)
    private String parentCompanyCode;


    /**
     * 政策宣讲反馈时候填写内容
     */

    @Column(length = 50)
    @ApiModelProperty(value = "宣讲时间", required = true)
    @ExcelVOAttribute(name = "宣讲时间", column = "F")
    private String policyTime;                    //宣讲时间


    @Column(length = 500)
    @ApiModelProperty(value = "宣讲地点", required = true)
    @ExcelVOAttribute(name = "宣讲地点", column = "G")
    private String policyAddress;                    //宣讲地点

    @Column(length = 500)
    @ApiModelProperty(value = "参与人员", required = true)
    @ExcelVOAttribute(name = "参与人员", column = "H")
    private String participants;                    //参与人员


    @Column(length = 500)
    @ApiModelProperty(value = "宣讲反馈附件Ids", required = true)
    private String feedBcakFileIds;                    //参与人员


    @Column(length = 500)
    @ApiModelProperty(value = "workItemId", required = true)
    private String workItemId;                    //workItemId



    @Column(length = 500)
    @ApiModelProperty(value = "宣讲提纲附件Ids", required = true)
    private String policyOutlineIds;                    //宣讲提纲



    @Column(length = 500)
    @ApiModelProperty(value = "流程实例ID", required = true)
    private Long processInstId;                 //流程实例ID


    @Column(length = 500)
    @ApiModelProperty(value = "指导员姓名", required = true)
    @ExcelVOAttribute(name = "指导员姓名", column = "D")
    private String trueName;



    @Transient
    @ApiModelProperty(value = "宣讲反馈附件", required = true)
    List<SysFile> feedBcakFile;                    //宣讲反馈附件


    @Transient
    @ApiModelProperty(value = "宣讲提纲", required = true)
    List<SysFile> policyOutline;                    //宣讲提纲



    @Transient
    List<UsPantchDetail> usPantchDetailList;


    

    @Transient
    private String source;                //来源


    @Transient
    private String currentUserCode;                //当前登录人


    @Transient
    Map<String,Object> maps;


    @Transient
    private  String city;

    @Transient
    private  String startTime;

    @Transient
    private  String endTime;



    @Transient
    @ApiModelProperty(value = "单位", required = true)
    @ExcelVOAttribute(name = "单位", column = "A")
    private String companyName;


    @Transient
    @ApiModelProperty(value = "部门", required = true)
    @ExcelVOAttribute(name = "部门", column = "B")
    private String depName;







}
