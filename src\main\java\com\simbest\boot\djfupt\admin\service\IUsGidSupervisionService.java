package com.simbest.boot.djfupt.admin.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsGidSupervisionManager;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IUsGidSupervisionService extends ILogicService<UsGidSupervisionManager, String> {
    JsonResponse insertInfo(UsGidSupervisionManager usGidSupervisionManager);

    JsonResponse updateInfo(UsGidSupervisionManager usGidSupervisionManager);

    JsonResponse delInfo(String id);

    Page<UsGidSupervisionManager> findListByPage(int page, int size, String direction, String properties, String belongCompanyName, String gridName, String gridTrueName, String supervisionTrueName);

    void importGidSupervision(HttpServletRequest request, HttpServletResponse response);

    UsGidSupervisionManager getByGridCode(String gridCode);

    void dolwnLoad(HttpServletRequest request, HttpServletResponse response);

    JsonResponse getCountyByCityName(String currentUserCode, String source, String city);

    JsonResponse getGridAndSupervision(String location, String source);
}
