package com.simbest.boot.djfupt.mainbills.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <strong>Title : PmInstence</strong><br>
 * <strong>Description : 主单据</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "us_pm_instence", uniqueConstraints = {@UniqueConstraint(columnNames = {"pmInsId"}, name = "unique_idx_pmInstId")})
@ApiModel(value = "主单据")
public class UsPmInstence extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UMF") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(name = "pmInsId",length = 40)
    @ApiModelProperty (value = "单据ID", required = true)
    protected String pmInsId;

    @Setter
    @Getter
    @Column(name = "pmInsTitle",length = 100)
    @ApiModelProperty (value = "单据标题", required = true)
    private String pmInsTitle;

    @Setter
    @Getter
    @Column(name = "pmInsType",nullable = false,  length = 100)
    @ApiModelProperty(value = "业务流程类型", required = true)
    private String pmInsType;

    @Setter
    @Getter
    @Column(length = 400)
    @ApiModelProperty(value = "工单编号", required = true)
    private String workCode;                    //工单编号


    @Setter
    @Getter
    @Column(length = 400)
    @ApiModelProperty(value = "流程名称", required = true)
    private String processName;                    //流程名称
}