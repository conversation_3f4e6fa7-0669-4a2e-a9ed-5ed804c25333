package com.simbest.boot.djfupt.policy.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_pantch_detail")
@ApiModel(value = "政策宣讲清单")
public class UsPantchDetail extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID", required = true)
    private String pmInsId;               //主单据ID  关联us_pm_instence表中的单据ID


    @Column(length = 40)
    @ApiModelProperty(value = "webId", required = true)
    private String webId;  //起草时，页面生成一个UUDID,绑定所有的的宣讲事项，流转下一步时或者保存草稿时，在绑定宣讲明细ID


    @Column(length = 40)
    @ApiModelProperty(value = "单据ID", required = true)
    private String policyId;               //政策宣讲明细表ID  关联us_policy_info表中的单据ID

    @Column(length = 1000)
    @ApiModelProperty(value = "宣讲事项", required = true)
    private String lectureItems;               //宣讲事项

    @Column(length = 1000)
    @ApiModelProperty(value = "宣讲事项类别", required = true)
    private String pantchType;               //宣讲事项类别 :1:必选项  2：推荐项目

    @Column(length = 300)
    @ApiModelProperty(value = "文件ID", required = true)
    private String fileIds;

    @Column(length = 300)
    @ApiModelProperty(value = "是否有效", required = true)
    private String isFlag;//在页面上省公司新增的都是1，都是有效的
                            //在页面上分公司上新增的都是2，是暂时有效，如果刷新，则查询不到
                            //待办调用getFromDetail 时查询的搜索有效的数据



    @Column(length = 400)
    @ApiModelProperty(value = "工单编号", required = true)
    private String workCode;                    //工单编号


    @ApiModelProperty(value = "父公司名称")
    @Column(length = 200)
    @ExcelVOAttribute(name = "单位", column = "A")
    private String parentCompanyName;

    @ApiModelProperty(value = "省/市公司编码")
    @Column(length = 200)
    private String parentCompanyCode;


    @Transient
    List<SysFile> drawFiles;                //附件


    @Transient
    private String source;                //来源


    @Transient
    private String currentUserCode;                //当前登录人


}
