package com.simbest.boot.djfupt.util;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class MergeUtil {


    /**
     * @param m1
     * @param m2
     * @param string 根据某个key来合并
     * @return
     */
    public static List<Map<String, Object>> merge(List<Map<String, Object>> m1, List<Map<String, Object>> m2, String string) {
        m1.addAll(m2);
        Set<String> set = new HashSet<>();
        return m1.stream().collect(Collectors.groupingBy(o -> {
            //暂存所有key
            set.addAll(o.keySet());
            return o.get(string);
        })).entrySet().stream().map(o -> {
            //合并
            Map<String, Object> map = o.getValue().stream().flatMap(m -> {
                return m.entrySet().stream();
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> b));
            //为没有key的赋值0
            set.stream().forEach(k -> {
                if (!map.containsKey(k))
                    map.put(k, 0);
            });
            return map;
        }).collect(Collectors.toList());
    }
}
