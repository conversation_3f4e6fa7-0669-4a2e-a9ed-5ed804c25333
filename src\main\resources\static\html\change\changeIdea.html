<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>思政纪实填报</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent();
        var DetailId = gps.id ? gps.id : '';
        var recordTypeList = []
        $(function () {
            var param = {
                "htmlName": "changeIdea",
                "formId": "ideaForm",
            };

            if (gps.type == 'join' || gps.type == 'draft') {
                $('.pageInfo').hide();
                $('#ideaForm').attr('cmd-select', 'action/usRecordFill/recordDetail?id=' + DetailId)
                formReadonly(param.formId);
                $('.cselectorImageUL .btn').hide()
                $('#ideaForm').css('margin-top', '10px')
                $('.delet').hide()
            }

            $('.body_page').attr('style', 'padding-top: 20px')
            $(".nextBtn,.saveDraft,.formReset").show();
            loadForm(param.formId, {currentUserCode: web.currentUser.username})
            var date = new Date()
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            m = m < 10 ? ('0' + m) : m;
            var d = date.getDate();
            d = d < 10 ? ('0' + d) : d;
            var h = date.getHours();
            h = h < 10 ? ('0' + h) : h;
            var minute = date.getMinutes();
            minute = minute < 10 ? ('0' + minute) : minute;
            var second = date.getSeconds();
            second = second < 10 ? ('0' + second) : second;
            var dateNow = y + '-' + m + '-' + d;

            if (!gps.location) {
                setTimeout(function () {
                    $('#applyTime').val(dateNow)
                }, 1500)
            }


            //hadmin修改
            $(document).on("click", ".hadminSave", function () {
                var datas = getFormValue("ideaForm")
                if (formValidate("ideaForm")) {
                    ajaxgeneral({
                        url: "action/usRecordFill/insertUsRecordFill",
                        data: datas,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            top.window.queryIdeaListDialog.window.dialogClosed()
                            top.tabClick("queryIdeaListDialog")
                        }
                    });
                }

            });

        });

        function beforeSubmit(data) {
            return data
        }

        function submitcallback(data) {
        }

        function getcallback(data) {
            if (!data) {
                $('#applyUser').val(web.currentUser.truename)
                if (web.currentUser.belongCompanyTypeDictValue == '03') {
                    $('#applyOrg').val(web.currentUser.belongCompanyNameParent + '/' + web.currentUser.belongDepartmentName)
                } else {
                    $('#applyOrg').val(web.currentUser.belongCompanyName + '/' + web.currentUser.belongDepartmentName)
                }
                $('#applyTime').val(getNow())
            } else {
                formval(data, 'ideaForm')
                if (data.recordTypeList) {
                    recordTypeList = data.recordTypeList.filter(function (item) {
                        return item
                    }).map(function (item) {
                        return {
                            name: item,
                            value: item
                        }
                    })
                }

                var xh = 1;
                for (let i = 0; i < data.talkContentList.length; i++) {
                    appTr(xh, data.talkContentList[i], i)
                    xh++
                }
            }

            if (gps.type == 'join') {
                $('.cselectorImageUL .btn').hide()
                $('.cselectorImageUL input[type="file"]').remove()
            }

            //只有台账页面点进来使用详情页面判断
            // if(gps.special=='special'){
            if (data.taskDescription) {
                $('#taskDescription').val(data.taskDescription)
                $(".taskDescriptionShow").show()
            }
            if (data.taskDescriptionFileId) {
                $(".taskDescriptionFilesShow").show()
                var datas = {
                    'taskDescriptionFiles': data.taskDescriptionFiles
                }
                formval(datas, 'ideaForm')
                idReadonly("taskDescriptionFiles")
            }

            if (gps.otherType == "hadminUpdata") {
                $('.body_page').attr('style', 'padding-top: 30px')
                $('.formReset').hide()
                $('.saveDraft').hide()
                $('.nextBtn').hide()
                $('.hadminSave').show()
                $('.pageInfo').show()
                idReadonlyNo("configDeptName")
                idReadonlyNo("configTime")
                idReadonlyNo("talkContent")
                idReadonlyNo("drawFiles")
                idReadonlyNo("talkTime")
                idReadonlyNo("talkAddress")
                idReadonlyNo("numberOfPanel")
                idReadonlyNo("taskDescription")
                idReadonlyNo("taskDescriptionFiles")
            }

        }

        window.getchoosedata = function () {
            if (!formValidate('ideaForm')) {
                return false
            }
            var datas = getFormValue('ideaForm');
            return {'data': datas, 'state': 1};
        };
        var processNext_flag = false;//防止接口慢时用户再次流转
        $(document).on("click", ".nextBtn", function () {
            if (processNext_flag) return false;
            processNext_flag = true
            var $form = $(this).parents("form");
            if (formValidate($form.attr("id"))) {
                var data = getFormValue('ideaForm');
                var files = data.taskDescriptionFiles
                var fileIds = []
                for (var i = 0; i < files.length; i++) {
                    fileIds.push(files[i].id)
                }
                data.taskDescriptionFileId = fileIds.join(',')

                var talkContentList = [];
                $("table.ctable tbody").find("tr").each(function (x, y) {
                    var listi = {};
                    $(y).find("input,textarea").each(function (a, b) {
                        if ($(b).attr("name")) {
                            listi[b.name] = b.value || "";
                        }
                    });
                    talkContentList.push(listi);
                });
                data.talkContentList = talkContentList

                var ajaxopts = {
                    url: "action/usRecordFill/insertUsRecordFill",
                    contentType: "application/json; charset=utf-8",
                    data: data,
                    loading: true,
                    success: function (data) {
                        processNext_flag = false;
                        formreset($form.attr("id"))
                        $('.ctable tbody').find('tr').each(function (x, y) {
                            $(y).remove()
                        })
                    }, error: function (err) {
                        processNext_flag = false;
                    }
                }
                ajaxgeneral(ajaxopts)
            } else {
                processNext_flag = false
            }
        })
        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            if (processNext_flag) return false;
            processNext_flag = true
            var data = getFormValue('ideaForm');

            var talkContentList = [];
            $("table.ctable tbody").find("tr").each(function (x, y) {
                var listi = {};
                $(y).find("input,textarea").each(function (a, b) {
                    if ($(b).attr("name")) {
                        listi[b.name] = b.value || "";
                    }
                });
                talkContentList.push(listi);
            });
            data.talkContentList = talkContentList

            var ajaxopts = {
                url: "action/usRecordFill/saveUsRecordFill",
                contentType: "application/json; charset=utf-8",
                loading: true,
                data: data,
                success: function (data) {
                    processNext_flag = false;
                    top.dialogClose(gps.fromIfr);
                }, error: function () {
                    processNext_flag = false;
                }
            };
            ajaxgeneral(ajaxopts)
        })
        //重置表单
        $(document).on("click", ".formReset", function () {
            formreset('ideaForm')
            var date = new Date()
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            m = m < 10 ? ('0' + m) : m;
            var d = date.getDate();
            d = d < 10 ? ('0' + d) : d;
            var h = date.getHours();
            h = h < 10 ? ('0' + h) : h;
            var minute = date.getMinutes();
            minute = minute < 10 ? ('0' + minute) : minute;
            var second = date.getSeconds();
            second = second < 10 ? ('0' + second) : second;
            var dateNow = y + '-' + m + '-' + d;

            $('#applyTime').val(dateNow)
            formreset('ideaForm')
        });
        // 谈话或走访内容控制
        $(document).on("click", ".a_add_org", function () {
            var $tr = $(".ctable").find("thead.trow tr").clone();
            var len = $(".ctable").find("tbody").find("tr[path]").length + 1;
            $tr.attr("path", len);
            $tr.find("td[path=xh]").text(len);
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPathI($(y), $tr);
            });
            comboboxChange($tr, "");
            $tr.appendTo($(".ctable").find("tbody"));
        });

        //关闭
        $(document).on("click", ".optClose", function () {
            var param = {htmlName: 'changeIdea'}
            if (gps.location || gps.initForm) {
                if (gps.from) {
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else if (gps.formToTab) {
                    top.tabClose("li_" + param.htmlName);
                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                    var iframs = gps.initForm.split("@");
                    setTimeout(function () {
                        if (iframs[0].indexOf("_") > -1) {
                            var ifi = iframs[0].split("_");
                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                        } else {
                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                            }
                        }
                    }, 1500);
                } else if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.dialogClose("audit");
                }
            } else {
                if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.newPage) {
                    top.tabClick("li_processTask");
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.tabOpen("html/process/processTask.html", "我的待办");
                    top.tabClose("li_" + param.htmlName);
                }
            }
        });

        function tdPathI($id, $tr, val) {
            if (val) {
                tdPath($id, val);
            } else {
                tdPath($id);
            }
        };

        //combobox变形
        function comboboxChange($tr, code) {
            $tr.find("input.type").combobox({
                "valueField": 'value',
                "ischooseall": false,
                "textField": 'name',
                "editable": false,
                "panelHeight": '200',
                "data": recordTypeList
            });
        };

        //删除
        $(document).on("click", ".a_del_btn", function () {
            var $tr = $(this).parents("tr");
            var $trs, path;
            if ($tr.attr("path")) {
                path = $tr.attr("path");
                $tr.remove();
            }
            xh();
        });

        function xh() {
            $("table.ctable tbody").find("tr[path]").each(function (x, y) {
                var path = x + 1;
                $(y).find("td[path=td]").text(path);
                $(y).attr("path", path);
            });
        };

        function appTr(xh, data, i) {
            var $tr = $(".ctable").find("thead.trow tr").clone();
            $tr.attr("path", xh);
            $tr.find("td[path=xh]").text(xh)
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPath($(y), data[$(y).attr("name")]);
            });
            comboboxChange($tr, i);
            $tr.appendTo($(".ctable").find("tbody"));
        };
    </script>
    <style>
        textarea {
            white-space: normal !important;
        }

        .formTable {
            width: 100%;
            margin-top: 20px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
            table-layout: fixed;
        }

        .formTable > tbody > tr:not(:first-child) > td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable > tbody > tr > td input,
        .formTable > tbody > tr > td span,
        .formTable > tbody > tr > td textarea,
        .formTable > tbody > tr > td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: left;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #fff;
        }

        input:read-only {
            background-color: #fff;
        }

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: -15px;
        }

        .cselectorImageUL input[type='file'] {
            width: 60px !important;
            display: inline-block
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        #ideaForm {
            margin-top: 60px;
        }

        .validatebox-invalid {
            background-color: #fff;
        }

        .textbox-invalid {
            background-color: #fff;
        }

        .uploadImageI {
            padding-top: 0;
            font-weight: normal;
        }

        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        .blockTitle {
            border-left: 5px solid #f79e40;
            padding: 5px 0px 5px 10px;
            margin: 10px 0px;
            font-size: 18px;
            display: inline-block;
            font-weight: bold;
        }

        .a_add_org {
            margin-top: 10px;
        }

        .ctable .textbox {
            width: 100% !important;
        }

        .panel .combobox-item {
            font-size: 14px;
            padding: 6px;
        }

        .type-td .validatebox-text {
            width: 90% !important;
        }
    </style>
</head>

<body class="body_page">
<form id="ideaForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
      cmd-select="action/usRecordFill/getFromDetail" beforeSubmit="beforeSubmit()" submitcallback="submitcallback()"
      getcallback="getcallback()">
    <input id="pmInsId" name="pmInsId" hidden>
    <input id="id" name="id" hidden>
    <div class="pageInfo" style="min-width: 0">
        <div class="pageInfoD">
            <a class="btn small fl mr15 nextBtn hide mb20 "><i class="iconfont">&#xe688;</i>
                <font>流转下一步</font>
            </a>
            <a class="btn small fl mr15 saveDraft hide mb20"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 hadminSave hide mb20"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 formReset hide mb20"><i class="iconfont">&#xe646;</i>
                <font>重置</font>
            </a>
            <a class="btn small fl mr15 optClose"><i class="iconfont">&#xe690;</i>
                <font>关闭</font>
            </a>
        </div>
    </div>

    <div style="width: 100%;">
        <table class="formTable" border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="11.1%"></td>
                <td width="22.2%"></td>
                <td width="11.1%"></td>
                <td width="22.2%"></td>
                <td width="11.1%"></td>
                <td width="22.2%"></td>
            </tr>
            <tr>
                <td colspan="6" style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding: 5px;">
                    思政纪实填报
                </td>
            </tr>
            <tr>
                <td class="lable"><span>谈话主题或走访事项</span> <font class="col_r">*</font></td>
                <td colspan="5">
                    <input class="easyui-validatebox" id="configDeptName" name="configDeptName" type="text"
                           required="true" style="width:100%; height: 32px;" validType="maxLength[100]"/>
                </td>
            </tr>
            <tr>
                <td class="lable"> 填报人</td>
                <td><input id="applyUser" name="applyUser" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">填报组织</td>
                <td><input id="applyOrg" name="applyOrg" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">填报时间</td>
                <td><input id="applyTime" name="applyTime" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
            </tr>

            <tr class="hide taskDescriptionShow">
                <td class="lable"><span>任务描述</span></td>
                <td colspan="5">
                        <textarea id="taskDescription" name="taskDescription" class="easyui-validatebox" readonly
                                  noReset="true"
                                  validType="maxLength[1000,'talkContentTips']" style="min-height:120px;"></textarea>
                    <p class="talkContentTips"></p>
                </td>
            </tr>
            <tr class="hide taskDescriptionFilesShow">
                <td class="lable">附件清单</td>
                <!-- <td colspan="5" >
                    <input id="taskDescriptionFiles" readonly name="taskDescriptionFiles" type="text" file="true" mulaccept="true" OtherInfo="funfun"  noReset="true"
                           class="cselectorImageUpload" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
                </td> -->

                <td colspan="5" style="padding-left: 7px">
                    <input id="taskDescriptionFiles" name="taskDescriptionFiles" type="text" file="true"
                           mulaccept="true" noReset="true"
                           class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
                </td>
            </tr>


            <tr>
                <td class="lable">谈话或走访时间<font class="col_r">*</font></td>
                <td>
                    <input id="talkTime" name="talkTime" type="text" class="easyui-datetimebox" required="true"
                           data-options="editable:false" style="width:100%;height:32px;"/>
                </td>
                <td class="lable">谈话或走访地点<font class="col_r">*</font></td>
                <td>
                    <input class="easyui-validatebox" id="talkAddress" name="talkAddress" type="text"
                           required="true" style="width:100%; height: 32px;" validType="maxLength[100]"/>
                </td>
                <td class="lable">座谈人数<font class="col_r">*</font></td>
                <td>
                    <input class="easyui-validatebox" id="numberOfPanel" name="numberOfPanel" type="text"
                           required="true" validType="zinteger" style="width:100%; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td class="lable"><span>谈话或走访对象</span> <font class="col_r">*</font></td>
                <td colspan="5">
                    <input class="easyui-validatebox" id="configTime" name="configTime" type="text" required="true"
                           style="width:100%; height: 32px;" validType="maxLength[100]"/>
                </td>
            </tr>
<!--            <tr>-->
<!--                <td class="lable"><span>谈话或走访内容简述</span> <font class="col_r">*</font></td>-->
<!--                <td colspan="5">-->
<!--                        <textarea id="talkContent" name="talkContent" class="easyui-validatebox" required="true"-->
<!--                                  validType="maxLength[1000,'talkContentTips']" style="min-height:120px;"></textarea>-->
<!--                    <p class="talkContentTips"></p>-->
<!--                </td>-->
<!--            </tr>-->
            <tr style="line-height: 32px">
                <td class="lable">谈话或走访附件</td>
                <td colspan="5" style="padding-left: 7px">
                    <input id="drawFiles" name="drawFiles" type="text" file="true" mulaccept="true"
                           class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
                </td>
            </tr>
        </table>
    </div>
    <div class="block" style="width: 100%;margin-bottom: 20px;">
        <span class="blockTitle">谈话或走访内容</span>
        <a class="btn a_add_org fr"><span>新增上报内容</span></a>
        <table border="0" cellpadding="0" cellspacing="0" class="ctable w100">
            <thead>
            <tr>
                <td align="center" width="30">序号</td>
                <td align="center" width="100">类别</td>
                <td align="center" width="300">问题描述</td>
                <td align="center" width="100" class="delet">操作</td>
            </tr>
            </thead>
            <thead class="trow hide">
            <tr path="0">
                <td align="center" width="30" path="xh"></td>
                <td width="100" style="border-left: 1px solid #e6e6e6;" class="type-td">
                    <input name="type" class="type" classpath="validatebox" style="height: 32px;"/>
                </td>
                <td width="300" style="border-left: 1px solid #e6e6e6;border-right: 1px solid #e6e6e6;">
                    <textarea name="remark" class="remark" classpath="validatebox"></textarea>
                </td>
                <td align="center" width="50" class="delet">
                    <div class='a_del_btn col_r divBtn' style="cursor: pointer;">
                        <span>删除</span>
                    </div>
                </td>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</form>
</body>

</html>