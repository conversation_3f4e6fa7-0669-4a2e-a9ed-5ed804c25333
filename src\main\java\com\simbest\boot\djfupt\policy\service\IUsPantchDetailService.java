package com.simbest.boot.djfupt.policy.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IUsPantchDetailService extends ILogicService<UsPantchDetail, String> {


    /**
     * 政策宣讲清单更新附件
     *
     * @param usPantchDetail
     */
    public void updateFileInfoById(UsPantchDetail usPantchDetail);

    /**
     * 宣讲清单列表
     *
     * @param source
     * @param currentUserCode
     * @param webId           页面UUID
     * @param policyId        宣讲清单页面ID
     * @return
     */
    List<Map<String, Object>> mattersList(String source, String currentUserCode, String pmInsId, String webId);

    /**
     * 政策宣讲清单-宣讲事项一键下载
     *
     * @param usPantchId 宣讲事项列表ID
     * @param response
     * @param request
     * @return
     */
    public ResponseEntity<?> usPantchDetailExport(String usPantchId, HttpServletResponse response, HttpServletRequest request);

    /**
     * 根据主单据ID和政策宣讲ID查询
     *
     * @param pmInsId
     * @param policyId
     * @return
     */

    List<UsPantchDetail> findUsPantchDetailInfo(String pmInsId,String creator);


}
