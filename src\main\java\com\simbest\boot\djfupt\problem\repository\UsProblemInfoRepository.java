package com.simbest.boot.djfupt.problem.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface UsProblemInfoRepository extends LogicRepository<UsProblemInfo, String> {


    @Transactional
    @Query(
            value = "select a.* from US_PROBLEM_INFO a,us_pm_instence p where a.enabled = 1 and a.pm_ins_id = p.pm_ins_id and p.id=:id",
            nativeQuery = true
    )
    UsProblemInfo getFromDetail(@Param("id") String id);

    /**
     * 根据pmInsId查询业务单据信息
     *
     * @param pmInsId
     * @return
     */
    @Transactional
    @Query(
            value = "select a.* from US_PROBLEM_INFO a where a.enabled = 1 and a.pm_ins_id =:pmInsId",
            nativeQuery = true
    )
    UsProblemInfo getFormDetailByPmInsId(@Param("pmInsId") String pmInsId);


    //查询所有未归档工单且state=1 期望时间已经过了的工单
    @Transactional
    @Query(
            value = "    select t.*" +
                    "    from US_PROBLEM_INFO t, act_business_status act" +
                    "    where t.pm_ins_id =act.receipt_code" +
                    "    and t.enabled=1" +
                    "    and act.enabled=1" +
                    "    and t.expected_completion_time < :time" +
                    "    and t.state =1" +
                    "    and act.current_state !=7",
            nativeQuery = true
    )
    List<UsProblemInfo> findAllByStateAndTime(@Param("time") String time);


    @Query(
            value = "    select t.* from WF_WORKITEM_MODEL t where t.receipt_code = :receiptCode order by t.created_time desc   ",
            nativeQuery = true
    )
    List<Map<String,Object>> findAllByReceiptCode(@Param("receiptCode") String receiptCode);

    //查询年份下当前人有没有起草工单
    @Query(
            value = " select t.* " +
                    "  from US_PROBLEM_INFO t " +
                    " where t.enabled = 1 " +
                    "   and t.creator =:creator " +
                    "   and to_CHAR(t.created_time, 'YYYY') = :year   ",
            nativeQuery = true
    )
    List<UsProblemInfo> findAllByCreatorAndYear(@Param("year") int year,
                                                @Param("creator") String creator);



    @Query(
            value = " select t.*" +
                    "    from WF_WORKITEM_MODEL t" +
                    "    where t.enabled = 1" +
                    "    and t.current_state = 10" +
                    "    and t.receipt_code=:receiptCode ",
            nativeQuery = true
    )
   Map<String,Object> findAllByReceiptCodeAndCurrentState(@Param("receiptCode") String receiptCode);

    @Query(
            value = " select t.*" +
                    "    from WF_WORKITEM_MODEL t" +
                    "    where t.enabled = 1" +
                    "    and t.participant = :creator " +
                    "    and t.receipt_code=:receiptCode ",
            nativeQuery = true
    )
    Map<String,Object> findAllByReceiptCodeAndCurrentStateAndCreator(@Param("receiptCode") String receiptCode,
                                                           @Param("creator") String creator);

    @Query(
            value = " select count(id)" +
                    "  from US_PROBLEM_INFO t, act_business_status act" +
                    " where t.pm_ins_id = act.receipt_code" +
                    "   and t.enabled = 1" +
                    "   and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS')" +
                    "   and t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS')" +
                    "   and act.receipt_code = 1 and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode ) ",
            nativeQuery = true
    )
    int findAllByBelongCompanyCodeAndEnabled(@Param("companyCode") String companyCode,
                                             @Param("startTime") String startTime,
                                             @Param("endTime") String endTime);

    @Query(
            value = " select count(id)" +
                    "  from US_PROBLEM_INFO t, act_business_status act" +
                    " where t.pm_ins_id = act.receipt_code" +
                    "   and t.enabled = 1" +
                    "   and act.current_state=7" +
                    "   and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS')" +
                    "   and t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS')" +
                    "   and act.receipt_code = 1 and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode ) ",
            nativeQuery = true
    )
    int findAllByBelongCompanyCodeAndEnabledAndCurrState(@Param("companyCode") String companyCode,
                                                         @Param("startTime") String startTime,
                                                         @Param("endTime") String endTime);

    @Query(
            value = " sELECT dv.*" +
                    "  from sys_dict d, sys_dict_value dv" +
                    " where d.dict_type = dv.dict_type" +
                    "   and d.enabled = 1" +
                    "   and dv.enabled = 1" +
                    "   and d.dict_type =:type" +
                    "   and dv.name=:name" +
                    " order by dv.display_order asc" +
                    "          " +
                    "      ",
            nativeQuery = true
    )
    List<SysDictValue> findAllByNameAndType(@Param("type") String type,
                                            @Param("name") String name);



    //查询所有未归档工单且state=1 期望时间已经过了的工单
    @Transactional
    @Query(
            value = "    select t.*" +
                    "    from US_PROBLEM_INFO t, act_business_status act" +
                    "    where t.pm_ins_id =act.receipt_code" +
                    "    and t.enabled=1" +
                    "    and act.enabled=1" +
                    "    and act.current_state !=7",
            nativeQuery = true
    )
    List<UsProblemInfo> findAllByStateAndTimes();



    @Query(
            value = "    select count(1)" +
                    "  from us_problem_info t, act_business_status act" +
                    " where t.enabled = 1 and act.enabled = 1 " +
                    "   and t.pm_ins_id = act.receipt_code" +
                    "   and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode ) " +
                    "   and to_char(t.created_time,'yyyy-MM-dd hh:mm:ss') >=:startTime" +
                    "   and to_char(t.created_time,'yyyy-MM-dd hh:mm:ss') <=:endTime   and  act.current_state=7 " +

                    "   ",
            nativeQuery = true
    )
    int findAllByEnabledAndQuestionMode(@Param("startTime") String startTime,
                                        @Param("endTime") String endTime,
                                        @Param("companyCode") String companyCode);

    @Query(
            value = "select t.*" +
                    "  from us_problem_info t" +
                    " where t.enabled = 1" +
                    "   and t.removed_time is null" +
                    "   and t.is_draft = '2'" +
                    "   and t.creator =:creator order by t.created_time desc",
            nativeQuery = true
    )
    List<UsProblemInfo> getFromDetailByUserName(@Param("creator") String creator);



    @Query(
            value = " select count(distinct t.creator)\n" +
                    "  from us_problem_info t\n" +
                    " where t.enabled = 1\n" +
                    "   and to_char(t.created_time,'yyyy-MM') = :applyTime\n" +
                    "   and t.belong_company_name = :companyName\n" +
                    "   and t.is_draft = 1 ",
            nativeQuery = true
    )
    int countByCompany(@Param("applyTime") String applyTime,
                       @Param("companyName") String companyName);



    @Query(
            value = " select t.* " +
                    "  from US_PROBLEM_INFO t " +
                    " where t.enabled = 1 " +
                    "   and t.creator =:creator and  t.is_draft=1 " +
                    "   and to_CHAR(t.created_time, 'YYYY') = :year   ",
            nativeQuery = true
    )
    List<UsProblemInfo> findAllByCreatorAndYearAndIsDraft(@Param("year") int year,
                                                @Param("creator") String creator);


    @Query(
            value = " select t.* " +
                    "  from US_PROBLEM_INFO t " +
                    " where t.enabled = 1 " +
                    "   and t.creator =:creator and  t.is_draft=1 " +
                    "   and to_CHAR(t.created_time, 'YYYY-MM') = :month   ",
            nativeQuery = true
    )
    List<UsProblemInfo> findAllByCreatorAndMonthAndIsDraft(@Param("month") String month,
                                                          @Param("creator") String creator);
}
