package com.simbest.boot.djfupt.mainbills.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.djfupt.mainbills.repository.UsPmInstenceRepository;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * <strong>Title : PmInstenceServiceImpl</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> zhang<PERSON><PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service(value = "pmInstenceService")
public class UsPmInstenceServiceImpl extends LogicService<UsPmInstence, String> implements IUsPmInstenceService {

    private UsPmInstenceRepository pmInstenceRepository;

    @Autowired
    public UsPmInstenceServiceImpl(UsPmInstenceRepository pmInstenceRepository) {
        super(pmInstenceRepository);
        this.pmInstenceRepository = pmInstenceRepository;
    }

    /**
     * 逻辑删除操作
     *
     * @param id
     * @return
     */
    @Override
    public int deleteByPmId(String id) {
        return pmInstenceRepository.deleteByFromId(id);
    }

    /**
     * 查找主单据
     *
     * @param pmInsId 单据ID
     * @return
     */
    @Override
    public UsPmInstence findByPmInsId(String pmInsId) {
        return pmInstenceRepository.findByPmInsId(pmInsId);
    }

    /**
     * 保存主数据
     *
     * @param usPmInstence 主数据
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public UsPmInstence saveData(UsPmInstence usPmInstence) {
        /***/
        UsPmInstence instence = null;
        try {
            wrapCreateInfo(usPmInstence);
            instence = this.saveAndFlush(usPmInstence);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            Exceptions.printException(e);
        }
        return instence;
    }

    /**
     * 查找主单据
     *
     * @param businessKey 单据ID
     * @return
     */
    @Override
    public UsPmInstence findByBusinessKey(String businessKey) {
        return pmInstenceRepository.findById(businessKey).get();
    }

    /**
     * 获取个数
     * @param pmInsType
     * @param createdTime
     * @return
     */

    @Override
    public String getCounts(String pmInsType, String createdTime) {
        return pmInstenceRepository.getCounts(pmInsType, createdTime);
    }

}
