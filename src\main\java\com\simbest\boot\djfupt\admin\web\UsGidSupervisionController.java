package com.simbest.boot.djfupt.admin.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsGidSupervisionManager;
import com.simbest.boot.djfupt.admin.service.IUsGidSupervisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 网格监督员管理相关接口
 */
@Api(description = "网格监督员管理相关接口")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/usGidSupervision")
public class UsGidSupervisionController extends LogicController<UsGidSupervisionManager, String> {

    private IUsGidSupervisionService usGidSupervisionService;

    @Autowired
    public UsGidSupervisionController(IUsGidSupervisionService usGidSupervisionService) {
        super(usGidSupervisionService);
        this.usGidSupervisionService = usGidSupervisionService;
    }

    @ApiOperation(value = "网格长和监督员管理(分页)", notes = "网格长和监督员管理(分页)")
    @PostMapping(value = {"/findListByPage", "/sso/findListByPage", "/api/findListByPage"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页容量", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "direction", value = "排序方向asc/desc", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序属性", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "belongCompanyName", value = "单位名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gridName", value = "网格名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gridTrueName", value = "网格长OA账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "supervisionTrueName", value = "监督员姓名", dataType = "String", paramType = "query"),
    })
    public JsonResponse findListByPage(@RequestParam(required = false, defaultValue = "1") int page,
                                       @RequestParam(required = false, defaultValue = "10") int size,
                                       @RequestParam(required = false, defaultValue = "desc") String direction,
                                       @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                       @RequestBody UsGidSupervisionManager usGidSupervisionManager
    ) {
        try {
            Page<UsGidSupervisionManager> pageList = usGidSupervisionService.findListByPage(page, size, direction, properties,usGidSupervisionManager.getBelongCompanyName(),usGidSupervisionManager.getGridName(),usGidSupervisionManager.getGridTrueName(),usGidSupervisionManager.getSupervisionTrueName());
            return JsonResponse.success(pageList);
        } catch (Exception e) {
            log.error("网格长和监督员管理分页查询接口异常，{}", e);
            return JsonResponse.fail("接口异常！");
        }
    }

    @ApiOperation(value = "网格和监督员新增")
    @PostMapping(value = {"/insert", "/insert/sso", "/insert/api"})
    public JsonResponse insert(@RequestBody UsGidSupervisionManager usGidSupervisionManager) {
        return usGidSupervisionService.insertInfo(usGidSupervisionManager);
    }

    @ApiOperation(value = "网格及监督员修改")
    @PostMapping(value = {"/updateInfo", "/updateInfo/sso", "/updateInfo/api"})
    public JsonResponse updateInfo(@RequestBody UsGidSupervisionManager usGidSupervisionManager) {
        return usGidSupervisionService.updateInfo(usGidSupervisionManager);
    }

    @ApiOperation(value = "网格及监督员信息删除")
    @PostMapping(value = {"/delInfo", "/delInfo/sso", "/delInfo/api"})
    public JsonResponse delInfo(@RequestParam String id) {
        return usGidSupervisionService.delInfo(id);
    }

    @ApiOperation(value = "网格及监督员信息导入")
    @PostMapping(value = {"/importGidSupervision", "/importGidSupervision/sso", "/importGidSupervision/api"})
    public void importGidSupervision(HttpServletRequest request, HttpServletResponse response) {
         usGidSupervisionService.importGidSupervision(request,response);
    }

    @ApiOperation(value = "根据网格编码获取网格管理员及监督员信息")
    @PostMapping(value = {"/getByGridCode", "/getByGridCode/sso", "/getByGridCode/api"})
    public JsonResponse getByGridCode(@RequestParam String gridCode) {
        return JsonResponse.success(usGidSupervisionService.getByGridCode(gridCode));
    }

    @ApiOperation(value = "网格及监督员信息模板下载")
    @GetMapping(value = {"/dolwnLoad", "/dolwnLoad/sso", "/dolwnLoad/api"})
    public void dolwnLoad(HttpServletRequest request, HttpServletResponse response) {
        usGidSupervisionService.dolwnLoad(request,response);
    }

    @ApiOperation(value = "根据地市名称获取县级列表", notes = "根据地市名称获取县级列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "city", value = "city", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getCountyByCityName", "/api/getCountyByCityName", "/getCountyByCityName/sso"})
    public JsonResponse getCountyByCityName(@RequestParam(required = false) String currentUserCode ,
                                            @RequestParam(required = false) String source,
                                            @RequestParam(required = true) String city) {
        return usGidSupervisionService.getCountyByCityName(currentUserCode , source, city);
    }

    @ApiOperation(value = "获取网格员及监督员", notes = "根据地市名称获取县级列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getGridAndSupervision", "/api/getGridAndSupervision", "/getGridAndSupervision/sso"})
    public JsonResponse getGridAndSupervision(@RequestParam(required = false) String currentUserCode ,
                                            @RequestParam(required = false) String source) {
        return usGidSupervisionService.getGridAndSupervision(currentUserCode , source);
    }

}
