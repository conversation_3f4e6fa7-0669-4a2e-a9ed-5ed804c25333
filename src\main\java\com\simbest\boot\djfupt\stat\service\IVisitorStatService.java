package com.simbest.boot.djfupt.stat.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.stat.model.VisitorStat;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <strong>Title : IVisitorStatService</strong><br>
 * <strong>Description : 访客统计分析服务接口</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
public interface IVisitorStatService extends ILogicService<VisitorStat, String> {

    /**
     * 保存访客统计数据
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param visitorStat 访客统计数据
     * @return 操作结果
     */
    JsonResponse saveVisitorStat(String source, String currentUserCode, VisitorStat visitorStat);

    /**
     * 更新访客统计数据
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param visitorStat 访客统计数据
     * @return 操作结果
     */
    JsonResponse updateVisitorStat(String source, String currentUserCode, VisitorStat visitorStat);

    /**
     * 根据ID查询访客统计数据
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param id 访客统计数据ID
     * @return 访客统计数据
     */
    JsonResponse findVisitorStatById(String source, String currentUserCode, String id);

    /**
     * 删除访客统计数据
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param id 访客统计数据ID
     * @return 操作结果
     */
    JsonResponse deleteVisitorStat(String source, String currentUserCode, String id);

    /**
     * 查询访客统计数据列表
     * @param visitorStat 查询条件
     * @param pageable 分页参数
     * @return 访客统计数据列表
     */
    JsonResponse findAllVisitorStat(VisitorStat visitorStat, Pageable pageable);

    /**
     * 统计各单位的访问量
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @return 单位访问量统计结果
     */
    JsonResponse countVisitsByCompany(String source, String currentUserCode);

    /**
     * 根据条件查询访客统计数据
     * @param page 页码
     * @param size 每页大小
     * @param direction 排序方向
     * @param properties 排序属性
     * @param params 查询参数
     * @return 访客统计数据列表
     */
    JsonResponse queryVisitorStatByCondition(int page, int size, String direction, String properties, Map<String, Object> params);

    /**
     * 查询访客统计数据的公司层级树结构
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @return 公司层级树结构数据
     */
    JsonResponse getCompanyHierarchyTree(String source, String currentUserCode);

    /**
     * 同步会话数据到访客统计
     * @param username 用户名
     * @param conversationId 会话ID
     * @return 操作结果
     */
    JsonResponse syncConversationData(String username, String conversationId,String apiKey);

    /**
     * 新的查询接口 - 支持模糊查询和分页
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param page 页码
     * @param size 每页大小
     * @param direction 排序方向
     * @param properties 排序属性
     * @param keyword 关键词（模糊搜索）
     * @return 查询结果，包含分页数据
     */
    JsonResponse newFindAll(String source, String currentUserCode, int page, int size, String direction, String properties, String keyword);

    /**
     * 统计访客数据总数和sum字段总和，按belongCompanyName分类
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param params 查询参数
     * @return 统计结果，包含按公司分类的统计数据
     */
    JsonResponse findCount(String source, String currentUserCode, Map<String, Object> params);

    /**
     * 导出访客统计数据
     * @param source 来源
     * @param currentUserCode 当前用户代码
     * @param response HTTP响应对象
     * @param request HTTP请求对象
     */
    void exportVisitorStat(String source, String currentUserCode, HttpServletResponse response, HttpServletRequest request);
}
