package com.simbest.boot.djfupt.util.kms;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.simbest.boot.djfupt.util.BpsConfig;
import com.simbest.boot.djfupt.util.SignatureUtil;
import com.simbest.boot.djfupt.util.kms.vo.KeyWordResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class KMSUtils {

	private final static String appkey = "dee756dd2b78c712e395c8668134915c";

	private final static Integer KEYWORD_RESPONSE_SUCCESS = 0; //关键词分词接口返回结果 0 成功

	@Autowired
	private BpsConfig bpsConfig;

	public  List<String> getKeyWords(String keyWord){
		List<String> keyWords = new ArrayList<>();
		if(StringUtils.isNotEmpty(keyWord)){
			try{
				//获取时间戳
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
				String timestamp = LocalDateTime.now().format(formatter) + "000";
				String signature = SignatureUtil.signature(timestamp);
				Map<String,String> contentMap = new HashMap<>();
				contentMap.put("keyword",keyWord);

				String keyWordUrl = bpsConfig.kmsAddress + "/api/kms/keywords/noToken/noTenant/participle";
				//String keyWordUrl = "http://localhost:9070/api/kms/keywords/noToken/noTenant/participle"; //本地测试 配合 soapui模拟返回使用
				HttpPost post = new HttpPost(keyWordUrl);
				post.addHeader("Content-type", "application/json; charset=utf-8");
				post.setHeader("Accept", "application/json");
				post.addHeader("appkey", appkey);
				post.addHeader("timestamp", timestamp);
				post.addHeader("signature", signature);

				log.info("AI数智化分词检索、contentMap：" + JSONObject.toJSON(contentMap).toString());
				post.setEntity(new StringEntity(JSONObject.toJSON(contentMap).toString(), Charset.forName("UTF-8")));
				org.apache.http.client.HttpClient client = HttpClientBuilder.create().build();
				HttpResponse res = client.execute(post);
				org.apache.http.HttpEntity entity = res.getEntity();
				String result = EntityUtils.toString(entity);
				log.info("AI智能化分词检索, 查询结果：" + result.replace("\n","").replace("\r","").replace(" ",""));

				KeyWordResponseVo keyWordResponse = JSONUtil.toBean(result, KeyWordResponseVo.class);
				if(KEYWORD_RESPONSE_SUCCESS == keyWordResponse.getCode()){
					keyWords = keyWordResponse.getKeyWords();
					log.info("AI智能化分词检索, keyWords: "+ JSONUtil.toJsonStr(keyWords));
				}

			}catch (Exception e){
				log.error("get kms keyword fail,error:{}", e);
			}

		}
		return keyWords;
	}


}
