package com.simbest.boot.djfupt.findfaults.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.util.PageTool;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.findfaults.model.ExportFaultsCountInfo;
import com.simbest.boot.djfupt.findfaults.model.ExportFaultsInfo;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsIdea;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsModel;
import com.simbest.boot.djfupt.findfaults.repository.UsFindFaultsRepository;
import com.simbest.boot.djfupt.findfaults.service.ISysFindFaultsIdeaService;
import com.simbest.boot.djfupt.findfaults.service.ISysFindFaultsService;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import com.simbest.boot.djfupt.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.security.IAuthService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimplePosition;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysUserGroupApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SysFindFaultsServiceImpl extends LogicService<UsFindFaultsModel, String> implements ISysFindFaultsService {

    private UsFindFaultsRepository repository;
    @Autowired
    private UumsSysUserGroupApi uumsSysUserGroupApi;

    @Autowired
    public SysFindFaultsServiceImpl(UsFindFaultsRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    String param1 = "/action/findFaults";

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;

    @Autowired
    private ISysFindFaultsIdeaService iSysFindFaultsIdeaService;

    @Autowired
    private SysFileService sysFileService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private ISysFindFaultsIdeaService ideaService;

    @Override
    public JsonResponse saveDraft(String source, String currentUserCode, UsFindFaultsModel usFindFaultsModel) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "carRepair=" + usFindFaultsModel.toString() + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 保存草稿
            if (StringUtils.isEmpty(usFindFaultsModel.getId())) {
                // 保存业务单据信息
                this.saveBusinessData(usFindFaultsModel);
            }
            // 更新草稿
            else {
                //更新业务单据信息
                this.updateBusinessData(usFindFaultsModel);
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usFindFaultsModel, Constants.MESSAGE_SUCCESS);
    }

    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId) {
        JsonResponse jsonResponse = new JsonResponse();
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                UsFindFaultsModel form = null;
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    form = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), UsFindFaultsModel.class);
                } else {
                    if (StringUtils.isNotEmpty(formId) && "MOBILE".equals(source)) {
                        form = this.findById(formId);
                    }
                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }
                tempList = (List<Map<String, String>>) map.get("copyNextUserNames");
                String copyNextUserNames = "";
                if (null != tempList && !tempList.isEmpty()) {
                    for (Map<String, String> mapObj : tempList) {
                        String copyName = mapObj.get("value");
                        if (!org.springframework.util.StringUtils.isEmpty(copyName)) {
                            copyNextUserNames = copyName + "," + copyNextUserNames;
                        }
                    }
                }
                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;
                String copyMessage = map.get("copyNextUserName") != null ? map.get("message").toString() : null;

                /**如果表单的id不为空，则不是起草环节，走审批流程**/
                if (form != null && form.getId() != null && StringUtils.isNotEmpty(workItemId)) {
                    //起草环节
                    jsonResponse = saveSubmitTask(form, workItemId, outcome, message, nextUserName, location, copyLocation, copyMessage, copyNextUserNames, notificationId, source, currentUserCode);
                } else {
                    //审批流程环节
                    jsonResponse = startProcess(form, nextUserName, outcome, message, source, currentUserCode);//创建提交
                }
            }
        }
        return jsonResponse;
    }

    @Override
    public JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location) {
        UsFindFaultsModel usFindFaultsModel = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source +
                ",userCode=" + userCode + ",location=" + location + ",pmInsId=" + pmInsId;
        operateLog.setInterfaceParam(params);
        try {
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**点击办理查看详情**/
            usFindFaultsModel = repository.getFromDetail(pmInsId);
            if (usFindFaultsModel != null) {
                operateLog.setBussinessKey(usFindFaultsModel.getPmInsId());
                //获取意见列表
                Specification<UsFindFaultsIdea> specification = Specifications.<UsFindFaultsIdea>and()
                        .eq("enabled", Boolean.TRUE)
                        .eq("pmInsId", pmInsId).build();
                List<UsFindFaultsIdea> allNoPage = iSysFindFaultsIdeaService.findAllNoPage(specification);
                if (CollectionUtil.isNotEmpty(allNoPage)) {
                    for (UsFindFaultsIdea usFindFaultsIdea : allNoPage) {
                        String id = usFindFaultsIdea.getId();
                        Specification<SysFile> sysFileSpecification = Specifications.<SysFile>and()
                                .eq("enabled", Boolean.TRUE)
                                .eq("pmInsId", id).build();
                        List<SysFile> allNoPage1 = sysFileService.findAllNoPage(sysFileSpecification);
                        if (CollectionUtil.isNotEmpty(allNoPage1)) {
                            usFindFaultsIdea.setSysFiles(allNoPage1);
                        }
                    }
                    usFindFaultsModel.setUsFindFaultsIdea(allNoPage);
                }
                //获取附件
            }


        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(usFindFaultsModel);
    }

    @Override
    public JsonResponse queryAllFaultsInfo(Map<String, String> map, Integer page, Integer size, String faultsType) {
        String companyName = MapUtil.getStr(map, "companyName");
        String appName = MapUtil.getStr(map, "appName");
        String gridName = MapUtil.getStr(map, "gridName");
        String startDate = MapUtil.getStr(map, "startDate");
        String endDate = MapUtil.getStr(map, "endDate");
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" select t.company_name," +
                "       t.department_name," +
                "       t.apply_user," +
                "       t.apply_user_phone," +
                "       i.faults_app_name app_name," +
                "       to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') as created_time," +
                "       t.pm_ins_id," +
                "       act.process_inst_id," +
                "       act.id" +
                "  from US_FIND_FAULTS_MODEL t," +
                "       US_FIND_FAULTS_IDEA  i," +
                "       act_business_status  act" +
                " where t.enabled = 1" +
                "   and i.enabled = 1" +
                "   and act.enabled = 1" +
                "   and act.receipt_code = t.pm_ins_id" +
                "   and t.pm_ins_id = i.pm_ins_id ");
        if (StringUtils.isNotEmpty(faultsType)) {
            baseSql.append(" and  t.faults_type = :faultsType ");
            paramMap.put("faultsType", faultsType);
        }

        if (StringUtils.isNotEmpty(companyName)) {
            baseSql.append(" and  t.company_name = :companyName ");
            paramMap.put("companyName", companyName);
        }
        if (StringUtils.isNotEmpty(appName)) {
            baseSql.append(" and  i.faults_app_name like :appName ");
            paramMap.put("appName", "%" + appName + "%" );
        }
        if (StringUtils.isNotEmpty(gridName)) {
            baseSql.append(" and  t.grid_name = :gridName ");
            paramMap.put("gridName", gridName);
        }
        if (StringUtil.isNotEmpty(startDate)) {
            baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') >= :startDate");
            paramMap.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(endDate)) {
            baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') <= :endDate");
            paramMap.put("endDate", endDate);
        }
        baseSql.append(" order by t.created_time  desc,i.created_time desc");

        List<Map<String, Object>> mapList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);

        // 按部门权限过滤
        JsonResponse response = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_ADMIN, Constants.APP_CODE);
        String zcAdmin = Optional.ofNullable(response).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
        response = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_F_ADMIN, Constants.APP_CODE);
        String zcFAdmin = Optional.ofNullable(response).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
        List<SimpleUser> userByRoleNoPage = uumsSysUserinfoApi.findUserByRoleNoPage(Constants.FJFUPT_BRO, Constants.APP_CODE);
        String djAdmin = Optional.ofNullable(userByRoleNoPage).map(v -> v.stream().map(SimpleUser::getUsername).collect(Collectors.joining(","))).orElse("");

        IUser user = SecurityUtils.getCurrentUser();
        // 管理员或者 党建指导员
        if (djAdmin.contains(user.getUsername())
                || zcAdmin.contains(user.getUsername())) {
            // 不做过滤
        } else if (zcFAdmin.contains(user.getUsername())) {
            // 分公司管理人员
            mapList = mapList.stream().filter(v -> Objects.equals(BelongInfoTool.getBelongCompanyName(), MapUtil.getStr(v, "COMPANY_NAME"))).collect(Collectors.toList());
        } else {
            // 非管理人员
            mapList = Lists.newArrayList();
        }

        Pageable pageable = getPageable(page, size, null, null);
        if (CollectionUtil.isNotEmpty(mapList)) {
            mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList(mapList);
            List<Map<String, Object>> pagination = PageTool.pagination(mapList, page, size);
            PageImpl assetsInfoPage = new PageImpl(pagination, pageable, mapList.size());
            return JsonResponse.success(assetsInfoPage);
        }

        return JsonResponse.success(pageable);
    }

    @Override
    public void exportParameter(String companyName, String startDate, String endDate, String appName, String gridName, HttpServletRequest request, HttpServletResponse response, String faultsType) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" select t.company_name," +
                    "       t.department_name," +
                    "       t.apply_user," +
                    "       t.apply_user_phone," +
                    "       i.faults_app_name app_name," +
                    "       to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') as created_time,t.grid_name," +
                    "       (case" +
                    "         when t.status = 0 then" +
                    "          '未处理'" +
                    "         when t.status = 1 then" +
                    "          '解决中'" +
                    "         when t.status = 2 then" +
                    "          '已解决未归档'" +
                    "         when t.status = 3 then" +
                    "          '已解决归档'" +
                    "       end) as status,i.idea_des,t.feed_back_idea" +
                    "  from US_FIND_FAULTS_MODEL t, US_FIND_FAULTS_IDEA i,act_business_status  act" +
                    " where t.enabled = 1" +
                    "   and i.enabled = 1" +
                    "   and act.enabled = 1" +
                    "   and act.receipt_code = t.pm_ins_id" +
                    "   and t.pm_ins_id = i.pm_ins_id ");
            if (StringUtils.isNotEmpty(companyName)) {
                baseSql.append(" and t.company_name = :companyName ");
                paramMap.put("companyName", companyName);
            }
            if (StringUtils.isNotEmpty(faultsType)) {
                baseSql.append(" and  t.faults_type = :faultsType ");
                paramMap.put("faultsType", faultsType);
            }

            if (StringUtils.isNotEmpty(appName)) {
                baseSql.append(" and i.faults_app_name = :appName ");
                paramMap.put("appName", appName);
            }
            if (StringUtils.isNotEmpty(gridName)) {
                baseSql.append(" and t.grid_name = :gridName ");
                paramMap.put("gridName", gridName);
            }
            if (StringUtil.isNotEmpty(startDate)) {
                baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') >= :startDate");
                paramMap.put("startDate", startDate);
            }
            if (StringUtil.isNotEmpty(endDate)) {
                baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') <= :endDate");
                paramMap.put("endDate", endDate);
            }
            baseSql.append(" order by t.created_time  desc,i.created_time desc");

            List<Map<String, Object>> listMap = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);

            // 按部门权限过滤
            JsonResponse response1 = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_ADMIN, Constants.APP_CODE);
            String zcAdmin = Optional.ofNullable(response1).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
            response1 = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_F_ADMIN, Constants.APP_CODE);
            String zcFAdmin = Optional.ofNullable(response1).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
            List<SimpleUser> userByRoleNoPage = uumsSysUserinfoApi.findUserByRoleNoPage(Constants.FJFUPT_BRO, Constants.APP_CODE);
            String djAdmin = Optional.ofNullable(userByRoleNoPage).map(v -> v.stream().map(SimpleUser::getUsername).collect(Collectors.joining(","))).orElse("");

            IUser user = SecurityUtils.getCurrentUser();
            // 管理员或者 党建指导员
            if (djAdmin.contains(user.getUsername())
                    || zcAdmin.contains(user.getUsername())) {
                // 不做过滤
            } else if (zcFAdmin.contains(user.getUsername())) {
                // 分公司管理人员
                listMap = listMap.stream().filter(v -> Objects.equals(BelongInfoTool.getBelongCompanyName(), MapUtil.getStr(v, "company_name"))).collect(Collectors.toList());
            } else {
                // 非管理人员
                listMap = Lists.newArrayList();
            }

            listMap = com.simbest.boot.util.MapUtil.formatHumpNameForList(listMap);

            String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

            String json = JacksonUtils.obj2json(listMap);

            List<ExportFaultsInfo> appFunsLogCounts = JacksonUtils.json2Type(json, new TypeReference<List<ExportFaultsInfo>>() {
            });
            //设置导出Excel的名称
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = " 找茬活动统计" + dateStr + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");

            response.setHeader("Content-Type", "application/msexcel");
            response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<ExportFaultsInfo> exportUtil = new ExcelUtil<>(ExportFaultsInfo.class);
            exportUtil.exportExcel(appFunsLogCounts, "找茬活动统计", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }


    /**
     * 提交起草流程
     *
     * @param usFindFaultsModel 割接计划表单
     * @param nextUserName      审批人
     * @param outcome           连线规则
     * @param message           审批意见
     * @param source            来源
     * @param userCode          当前用户
     * @return
     */
    public JsonResponse startProcess(UsFindFaultsModel usFindFaultsModel, String nextUserName, String outcome, String message, String source, String userCode) {
        log.debug("起草接口----------startProcess---------->" + usFindFaultsModel.toString());
        long ret = 0;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",userCode=" + userCode + ",applicationForm=" + usFindFaultsModel.toString() + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser iuser = SecurityUtils.getCurrentUser();

            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                /**校验表单和下一步审批人是否为空**/
                if (StringUtils.isNotEmpty(nextUserName)) {
                    /**获取登录人所在公司应启动的流程**/

                    Map<String, String> map = commonService.getProcessMap("D");
                    String processDefId = map.get("processName");
                    String processType = map.get("processType");
                    if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                        boolean flag = false;
                        UsPmInstence usPmInstence = new UsPmInstence();
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        if (StringUtils.isEmpty(usFindFaultsModel.getId())) {
                            flag = this.savePlanTask(usFindFaultsModel, usPmInstence);
                        } else {
                            usPmInstence = usPmInstenceService.findByPmInsId(usFindFaultsModel.getPmInsId());
                            flag = true;

                            usFindFaultsModel.setStatus(1);
                            this.update(usFindFaultsModel);
                        }
                        /**启动发起流程**/
                        if (flag) {
                            Map<String, Object> variables = Maps.newHashMap();
                            String currentUserCode = iuser.getUsername();
                            String currentUserName = iuser.getTruename();
                            variables.put("inputUserId", currentUserCode);
                            variables.put("receiptId", usPmInstence.getId());
                            variables.put("title", usPmInstence.getPmInsTitle());
                            variables.put("code", usPmInstence.getPmInsId());
                            variables.put("currentUserCode", currentUserCode);
                            variables.put("activityDefID", Constants.ACTIVITY_START);
                            variables.put("appCode", Constants.APP_CODE);
                            //第一个参数为流程定义名称
                            Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                            if (workItemId != 0) {
                                /**提交表单审批处理**/
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                operateLog.setErrorMsg("启动流程失败");
                                JsonResponse.fail(null, "启动流程失败");
                            }
                        } else {
                            operateLog.setErrorMsg("保存割接计划失败");
                            JsonResponse.fail(null, "保存割接计划失败");
                        }
                    } else {
                        operateLog.setErrorMsg("获取流程失败");
                        JsonResponse.fail(null, "操作失败，获取流程失败!");
                    }
                } else {
                    operateLog.setErrorMsg("表单为空或审批人为空");
                    JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
                }
            } else {
                Map<String, String> map = commonService.getProcessMap("D");
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                    boolean flag = false;
                    UsPmInstence usPmInstence = new UsPmInstence();
                    usPmInstence.setPmInsType(processType);
                    /**保存业务数据**/
                    if (StringUtils.isEmpty(usFindFaultsModel.getId())) {
                        flag = this.savePlanTask(usFindFaultsModel, usPmInstence);
                    } else {
                        usPmInstence = usPmInstenceService.findByPmInsId(usFindFaultsModel.getPmInsId());
                        flag = true;
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);
                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (!"end".equals(outcome) && !"djfupt.abolishend".equals(outcome)) {
                                if (StringUtils.isNotEmpty(nextUserName)) {
                                    ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);
                                } else {
                                    operateLog.setErrorMsg("获取审批人失败");
                                    JsonResponse.fail(null, "获取审批人失败");
                                }
                            } else {
                                ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);

                            }

                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存割接计划失败");
                        JsonResponse.fail(null, "保存割接计划失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
    }

    private boolean savePlanTask(UsFindFaultsModel usFindFaultsModel, UsPmInstence usPmInstence) {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存表单**/
        try {
            if (StringUtils.isEmpty(usFindFaultsModel.getId())) {
                Map<String, String> map = commonService.getProcessMap("D");
                String processName = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    String pmInsId = processType + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                    String title = "找茬活动上报";
                    usPmInstence.setPmInsId(pmInsId);
                    usPmInstence.setPmInsTitle(title);
                    usPmInstence.setPmInsType(processType);
                    usPmInstence.setProcessName(processName);
                    usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                    usPmInstenceService.insert(usPmInstence);
                }
            }
            String usPmId = usPmInstence.getId();
            String pmInsId = usPmInstence.getPmInsId();
            if (StringUtils.isNotEmpty(usPmId)) {
                usFindFaultsModel.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usFindFaultsModel.setOrgCode(iuser.getBelongOrgCode());
                usFindFaultsModel.setOrgName(iuser.getBelongOrgName());
                usFindFaultsModel.setCompanyCode(iuser.getBelongCompanyCode());
                usFindFaultsModel.setCompanyName(iuser.getBelongCompanyName());
                usFindFaultsModel.setDepartmentCode(iuser.getBelongDepartmentCode());
                usFindFaultsModel.setDepartmentName(iuser.getBelongDepartmentName());
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usFindFaultsModel.setCompanyCode(iuser.getBelongCompanyCodeParent());
                    usFindFaultsModel.setCompanyName(iuser.getBelongCompanyNameParent());
                    usFindFaultsModel.setDepartmentCode(iuser.getBelongCompanyCode());
                    usFindFaultsModel.setDepartmentName(iuser.getBelongCompanyName());
                }
                usFindFaultsModel.setApplyUserName(iuser.getUsername());
                usFindFaultsModel.setPmInsId(usPmInstence.getPmInsId());
                usFindFaultsModel.setPmInsId(pmInsId);
                usFindFaultsModel.setStatus(1);
                usFindFaultsModel.setCreator(iuser.getUsername());
                usFindFaultsModel.setModifier(iuser.getUsername());
                usFindFaultsModel.setEnabled(Boolean.TRUE);
                UsFindFaultsModel findFaultsModel = this.insert(usFindFaultsModel);
                if (StringUtils.isNotEmpty(findFaultsModel.getId())) {
                    this.saveIdeaByPmInsId(findFaultsModel, iuser);//更新意见建议
                }
                return true;
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }


    private JsonResponse saveSubmitTask(UsFindFaultsModel usFindFaultsModel, String workItemId, String outcome, String message, String nextUserName, String location, String copyLocation, String copyMessage, String copyNextUserNames, String notificationId, String source, String userCode) {
        log.debug("起草接口----------saveSubmitTask---------->" + usFindFaultsModel.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "applicationForm=" + usFindFaultsModel.toString() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + copyLocation + ",copyMessage"
                + copyMessage + ",copyNextUserNames=" + copyNextUserNames + ",notificationId=" + notificationId + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            String pmInsId = usFindFaultsModel.getPmInsId();
            operateLog.setBussinessKey(pmInsId);
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**相关流转审批操作**/
            if (pmInstence != null) {

                /**获取用户**/
                IUser user = SecurityUtils.getCurrentUser();
                /**审批流转**/
                if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                    if ((!"end".equals(outcome) && StringUtils.isNotEmpty(nextUserName)) || "end".equals(outcome)) {
                        ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence);
                        //退回修改
                        if ("djfupt.start.to.provincialManager".equals(outcome)) {
                            IUser iuser = SecurityUtils.getCurrentUser();
                            //删除之前的问题和意见列表
                            this.deleteIdeaByPmInsId(usFindFaultsModel.getPmInsId());
                            //保存新的问题和意见列表
                            this.saveIdeaByPmInsId(usFindFaultsModel, iuser);
                            usFindFaultsModel.setStatus(1);
                            usFindFaultsModel = this.update(usFindFaultsModel);
                        }
                        //如果是请起草人确认环节，需要修改工单的状态为一办理未归档
                        if (("djfupt.provinceManager.to.end").equals(outcome) || ("djfupt.assist.to.end").equals(outcome)) {
                            usFindFaultsModel.setStatus(2);
                            usFindFaultsModel = this.update(usFindFaultsModel);
                        }
                        //如果是归档环节，修改公道状态为已解决并归档
                        if ("end".equals(outcome)) {
                            usFindFaultsModel.setStatus(3);
                            usFindFaultsModel = this.update(usFindFaultsModel);
                        }
                        //保存反馈意见
                        if ("djfupt.assist.to.end".equals(outcome) || "djfupt.provinceManager.to.end".equals(outcome) || "djpupt.provinceManager.to.assist".equals(outcome)
                                || "djfupt.assist.to.assist".equals(outcome) || "djfupt.provinceManager.to.start".equals(outcome) || "djfupt.assist.to.start".equals(outcome)) {
                            this.update(usFindFaultsModel);
                        }
                    } else {
                        ret = 0;
                        operateLog.setErrorMsg("审批人不能为空");
                        return JsonResponse.fail("审批人不能为空");
                    }
                }
            } else {
                operateLog.setErrorMsg("请联系管理员  ，主数据查找异常！pmInsId = " + pmInsId);
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
            /**提醒流转下一步信息**/
            String showMessage = this.getTemplate(nextUserName);
            return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        }
    }

    private long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence) {
        long ret;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            workItemService.submitApprovalMsg(workItemID, message);
            //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
            ret = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }

    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(nextUserName)) {
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                }
            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }

        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }

    /**
     * 修改业务单据信息
     *
     * @param usFindFaultsModel
     * @return
     */
    private void updateBusinessData(UsFindFaultsModel usFindFaultsModel) {
        IUser iuser = SecurityUtils.getCurrentUser();
        //删除之前的问题和意见列表
        this.deleteIdeaByPmInsId(usFindFaultsModel.getPmInsId());
        //保存新的问题和意见列表
        this.saveIdeaByPmInsId(usFindFaultsModel, iuser);

        usFindFaultsModel.setStatus(0);
        this.update(usFindFaultsModel);
    }

    private void deleteIdeaByPmInsId(String pmInsId) {
        Specification<UsFindFaultsIdea> specification = Specifications.<UsFindFaultsIdea>and()
                .eq("pmInsId", pmInsId).build();
        List<UsFindFaultsIdea> allNoPage = iSysFindFaultsIdeaService.findAllNoPage(specification);
        if (CollectionUtil.isNotEmpty(allNoPage)) {
            for (UsFindFaultsIdea usFindFaultsIdea : allNoPage) {
                iSysFindFaultsIdeaService.deleteById(usFindFaultsIdea.getId());
            }
        }
    }

    /**
     * 保存业务单据信息
     *
     * @param usFindFaultsModel
     * @return
     */
    private Map<String, Object> saveBusinessData(UsFindFaultsModel usFindFaultsModel) {
        Map<String, Object> result = Maps.newHashMap();
        IUser iuser = SecurityUtils.getCurrentUser();
        UsPmInstence usPmInstence = new UsPmInstence();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StringUtils.isEmpty(usFindFaultsModel.getId())) {
                Map<String, String> map = commonService.getProcessMap("D");
                String processName = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processName) && StringUtils.isNotEmpty(processType)) {
                    String pmInsId = processType + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                    String title = "找茬活动上报";
                    usPmInstence.setPmInsId(pmInsId);
                    usPmInstence.setPmInsTitle(title);
                    usPmInstence.setPmInsType(processType);
                    usPmInstence.setProcessName(processName);
                    usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                    usPmInstenceService.insert(usPmInstence);
                }
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            String pmInsId = usPmInstence.getPmInsId();
            if (StringUtils.isNotEmpty(usPmId)) {
                usFindFaultsModel.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usFindFaultsModel.setOrgCode(iuser.getBelongOrgCode());
                usFindFaultsModel.setOrgName(iuser.getBelongOrgName());
                usFindFaultsModel.setCompanyCode(iuser.getBelongCompanyCode());
                usFindFaultsModel.setCompanyName(iuser.getBelongCompanyName());
                usFindFaultsModel.setDepartmentCode(iuser.getBelongDepartmentCode());
                usFindFaultsModel.setDepartmentName(iuser.getBelongDepartmentName());
                if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                    usFindFaultsModel.setCompanyCode(iuser.getBelongCompanyCodeParent());
                    usFindFaultsModel.setCompanyName(iuser.getBelongCompanyNameParent());
                    usFindFaultsModel.setDepartmentCode(iuser.getBelongCompanyCode());
                    usFindFaultsModel.setDepartmentName(iuser.getBelongCompanyName());
                }
                usFindFaultsModel.setApplyUserName(iuser.getUsername());
                usFindFaultsModel.setPmInsId(usPmInstence.getPmInsId());
                usFindFaultsModel.setPmInsId(pmInsId);
                usFindFaultsModel.setStatus(0);
                usFindFaultsModel.setCreator(iuser.getUsername());
                usFindFaultsModel.setModifier(iuser.getUsername());
                usFindFaultsModel.setEnabled(Boolean.TRUE);
                UsFindFaultsModel findFaultsModel = this.insert(usFindFaultsModel);
                if (StringUtils.isNotEmpty(findFaultsModel.getId())) {
                    this.saveIdeaByPmInsId(findFaultsModel, iuser);//更新意见建议
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return result;
        }
        return result;
    }

    private void saveIdeaByPmInsId(UsFindFaultsModel findFaultsModel, IUser iuser) {
        List<UsFindFaultsIdea> usFindFaultsIdea = findFaultsModel.getUsFindFaultsIdea();
        if (CollectionUtil.isNotEmpty(usFindFaultsIdea)) {
            for (UsFindFaultsIdea findFaultsIdea : usFindFaultsIdea) {
                findFaultsIdea.setPmInsId(findFaultsModel.getPmInsId());
                findFaultsIdea.setCreator(iuser.getUsername());
                findFaultsIdea.setModifier(iuser.getUsername());
                findFaultsIdea.setEnabled(Boolean.TRUE);
                findFaultsIdea.setPmInsId(findFaultsModel.getPmInsId());
                UsFindFaultsIdea insert = iSysFindFaultsIdeaService.insert(findFaultsIdea);
                //保存附件信息
                List<SysFile> sysFiles = findFaultsIdea.getSysFiles();
                if (CollectionUtil.isNotEmpty(sysFiles)) {
                    for (SysFile sysFile : sysFiles) {
                        sysFile.setPmInsId(insert.getId());
                        sysFileService.update(sysFile);
                    }
                }
            }
        }
    }

    @Override
    public JsonResponse queryAllFaultsCountInfo(Map<String, String> map, Integer page, Integer size, String faultsType) {
        String companyName = MapUtil.getStr(map, "companyName");
        String startDate = MapUtil.getStr(map, "startDate");
        String endDate = MapUtil.getStr(map, "endDate");
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" select name,display_order,all_Count,solving_Count, not_Filed_Count,Filed_Count from (select t1.name,t1.display_order," +
                "         sum((case when t2.status is not null and t2.status != 0 then 1 else  0  end)) all_Count," +
                "         sum((case when t2.status = 1 then 1 else  0  end)) as solving_Count," +
                "         sum((case when t2.status = 2 then 1 else  0  end)) as not_Filed_Count," +
                "         sum((case when t2.status = 3 then 1 else  0  end)) as Filed_Count" +
                "    from (SELECT dv.name, dv.display_order" +
                "            from sys_dict d, sys_dict_value dv" +
                "           where d.dict_type = dv.dict_type" +
                "             and d.enabled = 1" +
                "             and dv.enabled = 1" +
                "             and d.dict_type = 'company' ");
        if (StringUtils.isNotEmpty(companyName)) {
            baseSql.append(" and dv.name = :companyName ");
            paramMap.put("companyName", companyName);
        }
        baseSql.append(" order by dv.display_order asc) t1" +
                "    left join (select t.company_name, t.status" +
                "                 from US_FIND_FAULTS_MODEL t, US_FIND_FAULTS_IDEA i,act_business_status  act" +
                "                where t.enabled = 1" +
                "                  and i.enabled = 1 and act.enabled = 1 and act.receipt_code = t.pm_ins_id" +
                "                  and t.pm_ins_id = i.pm_ins_id ");
        if (StringUtil.isNotEmpty(startDate)) {
            baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') >= :startDate");
            paramMap.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(endDate)) {
            baseSql.append(" and to_char(t.modified_time, 'yyyy-MM-dd HH24:mi:ss') <= :endDate");
            paramMap.put("endDate", endDate);
        }
        if (StringUtil.isNotEmpty(faultsType)) {
            baseSql.append(" and  t.faults_type = :faultsType ");
            paramMap.put("faultsType", faultsType);
        }
        baseSql.append(" ) t2 on t1.name = t2.company_name  group by t1.name,t1.display_order) order by display_order");

        List<Map<String, Object>> mapList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);

        // 按部门权限过滤
        JsonResponse response = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_ADMIN, Constants.APP_CODE);
        String zcAdmin = Optional.ofNullable(response).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
        response = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_F_ADMIN, Constants.APP_CODE);
        String zcFAdmin = Optional.ofNullable(response).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
        IUser user = SecurityUtils.getCurrentUser();

        // 分公司管理人员
        if (zcFAdmin.contains(user.getUsername())) {
            mapList = mapList.stream().filter(v -> Objects.equals(BelongInfoTool.getBelongCompanyName(), MapUtil.getStr(v, "name"))).collect(Collectors.toList());
        }
        // 非管理人员
        if (!zcFAdmin.contains(user.getUsername()) && !zcAdmin.contains(user.getUsername())) {
            mapList = Lists.newArrayList();
        }

        Pageable pageable = getPageable(page, size, null, null);
        if (CollectionUtil.isNotEmpty(mapList)) {
            mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList(mapList);
            List<Map<String, Object>> pagination = PageTool.pagination(mapList, page, size);
            PageImpl assetsInfoPage = new PageImpl(pagination, pageable, mapList.size());
            return JsonResponse.success(assetsInfoPage);
        }

        return JsonResponse.success(pageable);
    }

    @Override
    public void exportAllFaultsCountInfo(String companyName, String startDate, String endDate, HttpServletRequest request, HttpServletResponse response, String faultsType) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append("  select name,display_order,all_Count,solving_Count, not_Filed_Count,Filed_Count from (select t1.name,t1.display_order," +
                    "         sum((case when t2.status is not null and t2.status != 0 then 1 else  0  end)) all_Count," +
                    "         sum((case when t2.status = 1 then 1 else  0  end)) as solving_Count," +
                    "         sum((case when t2.status = 2 then 1 else  0  end)) as not_Filed_Count," +
                    "         sum((case when t2.status = 3 then 1 else  0  end)) as Filed_Count" +
                    "    from (SELECT dv.name, dv.display_order" +
                    "            from sys_dict d, sys_dict_value dv" +
                    "           where d.dict_type = dv.dict_type" +
                    "             and d.enabled = 1" +
                    "             and dv.enabled = 1" +
                    "             and d.dict_type = 'company' ");
            if (StringUtils.isNotEmpty(companyName)) {
                baseSql.append(" and dv.name = :companyName ");
                paramMap.put("companyName", companyName);
            }
            baseSql.append(" order by dv.display_order asc) t1" +
                    "    left join (select t.company_name, t.status" +
                    "                 from US_FIND_FAULTS_MODEL t, US_FIND_FAULTS_IDEA i,act_business_status  act" +
                    "                where t.enabled = 1" +
                    "                  and i.enabled = 1 and act.enabled = 1" +
                    "                  and t.pm_ins_id = i.pm_ins_id  and act.receipt_code = t.pm_ins_id");
            if (StringUtil.isNotEmpty(startDate)) {
                baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') >= :startDate");
                paramMap.put("startDate", startDate);
            }
            if (StringUtil.isNotEmpty(endDate)) {
                baseSql.append(" and to_char(t.created_time, 'yyyy-MM-dd HH24:mi:ss') <= :endDate");
                paramMap.put("endDate", endDate);
            }
            if (StringUtils.isNotEmpty(faultsType)){
                baseSql.append(" and  t.faults_type = :faultsType ");
                baseSql.append(" and  i.faults_type = :faultsType ");
                paramMap.put("faultsType", faultsType);
            }
            baseSql.append(" ) t2 on t1.name = t2.company_name group by t1.name,t1.display_order) order by display_order");

            List<Map<String, Object>> listMap = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);

            // 按部门权限过滤
            JsonResponse response1 = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_ADMIN, Constants.APP_CODE);
            String zcAdmin = Optional.ofNullable(response1).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
            response1 = uumsSysUserinfoApi.findUserByGroupSort(Constants.GROUP_ZC_F_ADMIN, Constants.APP_CODE);
            String zcFAdmin = Optional.ofNullable(response1).map(v -> String.join(",", ((Map<String, Object>) v.getData()).keySet())).orElse("");
            IUser user = SecurityUtils.getCurrentUser();

            // 分公司管理人员
            if (zcFAdmin.contains(user.getUsername())) {
                listMap = listMap.stream().filter(v -> Objects.equals(BelongInfoTool.getBelongCompanyName(), MapUtil.getStr(v, "name"))).collect(Collectors.toList());
            }
            // 非管理人员
            if (!zcFAdmin.contains(user.getUsername()) && !zcAdmin.contains(user.getUsername())) {
                listMap = Lists.newArrayList();
            }

            listMap = com.simbest.boot.util.MapUtil.formatHumpNameForList(listMap);

            String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

            String json = JacksonUtils.obj2json(listMap);

            List<ExportFaultsCountInfo> appFunsLogCounts = JacksonUtils.json2Type(json, new TypeReference<List<ExportFaultsCountInfo>>() {
            });
            //设置导出Excel的名称
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = " 找茬活动台账" + dateStr + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");

            response.setHeader("Content-Type", "application/msexcel");
            response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<ExportFaultsCountInfo> exportUtil = new ExcelUtil<>(ExportFaultsCountInfo.class);
            exportUtil.exportExcel(appFunsLogCounts, "找茬活动台账", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsFindFaultsModel usFindFaultsModel) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);
            usPmInstenceService.delete(pmInstence);
            // 删除业务单据数据
            this.delete(usFindFaultsModel);

            Specification<UsFindFaultsIdea> specification = Specifications.<UsFindFaultsIdea>and()
                    .eq("pmInsId", pmInsId).build();
            List<UsFindFaultsIdea> allNoPage = iSysFindFaultsIdeaService.findAllNoPage(specification);
            if (CollectionUtil.isNotEmpty(allNoPage)) {
                for (UsFindFaultsIdea usFindFaultsIdea : allNoPage) {
                    iSysFindFaultsIdeaService.deleteById(usFindFaultsIdea.getId());
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }

    @Override
    public JsonResponse getFormDetailByPmInsId(String pmInsId) {
        UsFindFaultsModel usFindFaultsModel = new UsFindFaultsModel();
        usFindFaultsModel = repository.getFromDetail(pmInsId);
        if (usFindFaultsModel != null) {
            //获取意见列表
            Specification<UsFindFaultsIdea> specification = Specifications.<UsFindFaultsIdea>and()
                    .eq("enabled", Boolean.TRUE)
                    .eq("pmInsId", pmInsId).build();
            List<UsFindFaultsIdea> allNoPage = iSysFindFaultsIdeaService.findAllNoPage(specification);
            if (CollectionUtil.isNotEmpty(allNoPage)) {
                for (UsFindFaultsIdea usFindFaultsIdea : allNoPage) {
                    String id = usFindFaultsIdea.getId();
                    Specification<SysFile> sysFileSpecification = Specifications.<SysFile>and()
                            .eq("enabled", Boolean.TRUE)
                            .eq("pmInsId", id).build();
                    List<SysFile> allNoPage1 = sysFileService.findAllNoPage(sysFileSpecification);
                    if (CollectionUtil.isNotEmpty(allNoPage1)) {
                        usFindFaultsIdea.setSysFiles(allNoPage1);
                    }
                }
            }
            usFindFaultsModel.setUsFindFaultsIdea(allNoPage);
            return JsonResponse.success(usFindFaultsModel);
        }
        return JsonResponse.fail("获取工单信息异常");
    }

    @Override
    public JsonResponse getUserGridName(String username) {
        StringBuffer sql = new StringBuffer("select t.*  from US_ADMIN_MANAGER t where t.enabled=1 and t.removed_time is null and t.user_name  = :username ");
        HashMap<String, Object> map = new HashMap<>();
        map.put("username", username);
        List<Map<String, Object>> mapList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
        String gridName = "";
        if (CollectionUtil.isNotEmpty(mapList)) {
            mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList(mapList);
            Map<String, Object> info = mapList.get(0);
            gridName = info.get("belongCompanyName") + "\\" + info.get("belongDepartmentName") + "\\" + info.get("gridName");
        }
        if (StringUtils.isEmpty(gridName)) {
            loginUtils.manualLogin(rsaEncryptor.encrypt(username), "djfupt");
            IUser currentUser = SecurityUtils.getCurrentUser();
            gridName = currentUser.getBelongCompanyName() + "\\" + currentUser.getBelongDepartmentName() + "\\" + currentUser.getBelongOrgName();
        }
        map.put("gridName", gridName);
        return JsonResponse.success(map);
    }
}
