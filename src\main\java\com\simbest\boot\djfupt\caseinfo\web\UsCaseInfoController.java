package com.simbest.boot.djfupt.caseinfo.web;


import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.caseinfo.model.CaseStatisticsVo;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 积极经验推广相关接口
 */
@Api(description = "积极经验推广相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/usCaseInfo")
public class UsCaseInfoController extends LogicController<UsCaseInfo, String> {

    private IUsCaseInfoService usCaseInfoService;

    @Autowired
    public UsCaseInfoController(IUsCaseInfoService usCaseInfoService) {
        super(usCaseInfoService);
        this.usCaseInfoService = usCaseInfoService;
    }

    @Autowired
    private PaginationHelps paginationHelp;

    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private OperateLogTool operateLogTool;

    String param1 = "/action/usCaseInfo";

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;


    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param notificationId  待阅id
     * @param bodyParam       提交参数
     * @param formId          表单id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "nextUserName", value = "审批人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "copyLocation", value = "抄送下一环节", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyMessage", value = "抄送意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyNextUserNames", value = "抄送人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
    })
    @PostMapping(value = {"/startSubmitProcess", "/api/startSubmitProcess", "/startSubmitProcess/sso", "/anonymous/startSubmitProcess"})
    public JsonResponse startSubmitProcess(@RequestParam String currentUserCode,
                                           @RequestParam String source,
                                           @RequestParam(required = false) String workItemId,
                                           @RequestParam(required = false) String outcome,
                                           @RequestParam(required = false) String location,
                                           @RequestParam(required = false) String copyLocation,
                                           @RequestParam(required = false) String notificationId,
                                           @RequestParam(required = false) String formId,
                                           @RequestBody Map<String, Object> bodyParam) throws Exception {
        return usCaseInfoService.startSubmitProcess(source, currentUserCode, workItemId, outcome, location, copyLocation, bodyParam, formId, notificationId);
    }


    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "userCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getFormDetail", "/api/getFormDetail", "/getFormDetail/sso", "/anonymous/getFormDetail"})
    public JsonResponse getFormDetail(@RequestParam(required = false) Long processInstId,
                                      @RequestParam(required = false) String workFlag,
                                      @RequestParam(required = false) String source,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String pmInsId,
                                      @RequestParam(required = false) String currentUserCode) {
        return usCaseInfoService.getFormDetail(processInstId, workFlag, source, currentUserCode, pmInsId, location);
    }

    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation(value = "查询决策", notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getDecisions", "/api/getDecisions", "/getDecisions/sso"})
    public JsonResponse getDecisions(
            @RequestParam(required = false) String processInstId,
            @RequestParam(required = false) String processDefName,
            @RequestParam String location,
            @RequestParam String source,
            @RequestParam(required = false) String questionMode,
            @RequestParam(required = false) String currentUserCode,
            @RequestParam String processType) {
        return iCommonService.getDecisions(processInstId, processDefName, location, source, currentUserCode, processType, param1, questionMode);
    }

    /**
     * 获取到决策下组织人员
     *
     * @param processInstId 流程实例id
     * @param appDecision   决策对象
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getOrgAndUser", "/api/getOrgAndUser", "/getOrgAndUser/sso"})
    public JsonResponse getOrgAndUser(@RequestParam String source,
                                      @RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestBody SimpleAppDecision appDecision) {
        return iCommonService.getOrgAndUser(processInstId, source, currentUserCode, appDecision, param1,null);
    }

    /**
     * 保存草稿
     *
     * @return
     */
    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/api/saveDraft", "/saveDraft/sso"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody UsCaseInfo usCaseInfo) {
        return usCaseInfoService.saveDraft(source, currentUserCode, usCaseInfo);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/api/deleteDraft", "/deleteDraft/sso"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody UsCaseInfo usCaseInfo) {
        return usCaseInfoService.deleteDraft(source, currentUserCode, pmInsId, usCaseInfo);
    }


    @PostMapping(value = {"/exportParameter", "/api/exportParameter", "/sso/exportParameter"})
    public void exportParameter(@RequestParam(required = false) String startDate,
                                @RequestParam(required = false) String endDate,
                                @RequestParam(required = false) String problemName,
                                @RequestParam(required = false) String companyCode,
                                @RequestParam(required = false) Integer page,
                                @RequestParam(required = false) Integer rows,
                                @RequestParam(required = false) String state,
                                @RequestParam(required = false) String source,
                                @RequestParam(required = false) String userCode,
                                HttpServletResponse response,
                                HttpServletRequest request) {
        rows = 99999;
        usCaseInfoService.exportParameter(page, rows, source, userCode, startDate, endDate, problemName, companyCode, response, request, state);
    }


    /**
     * 台账查询
     *
     * @param startTime            开始时间
     * @param endTime              结束时间
     * @param problemName          优秀案例名称
     * @param belongDepartmentCode
     * @return
     */
    @PostMapping(value = {"/queryAllUsCaseInfo", "/api/queryAllUsCaseInfo", "/queryAllUsCaseInfo/sso"})
    public JsonResponse queryAllUsCaseInfo(@RequestParam(required = false) String startTime,
                                           @RequestParam(required = false) String endTime,
                                           @RequestParam(required = false) String problemName,
                                           @RequestParam(required = false) String belongDepartmentCode,
                                           @RequestParam(required = false) Integer page,
                                           @RequestParam(required = false) Integer rows,
                                           @RequestParam(required = false) String source,
                                           @RequestParam(required = false) String userCode,
                                           @RequestParam(required = false) String state
    ) {
        return usCaseInfoService.queryApplication(page, rows, source, userCode, startTime, endTime, problemName, belongDepartmentCode, state);
    }


    /**
     * 思政工作纪实列表信息
     */
    @ApiOperation(value = "经验推广台账", notes = "经验推广台账")
    @PostMapping(value = {"/findAllApplication", "/api/findAllApplication", "/findAllApplication/sso"})
    public JsonResponse findAllApplication(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestParam(required = false) String source,
                                           @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                           @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                           @RequestParam(required = false) String properties, //排序规则（属性名称）
                                           @RequestBody(required = false) Map<String, Object> resultMap) {
        operateLogTool.operationSource(source, currentUserCode);
        List<Map<String, Object>> resultList = usCaseInfoService.findAllApplication(resultMap);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }

        return JsonResponse.success(null, "暂不支持改流程类型");

    }


    /**
     * 优秀案例数量
     */
    @ApiOperation(value = "优秀案例数量", notes = "优秀案例数量")
    @PostMapping(value = {"/caseCount", "/api/caseCount", "/caseCount/sso"})
    public JsonResponse caseCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usCaseInfoService.caseCount(resultMap);
        return JsonResponse.success(resultList);
    }


    /**
     * 优秀案例数量
     */
    @ApiOperation(value = "优秀案例数量", notes = "优秀案例数量")
    @PostMapping(value = {"/caseAllCount", "/api/caseAllCount", "/caseAllCount/sso"})
    public JsonResponse caseAllCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usCaseInfoService.caseAllCount(resultMap);
        return JsonResponse.success(resultList);
    }


    /**
     * 经验数据统计
     *
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/caseStatistics", "/api/caseStatistics", "/caseStatistics/sso", "/anonymous/caseStatistics"})
    public JsonResponse caseStatistics(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                       @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                       @RequestBody(required = false) Map<String, Object> resultMap,
                                       @RequestParam(required = false) String currentUserCode,
                                       @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        IUser user = SecurityUtils.getCurrentUser();
        List<CaseStatisticsVo> resultList = null;
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin =  simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));

        //省公司
        if (user.getBelongCompanyTypeDictValue().equals("01")||isAdmin) {
            resultList = usCaseInfoService.caseStatistics(page, rows, resultMap);
       } else {
            resultList = usCaseInfoService.caseStatisticsOther(page, rows, resultMap);
        }

        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "查询数据有误");
    }

    @PostMapping(value = {"/caseProblemStatistics", "/api/caseProblemStatistics", "/sso/caseProblemStatistics"})
    public void caseProblemStatistics(UsCaseInfo caseInfo,
                                      HttpServletResponse response,
                                      HttpServletRequest request,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", caseInfo.getStartTime());
        map.put("endTime", caseInfo.getEndTime());
        map.put("companyName", caseInfo.getCompanyName());
        usCaseInfoService.caseProblemStatistics(map, response, request);
    }

    @PostMapping(value = {"/updateCaseInfo", "/api/updateCaseInfo", "/sso/updateCaseInfo"})
    public JsonResponse updateCaseInfo(@RequestBody  UsCaseInfo caseInfo){
        return usCaseInfoService.updateCaseInfo(caseInfo);
    }

}
