package com.simbest.boot.djfupt.util;

import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2020/7/24</strong><br>
 * <strong>Modify on : 2020/7/24</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> l<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class FormatTool {

    /**
     * 下换线转小驼峰
     * @param data
     * @return
     */
    public static List<Map<String,Object>> formatConversion(List<Map<String,Object>> data) {
        List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
        try {
            for (Map<String,Object> m : data) {
                Set<String> keySet = m.keySet();
                Map<String, Object> newMap = Maps.newHashMap();
                for (String s : keySet) {
                    String[] split = s.split("_");
                    String newKey = "";
                    for (int i = 0 ; i < split.length ; i++) {
                        if(i == 0) {
                            newKey = split[i].toLowerCase();
                        } else {
                            newKey += split[i].substring(0,1) + split[i].substring(1).toLowerCase();
                        }
                    }
                    newMap.put(newKey,m.get(s));
                }
                result.add(newMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return result;
    }

    /**
     * 乘法运算
     *
     *
     * @param num1
     *            乘数
     * @param num2
     *            乘数
     */
    public static String mul(String num1, String num2) {
        num1 = getNumberStr(num1);
        num2 = getNumberStr(num2);
        BigDecimal b1 = new BigDecimal(num1);
        BigDecimal b2 = new BigDecimal(num2);
        b1 = b1.multiply(b2);
        return getNumberStr(b1);
    }

    /**
     * 乘法运算四舍五入并保留两位小数
     *
     *
     * @param num1
     *            乘数
     * @param num2
     *            乘数
     */
    public static String mulScale(String num1, String num2) {
        return mulScales(num1, num2, 2);
    }

    /**
     * 乘法运算四舍五入并保留相应小数位数
     *
     *
     * @param num1
     *            乘数
     * @param num2
     *            乘数
     * @param
     */
    public static String mulScales(String num1, String num2, int scale) {
        String mul = mul(num1, num2);
        return formatNumber(mul, scale);
    }

    /**
     * 除法运算
     *
     *
     * @param num1
     *            被除数
     * @param num2
     *            除数
     */
    public static String div(String num1, String num2) {
        num1 = getNumberStr(num1);
        num2 = getNumberStr(num2);
        BigDecimal b1 = new BigDecimal(num1);
        BigDecimal b2 = new BigDecimal(num2);
        try {
            b1 = b1.divide(b2, 50, BigDecimal.ROUND_HALF_UP);
            return getNumberStr(b1);
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 除法运算四舍五入并保留两位小数
     *
     *
     * @param num1
     *            被除数
     * @param num2
     *            除数
     */
    public static String divScale(String num1, String num2) {
        return divScale(num1, num2, 2);
    }

    /**
     * 除法运算四舍五入并保留相应小数位数
     *
     *
     * @param num1
     *            除数
     * @param num2
     *            被除数
     * @param
     */
    public static String divScale(String num1, String num2, int scale) {
        String div = div(num1, num2);
        return formatNumber(div, scale);
    }

    /**
     * 把一个数值类型的数四舍五入，并保留小数点后相应的位数
     *
     * @param num
     *            要四舍五入的数值
     * @param scale
     *            小数点后要保留的位数
     */
    public static String formatNumber(String num, Integer scale) {
        num = getNumberStr(num);
        BigDecimal numb = new BigDecimal(num);
        numb = numb.setScale(scale, BigDecimal.ROUND_HALF_UP);
        return getNumberStr(numb);
    }

    /**
     * 将String类型的科术计数法改为正常数值计数格式
     *
     * @param
     */
    public static String getNumberStr(String value) {
        if (value == null) {
            return null;
        }
        String valStr = value;
        boolean isMinus = false;
        if ("-".equals(valStr.substring(0,1))) {
            isMinus = true;
        }
        if (valStr.contains("E")) {
            if (isMinus) {
                valStr = valStr.replaceFirst("-", "");
            }
            // 科学计数法时
            // 取出科学计数的指数
            int index = Integer.valueOf(valStr.substring(valStr.indexOf("E") + 1, valStr.length()));
            // valStr 去除E之后部分
            valStr = valStr.substring(0, valStr.indexOf("E"));
            // 去除"."
            valStr = valStr.replaceAll("\\.", "");
            // 除第一位后边还有多少位
            int digit = valStr.length() - 1;
            if (digit > index && index > 0) {
                // 指数小于底数的位数时，重新上小数点
                valStr = valStr.substring(0, index + 1) + "." + valStr.substring(index + 1, valStr.length());
            } else if (digit < index) {
                // 指数大于底数的位数时
                // 相差位数
                int difference = index - digit;
                for (int i = 0; i < difference; i++) {
                    valStr = valStr + "0";
                }
            }
            if (digit > index && index < 0) {
                index = 0 - index;
                for (int i = 0; i < index; i++) {
                    if (index - i - 1 == 0) {
                        // 最后一个时
                        valStr = "0." + valStr;
                    } else {
                        valStr = "0" + valStr;
                    }
                }
            }
        }
        if (isMinus) {
            valStr = valStr.replaceAll("-", "");
            valStr = "-" + valStr;
        }
        return valStr;
    }


    /**
     * 将BigDecimal类型的科术计数法改为正常数值计数格式
     *
     * @param
     */
    public static String getNumberStr(BigDecimal value) {
        if (value == null) {
            return null;
        }
        String valStr = value.toString();
        boolean isMinus = false;
        if ("-".equals(valStr.substring(0,1))) {
            isMinus = true;
        }
        if (valStr.contains("E")) {
            if (isMinus) {
                valStr = valStr.replaceFirst("-", "");
            }
            // 科学计数法时
            // 取出科学计数的指数
            int index = Integer.valueOf(valStr.substring(valStr.indexOf("E") + 1, valStr.length()));
            // valStr 去除E之后部分
            valStr = valStr.substring(0, valStr.indexOf("E"));
            // 去除"."
            valStr = valStr.replaceAll("\\.", "");
            // 除第一位后边还有多少位
            int digit = valStr.length() - 1;
            if (digit > index && index > 0) {
                // 指数小于底数的位数时，重新上小数点
                valStr = valStr.substring(0, index + 1) + "." + valStr.substring(index + 1, valStr.length());
            } else if (digit < index) {
                // 指数大于底数的位数时
                // 相差位数
                int difference = index - digit;
                for (int i = 0; i < difference; i++) {
                    valStr = valStr + "0";
                }
            }
            if (digit > index && index < 0) {
                index = 0 - index;
                for (int i = 0; i < index; i++) {
                    if (index - i - 1 == 0) {
                        // 最后一个时
                        valStr = "0." + valStr;
                    } else {
                        valStr = "0" + valStr;
                    }
                }
            }
        }
        if (isMinus) {
            valStr = valStr.replaceAll("-", "");
            valStr = "-" + valStr;
        }
        return valStr;
    }
}
