package com.simbest.boot.djfupt.mainbills.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.mainbills.model.UsPmInstence;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface UsPmInstenceRepository extends LogicRepository<UsPmInstence, String> {

    String sql1 = "select * from us_pm_instence a WHERE a.pm_ins_id=:pmInsId";

    @Query(value = sql1, nativeQuery = true)
    UsPmInstence findByPmInsId(@Param("pmInsId") String pmInsId);


    @Transactional
    @Modifying
    @Query(
            value = "update us_pm_instence set enabled=0 where id = :id",
            nativeQuery = true
    )
    int deleteByFromId(@Param("id") String id);

    /**
     * 获取个数
     *
     * 切记：要把无效的也算在里面，不然迟早会出现重复编码的
     *
     * @param createdTime
     * @param pmInsType
     * @return
     */
    @Transient
    @Query(
            value = "select count(*)" +
                    "  from us_pm_instence t" +
                    " where t.pm_ins_type =:pmInsType" +
                    "   and to_char(t.created_time, 'yyyy-MM-dd')=:createdTime",
            nativeQuery = true
    )
    String getCounts(@Param("pmInsType") String pmInsType, @Param("createdTime") String createdTime);
}
