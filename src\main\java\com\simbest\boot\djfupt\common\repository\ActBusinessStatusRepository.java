package com.simbest.boot.djfupt.common.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.beans.Transient;
import java.util.List;
import java.util.Map;

@Repository
public interface ActBusinessStatusRepository extends LogicRepository<ActBusinessStatus, String> {


    @Transient
    @Query(
            value = "select * from ACT_BUSINESS_STATUS a where a.receipt_code =:pmId",
            nativeQuery = true
    )
    Map<String,Object> findByActBusinessStatus(@Param("pmId") String pmId);

    @Query(
            value = " SELECT act.ID,act.BUSINESS_KEY," +
                    " act.CREATE_ORG_CODE, " +
                    " us.pm_ins_title as title ,  " +
//                     "nh.belong_department_code as createDepartment," +
//                     "nh.creator as createUser," +
//                     "nh.created_time as createdTime , " +
                    " act.CREATE_ORG_NAME," +
                    " to_char(act.CREATE_TIME,'yyyy-MM-dd HH24:mi:ss') as CREATE_TIME," +
                    " act.CREATE_USER_CODE,       " +
                    " act.CREATE_USER_ID," +
                    " act.CREATE_USER_NAME," +
                    " act.CURRENT_STATE," +
                    " act.DURATION,to_char(act.END_TIME,'yyyy-MM-dd HH24:mi:ss') as END_TIME," +
                    " act.PARENT_PROC_ID," +
                    " act.PREVIOUS_ASSISTANT,       " +
                    " to_char(act.PREVIOUS_ASSISTANT_DATE,'yyyy-MM-dd HH24:mi:ss') as PREVIOUS_ASSISTANT_DATE," +
                    " act.PREVIOUS_ASSISTANT_NAME," +
                    " act.PREVIOUS_ASSISTANT_ORG_CODE,       " +
                    " act.PREVIOUS_ASSISTANT_ORG_NAME," +
                    " act.PROCESS_CH_NAME," +
                    " act.PROCESS_DEF_ID," +
                    " act.PROCESS_DEF_NAME," +
                    " act.PROCESS_INST_ID," +
                    " act.RECEIPT_CODE," +
                    " act.RECEIPT_TITLE,       " +
                    " act.REMOVED," +
                    " to_char(act.START_TIME,'yyyy-MM-dd HH24:mi:ss') as START_TIME," +
                    " to_char(act.UPDATE_TIME,'yyyy-MM-dd HH24:mi:ss') as UPDATE_TIME,    " +
                    " wk.WORK_ITEM_ID as workItemId,   " +
                    "  wk.ACTIVITY_DEF_ID as activityDefId,    " +
                    " wk.ACTIVITY_INST_NAME as activityInstName,   " +
                    " wk.parti_name,    " +
                    " wk.participant as participant,    " +
                    " wk.assistant as assistant,    " +
                    " wk.start_Time as workItemStartTime,    " +
                    " us.pm_ins_type    " +
                    " FROM act_business_status act,         wf_workitem_model wk,         US_PM_INSTENCE us   " +
                    " WHERE " +
                    " act.PROCESS_INST_ID = wk.PROCESS_INST_ID     " +
                    " and act.BUSINESS_KEY = us.id    " +
                    " and us.pm_ins_type in :pmInsType     " +
                    " and act.RECEIPT_TITLE like concat(concat('%',:dynamicWhere),'%')     " +
                    " and act.CREATE_USER_CODE = :createUser     " +
                    " and wk.parti_name like concat(concat('%',:partiName),'%')     " +
                    " and wk.current_State = 10     " +
                    " and wk.enabled = 1     " +
                    " and wk.enabled = 1     " +
                    " and us.enabled = 1     " +
//                    "and nh.pm_ins_id=act.receipt_code   "+
                    " order by wk.START_TIME desc",
            countQuery = "SELECT count(1)    " +
                    " FROM act_business_status act,         wf_workitem_model wk,         US_PM_INSTENCE us    " +
                    " WHERE act.PROCESS_INST_ID = wk.PROCESS_INST_ID    " +
                    " and act.BUSINESS_KEY = us.id    " +
                    " and us.pm_ins_type in :pmInsType     " +
                    " and act.RECEIPT_TITLE like concat(concat('%',:dynamicWhere),'%')    " +
                    " and act.CREATE_USER_CODE = :createUser     " +
                    " and wk.parti_name like concat(concat('%',:partiName),'%')     " +
                    " and wk.current_State = 10    " +
                    " and wk.enabled = 1     " +
                    " and wk.enabled = 1     " +
//                     "and nh.pm_ins_id=act.receipt_code   "+
                    " and us.enabled = 1 ",
            nativeQuery = true
    )
    Page<Map<String, Object>> getMyCreateAllTodoPage(@Param("dynamicWhere") String var1, @Param("pmInsType") List<String> var2, @Param("createUser") String var3, @Param("partiName") String var5, Pageable var4);




    @Transient
    @Query(
            value = "select * from ACT_BUSINESS_STATUS a where a.receipt_code =:pmId",
            nativeQuery = true
    )
    ActBusinessStatus findByOldPmInsIdOne(@Param("pmId") String pmId);


    @Modifying
    @Query(
            value = "update act_business_status t set t.current_state=8 WHERE t.receipt_code = :receiptCode",
            nativeQuery = true
    )
    int updateCurrDate(@Param("receiptCode")String receiptCode);

}
