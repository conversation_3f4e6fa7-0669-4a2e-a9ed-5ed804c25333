<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>新增宣讲事项</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent();

        $(function () {
            if (gps.type == 'edit') {
                // loadForm()
            } else {

            }
        })
    </script>
    <style>
        .ctableT {
            border: none;
            text-align: left;
        }

        textarea {
            white-space: normal !important;
        }

        a.mybtn {
            width: auto;
            height: 32px;
            float: left;
            cursor: pointer;
            line-height: 30px;
            padding: 0 18px;
            color: #fff;
            background-color: #39aef5;
            text-align: center;
            -moz-border-radius: 2px;
            -webkit-border-radius: 2px;
            border-radius: 2px;
        }

        a.mybtn:hover {
            text-decoration: none;
            background: #38a8ec;
        }

        .tips p {
            font-size: 12px;
            color: #333;
        }

        .tips {
            display: none;
            margin-bottom: 30px;
        }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
            margin-top: 15px;
        }

        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: center;
            max-width: 100px;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #f7f7f7;
        }

        input:read-only {
            background-color: #f7f7f7;
        }

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: 3px;
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }
        .dialog-button {
            text-align: center;
        }
        .dialog-button .l-btn {
            margin-left: 30px;
        }
    </style>
</head>

<body class="body_page">
    <form id="experienceForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
        cmd-select="/action/usProblemInfo/getFormDetail" beforeSubmit="beforeSubmit()" submitcallback="submitcallback()"
        getcallback="getcallback()">
        <div style="width: 100%;">
            <table class="formTable" border="0" cellpadding="0" th:colspan="6" cellspacing="10" width="90%"
                style="white-space: nowrap">
                <tr>
                    <td class="lable" align="center" width="20%">
                        宣讲事项111<font class="col_r">*</font>
                    </td>
                    <td width="80%">
                        <textarea id="problemDescribe" name="problemDescribe" class="easyui-validatebox" required="true"
                            validType="maxLength[100]" style="min-height:120px;"></textarea>
                    </td>
                </tr>
                <tr>
                    <td class="lable" align="center" width="20%">
                        类别<font class="col_r">*</font>
                    </td>
                    <td width="80%">
                        <input id="roleUserId" name="roleUserId" class="easyui-combobox"
                            style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            panelHeight:'auto',
                            ischooseall:true,
                            textField: 'text',
                            editable:false,
                            required:true,
                            data:[
                            {value:'0',text:'必选项'},
                            {value:'1',text:'推荐项'}]" />
                    </td>
                </tr>
                <tr style="height: 40px">
                    <td class="lable" align="center" width="10%">支撑材料附件</td>
                    <td colspan="5" width="90%" style="padding-left: 7px">
                        <input id="nmaFile" name="nmaFile" type="text" file="true" class="cselectorImageUpload"
                            btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                            href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
                    </td>
                </tr>
            </table>
        </div>
    </form>
</body>

</html>