package com.simbest.boot.djfupt.findfaults.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_App_Name_Model")
@ApiModel(value = "归属系统名称")
public class UsAppNameModel extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UGM") //主键前缀，此为可选项注解
    private String id;


    @Column(name = "app_name",  length = 200)
    @ApiModelProperty(value = "归属系统名称")
    @Getter
    @Setter
    private String appName;

    @Column(name = "create_Truename",  length = 200)
    @ApiModelProperty(value = "创建人姓名")
    @Getter
    @Setter
    private String createTruename;

    @Column(name = "create_Username",  length = 200)
    @ApiModelProperty(value = "创建人账号")
    @Getter
    @Setter
    private String createUsername;

    @Column(name = "remarks",  length = 2000)
    @ApiModelProperty(value = "备注")
    @Getter
    @Setter
    private String remarks;

}
