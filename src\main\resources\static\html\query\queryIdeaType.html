<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>思政纪实分类</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
    </script>
    <script type="text/javascript">
        getCurrent()
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#queryIdeaTable", //table列表的id名称，需加#
                    "querycmd": "action/usRecordType/findInfoPage", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            {title: "名称", field: "name", tooltip: true, width: 300, align: "center"},
                            {
                                title: "最后修改时间",
                                field: "modifiedTime",
                                width: 160,
                                tooltip: true,
                                align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    return getNow('yyyy-MM-dd hh:mm:ss', false, row.modifiedTime)
                                }
                            },
                            {
                                field: "opt", title: "操作", width: 120, rowspan: 1, align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = ""
                                    g += "<a href='#' class='edit' id=" + row.id + " name=" + row.name + ">【编辑】</a>";
                                    g += "<a href='#' class='delete' id=" + row.id + ">【删除】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                }
            };
            loadGrid(pageparam);
        });

        $(document).on('click', '.add', function () {
            top.dialogP('html/query/queryIdeaEdit.html?type=add', window.name, '新增', 'queryIdeaCallBack', true, 800, 200)
        })

        $(document).on('click', '.edit', function () {
            var id = $(this).attr('id')
            var name = $(this).attr('name')
            console.log(id, name)
            top.dialogP('html/query/queryIdeaEdit.html?type=edit&id=' + id + '&name_other=' + name, window.name, '编辑', 'queryIdeaCallBack', true, 800, 200)
        })

        window.queryIdeaCallBack = function () {

        }

        $(document).on('click', 'a.delete', function () {
            var id = $(this).attr('id');
            top.mesConfirm("温馨提示", "请确认是否删除该思政纪实分类", function () {
                ajaxgeneral({
                    url: 'action/usRecordType/deleteById?id=' + id,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        $("#queryIdeaTable").datagrid("reload");
                    }
                });
            });
        })

        // 弹框关闭回调
        function dialogClosed() {
            top.dialogClose("queryIdeaCallBack")
            $("#queryIdeaTable").datagrid("reload");
        }
    </script>
    <style>
        textarea {
            white-space: normal !important;
        }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }

        .formTable > tbody > tr > td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable > tbody > tr > td input,
        .formTable > tbody > tr > td span,
        .formTable > tbody > tr > td textarea,
        .formTable > tbody > tr > td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: center;
            max-width: 100px;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #fff;
        }

        /* input:read-only { background-color: #f7f7f7; } */

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: -15px;
        }

        .cselectorImageUL input[type='file'] {
            display: inline-block;
            width: 60px !important;
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        .cselectorImageUL {
            width: 100%;
        }

        .dialog-button {
            text-align: center;
        }

        .dialog-button .l-btn {
            margin-left: 30px;
        }

        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }

        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</head>

<body class="body_page">
<table border="0" cellpadding="0" cellspacing="6" width="100%">
    <tr>
        <td>
            <a class="btn fr ml10 add"><span>新增</span></a>
        </td>
    </tr>
</table>
<!--table-->
<div class="queryIdeaTable">
    <table id="queryIdeaTable"></table>
</div>

</body>

</html>