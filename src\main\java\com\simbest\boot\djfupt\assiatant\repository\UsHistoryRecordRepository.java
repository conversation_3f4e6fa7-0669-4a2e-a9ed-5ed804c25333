package com.simbest.boot.djfupt.assiatant.repository;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.assiatant.model.UsHistoryRecord;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.beans.Transient;


public interface UsHistoryRecordRepository extends LogicRepository<UsHistoryRecord, String> {
    @Transient
    @Modifying
    @Query(
            value = " update US_HISTORY_RECORD t set t.enabled=0,t.removed_time = sysdate where t.pm_ins_id=:pmInsId and t.user_name=:currentUserName ",
            nativeQuery = true
    )
    int clearByPmInsId(@Param("pmInsId")String pmInsId, @Param("currentUserName")String currentUserName);

    @Transient
    @Modifying
    @Query(
            value = " update US_HISTORY_RECORD t set t.enabled=0,t.removed_time = sysdate where  t.user_name=:currentUserName ",
            nativeQuery = true
    )
    int clearAll(@Param("currentUserName")String currentUserName);
}