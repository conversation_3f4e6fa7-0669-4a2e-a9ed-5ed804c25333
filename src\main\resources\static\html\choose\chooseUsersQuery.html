<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style type="text/css">
    .ztree li ul.line{height:auto;}
    .keyinput{width: 480px;padding: 10px;}
    #key{width: 415px;}
    .tree-file{
        background-position: -273px 0 !important;
    }
    .tree-folder-open{
        background-position: -273px 0 !important;
    }
</style>
<body class="page_body">
<div class="keyinput">
   <input id="key" type="text" placeholder="请输入姓名进行查询"/>
   <a class="btn a_warning " title="人员查询" style="float: right;height: 32px;padding: 0 10px;line-height: 32px;"  onclick="chooseuser()"><i class="iconfont">&#xe634;</i></a>
</div>
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    $(function(){
        // treeLoadSuccess();  //回显
        initTree();
        $(document).on("click",".role a i",function(){
            $(this).parent("a").remove();
        });
    });
    //初始化
    function initTree(truename) {
        if(truename == undefined || truename == ""){
            var ajaxopts = {
                //uums
                url:"uums/sys/userinfo/findOneStep?appcode="+web.appCode,
                data:{"appCode":web.appCode},
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    //只展示省公司的人
                    var shengData = [];
                    for(var i in data.data){
                        // if(data.data[i].id=="00000000000000000000" || data.data[i].id=="4772338661636601428"){
                        shengData.push(data.data[i]);
                        // }
                    }
                    userTree(shengData,'all');
                }
            };
        }else{
            var ajaxopts = {
                url: 'uums/sys/userinfo/findDimUserTree?appcode=' + web.appCode,
                contentType: "application/json; charset=utf-8",
                data: {'truename': truename},
                success: function (data) {
                    userIdzNodes = data.data;
                    userTree(userIdzNodes,'user');
                }
            }
        }
        ajaxgeneral(ajaxopts);
    }
    //树
    function userTree(userIdzNodes,treesta) {
        if(treesta == 'all'){
            var datas=toTreeData(userIdzNodes,"id","parentId","id|id,name|text,parentId,id,treeType,orgDisplayName");
            $("#orgTree").tree({
                //checkbox:true,//是否在每一个借点之前都显示复选框
                lines:true,//是否显示树控件上的虚线
                treePid:'parentId',
                queryParams:{"appCode":web.appCode},
                contentType: "application/json; charset=utf-8",
                //cascadeCheck:false,
                // onlyLeafCheck:true,
                onlyone:gps.multi==0?true:false,//不要乱配
                // onlyone:true,
                data: datas,
                fileds:'id,parentId,name|text,treeType',
                animate:true,//节点在展开或折叠的时候是否显示动画效果
                onClick:function(node){
                    if(node.treeType=="org"){
                        if(node.children){
                            if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
                        }else{
                            ajaxgeneral({
                                url:"uums/sys/userinfo/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
                                data:{"appCode":web.appCode,"orgCode":node.id},
                                contentType: "application/json; charset=utf-8",
                                success:function(data){
                                    if(data.data.length==0){
                                        top.mesAlert("提示信息","该组织无下级数据！", 'info');
                                    }else{
                                        for(var i in data.data){
                                            data.data[i].text=data.data[i].name;
                                        }
                                        $("#orgTree").tree("append", {
                                            parent : node.target,
                                            data : data.data
                                        });
                                    }
                                }
                            });
                        }
                    }
                },
                onLoadSuccess:function(node,data){
                    $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
                    // $("#_easyui_tree_2").trigger("click");
                },
                onBeforeSelect:function(node){
                    if(node.treeType=="org") return false;
                    if(gps.multi==0){
                        var nodes=$("#orgTree").tree("getChecked");
                        for(var i in nodes){
                            //var nodei=$("#orgTree").tree("find",nodes[i].id);
                            $("#orgTree").tree("uncheck",nodes[i].target);
                        }
                        $(".role").html("");
                    }
                    if((getObjects("id,name",node.id+","+node.name)) == true){
                        $(".role").append("<a orgDisplayName='"+node.orgDisplayName+"' name='"+node.name+"' id='"+node.id+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                    }
                }
            });
        }else{
            if(userIdzNodes.length > 500){
                var dataone = [];
                for(var i in userIdzNodes){
                    if(userIdzNodes[i].id == "00000000000000000000" || userIdzNodes[i].parentId == "00000000000000000000" || userIdzNodes[i].parentId == "OW19"){
                        dataone.push(userIdzNodes[i]);
                    }
                }
                var datas=toTreeData(dataone,"id","parentId","id|id,name|text,parentId,id,treeType,orgDisplayName");
                $("#orgTree").tree({
                    data: datas,
                    lines:true,//是否显示树控件上的虚线
                    treePid:'parentId',
                    fileds:'id,parentId,name|text,treeType',
                    animate:true,//节点在展开或折叠的时候是否显示动画效果
                    onlyone:gps.multi==0?true:false,//不要乱配
                    onLoadSuccess:function(node,data){
                        $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
                        $("#_easyui_tree_2").trigger("click");
                    },
                    onClick:function(node){
                        if(node.treeType=="org"){
                            var datatwo = [];
                            for(var i in userIdzNodes){
                                if(userIdzNodes[i].parentId == node.id){
                                    userIdzNodes[i].text = userIdzNodes[i].name;
                                    datatwo.push(userIdzNodes[i]);
                                }
                            };
                            if(node.clicks == undefined){
                                node.clicks = true;
                                $("#orgTree").tree("append", {
                                    parent : node.target,
                                    data : datatwo
                                });
                            }
                        }
                    },
                    onBeforeSelect:function(node){
                        if(node.treeType=="org") return false;
                        if(gps.multi==0){
                            $(".role").html("");
                        }
                        if((getObjects("id,name",node.id+","+node.name)) == true){
                            $(".role").append("<a orgDisplayName='"+node.orgDisplayName+"' name='"+node.name+"' id='"+node.id+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                        }
                    }
                });
            }else{
                var datas=toTreeData(userIdzNodes,"id","parentId","id|id,name|text,parentId,id,treeType,orgDisplayName");
                $("#orgTree").tree({
                    data: datas,
                    lines:true,//是否显示树控件上的虚线
                    treePid:'parentId',
                    fileds:'id,parentId,name|text,treeType',
                    animate:true,//节点在展开或折叠的时候是否显示动画效
                    onlyone:gps.multi==0?true:false,//不要乱配
                    onLoadSuccess:function(node,data){
                        $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'rgb(255, 230, 176)','border':'1px solid rgb(255, 185, 81)','color':'#5f4c31'});   // 选择最后一个节点 并添加 float:left
                    },
                    onBeforeSelect:function(node){
                        if(node.treeType=="org") return false;
                        if(gps.multi==0){
                            $(".role").html("");
                        }
                        if((getObjects("id,name",node.id+","+node.name)) == true){
                            $(".role").append("<a orgDisplayName='"+node.orgDisplayName+"' name='"+node.name+"' id='"+node.id+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                        }
                    }
                });
            }
        }


    }


    //点击查询
    function chooseuser() {
        initTree($('#key').val());
    };
    //是否有该条数据
    function getObjects(idas,keyas){
        var a=false;
        var ids=idas.split(",");
        var idkeys=keyas.split(",");
        var b = true;
        $(".role a").each(function(i,v){
            if($(v).attr('id') == idkeys[0]) {
                b = false
            }
            if(i == $(".role a").length-1){
                if(b == false){
                    top.mesAlert("提示信息","请勿重复添加！");
                }
                return b;
            }
        });
        return b;
    };
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow = top.chooseWeb[gps.name] ? top.chooseWeb[gps.name].data : [];
        for(var i in chooseRow){
            //chooseRow[i].truename 是为了判断不为空时默认不加为空的X号
            if((getObjects("userName,trueName",chooseRow[i].userName+","+chooseRow[i].trueName)) == true && chooseRow[i].trueName){
                $(".role").append("<a  orgDisplayName='"+chooseRow[i].orgDisplayName+"' name='"+chooseRow[i].trueName+"' id='"+chooseRow[i].userName+"'><font>"+chooseRow[i].trueName+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            }
        }
    }
    window.getchoosedata = function () {
        var datas = [];
        var names = ["userName", "trueName", "belongCompanyCode", "belongCompanyName", "belongDepartmentCode", "belongDepartmentName","orgDisplayName"];
        $(".role a").each(function (i, v) {
            var data = {};
            for (var i in names) {
                data[names[i]] = $(v).attr(names[i]);
            }
            data.userName = $(v).attr("id");
            data.trueName = $(v).attr("name");
            datas.push(data);
        });
        return { "data": datas, "state": 1 };//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
