package com.simbest.boot.djfupt.problem.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @data 2022/11/26 0026 12:21
 */
@Data
public class UsproblemIbnfoExcel {


    @ExcelVOAttribute(name = "编号", column = "A")
    @Excel(name = "编号")
    private int rownum;



    @Excel(name = "问题名称")
    @ExcelVOAttribute(name = "问题名称", column = "B")
    private String problemName;



    @Excel(name = "问题具体描述")
    @ExcelVOAttribute(name = "问题具体描述", column = "C")
    private String problemDescribe;

    @ExcelVOAttribute(name = "上报人", column = "D")
    @Excel(name = "上报人")
    private String applyUser;                   //发起人


    @ExcelVOAttribute(name = "上报人组织", column = "E")
    @Excel(name = "上报人组织")
    private String belongOrgName;                    //发起人组织





    @Column(length = 40)
    @ApiModelProperty(value = "上报时间", required = true)
    @Excel(name = "上报时间")
    @ExcelVOAttribute(name = "上报时间", column = "F")
    private String createdTime;

}
