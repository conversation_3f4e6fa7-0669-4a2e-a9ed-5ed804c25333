package com.simbest.boot.djfupt.admin.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @用途:
 * @作者：zsf
 * @时间: 2020/7/28
 */
public interface WfWorkItemRepository extends LogicRepository<WfWorkItemModel, String> {

    @Query(
            value = "select a.* from ( select t.* from WF_WORKITEM_MODEL t where t.process_inst_id=:processInstId and t.activity_def_id='zxsj.assist'and t.current_state='12' order by t.end_time desc) a where rownum=1",
            nativeQuery = true
    )
    WfWorkItemModel findPreWorkItem(@Param("processInstId") Long processInstId);

    //查询整改流程责任人反馈结束时间
    @Query(
            value = "select * from (select t.*  from WF_WORKITEM_MODEL t where t.enabled=1 and t.receipt_code=:pmInsId and t.activity_def_id='zxsj.assist' order by t.created_time desc) c where rownum=1",
            nativeQuery = true
    )
    WfWorkItemModel findEndTime(@Param("pmInsId") String pmInsId);


    // 根据主单据ID查询当前流程审批人
    @Query(
            value = "select t.participant from WF_WORKITEM_MODEL t where t.enabled=1 and t.receipt_code=:pmInsId and t.current_state = 10", nativeQuery = true
    )
    List<String> getDealUserByPmId(@Param("pmInsId") String pmInsId);

    @Query(
            value = "select t.* from WF_WORKITEM_MODEL t where t.enabled=1 and t.receipt_code=:pmInsId and t.participant = :dealUser and t.current_state = 10", nativeQuery = true
    )
    WfWorkItemModel findByPmIDAndDealUser(@Param("pmInsId") String pmInsId, @Param("dealUser") String dealUser);

    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT * FROM WF_WORKITEM_MODEL WHERE ENABLED=1 and RECEIPT_CODE =:receipt_code ORDER BY CREATE_TIME ASC",
            nativeQuery = true)
    List<WfWorkItemModel> findProcessInstId(@Param("receipt_code") String receipt_code);

    /**
     * @return
     */
    @Transactional
    @Query(value = "select t.*" +
            "  from WF_WORKITEM_MODEL t" +
            " where t.enabled = 1" +
            "   and (t.process_def_name = 'com.zxsj.flow.sjzgwz_province' or" +
            "       t.process_def_name = 'com.zxsj.flow.sjzgwz_branch')" +
            "   and t.activity_def_id = 'zxsj.takeTheLead'" +
            "   and t.current_state = '10'" +
            "   and (t.created_time + 7) > sysdate ",
            nativeQuery = true)
    List<WfWorkItemModel> findAllIsOver();

    /**
     * 获取当前状态
     *
     * @return
     */
    @Transactional
    @Query(value = "select *" +
            "  from wf_workitem_model" +
            " where WORK_ITEM_ID in (select max(WORK_ITEM_ID)" +
            "                          from wf_workitem_model B" +
            "                         where B.Receipt_Code =:pmInsId" +
            "                         group by B.process_Inst_Id)",
            nativeQuery = true)
    List<WfWorkItemModel> findCurrentStateu(@Param("pmInsId") String pmInsId);

    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT * FROM WF_WORKITEM_MODEL WHERE ENABLED=1 and RECEIPT_CODE =:receipt_code ORDER BY CREATE_TIME ASC",
            nativeQuery = true)
    List<WfWorkItemModel> findWfWorkItemModelByCode(@Param("receipt_code") String receipt_code);


    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT * FROM WF_WORKITEM_MODEL WHERE ENABLED=1 and RECEIPT_CODE =:receipt_code ORDER BY CREATE_TIME DESC",
            nativeQuery = true)
    List<WfWorkItemModel> findWfWorkItemByCode(@Param("receipt_code") String receipt_code);


    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT * FROM WF_WORKITEM_MODEL t WHERE ENABLED=1 and t.RECEIPT_CODE =:receipt_code and t.current_state='10' ORDER BY CREATE_TIME DESC",
            nativeQuery = true)
    List<WfWorkItemModel> findWfWorkItemDone(@Param("receipt_code") String receipt_code);


    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT * FROM WF_WORKITEM_MODEL t WHERE ENABLED=1 and  t.process_inst_id=:processInstId and t.current_state='10' ORDER BY CREATE_TIME DESC",
            nativeQuery = true)
    List<WfWorkItemModel> findWfWorkItem(@Param("processInstId") String processInstId);

    /**
     * @param receipt_code
     * @return
     */
    @Transactional
    @Query(value = "SELECT t.*,s.current_state" +
            "  FROM WF_WORKITEM_MODEL t, act_business_status s" +
            " WHERE t. ENABLED = 1" +
            "   and s.enabled = 1" +
            "   and t.receipt_code = s.receipt_code" +
            "   and t.RECEIPT_CODE = :receipt_code" +
            "   and t.current_state = '12'" +
            "   and s.current_state ='7'" +
            " ORDER BY t.CREATE_TIME DESC",
            nativeQuery = true)
    List<WfWorkItemModel> findWfWorkItemDo(@Param("receipt_code") String receipt_code);


    @Query(value = "select s.* from(select * from WF_WORKITEM_MODEL t where t.process_inst_id=:processInstId and t.current_state='10' order by t.created_time asc) s where rownum=1", nativeQuery = true)
    WfWorkItemModel findByProcessInstIdAndState(@Param("processInstId") String processInstId);


    /**
     * 根据workItemId查询
     *
     * @param workItemId
     * @return
     */
    @Query(value = "select * from wf_workitem_model t where t.enabled=1 and t.work_item_id=:workItemId", nativeQuery = true)
    WfWorkItemModel findByWorkItemId(@Param("workItemId") String workItemId);

    @Query(value = "select t.*" +
            "      from WF_WORKITEM_MODEL t" +
            "     where " +
            "        SUBSTR(t.receipt_code, 0, 1) = 'C'" +
            "       and t.participant = :creator" +
            "       and t.enabled = 1 " +
            "       and t.current_state = 10", nativeQuery = true)
    List<WfWorkItemModel> findAllByCreatorAndParticipant(@Param("creator") String creator
    );


    @Modifying
    @Query(value = "update WF_WORKITEM_MODEL t set t.participant=:userName,t.parti_name=:trueName where t.id=:id", nativeQuery = true)
    int updateParticpantAndPartNamebyId(@Param("userName") String userName,
                                        @Param("trueName") String trueName,
                                        @Param("id") String id);


    WfWorkItemModel findAllByEnabledAndReceiptCodeAndParticipant(Boolean enabled, String receiptCode, String partiName);
}




