package com.simbest.boot.djfupt.findfaults.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ExportFaultsCountInfo {

    @ExcelVOAttribute(name = "归属单位",column = "A")
    @ApiModelProperty(value = "归属单位")
    private String name;

    @ExcelVOAttribute(name = "上报意见数量",column = "B")
    @ApiModelProperty(value = "上报意见数量")
    private String allCount;


    @ExcelVOAttribute(name = "解决中数量",column = "C")
    @ApiModelProperty(value = "解决中数量")
    private String solvingCount;


    @ExcelVOAttribute(name = "已解决未归档数量",column = "D")
    @ApiModelProperty(value = "已解决未归档数量")
    private String notFiledCount;


    @ExcelVOAttribute(name = "已解决归档数量",column = "E")
    @ApiModelProperty(value = "已解决归档数量")
    private String filedCount;


}
