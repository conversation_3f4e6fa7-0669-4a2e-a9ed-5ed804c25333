package com.simbest.boot.djfupt.process.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.process.model.SysProcessInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * <strong>Title :SysProcessInfoRepository </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/22</strong><br>
 * <strong>Modify on : 2022/6/22</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

public interface SysProcessInfoRepository extends LogicRepository<SysProcessInfo, String> {

    @Transactional
    @Query(
            value = "select t.*" +
                    "  from SYS_PROCESS_INFO t" +
                    " where t.enabled = 1" +
                    "   and t.process_def_id =:processDefId" +
                    "   and t.from_activity_id=:fromActivityId" +
                    "   and t.outcome=:outcome",
            nativeQuery = true
    )
    SysProcessInfo findSysProcessInfo(@Param("processDefId") String processDefId, @Param("fromActivityId") String fromActivityId, @Param("outcome") String outcome);
}
