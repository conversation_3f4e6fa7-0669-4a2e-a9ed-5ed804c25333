package com.simbest.boot.djfupt.stat.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.stat.model.VisitorStat;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <strong>Title : VisitorStatRepository</strong><br>
 * <strong>Description : 访客统计分析数据访问层</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Repository
public interface VisitorStatRepository extends LogicRepository<VisitorStat, String> {



    /**
     * 统计各单位的访问量
     * @return 单位访问量统计结果
     */
    @Query(value = "SELECT belong_company_name as companyName, COUNT(*) as visitCount " +
            "FROM us_visitor_stat " +
            "WHERE enabled = 1 " +
            "GROUP BY belong_company_name", nativeQuery = true)
    List<Object[]> countVisitsByCompany();



    /**
     * 根据会话ID查询访客统计数据
     * @param conversationId 会话ID
     * @param enabled 是否启用
     * @return 访客统计数据
     */
    @Query("SELECT v FROM us_visitor_stat v WHERE v.conversationId = :conversationId AND v.enabled = :enabled")
    VisitorStat findByConversationIdAndEnabled(@Param("conversationId") String conversationId, @Param("enabled") Boolean enabled);
}
