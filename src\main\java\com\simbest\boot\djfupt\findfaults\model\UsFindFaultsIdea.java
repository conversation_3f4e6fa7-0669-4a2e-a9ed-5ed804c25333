package com.simbest.boot.djfupt.findfaults.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_Find_Faults_Idea")
@ApiModel(value = "找茬上报 意见描述实体类")
public class UsFindFaultsIdea extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UFFI") //主键前缀，此为可选项注解
    private String id;

    @Column(name = "pm_ins_id",  length = 100)
    @ApiModelProperty(value = "主单据ID")
    @Getter
    @Setter
    private String pmInsId;


    @Column(name = "idea_des",  length = 1000)
    @ApiModelProperty(value = "意见或建议描述")
    @Getter
    @Setter
    private String ideaDes;

    @Column(name = "faults_app_name",  length = 200)
    @ApiModelProperty(value = "意见或建议归属系统")
    @Getter
    @Setter
    private String faultsAppName;

    //todo 找茬活动类型临时设计为2期
    @Column(name = "faults_type",  length = 200)
    @ApiModelProperty(value = "期数（一期、二期、三期）")
    @Getter
    @Setter
    private String faultsType="2";

    @Transient
    List<SysFile> sysFiles;//对应的问题截图
}

