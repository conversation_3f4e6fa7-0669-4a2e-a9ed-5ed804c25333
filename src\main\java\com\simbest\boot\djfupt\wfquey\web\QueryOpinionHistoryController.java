package com.simbest.boot.djfupt.wfquey.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.wfquey.service.IQueryOpinionHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用途：查看审批意见 web层控制
 * 作者： zsf
 * 时间： 2018/07/04
 */
@Api(description = "查询审批意见相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/queryOpinionHistory")
public class QueryOpinionHistoryController {

    @Autowired
    private IQueryOpinionHistoryService queryOpinionHistoryService;


    /**
     * 查询工单审批意见
     *
     * @param processInstId   流程实例ID
     * @param currentUserCode 当前人
     * @return
     */
    @ApiOperation(value = "查询审批意见", notes = "查询审批意见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "工作项ID", dataType = "Long", paramType = "query", required = true),
    })
    @PostMapping(value = {"/getWfOptMags", "/api/getWfOptMags", "/getWfOptMags/sso"})
    public JsonResponse getWfOptMags(@RequestParam Long processInstId,
                                     @RequestParam(required = false) String currentUserCode) {
        return JsonResponse.success(queryOpinionHistoryService.getWfOptMags(processInstId, currentUserCode));
    }

}
