package com.simbest.boot.djfupt.caseinfo.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

@Data
public class CaseStatisticsVo {
    @ExcelVOAttribute(name = "编号", column = "A")
    @Excel(name = "编号")
    private  int num;

    @ExcelVOAttribute(name = "公司名称", column = "B")
    @Excel(name = "公司名称")
    private  String companyName;


    private  String companyCode;

    @ExcelVOAttribute(name = "上报先进经验数量", column = "C")
    @Excel(name = "上报先进经验数量")
    private  int nums;
}
