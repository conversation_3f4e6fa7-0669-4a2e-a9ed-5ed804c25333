package com.simbest.boot.djfupt.record.web;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.attachment.service.IFileExtendService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.record.model.RecordVo;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.record.model.UsTalkContent;
import com.simbest.boot.djfupt.record.repository.UsRecordConfigRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordFillService;
import com.simbest.boot.djfupt.record.service.IUsTalkContentService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * 思政纪实填报相关接口
 */
@Api(description = "思政纪实填报相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/usRecordFill")
public class UsRecordFillController extends LogicController<UsRecordFill, String> {

    private IUsRecordFillService usRecordFillService;

    @Autowired
    public UsRecordFillController(IUsRecordFillService usRecordFillService) {
        super(usRecordFillService);
        this.usRecordFillService = usRecordFillService;
    }

    @Autowired
    private IFileExtendService fileExtendService;


    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private PaginationHelps paginationHelp;


    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private UsRecordConfigRepository configRepository;

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private IUsTalkContentService talkContentService;


    /**
     * 提交思政纪实填报
     * ：页面重置，且台账可以查询到
     */
    @ApiOperation(value = "提交思政纪实填报", notes = "提交思政纪实填报")
    @PostMapping(value = {"/insertUsRecordFill", "/api/insertUsRecordFill", "/insertUsRecordFill/sso"})
    public JsonResponse insertUsRecordFill(@RequestParam(required = false) String source,
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestBody UsRecordFill usRecordFill) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser iuser = SecurityUtils.getCurrentUser();
        UsRecordFill newUsRecordFill = null;


        if (ObjectUtil.isNotEmpty(usRecordFill)) {
            if (StringUtils.isNotEmpty(usRecordFill.getId())) {
                newUsRecordFill = usRecordFill;
                newUsRecordFill.setIsDraft("1");
                usRecordFillService.updateFileInfoBy(newUsRecordFill);//更新附件
                usRecordFillService.update(newUsRecordFill);
                talkContentService.dealInfo(newUsRecordFill.getId(),usRecordFill.getTalkContentList());
            } else {
                newUsRecordFill = usRecordFillService.insert(usRecordFill);
                newUsRecordFill.setIsDraft("1");
                    if (iuser.getBelongCompanyTypeDictValue().equals("03")) {
                        usRecordFill.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                        usRecordFill.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                        usRecordFill.setBelongDepartmentName(iuser.getBelongCompanyName());
                        usRecordFill.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    }
                    if (iuser.getBelongCompanyTypeDictValue().equals("02") || iuser.getBelongCompanyTypeDictValue().equals("01")) {
                        usRecordFill.setBelongCompanyName(iuser.getBelongCompanyName());
                        usRecordFill.setBelongCompanyCode(iuser.getBelongCompanyCode());
                        usRecordFill.setBelongDepartmentName(iuser.getBelongDepartmentName());
                        usRecordFill.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    }
                usRecordFillService.updateFileInfoBy(usRecordFill);//更新附件
                usRecordFillService.update(newUsRecordFill);
                talkContentService.dealInfo(newUsRecordFill.getId(),usRecordFill.getTalkContentList());
            }

        }
        if (ObjectUtil.isNotEmpty(newUsRecordFill)) {
            return JsonResponse.success("添加成功");
        } else {
            return JsonResponse.success("添加失败");
        }
    }

    /**
     * 保存思政纪实填报
     * ：页面不重置，且台账可以查询到
     */
    @ApiOperation(value = "保存思政纪实填报", notes = "保存思政纪实填报")
    @PostMapping(value = {"/saveUsRecordFill", "/api/saveUsRecordFill", "/saveUsRecordFill/sso"})
    public JsonResponse saveUsRecordFill(@RequestParam(required = false) String source,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestBody UsRecordFill usRecordFill) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        if (ObjectUtil.isNotEmpty(usRecordFill)) {
            if (StringUtils.isEmpty(usRecordFill.getId())) {
                usRecordFill.setIsDraft("2");
                BelongInfoTool.setBelongCompanyAndDepartment(usRecordFill);
                UsRecordFill newUsRecordFill = usRecordFillService.insert(usRecordFill);
                usRecordFillService.updateFileInfoBy(usRecordFill);//更新附件
                usRecordFillService.updateWithNull(usRecordFill);
                //保存谈话内容列表
                talkContentService.dealInfo(newUsRecordFill.getId(),usRecordFill.getTalkContentList());
            } else {
                usRecordFill.setIsDraft("2");
                BelongInfoTool.setBelongCompanyAndDepartment(usRecordFill);
                usRecordFillService.updateFileInfoBy(usRecordFill);//更新附件
                usRecordFillService.updateWithNull(usRecordFill);
                //保存谈话内容列表
                talkContentService.dealInfo(usRecordFill.getId(),usRecordFill.getTalkContentList());
            }
        }
        return JsonResponse.success("保存成功");
    }

    /**
     * 思政纪实填报页面明细
     * ：页面不重置，且台账可以查询到
     */
    @ApiOperation(value = "思政纪实填报页面明细", notes = "思政纪实填报页面明细")
    @PostMapping(value = {"/getFromDetail", "/api/getFromDetail", "/getFromDetail/sso"})
    public JsonResponse getFromDetail(@RequestParam(required = false) String source,
                                      @RequestParam(required = false) String id,
                                      @RequestParam(required = false) String currentUserCode) {
        if (StrUtil.equals(source, Constants.MOBILE) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        IUser user = SecurityUtils.getCurrentUser();
        List<SysFile> fileList = new ArrayList<>();
        UsRecordFill usRecordFill = new UsRecordFill();
        if (StringUtils.isNotEmpty(currentUserCode)) {
            List<UsRecordFill> fromDetail = usRecordFillService.getFromDetail(currentUserCode);
            if (CollectionUtil.isNotEmpty(fromDetail)) {
                String fileids = fromDetail.get(0).getFileIds();
                if (StringUtils.isNotEmpty(fileids)) {
                    fileList = fileExtendService.findAllByIDs(Arrays.asList(fileids.split(",")));
                    /*List<String> fileIdList = Arrays.asList(fileids.split(","));
                    for (String fileId : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileId);
                        if(sysFile!=null){
                            fileList.add(sysFile);
                        }

                    }*/
                }
                fromDetail.get(0).setDrawFiles(fileList);
                usRecordFill = fromDetail.get(0);

            }

        } else {
            usRecordFill = usRecordFillService.findById(id);
            if (ObjectUtil.isNotEmpty(usRecordFill)) {
                if (StringUtils.isNotEmpty(usRecordFill.getFileIds())) {
                    fileList = fileExtendService.findAllByIDs(Arrays.asList(usRecordFill.getFileIds().split(",")));
                    usRecordFill.setDrawFiles(fileList);
                    /*List<String> fileIdList = Arrays.asList(usRecordFill.getFileIds().split(","));
                    for (String fileId : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileId);
                        if(sysFile!=null){
                            fileList.add(sysFile);
                        }

                    }*/
                }

            }
        }

        if (usRecordFill != null) {
            List<UsRecordConfig> recordConfigs = configRepository.findAllByEnabledOrderByCreatedTimeDesc(Boolean.TRUE);
            if (recordConfigs.size() > 0) {
                //获取思政纪实类型列表
                String recordType = recordConfigs.get(0).getRecordType();
                if(StringUtils.isNotEmpty(recordType)){
                    usRecordFill.setRecordTypeList(Arrays.asList(recordType.split(",")));
                }
                //获取谈话内容列表
                Specification<UsTalkContent> talkContentSpec = Specifications.<UsTalkContent>and()
                        .eq("enabled", Boolean.TRUE)
                        .eq("mainId", usRecordFill.getId())
                        .build();
                List<UsTalkContent> talkContentList = talkContentService.findAllNoPage(talkContentSpec, Sort.by(Sort.Direction.ASC, "createdTime"));
                usRecordFill.setTalkContentList(talkContentList);


                String fileIds = recordConfigs.get(0).getTaskDescriptionFileId();
                usRecordFill.setTaskDescriptionFileId(fileIds);
                usRecordFill.setTaskDescription(recordConfigs.get(0).getTaskDescription());


                if (StringUtils.isNotEmpty(fileIds)) {
                    List<SysFile> sysFiles = fileExtendService.findAllByIDs(Arrays.asList(fileIds.split(",")));
                    /*List<SysFile> sysFiles = new ArrayList<>();
                    List<String> fileIdList = Arrays.asList(usRecordFill.getTaskDescriptionFileId().split(","));
                    for (String fileId : fileIdList) {
                        SysFile sysFile = fileExtendService.findById(fileId);
                        if (sysFile != null) {
                            sysFiles.add(sysFile);
                        }
                    }*/
                    usRecordFill.setTaskDescriptionFiles(sysFiles);
                }

            }

        }
        if (StringUtils.isEmpty(usRecordFill.getId())) {
            if (user.getBelongCompanyTypeDictValue().equals("03")) {
                usRecordFill.setApplyOrg(user.getBelongCompanyNameParent() + '/' + user.getBelongDepartmentName());

            } else {
                usRecordFill.setApplyOrg(user.getBelongCompanyName() + '/' + user.getBelongDepartmentName());

            }
            usRecordFill.setApplyUser(user.getTruename());
        }


        return JsonResponse.success(usRecordFill);
    }


    /**
     * 思政工作纪实台账列表信息
     */
    @ApiOperation(value = "思政工作纪实台账列表信息", notes = "思政工作纪实台账列表信息")
    @PostMapping(value = {"/findAllUsRecordFill", "/api/findAllUsRecordFill", "/findAllUsRecordFill/sso"})
    public JsonResponse findAllInfo(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                    @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                    @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                    @RequestParam(required = false) String properties, //排序规则（属性名称）
                                    @RequestBody(required = false) Map<String, Object> resultMap,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        List<Map<String, Object>> resultList = usRecordFillService.findAllUsRecordFills(resultMap);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");

    }


    @ApiOperation(value = "导出思政纪实台账数据", notes = "导出思政纪实台账数据")
    @PostMapping(value = {"/exportUsRecordFillDate", "/api/exportUsRecordFillDate", "/exportUsRecordFillDate/sso"})
    public void exportUsRecordFillDate(@RequestParam(required = false) String currentUserCode,
                                       HttpServletRequest request,
                                       HttpServletResponse response,
                                       UsRecordFill usRecordFill,
                                       @RequestParam(required = false) String source) throws Exception {
        operateLogTool.operationSource(source, currentUserCode);
        usRecordFillService.exportUsRecordFillDate(currentUserCode, request, response, usRecordFill);
    }

    @PostMapping(value = {"/recordDetail", "/api/recordDetail", "/recordDetail/sso"})
    public JsonResponse recordDetail(@RequestParam(required = false) String id) {
        return usRecordFillService.recordDetail(id);
    }


    /**
     * 政策宣讲数量
     */
    @ApiOperation(value = "思政数量", notes = "思政数量")
    @PostMapping(value = {"/findAllRecordCount", "/api/findAllRecordCount", "/findAllRecordCount/sso"})
    public JsonResponse policyAllCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usRecordFillService.findAllRecordCount(resultMap);
        return JsonResponse.success(resultList);
    }

    /**
     * 思政
     *
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/recordStatistics", "/api/recordStatistics", "/recordStatistics/sso", "/anonymous/recordStatistics"})
    public JsonResponse recordStatistics(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                         @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                         @RequestBody(required = false) Map<String, Object> resultMap,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        IUser user = SecurityUtils.getCurrentUser();
        List<RecordVo> resultList;
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin =  simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));

        if (user.getBelongCompanyTypeDictValue().equals("01")||isAdmin) {
            resultList = usRecordFillService.recordStatistics(page, rows, resultMap);
        } else {
            resultList = usRecordFillService.recordStatisticsOther(page, rows, resultMap);
        }

        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "查询数据有误");
    }

    @PostMapping(value = {"/recordProblemStatistics", "/api/recordProblemStatistics", "/sso/recordProblemStatistics"})
    public void recordProblemStatistics(UsRecordFill resultMap,
                                        HttpServletResponse response,
                                        HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        map.put("companyName", resultMap.getCompanyName());
        map.put("year", resultMap.getYear());
        map.put("month", resultMap.getMonth());
        usRecordFillService.exportProblemStatistics(map, response, request);
    }

    /**
     * 获取最新的配置
     *
     * @return
     */
    @PostMapping(value = {"/configuration", "/api/configuration", "/configuration/sso", "/anonymous/configuration"})
    public JsonResponse configuration() {
        return usRecordFillService.configuration();
    }


}
