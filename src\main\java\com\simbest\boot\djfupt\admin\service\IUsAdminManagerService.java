package com.simbest.boot.djfupt.admin.service;


import com.kp.metadata.bo.impl.Bo;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IUsAdminManagerService extends ILogicService<UsAdminManager, String> {

    JsonResponse queryHadmin(String trueName, String  userName, String roleUserId, String  belongComCode,Integer page,Integer size,String type,String gridName );


    /**
     * 查询所有管理员信息
     * @param resultMap
     * @return
     */
    List<Map<String,Object>> findAllAdmin(Map<String, Object> resultMap,String roleUserId,String type,  String gridName);

    void exportExcel( String direction, //排序规则（asc/desc）
                      String properties, //排序规则（属性名称）
                      String roleUserId, //排序规则（属性名称）
                      String type, //排序规则（属性名称）
                      Map<String, Object> resultMap,
                      HttpServletResponse response,
                      HttpServletRequest request,
                      String gridName);

    JsonResponse insertInfo(@RequestBody UsAdminManager UsAdminManager);

    JsonResponse updateInfo(@RequestBody UsAdminManager usAdminManager);

    JsonResponse delInfo(String id);

    JsonResponse queryUserOrgInfo( String userName);

    /**
     * 下载模板
     * @return
     */
    void exportHadmin(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入模板
     * @param request
     * @param response
     */
    void importInfoHadmin(HttpServletRequest request, HttpServletResponse response);

    /**
     * 下载模板
     * @return
     */
    void exportPartyBuilding(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入模板
     * @param request
     * @param response
     */
    void importInfoPartyBuilding(HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询管理员信息
     * @param userName
     * @return
     */
    List<UsAdminManager> findByUserNameAndEnabled(String userName);

    /**
     * 查询党建管理员信息
     * @param belongComCode
     * @param type
     * @param roleUserId
     * @return
     */
    List<String> findDjAdminByCode(String belongComCode, String type,String roleUserId);

    /**
     * 查询管理员权限信息，并以此为依据进行删改
     * @return
     */
    List<Map<String, Object>>fiveQuesinfo(String userName);









    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 发现网格数量和党建指导员数量
     * @return
     */

    List<Map<String,Object>> findGridCount(Map<String, Object> resultMap);



    /**
     * 发现网格数量和党建指导员数量
     * @return
     */

    List<Map<String,Object>> findAllGridCount(Map<String, Object> resultMap);



    int findByCompanyCodeAndEnabledAndRoleUserId(String belongCompanyName,String roleUserId);


    Boolean jiJian() throws ParseException;



}
