package com.simbest.boot.djfupt.index.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.BadElementException;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.listener.service.IWfWorkItemModelService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.djfupt.admin.model.UsAdminManager;
import com.simbest.boot.djfupt.admin.repository.UsAdminManagerRepository;
import com.simbest.boot.djfupt.caseinfo.model.CaseStatisticsVo;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.caseinfo.repository.UsCaseInfoRepository;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.common.repository.ActBusinessStatusRepository;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;
import com.simbest.boot.djfupt.index.model.Index;
import com.simbest.boot.djfupt.index.service.IndexService;
import com.simbest.boot.djfupt.policy.model.UsPantchDetail;
import com.simbest.boot.djfupt.policy.model.UsPolicyExport;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.djfupt.policy.repository.UsPantchDetailRepository;
import com.simbest.boot.djfupt.policy.repository.UsPolicyInfoRepository;
import com.simbest.boot.djfupt.policy.service.IUsPantchDetailService;
import com.simbest.boot.djfupt.policy.service.IUsPolicyInfoService;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.repository.UsProblemInfoRepository;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.record.repository.UsRecordFillRepository;
import com.simbest.boot.djfupt.record.service.IUsRecordFillService;
import com.simbest.boot.djfupt.util.*;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.AppFileUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.simbest.boot.constants.ApplicationConstants.SLASH;

@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    @Autowired
    private UsProblemInfoRepository problemInfoRepository;

    @Autowired
    private UsCaseInfoRepository caseInfoRepository;

    @Autowired
    private UsPolicyInfoRepository policyInfoRepository;

    @Autowired
    private UsRecordFillRepository recordFillRepository;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private IUsProblemInfoService problemInfoService;

    @Autowired
    private UsCaseInfoRepository usCaseInfoRepository;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private UsPantchDetailRepository pantchDetailRepository;


    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AppFileUtil appFileUtil;

    @Getter
    public Charset charset = Charset.forName("GBK");

    @Autowired
    private ActBusinessStatusRepository actBusinessStatusRepository;

    @Autowired
    private IUsRecordFillService iUsRecordFillService;

    @Autowired
    private CustomDynamicWhere dynamicRepository;

    @Autowired
    private IUsCaseInfoService caseInfoService;

    @Autowired
    private PaginationHelps paginationHelp;

    @Autowired
    private IWfWorkItemModelService wfWorkItemModelService;

    @Autowired
    private UsAdminManagerRepository adminManagerRepository;

    @Autowired
    private UsProblemInfoRepository usProblemInfoRepository;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private SysDictValueService dictValueService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    /**
     * 待办事项通知
     *
     * @return
     */
    @Override
    public JsonResponse todoItems() {
        IUser user = SecurityUtils.getCurrentUser();
        Calendar calendar = Calendar.getInstance();
        // 获取当前年
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月
        int month = calendar.get(Calendar.MONTH) + 1;
        List<String> todoItems = new ArrayList<>();

        //查询政策宣讲任务   查本月有没有起草的工单
        List<UsPolicyInfo> policyInfos = policyInfoRepository.findAllByCreatorAndYear(month, user.getUsername());
        if (policyInfos.size() == 0) {
            todoItems.add("您" + month + "月有一个政策宣讲任务");
        }

        //查询网格走访任务   查本月有没有起草的工单
        List<UsRecordFill> usRecordFills = recordFillRepository.findAllByCreatorAndYear(month, user.getUsername());
        if (usRecordFills.size() == 0) {
            todoItems.add("您" + month + "月有一个网格走访任务");
        }


        //查询问题上报   查本年有没有起草的工单
        List<UsProblemInfo> problemInfos = problemInfoRepository.findAllByCreatorAndYear(year, user.getUsername());
        if (problemInfos.size() == 0) {
            todoItems.add("您" + year + "年有一个问题上报任务");
        }

        //查询经验推广   查本年有没有起草的工单
        List<UsCaseInfo> caseInfos = caseInfoRepository.findAllByCreatorAndYear(year, user.getUsername());
        if (caseInfos.size() == 0) {
            todoItems.add("您" + year + "年有一个经验推广填报任务");
        }


        return JsonResponse.success(todoItems);
    }

    /**
     * 导出工作写实
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public ResponseEntity<?> importExcel(String startTime, String endTime,
                                         HttpServletResponse response,
                                         HttpServletRequest request) {
        startTime = startTime + " 00:00:00";
        endTime = endTime + " 23:59:59";
        //查询问题上报的数据
        List<Map<String, Object>> problemForms = problemForms(startTime, endTime);
        //查询经验上报的数据
        List<Map<String, Object>> caseInfoForms = caseInfoForms(startTime, endTime);
        //政策
        List<UsPolicyInfo> policyInfoForm = policyInfoForm(startTime, endTime);
        //思政
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        List<Map<String, Object>> recordForms = iUsRecordFillService.findAllUsRecordFill(map);
        Resource resource = null;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        try {
            List<File> fileList = Lists.newArrayList();
            File filePdf = exportApplyForm(request, response, problemForms, caseInfoForms, recordForms, policyInfoForm);
            if (filePdf != null) {
                fileList.add(filePdf);
            }
//            String fileName=df.format(new Date())+"党建指导员工作写实";
//            File zipFile = appFileUtil.createTempFileWithName(fileName.concat(".zip"));
//            ZipUtil.zip(zipFile, charset, true, fileList.toArray(new File[]{}));
//            String fileName1 = URLEncoder.encode(fileName.concat(".zip"), ApplicationConstants.UTF_8);
//            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=\"" + fileName1 + "\"");
//            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            //  resource = new InputStreamResource(new FileInputStream(zipFile));
            FileUtil.del(filePdf);
        } catch (Exception e) {
            Exceptions.printException(e);
        }


        return null;
    }


    /**
     * 生成word文档
     * isExport true为导出pdf, false为一键下载   因为一键下载需要返回file 添加到附件list中 进行压缩
     */
    @Autowired
    private IUsPolicyInfoService usPolicyInfoService;

    @Autowired
    private IUsPantchDetailService usPantchDetailService;

    public File exportApplyForm(HttpServletRequest request,
                                HttpServletResponse response,
                                List<Map<String, Object>> problemForms,
                                List<Map<String, Object>> caseInfoForms,
                                List<Map<String, Object>> recordForms,
                                List<UsPolicyInfo> policyInfoForm) throws Exception {
        IUser user = SecurityUtils.getCurrentUser();
        Map<String, Object> wordDataMap = new HashMap<String, Object>();// 存储报表全部数据
        Map<String, Object> parametersMap = new HashMap<String, Object>();// 存储报表中不循环的数据
        List<Map<String, Object>> table1 = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> table2 = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> table3 = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> table4 = new ArrayList<Map<String, Object>>();
        parametersMap.put("companyName", user.getBelongCompanyName());
        parametersMap.put("trueName", user.getTruename());

        if (policyInfoForm.size() > 0) {

            for (UsPolicyInfo policyInfo : policyInfoForm) {
                int i = 1;

                Map<String, Object> map = new HashMap<String, Object>();

                map.put("spryj", i);
                map.put("policyTime", policyInfo.getPolicyTime());
                map.put("policyAddress", policyInfo.getPolicyAddress());
                List<UsPantchDetail> usPantchDetailInfo = usPantchDetailService.findUsPantchDetailInfo(policyInfo.getPmInsId(), policyInfo.getCreator());
                StringBuffer text = new StringBuffer();
                if (usPantchDetailInfo.size() > 0) {
                    for (UsPantchDetail detail : usPantchDetailInfo) {
                        text.append(detail.getLectureItems() + "\n");
                    }
                }
                map.put("lectureList", text);

                i++;
                table1.add(map);

            }
        }

        if (recordForms.size() > 0) {
            int i = 1;
            for (Map<String, Object> maps : recordForms) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("spryj", i);
                map.put("talkTime", maps.get("talkTime"));
                map.put("talkAddress", maps.get("talkAddress"));
                map.put("configTime", maps.get("configTime"));
                map.put("configDeptName", maps.get("configDeptName"));
                map.put("numberOfPanel", maps.get("numberOfPanel"));
                i++;
                table2.add(map);
            }
        }

        if (caseInfoForms.size() > 0) {
            int i = 1;
            for (Map<String, Object> maps : caseInfoForms) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("spryj", i);
                map.put("createdTime", maps.get("createdTime"));
                map.put("problemName", maps.get("problemName"));
                map.put("problemDescribe", maps.get("problemDescribe"));
                i++;
                table4.add(map);
            }

        }

        if (problemForms.size() > 0) {
            int i = 1;
            for (Map<String, Object> maps : problemForms) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("spryj", i);
                Map<String, Object> map1 = actBusinessStatusRepository.findByActBusinessStatus(maps.get("pmInsId").toString());
                if (map1.get("CURRENT_STATE").equals("7")) {
                    map.put("state", "是");
                } else {
                    map.put("state", "否");
                }

                String strDateFormat = "yyyy-MM-dd HH:mm:ss";
                SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);

                map.put("createdTime", sdf.format(maps.get("createdTime")));
                map.put("problemDescribe", maps.get("problemDescribe"));
                map.put("situation", maps.get("problemFeedback"));
                i++;
                table3.add(map);
            }

        }
        wordDataMap.put("table1", table1);
        wordDataMap.put("table2", table2);//意见
        wordDataMap.put("table3", table3);
        wordDataMap.put("table4", table4);
        wordDataMap.put("parametersMap", parametersMap);
        String srcPath = "model/";  //模板文件位置
        String fileName = "党建指导员写实.docx";
        srcPath = srcPath + fileName;
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(srcPath);
        File file = new File(srcPath);//改成你本地文件所在目录
        // 读取word模板
//        FileInputStream fileInputStream = new FileInputStream(file);
        WordTemplate template = new WordTemplate(inputStream);
        // 替换数据
        template.replaceDocument(wordDataMap);
        //生成文件
        String requestDir = "sdybgDownLoad";
        // /home/<USER>/simbestboot/file/sdybg/sdybgDownLoad
        String customDirectory = bpsConfig.getUploadPath() + SLASH + requestDir + "";

        File fileTemp = new File(customDirectory);
        FileUtil.mkdir(fileTemp);
        File outputFile = new File(customDirectory + SLASH + "党建指导员工作写实" + ".docx");//改成你本地文件所在目录

        FileOutputStream fos = new FileOutputStream(outputFile);
        template.getDocument().write(fos);

        File files = callOfficeConvert(outputFile, "党建指导员工作写实", customDirectory);

        FileTool.download(outputFile.getPath(), response);

        fos.close();
        FileUtil.del(outputFile);
//        if (isExport){
        FileUtil.del(files);
//        }
        FileUtil.del(fileTemp);

        return files;
    }


    public File callOfficeConvert(File file, String fileName, String zipDirPath) {
        try {
            IUser user = SecurityUtils.getCurrentUser();
            String currUserName = user.getUsername();
            if (StrUtil.isEmpty(fileName)) {
                fileName = file.getName().substring(0, file.getName().indexOf("."));
            }
            FileSystemResource resource = new FileSystemResource(file);
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("file", resource);
            ResponseEntity<byte[]> rsp = restTemplate.postForEntity("http://************:8088/office/action/fileConverter/toPdfReturnByBinary/sso?loginuser=" + rsaEncryptor.encrypt(currUserName) + "&appcode=" + "djfupt", param, byte[].class);
            String targetPath = zipDirPath.concat(SLASH).concat(fileName).concat(".pdf");
            //生成文件
            File outputFile = new File(targetPath);//改成你本地文件所在目录
            if (!outputFile.getParentFile().exists()) {
                outputFile.getParentFile().mkdirs();//创建父级文件路径
                outputFile.createNewFile();//创建文件
                System.out.println(outputFile.exists());
            }
            Files.write(Paths.get(targetPath), Objects.requireNonNull(rsp.getBody(),
                    "未获取到下载文件"));
            return new File(targetPath);
        } catch (Exception e) {
            e.printStackTrace();
            Exceptions.printException(new Exception("调用服务转换失败！", e.getCause()));
        }
        return null;
    }


    @Override
    public JsonResponse queryPolicyInfo(Map<String, Object> resultMap) {
        String startTime = MapUtil.getStr(resultMap, "startTime");
        String endTime = MapUtil.getStr(resultMap, "endTime");
        startTime = startTime + " 00:00:00";
        endTime = endTime + " 23:59:59";
        return JsonResponse.success(policyInfoForm(startTime, endTime));
    }


    public List<UsPolicyInfo> policyInfoForm(String startTime, String endTime) {
        Map<String, Object> queryMap = Maps.newHashMap();
        IUser user = SecurityUtils.getCurrentUser();
        List<Map<String, Object>> maps = null;
        List<UsPolicyInfo> policyInfos = new ArrayList<>();
        try {
            StringBuffer dataQuerySQL = new StringBuffer(" SELECT act.id, act.business_key, act.create_org_code, act.create_org_name, act.create_time, act.create_user_code, act.create_user_id, act.create_user_name, " +
                    " act.current_state, act.duration, act.enabled, act.end_time, act.parent_proc_id, act.previous_assistant, act.previous_assistant_date, act.previous_assistant_name, " +
                    " act.previous_assistant_org_code, act.previous_assistant_org_name, act.process_ch_name, act.process_def_id, act.process_def_name, act.process_inst_id, " +
                    " act.receipt_code, act.receipt_title, act.removed, act.start_time, act.update_time," +
                    " us.WORK_CODE," +
                    " t.PM_INS_ID as pmInsId," +
                    " wk.WORK_ITEM_ID as workItemId," +
                    " wk.ACTIVITY_DEF_ID as activityDefId," +
                    " wk.ACTIVITY_INST_NAME as activityInstName," +
                    " wk.participant as participant," +
                    " wk.assistant as assistant," +
                    " wk.current_State  as wkCurState," +
                    " wk.start_Time as workItemStartTime" +
                    " FROM act_business_status act," +
                    " us_pm_instence us," +
                    " US_POLICY_INFO  t," +
                    " wf_workitem_model wk" +
                    " WHERE act.PROCESS_INST_ID = wk.PROCESS_INST_ID" +
                    " and act.BUSINESS_KEY = us.id" +
                    " and us.pm_ins_type =:pmInsType" +
                    " and wk.participant = :participant" +
                    " and  t.creator=wk.participant" +
                    "  and t.pm_ins_id=act.receipt_code" +
                    " and act.enabled = 1" +
                    " and wk.enabled = 1" +
                    " and us.enabled = 1");
            queryMap.put("participant", user.getUsername());
            queryMap.put("pmInsType", "C");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                dataQuerySQL.append(" and  to_char(t.created_time,'yyyy-MM-dd HH:mm:ss' ) >= :startTime ");
                queryMap.put("startTime", startTime);
                dataQuerySQL.append(" and to_char(t.created_time,'yyyy-MM-dd HH:mm:ss' ) <= :endTime ");
                queryMap.put("endTime", endTime);
            }
            String orderSQL = " order by wk.START_TIME desc";
            maps = dynamicRepository.queryNamedParameterForList(dataQuerySQL.toString() + orderSQL, queryMap);
        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        if (maps.size() > 0) {
            for (Map<String, Object> map : maps) {
                UsPolicyInfo usPolicyInfo = usPolicyInfoService.findUsPolicyInfoInfo(user.getUsername(), map.get("PMINSID").toString());
                usPolicyInfo.setMaps(map);
                List<UsPantchDetail> usPantchDetailList = usPantchDetailService.findUsPantchDetailInfo(usPolicyInfo.getPmInsId(), usPolicyInfo.getCreator());
                usPolicyInfo.setUsPantchDetailList(usPantchDetailList);
                policyInfos.add(usPolicyInfo);
            }
        }
        return policyInfos;

    }

    public List<Map<String, Object>> problemForms(String startTime, String endTime) {

        List<Map<String, Object>> list = null;
        try {


            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer("select t.* from us_problem_info t ,  act_business_status act  where t.enabled = 1  and  t.pm_ins_id = act.receipt_code");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            }
            //如果为省公司管理员  执行一级查询
            List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
            //判断是否省公司管理员
            boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
            //如果不是省公司管理员执行五级查询，不是的话默认查看全部
            if (!isAdmin) {
                String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                switch (queryLevel) {
                    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_FOUR:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
                        break;
                    default:
                        sql.append(" and  t.creator =:username  ");
                        map.put("username", SecurityUtils.getCurrentUser().getUsername());
                        break;
                }
            }


            sql.append(" order by t.created_time desc");
            list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            list = FormatTool.formatConversion(list);//驼峰转换
        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        return list;
    }


    /**
     * 经验推广台账
     *
     * @return
     */

    public List<Map<String, Object>> caseInfoForms(String startTime, String endTime) {

        //获取当前当前月份
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        String treatTime = df.format(calendar.getTime());
        Map<String, Object> map = CollectionUtil.newHashMap();
        StringBuffer sql = new StringBuffer("   select t.*" +
                "  from US_CASE_INFO t, act_business_status act" +
                " where t.enabled = 1" +
                "   and act.enabled = 1" +
                "   and t.pm_ins_id = act.receipt_code" +
                "   and t.is_province='1'");
        if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
            sql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
            map.put("startTime", startTime);
            sql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
            map.put("endTime", endTime);
        } else {
            sql.append(" and  to_char(t.created_time,'yyyy-mm' )=:treatTime ");
            map.put("treatTime", treatTime);
        }

        //如果为省公司管理员  执行一级查询
        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        //判断是否省公司管理员
        boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));
        //如果不是省公司管理员执行五级查询，不是的话默认查看全部
        if (!isAdmin) {
            String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
            switch (queryLevel) {
                case DataPermissionConstants.QUERY_LEVEL_FIRST:
                    break;
                case DataPermissionConstants.QUERY_LEVEL_SECOND:
                    DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_THIRD:
                    DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                    break;
                case DataPermissionConstants.QUERY_LEVEL_FOUR:
                    DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
                    break;
                default:
                    sql.append(" and  t.creator =:username  ");
                    map.put("username", SecurityUtils.getCurrentUser().getUsername());
                    break;
            }
        }
        sql.append(" order by t.created_time desc");
        List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

        list = FormatTool.formatConversion(list);//驼峰转换
        return list;
    }

    @Override
    public JsonResponse workOrder(int page, int size, String title, String pmInsType, String state, String startTime, String endTime) {
        Page<List<Map<String, Object>>> mapList = null;
        Map<String, Object> resultMap = new HashMap<>();
        switch (pmInsType) {
            //  "A": "优秀案例上报",
            //    "B": "问题上报",
            //    "C": "政策宣讲"
            case "B":
                mapList = problemInfoService.conditionQuery(page, size, "PC", "", startTime, endTime, title, "", state);

                break;
            case "A":

                resultMap.put("problemName", title);
                resultMap.put("state", state);
                resultMap.put("startDate", startTime);
                resultMap.put("endDate", endTime);
                List<Map<String, Object>> resultList = caseInfoService.findAllApplication(resultMap);
                Pageable pageable = paginationHelp.getPageable(page, size, "", "");
                if (resultList != null) {
                    long totalRecords = resultList.size();
                    resultList = PageTool.pagination(resultList, page, size);
                    Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
                    return JsonResponse.success(pageInfo);
                }
                break;
            case "C":

                resultMap.put("title", title);
                resultMap.put("state", state);
                resultMap.put("startDate", startTime);
                resultMap.put("endDate", endTime);
                mapList = mapList(resultMap, page, size);


        }


        return JsonResponse.success(mapList);
    }

    @Override
    public void exportParameter(String title, String pmInsType, String state, String startTime, String endTime, HttpServletRequest request,
                                HttpServletResponse response) {
        List<Map<String, Object>> mapList = null;
        String name = "";
        Map<String, Object> resultMap = new HashMap<>();
        switch (pmInsType) {
            //  "A": "优秀案例上报",
            //    "B": "问题上报",
            //    "C": "政策宣讲"
            case "B":
                mapList = problemInfoService.conditionQuerys(1, 99999, "PC", "", startTime, endTime, title, "", state);
                name = "问题上报工单数据";
                break;
            case "A":
                name = "优秀案例上报工单数据";
                resultMap.put("problemName", title);
                resultMap.put("state", state);
                resultMap.put("startDate", startTime);
                resultMap.put("endDate", endTime);
                mapList = caseInfoService.findAllApplication(resultMap);

                break;
            case "C":
                name = "政策宣讲工单数据";
                resultMap.put("title", title);
                resultMap.put("state", state);
                resultMap.put("startDate", startTime);
                resultMap.put("endDate", endTime);
                mapList = policyOrder(resultMap);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
                String dateStr = LocalDateTime.now().format(formatter);
                String targetFileName = new StringBuffer(appConfig.getUploadTmpFileLocation()).append(ApplicationConstants.SLASH).append(name + "-").append(dateStr).append(".xls").toString();
                //String targetFileName = config.filePath + dateStr+ ".xls";
                File targetFile = new File(targetFileName);
                try {
                    if (mapList.size() > 0) {
                        ExcelUtil<Index> exportUtil = new ExcelUtil<Index>(Index.class);
                        List<Index> us = new ArrayList<>();
                        for (Map<String, Object> m : mapList) {
                            Index e = JacksonUtils.json2obj(JacksonUtils.obj2json(m), Index.class);
                            us.add(e);
                        }
                        int i = 1;
                        for (Index eu : us) {
                            eu.setId(String.valueOf(i));
                            i++;

                        }
                        exportUtil.exportExcel(us, "基本信息", new FileOutputStream(targetFile), null);
                        FileTool.download(targetFile.getPath(), request, response);
                    }
                } catch (Exception e) {

                }

        }

    }

    public Page<List<Map<String, Object>>> mapList(Map<String, Object> resultMap, Integer page, Integer size) {
        List<Map<String, Object>> list = policyOrder(resultMap);
        return caseInfoService.listPage(page, size, list);
    }

    public List<Map<String, Object>> policyOrder(Map<String, Object> resultMap) {

        IUser iuser = SecurityUtils.getCurrentUser();
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startDate");
        String title = cn.hutool.core.map.MapUtil.getStr(resultMap, "title");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endDate");
        String state = cn.hutool.core.map.MapUtil.getStr(resultMap, "state");

        try {
            //获取当前当前月份
            DateFormat df = new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            String treatTime = df.format(calendar.getTime());
            Map<String, Object> map = CollectionUtil.newHashMap();
            StringBuffer sql = new StringBuffer("select t.*,act.current_state,act.process_inst_id  as aaa from US_POLICY_INFO t ,  act_business_status act  where t.enabled = 1  and  t.pm_ins_id = act.receipt_code");
            if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                map.put("startTime", startTime);
                sql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                map.put("endTime", endTime);
            } else {
                sql.append(" and  to_char(t.created_time,'yyyy-mm' )=:treatTime ");
                map.put("treatTime", treatTime);
            }
            if (StringUtils.isNotEmpty(state)) {
                sql.append(" and   act.current_state   =:state  ");
                map.put("state", state);
            }
            if (StringUtils.isNotEmpty(title)) {
                sql.append(" and   t.title    like concat( concat('%',:title),'%')  ");
                map.put("title", title);
            }
            //如果为省公司管理员 或者观察员  执行一级查询
            List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
            //判断是否省公司管理员
            boolean isAdmin = simpleGroupList.stream().anyMatch(simpleGroup -> StrUtil.equals(Constants.PROVINCE_ADMIN_GROUP, simpleGroup.getSid()));


            //如果不是省公司管理员执行五级查询，不是的话默认查看全部
            if (!isAdmin) {
                String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                switch (queryLevel) {
                    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_SECOND);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_THIRD);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_FOUR:
                        DataPermissionTool.handleSql(sql, map, DataPermissionConstants.QUERY_LEVEL_FOUR);
                        //网格五级权限
                        sql.append(" and   t.grid_name   =:gridName  ");
                        List<UsAdminManager> adminManagers = adminManagerRepository.findByUserNameAndEnabledAndroleUserId(iuser.getUsername(), Constants.FJFUPT_BRO);
                        map.put("gridName", adminManagers.get(0).getGridName());
                        break;


                    default:
                        sql.append(" and  t.creator =:username  ");
                        map.put("username", SecurityUtils.getCurrentUser().getUsername());
                        break;
                }
            }
            sql.append(" order by t. created_time desc  ");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            if (list.size() > 0) {
                for (Map<String, Object> map1 : list) {
                    map1.put("processInstId", map1.get("AAA"));
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    map1.put("CREATED_TIME", sdf2.format(map1.get("CREATED_TIME")));
                    if (!map1.get("CURRENT_STATE").equals("7")) {
                        Map<String, Object> map2 = usProblemInfoRepository.findAllByReceiptCodeAndCurrentStateAndCreator(map1.get("PM_INS_ID").toString(), map1.get("CREATOR").toString());
                        map1.put("HANDLING_LINK", map2.get("WORK_ITEM_NAME"));
                        map1.put("ACTIVITY_DEF_ID", map2.get("ACTIVITY_DEF_ID"));

                    }
                }
            }
            list = FormatTool.formatConversion(list);//驼峰转换
            return list;


        } catch (Exception e) {
            Exceptions.printException(e);
            log.debug("条件查询--------conditionQuery----" + e.getMessage());
        }
        return null;
    }


    public List<DataScreeningVo> dataScreening( Map<String, Object> resultMap) {

        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//companyName
        String time = cn.hutool.core.map.MapUtil.getStr(resultMap, "time");//
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");
        if (StringUtils.isEmpty(endTime)) {
            startTime = "2001-01-01 01:01:01";
        } else {
            startTime = startTime + " 00:00:00";
        }
        if (StringUtils.isEmpty(endTime)) {
            endTime = "2901-01-01 01:01:01";
        } else {
            endTime = endTime + " 23:59:59";
        }
        List<SysDictValue> dictValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(companyName)) {
            SysDictValue dictValue = dictValueService.findByDictTypeAndName("company", companyName);
            if (dictValue != null) {
                dictValues.add(dictValue);
            }
        } else {
            dictValues = dictValueService.findByDictType("company");
        }

        List<DataScreeningVo> dataScreeningVos = new ArrayList<>();

        if (dictValues.size() > 0) {
            for (SysDictValue dictValue : dictValues) {
                DataScreeningVo dataScreeningVo = new DataScreeningVo();
                dataScreeningVo.setCompanyName(dictValue.getName());
                //查询管理员人数
                int gridNum = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserId(dictValue.getName(), Constants.FJFUPT_BRO);
                dataScreeningVo.setGridNum(gridNum);
                //政策宣讲完成数量(固定查询某月)
                int policyNum = policyInfoRepository.findAllByEnabledAndPolicyTime(dictValue.getName(), time);
                //计算政策宣讲完成比例
                String policy = "0";
                if (gridNum == 0 || policyNum == 0) {
                    policy = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(policyNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    policy = a.toString();
                }
                dataScreeningVo.setPolicy(policy + "%");
                dataScreeningVo.setPolicyNum(policyNum);
                //计算思政填报数量
                int recordFillNum = recordFillRepository.countByCompany(time, dictValue.getName());
                //计算思政完成比例
                String recordFill = "0";
                if (gridNum == 0 || recordFillNum == 0) {
                    recordFill = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(recordFillNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    recordFill = a.toString();
                }
                dataScreeningVo.setRecordFill(recordFill + "%");
                dataScreeningVo.setRecordFillNum(recordFillNum);
                Map<String, Object> map = CollectionUtil.newHashMap();
                //计算问题数量
                //int problemNum = problemInfoRepository.findAllByEnabledAndQuestionMode(startTime, endTime,dictValue.getValue());
                //int problemNum = problemInfoRepository.countByCompany(time, dictValue.getName());
                int problemNum = 0;
                StringBuffer problemNumSql = new StringBuffer(" select distinct t.creator\n" +
                        "  from us_problem_info t\n" +
                        " where t.enabled = 1\n" +
                        "   and t.belong_company_code = :companyName\n" +
                        "   and t.is_draft = 1 ");
                map.put("companyName", dictValue.getValue());

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    problemNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    problemNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }
                List<Map<String, Object>> problemNumList = customDynamicWhere.queryNamedParameterForList(problemNumSql.toString(), map);
                problemNum = problemNumList.size();

                //计算问题完成比例
                String problem = "0";
                if (gridNum == 0 || problemNum == 0) {
                    problem = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(problemNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    problem = a.toString();
                }
                dataScreeningVo.setProblemInfo(problem + "%");
                dataScreeningVo.setProblemInfoNum(problemNum);
                //计算经验数量
                int caseInfoNum = 0;
                StringBuffer sql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                        "  from US_CASE_INFO t, act_business_status act" +
                        " where t.enabled = 1" +
                        "   and act.enabled = 1" +
                        "   and act.current_state = 7  " +
                        "   and t.pm_ins_id = act.receipt_code" +
                        "   and t.question_mode ='0'" +
                        " and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode )" +
                        "   and (select count(0)" +
                        "          from wf_workitem_model wks" +
                        "         where wks.receipt_code = t.pm_ins_id" +
                        "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                        "           and wks.enabled = 1) > 0  ");
                map.put("companyCode", dictValue.getValue());

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    sql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    sql.append(" and  t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }
                sql.append(" order by t.created_time desc");
                List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
                caseInfoNum = resultList.size();
                //计算经验完成比例
                String caseInfo = "0";
                if (gridNum == 0 || caseInfoNum == 0) {
                    caseInfo = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(caseInfoNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    caseInfo = a.toString();
                }
                dataScreeningVo.setCaseInfo(caseInfo + "%");
                dataScreeningVo.setCaseInfoNum(caseInfoNum);
                dataScreeningVos.add(dataScreeningVo);
            }
        }

        return dataScreeningVos;
    }

    @Override
    public DataScreeningVo dataScreeningTotal(Map<String, Object> resultMap) {

        String companyName = cn.hutool.core.map.MapUtil.getStr(resultMap, "companyName");//companyName
        String time = cn.hutool.core.map.MapUtil.getStr(resultMap, "time");//
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");

        List<SysDictValue> dictValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(companyName)) {
            SysDictValue dictValue = dictValueService.findByDictTypeAndName("company", companyName);
            if (dictValue != null) {
                dictValues.add(dictValue);
            }
        } else {
            dictValues = dictValueService.findByDictType("company");
        }

        int gridNum = adminManagerRepository.findByEnabledAndRoleUserId( Constants.FJFUPT_BRO);
        int policyNum=0;
        BigDecimal policy = new BigDecimal("0");

        int recordFillNum=0;
        BigDecimal recordFill = new BigDecimal("0");

        int problemNum=0;
        BigDecimal problem = new BigDecimal("0");

        int caseInfoNum=0;
        BigDecimal caseInfo = new BigDecimal("0");
        DataScreeningVo dataScreeningVo = new DataScreeningVo();
        if (dictValues.size() > 0) {
            for (SysDictValue dictValue : dictValues) {
                //查询管理员人数
                //政策宣讲完成数量(固定查询某月)
                 policyNum+= policyInfoRepository.findAllByEnabledAndPolicyTime(dictValue.getName(), time);
                //计算政策宣讲完成比例
                if (!(gridNum == 0 || policyNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(policyNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    policy.add(a);
                }

                //计算思政填报数量
                 recordFillNum += recordFillRepository.countByCompany(time, dictValue.getName());
                //计算思政完成比例
                if (!(gridNum == 0 || recordFillNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(recordFillNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    recordFill.add(a);
                }

                Map<String, Object> map = CollectionUtil.newHashMap();
                //计算问题数量
                StringBuffer problemNumSql = new StringBuffer(" select distinct t.creator\n" +
                        "  from us_problem_info t\n" +
                        " where t.enabled = 1\n" +
                        "   and t.belong_company_code = :companyName\n" +
                        "   and t.is_draft = 1 ");
                map.put("companyName", dictValue.getValue());

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    problemNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    problemNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }
                List<Map<String, Object>> problemNumList = customDynamicWhere.queryNamedParameterForList(problemNumSql.toString(), map);
                problemNum += problemNumList.size();

                //计算问题完成比例
                if (!(gridNum == 0 || problemNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(problemNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    problem.add(a);
                }

                //计算经验数量
                StringBuffer sql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                        "  from US_CASE_INFO t, act_business_status act" +
                        " where t.enabled = 1" +
                        "   and act.enabled = 1" +
                        "   and act.current_state = 7  " +
                        "   and t.pm_ins_id = act.receipt_code" +
                        "   and t.question_mode ='0'" +
                        " and (t.belong_company_code = :companyCode or t.belong_company_code_parent = :companyCode )" +
                        "   and (select count(0)" +
                        "          from wf_workitem_model wks" +
                        "         where wks.receipt_code = t.pm_ins_id" +
                        "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                        "           and wks.enabled = 1) > 0  ");
                map.put("companyCode", dictValue.getValue());

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    sql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    sql.append(" and  t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }
                sql.append(" order by t.created_time desc");
                List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
                caseInfoNum += resultList.size();
                //计算经验完成比例
                if (gridNum == 0 || caseInfoNum == 0) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(caseInfoNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    caseInfo.add(a);
                }
            }
            dataScreeningVo.setGridNum(gridNum);
            dataScreeningVo.setPolicy(policy + "%");
            dataScreeningVo.setPolicyNum(policyNum);
            dataScreeningVo.setRecordFill(recordFill+ "%");
            dataScreeningVo.setRecordFillNum(recordFillNum);
            dataScreeningVo.setProblemInfo(problem+ "%");
            dataScreeningVo.setProblemInfoNum(problemNum);
            dataScreeningVo.setCaseInfo(caseInfo+ "%");
            dataScreeningVo.setCaseInfoNum(caseInfoNum);
        }else {
            dataScreeningVo.setGridNum(0);
            dataScreeningVo.setPolicy( "0.00%");
            dataScreeningVo.setPolicyNum(0);
            dataScreeningVo.setRecordFill("0.00%");
            dataScreeningVo.setRecordFillNum(0);
            dataScreeningVo.setProblemInfo("0.00%");
            dataScreeningVo.setProblemInfoNum(0);
            dataScreeningVo.setCaseInfo("0.00%");
            dataScreeningVo.setCaseInfoNum(0);
        }
        return dataScreeningVo;
    }


    public List<DataScreeningVo> dataScreeningBranch( Map<String, Object> resultMap) {
        String orgCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "orgCode");//companyName
        String time = cn.hutool.core.map.MapUtil.getStr(resultMap, "time");//
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");
        if (StringUtils.isEmpty(endTime)) {
            startTime = "2001-01-01 01:01:01";
        } else {
            startTime = startTime + " 00:00:00";
        }
        if (StringUtils.isEmpty(endTime)) {
            endTime = "2901-01-01 01:01:01";
        } else {
            endTime = endTime + " 23:59:59";
        }
        List<SysDictValue> sysDictValues = new ArrayList<>();
        //查出当前下的组织
        List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
        int i = 0;
        if (simpleOrgList.size() > 0) {
            for (SimpleOrg simpleOrg : simpleOrgList) {
                if (i == 0) {
                    simpleOrg.setOrgName(simpleOrg.getDisplayName());

                }
                if (StringUtils.isNotEmpty(orgCode)) {
                    if (!simpleOrg.getOrgCode().equals(orgCode)) {
                        continue;
                    }
                }
                if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                    continue;
                }
                SysDictValue sysDictValue = new SysDictValue();
                sysDictValue.setValue(simpleOrg.getOrgCode());
                sysDictValue.setName(simpleOrg.getOrgName());
                sysDictValues.add(sysDictValue);
                i++;
            }
        }
        List<DataScreeningVo> dataScreeningVos = new ArrayList<>();
        if (sysDictValues.size() > 0) {
            int is = 0;
            for (SysDictValue sysDictValue : sysDictValues) {
                DataScreeningVo dataScreeningVo = new DataScreeningVo();
                dataScreeningVo.setCompanyName(sysDictValue.getName());
                //计算网格数量
                int gridNum = 0;
                if (is == 0) {
                    gridNum = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                } else {
                    gridNum = adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIds(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                }
                dataScreeningVo.setGridNum(gridNum);

                //计算政策宣讲数量
                int policyNum = 0;
                if (is == 0) {
                    policyNum = policyInfoRepository.findAllByCountComCodes(sysDictValue.getValue(), time).size();
                } else {
                    policyNum = policyInfoRepository.findAllByCountComCode(sysDictValue.getValue(), time).size();
                }

                //计算政策宣讲完成比例
                String policy = "0";
                if (gridNum == 0 || policyNum == 0) {
                    policy = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(policyNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    policy = a.toString();
                }
                dataScreeningVo.setPolicy(policy+"%");
                dataScreeningVo.setPolicyNum(policyNum);
                //计算思政数量
                int recordFillNum = 0;
                StringBuffer sql = new StringBuffer(" select distinct t.creator" +
                        "    from US_RECORD_FILL t" +
                        "    where t.enabled = 1" +
                        "    and SUBSTR(nvl(t.talk_time, '1950-01'), 0, 7) = :applyTime" +
                        "     and t.is_draft = 1 ");
                Map<String, Object> param = Maps.newHashMap();
                if (is == 0) {
                    sql.append("  and (t.belong_department_code = :companyCode  ) ");
                } else {
                    sql.append("  and (t.belong_company_code = :companyCode or t.belong_department_code = :companyCode  ) ");
                }
                param.put("companyCode", sysDictValue.getValue());
                param.put("applyTime", time);
                List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
                recordFillNum = list.size();
                //计算思政比例
                String recordFill = "0";
                if (gridNum == 0 || recordFillNum == 0) {
                    recordFill = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(recordFillNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if(a.compareTo(new BigDecimal("100"))>0){
                        a=new BigDecimal("100");
                    }
                    recordFill = a.toString();
                }
                dataScreeningVo.setRecordFill(recordFill+"%");
                dataScreeningVo.setRecordFillNum(recordFillNum);


                //计算问题上报数量
                int problemNum = 0;
                Map<String, Object> map = new HashMap<>();
                StringBuffer problemNumSql = new StringBuffer(" select distinct t.creator \n" +
                        "  from us_problem_info t\n" +
                        " where t.enabled = 1\n" +
                        "   and t.is_draft = 1 ");
                map.put("companyCode", sysDictValue.getValue());

                if (is == 0) {
                    problemNumSql.append(" and t.belong_org_code = :companyCode ");
                } else {
                    problemNumSql.append("and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                }

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    problemNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    problemNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }

                //if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                //    problemNumSql.append(" and to_char(t.created_time, 'yyyy-MM-dd') >= :startTime ");
                //    map.put("startTime", startTime);
                //    problemNumSql.append(" and to_char(t.created_time, 'yyyy-MM-dd') <= :endTime ");
                //    map.put("endTime", endTime);
                //}

                List<Map<String, Object>> proList = customDynamicWhere.queryNamedParameterForList(problemNumSql.toString(), map);
                problemNum = proList.size();
                //计算问题比例
                String problemInfo = "0";
                if (gridNum == 0 || problemNum == 0) {
                    problemInfo = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(problemNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    problemInfo = a.toString();
                }
                dataScreeningVo.setProblemInfo(problemInfo+"%");
                dataScreeningVo.setProblemInfoNum(problemNum);
                //计算经验数量
                int caseInfoNum = 0;
                List<Map<String, Object>> resultList = new ArrayList<>();
                if (is == 0) {
                    Map<String, Object> caseInfoNumMap = CollectionUtil.newHashMap();
                    StringBuffer caseInfoNumSql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and act.current_state = 7  " +
                            "   and t.pm_ins_id = act.receipt_code" +
                            "   and t.question_mode ='0'" +
                            "  and    t.belong_org_code=:companyCode " +
                            "   and (select count(0)" +
                            "          from wf_workitem_model wks" +
                            "         where wks.receipt_code = t.pm_ins_id" +
                            "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                            "           and wks.enabled = 1) > 0  ");
                    caseInfoNumMap.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        caseInfoNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                        caseInfoNumMap.put("startTime", startTime);
                        caseInfoNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                        caseInfoNumMap.put("endTime", endTime);
                    }

                    //if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    //    caseInfoNumSql.append(" and  to_char(t.created_time,'yyyy-MM-dd' ) >= :startTime ");
                    //    caseInfoNumMap.put("startTime", startTime);
                    //    caseInfoNumSql.append(" and to_char(t.created_time,'yyyy-MM-dd' ) <= :endTime ");
                    //    caseInfoNumMap.put("endTime", endTime);
                    //}


                    caseInfoNumSql.append(" order by t.created_time desc");
                    resultList = customDynamicWhere.queryNamedParameterForList(caseInfoNumSql.toString(), caseInfoNumMap);

                } else {

                    Map<String, Object> maps = CollectionUtil.newHashMap();
                    StringBuffer sqls = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and t.pm_ins_id = act.receipt_code" +
                            " and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                    maps.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sqls.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                        maps.put("startTime", startTime);
                        sqls.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                        maps.put("endTime", endTime);
                    }


                    sqls.append(" order by t.created_time desc");
                    resultList = customDynamicWhere.queryNamedParameterForList(sqls.toString(), maps);

                }
                caseInfoNum = resultList.size();
                //计算经验比例
                String caseNum = "0";
                if (gridNum == 0 || caseInfoNum == 0) {
                    caseNum = "0";
                } else {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(caseInfoNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    caseNum = a.toString();
                }

                dataScreeningVo.setCaseInfo(caseNum+"%");
                dataScreeningVo.setCaseInfoNum(caseInfoNum);
                is++;
                dataScreeningVos.add(dataScreeningVo);

            }


        }


        return dataScreeningVos;
    }

    @Override
    public void exportDataScreening(HttpServletRequest request, HttpServletResponse response, Map<String, Object> resultMap) {
        String path = request.getServletContext().getRealPath("down");
        IUser user=SecurityUtils.getCurrentUser();
        try {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
        String dateStr = LocalDateTime.now().format(formatter);
        String fileName = "数据总览" + dateStr + ".xls";
        List<DataScreeningVo> dataScreeningVos = new ArrayList<>();
            if(user.getBelongCompanyTypeDictValue().equals("01")){
                dataScreeningVos= this.dataScreening(resultMap);
            }else {
                dataScreeningVos= this.dataScreeningBranch(resultMap);
            }
        fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Type", "application/msexcel");
        response.setHeader("Content-disposition", String.format("attachment; filename=\"%s\"", fileName));

        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String targetFileName = path + "" + fileName;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<DataScreeningVo> exportUtil = new ExcelUtil<>(DataScreeningVo.class);
        exportUtil.exportExcel(dataScreeningVos, "数据总览", new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), response);
    } catch (Exception e) {
        Exceptions.printException(e);
    }
    }

    @Override
    public JsonResponse judgeUser() {


        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);

        boolean  isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.CITY_OBSERVER, simpleRoless.getRoleCode()));
        if(!isRoleAdmin){
            isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.PROVINCE_OBSERVER, simpleRoless.getRoleCode()));

        }
        if(!isRoleAdmin){
            isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_City, simpleRoless.getRoleCode()));

        }
        if(!isRoleAdmin){
            isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));

        }
        if(!isRoleAdmin){
            isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_COUNTY, simpleRoless.getRoleCode()));

        }
        if(!isRoleAdmin){
            isRoleAdmin = simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.COUNTY_OBSERVER, simpleRoless.getRoleCode()));

        }
        Map<String,Object> map=new HashMap<>();
        map.put("isAdmin",isRoleAdmin);

        return JsonResponse.success(map);
    }

    @Override
    public DataScreeningVo dataScreeningBranchTotal(Map<String, Object> resultMap) {
        String orgCode = cn.hutool.core.map.MapUtil.getStr(resultMap, "orgCode");//
        String time = cn.hutool.core.map.MapUtil.getStr(resultMap, "time");//
        String startTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "startTime");
        String endTime = cn.hutool.core.map.MapUtil.getStr(resultMap, "endTime");
        List<SysDictValue> sysDictValues = new ArrayList<>();
        //查出当前下的组织
        List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);

        int i = 0;

        if (simpleOrgList.size() > 0) {
            for (SimpleOrg simpleOrg : simpleOrgList) {
                if (i == 0) {
                    simpleOrg.setOrgName(simpleOrg.getDisplayName());
                }
                if (StringUtils.isNotEmpty(orgCode)) {
                    if (!simpleOrg.getOrgCode().equals(orgCode)) {
                        continue;
                    }
                }
                if (simpleOrg.equals(SecurityUtils.getCurrentUser().getBelongOrgCode())) {
                    continue;
                }
                SysDictValue sysDictValue = new SysDictValue();
                sysDictValue.setValue(simpleOrg.getOrgCode());
                sysDictValue.setName(simpleOrg.getOrgName());
                sysDictValues.add(sysDictValue);
                i++;
            }
        }

        DataScreeningVo dataScreeningVo = new DataScreeningVo();
        dataScreeningVo.setCompanyName(SecurityUtils.getCurrentUser().getBelongCompanyName());
        if (sysDictValues.size() > 0) {
            int is = 0;
            int gridNum = 0;
            int policyNum = 0;
            BigDecimal policy = new BigDecimal("0");

            int recordFillNum = 0;
            BigDecimal recordFill = new BigDecimal("0");

            int problemNum = 0;
            BigDecimal problemInfo = new BigDecimal("0");

            int caseInfoNum = 0;
            BigDecimal caseNum = new BigDecimal("0");

            for (SysDictValue sysDictValue : sysDictValues) {
                //计算网格数量
                if (is == 0) {
                    gridNum += adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIdss(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                } else {
                    gridNum += adminManagerRepository.findByCompanyCodeAndEnabledAndRoleUserIds(sysDictValue.getValue(), Constants.FJFUPT_BRO);
                }

                //计算政策宣讲数量
                if (is == 0) {
                    policyNum += policyInfoRepository.findAllByCountComCodes(sysDictValue.getValue(), time).size();
                } else {
                    policyNum += policyInfoRepository.findAllByCountComCode(sysDictValue.getValue(), time).size();
                }

                //计算政策宣讲完成比例
                if (!(gridNum == 0 || policyNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(policyNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if (a.compareTo(new BigDecimal("100")) > 0) {
                        a = new BigDecimal("100");
                    }
                    policy.add(a);
                }
                //计算思政数量
                StringBuffer sql = new StringBuffer(" select distinct t.creator" +
                        "    from US_RECORD_FILL t" +
                        "    where t.enabled = 1" +
                        "    and SUBSTR(nvl(t.talk_time, '1950-01'), 0, 7) = :applyTime" +
                        "     and t.is_draft = 1 ");
                Map<String, Object> param = Maps.newHashMap();
                if (is == 0) {
                    sql.append("  and (t.belong_department_code = :companyCode  ) ");
                } else {
                    sql.append("  and (t.belong_company_code = :companyCode or t.belong_department_code = :companyCode  ) ");
                }
                param.put("companyCode", sysDictValue.getValue());
                param.put("applyTime", time);
                List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), param);
                recordFillNum += list.size();
                //计算思政比例
                if (!(gridNum == 0 || recordFillNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(recordFillNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    if (a.compareTo(new BigDecimal("100")) > 0) {
                        a = new BigDecimal("100");
                    }
                    recordFill.add(a);
                }


                //计算问题上报数量
                Map<String, Object> map = new HashMap<>();
                StringBuffer problemNumSql = new StringBuffer(" select distinct t.creator \n" +
                        "  from us_problem_info t\n" +
                        " where t.enabled = 1\n" +
                        "   and t.is_draft = 1 ");
                map.put("companyCode", sysDictValue.getValue());

                if (is == 0) {
                    problemNumSql.append(" and t.belong_org_code = :companyCode ");
                } else {
                    problemNumSql.append("and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                }

                if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                    problemNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("startTime", startTime);
                    problemNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                    map.put("endTime", endTime);
                }

                List<Map<String, Object>> proList = customDynamicWhere.queryNamedParameterForList(problemNumSql.toString(), map);
                problemNum += proList.size();
                //计算问题比例
                if (!(gridNum == 0 || problemNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(problemNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    problemInfo.add(a);
                }
                //计算经验数量
                List<Map<String, Object>> resultList = new ArrayList<>();
                if (is == 0) {
                    Map<String, Object> caseInfoNumMap = CollectionUtil.newHashMap();
                    StringBuffer caseInfoNumSql = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and act.current_state = 7  " +
                            "   and t.pm_ins_id = act.receipt_code" +
                            "   and t.question_mode ='0'" +
                            "  and    t.belong_org_code=:companyCode " +
                            "   and (select count(0)" +
                            "          from wf_workitem_model wks" +
                            "         where wks.receipt_code = t.pm_ins_id" +
                            "           and wks.activity_def_id = 'djfupt.proAdminCheck'" +
                            "           and wks.enabled = 1) > 0  ");
                    caseInfoNumMap.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        caseInfoNumSql.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                        caseInfoNumMap.put("startTime", startTime);
                        caseInfoNumSql.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                        caseInfoNumMap.put("endTime", endTime);
                    }
                    caseInfoNumSql.append(" order by t.created_time desc");
                    resultList = customDynamicWhere.queryNamedParameterForList(caseInfoNumSql.toString(), caseInfoNumMap);

                } else {

                    Map<String, Object> maps = CollectionUtil.newHashMap();
                    StringBuffer sqls = new StringBuffer("   select t.* ,act.current_state ,act.PROCESS_INST_ID" +
                            "  from US_CASE_INFO t, act_business_status act" +
                            " where t.enabled = 1" +
                            "   and act.enabled = 1" +
                            "   and t.pm_ins_id = act.receipt_code" +
                            " and (t.belong_org_code = :companyCode or t.belong_department_code = :companyCode )  ");
                    maps.put("companyCode", sysDictValue.getValue());

                    if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
                        sqls.append(" and  t.created_time >= to_date(:startTime,'YYYY-MM-DD HH24:MI:SS') ");
                        maps.put("startTime", startTime);
                        sqls.append(" and t.created_time <= to_date(:endTime,'YYYY-MM-DD HH24:MI:SS') ");
                        maps.put("endTime", endTime);
                    }


                    sqls.append(" order by t.created_time desc");
                    resultList = customDynamicWhere.queryNamedParameterForList(sqls.toString(), maps);

                }
                caseInfoNum += resultList.size();
                //计算经验比例
                if (!(gridNum == 0 || caseInfoNum == 0)) {
                    //保留两位小数
                    BigDecimal a = new BigDecimal(caseInfoNum).multiply(new BigDecimal(100)).divide(new BigDecimal(gridNum), 2, RoundingMode.HALF_UP);
                    a = a.compareTo(new BigDecimal(100)) > 0 ? new BigDecimal(100) : a;
                    caseNum.add(a);
                }
                is++;
            }
            dataScreeningVo.setGridNum(gridNum);
            dataScreeningVo.setPolicy(policy + "%");
            dataScreeningVo.setPolicyNum(policyNum);
            dataScreeningVo.setRecordFill(recordFill + "%");
            dataScreeningVo.setRecordFillNum(recordFillNum);
            dataScreeningVo.setProblemInfo(problemInfo + "%");
            dataScreeningVo.setProblemInfoNum(problemNum);
            dataScreeningVo.setCaseInfo(caseNum + "%");
            dataScreeningVo.setCaseInfoNum(caseInfoNum);
        }else {
            dataScreeningVo.setGridNum(0);
            dataScreeningVo.setPolicy( "0.00%");
            dataScreeningVo.setPolicyNum(0);
            dataScreeningVo.setRecordFill("0.00%");
            dataScreeningVo.setRecordFillNum(0);
            dataScreeningVo.setProblemInfo("0.00%");
            dataScreeningVo.setProblemInfoNum(0);
            dataScreeningVo.setCaseInfo("0.00%");
            dataScreeningVo.setCaseInfoNum(0);
        }
        return  dataScreeningVo;

    }


}
