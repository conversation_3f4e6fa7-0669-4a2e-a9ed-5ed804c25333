package com.simbest.boot.djfupt.admin.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.ScreenAdminManager;
import com.simbest.boot.djfupt.admin.service.IScreenAdminManagerService;
import com.simbest.boot.djfupt.admin.utils.MyUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


@Api(description = "管理员配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/screenAdminManager")
public class ScreenAdminManagerController extends LogicController<ScreenAdminManager, String> {

    private final IScreenAdminManagerService service;

    public ScreenAdminManagerController(IScreenAdminManagerService ScreenAdminManagerService) {
        super(ScreenAdminManagerService);
        this.service = ScreenAdminManagerService;
    }

    /**
     * 角色 分页
     * <br/>params [o]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/5/18 18:22
     */
    @PostMapping(value = {"/findAllInfo", "/findAllInfo/sso", "/api/findAllInfo"})
    public JsonResponse findAllInfo(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false) String direction, //
                                    @RequestParam(required = false) String properties,
                                    @RequestBody ScreenAdminManager o) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, direction, properties);
        return JsonResponse.success(service.findAllInfo(o, pageable));
    }

    /**
     * 批量新增角色
     * <br/>params [source]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/5/19 16:58
     */
    @PostMapping(value = {"/saveAllInfo", "/saveAllInfo/sso", "/api/saveAllInfo"})
    public JsonResponse saveAllInfo(@RequestBody ScreenAdminManager o,
                                    @RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode) {
        JsonResponse r = MyUtils.manualLogin(source, currentUserCode);
        return Objects.nonNull(r) ? r : JsonResponse.success(service.saveAllInfo(o.getUsername()));
    }

}
