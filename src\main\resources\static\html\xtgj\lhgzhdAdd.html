<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>装维入格调研台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
    </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
    </script>
    <script type="text/javascript">
        getCurrent()
        // 想要合并的行
        var wantSumArr = [
            {text:'上门准备', num: 2},
            {text:'入户规范', num: 4},
            {text:'测速展示', num: 2},
            {text:'演示培训', num: 2},
            {text:'现场清理', num: 2},
            {text:'满意引导', num: 2}
        ]
        var isSumArr = []
        $(function () {

            if (gps.type == 'add') {
                ajaxgeneral({
                    url: 'action/usZwrgdyInfo/getGridName',
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        formval({
                            gridName: data.data.gridName,
                            belongDepartmentName: web.currentUser.belongDepartmentName,
                            belongCompanyName: web.currentUser.belongCompanyName,
                            truename: web.currentUser.truename,
                            phone: web.currentUser.preferredMobile,
                            createdTime: getNow('yyyy-MM-dd hh:mm:ss')
                        }, '#glyTableAddForm')
                    }
                });

                ajaxgeneral({
                    url: 'sys/dictValue/findDictValue',
                    data: {
                        "dictType": "XTGJ_LHGZHD_TYPE"
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        var list = data.data
                        isSumArr = []
                        for (let i = 0; i < list.length; i++) {
                            var $tr = $(".ctable-1").find("thead.trow tr").clone();
                            var len = $(".ctable-1").find("tbody").find("tr[path]").length + 1;
                            $tr.attr("path", len);
                            $tr.find("td[path=xh]").text(len);
                            $tr.find("td[path=item]").text(list[i].name);
                            $tr.find("td[path=guifan]").text(list[i].value);
                            $tr.find('#dictId').val(list[i].id)
                            $tr.find('#dictValue').val(list[i].value)
                            $tr.find('#dictName').val(list[i].name)
                            $tr.find("textarea,input,select").each(function (x, y) {
                                tdPathI($(y), $tr);
                            });
                            if (len == 1) {
                                $tr.find('.cselectorRadioUL').hide()
                            } else {
                                $tr.find('.look-file').hide()
                            }

                            wantSumArr.forEach(function (item) {
                                if (item.text == list[i].name) {
                                    if (isSumArr.indexOf(item.text) === -1) {
                                        $tr.find("td[path=item]").attr('rowspan', item.num)
                                        isSumArr.push(item.text)
                                    } else {
                                        $tr.find("td[path=item]").hide()
                                    }
                                }
                            })

                            $tr.appendTo($(".ctable-1").find("tbody"));
                        }
                        $('.col-1').text(list.length + 1)
                        addIdeaRow()
                    }
                });
            } else {
                ajaxgeneral({
                    url: 'action/usLhgzhdInfo/findByIdInfo?source=PC&id=' + gps.id,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        formval(data.data, '#glyTableAddForm')
                        var xh = 1;
                        var operationList = data.data.operationList
                        isSumArr = []
                        for (let i = 0; i < operationList.length; i++) {
                            appTr1(xh, operationList[i], i)
                            xh++
                        }
                        $('.col-1').text(operationList.length + 1)
                        var itemInfoList = data.data.itemInfoList
                        for (let i = 0; i < itemInfoList.length; i++) {
                            appTr2('', itemInfoList[i], i)
                            xh++
                        }
                        formReadonly("glyTableAddForm")
                        $('.add-handle').hide()
                    }
                });
            }
        });

        function tdPathI($id, $tr, val) {
            if (val) {
                tdPath($id, val);
            } else {
                tdPath($id);
            }
        };

        //删除
        $(document).on("click", ".a_del_btn", function () {
            var $tr = $(this).parents("tr");
            var path;
            if ($tr.attr("path")) {
                path = $tr.attr("path");
                $tr.remove();
            }
            xh();
        });

        function xh() {
            $("table.ctable-2 tbody").find("tr[path]").each(function (x, y) {
                var path = x + 1;
                $(y).attr("path", path);
            });
        };

        //意见建议 新增点击
        $(document).on("click", ".a_add_btn", function () {
            addIdeaRow()
        });

        // 意见建议新增一行
        function addIdeaRow() {
            var $tr = $(".ctable-2").find("thead.trow tr").clone();
            var len = $(".ctable-2").find("tbody").find("tr[path]").length + 1;
            $tr.attr("path", len);
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPathI($(y), $tr);
            });
            comboboxChange($tr, "");
            if (len == 1) {
                $tr.find('.a_del_btn').hide()
            } else {
                $tr.find('.a_add_btn').hide()
            }
            $tr.appendTo($(".ctable-2").find("tbody"));
        }
        var dataList = [{name: '客户反映', value: '0'}, {name: '跟装发现', value: '1'}]
        //combobox变形
        function comboboxChange($tr, code) {
            $tr.find("input.opinionType").combobox({
                "valueField": 'value',
                "ischooseall": false,
                "textField": 'name',
                "editable": false,
                "panelHeight": '100',
                "data": dataList
                // "url": web.rootdir + 'sys/dictValue/findDictValue',
                // "queryParams": { "dictType": "XTGJ_LHGZHD01_TYPE" },
                // "contentType": "application/json"
            });
        };


        function appTr1(xh, data, i) {
            var $tr = $(".ctable-1").find("thead.trow tr").clone();
            var len = $(".ctable-1").find("tbody").find("tr[path]").length + 1;
            $tr.attr("path", len);
            $tr.find("td[path=xh]").text(len);
            $tr.find("td[path=item]").text(data.dictName);
            $tr.find("td[path=guifan]").text(data.dictValue);
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPath($(y), data[$(y).attr("name")]);
            });
            if (len == 1) {
                $tr.find('.cselectorRadioUL').hide()
            } else {
                $tr.find('.look-file').hide()
            }

            wantSumArr.forEach(function (item) {
                if (item.text == data.dictName) {
                    if (isSumArr.indexOf(item.text) === -1) {
                        $tr.find("td[path=item]").attr('rowspan', item.num)
                        isSumArr.push(item.text)
                    } else {
                        $tr.find("td[path=item]").hide()
                    }
                }
            })

            $tr.appendTo($(".ctable-1").find("tbody"));
        };

        function appTr2(xh, data, i) {
            var $tr = $(".ctable-2").find("thead.trow tr").clone();
            $tr.find("textarea,input,select").each(function (x, y) {
                tdPath($(y), data[$(y).attr("name")]);
            });
            comboboxChange($tr, i);
            $tr.appendTo($(".ctable-2").find("tbody"));
        };

        window.getchoosedata = function () {
            var operationList = [];
            $("table.ctable-1 tbody").find("tr").each(function (x, y) {
                var listi = {};
                $(y).find("input,textarea").each(function (a, b) {
                    if ($(b).attr("name")) {
                        listi[b.name] = b.value || "";
                    }
                });
                operationList.push(listi);
            });

            var flag = true
            var arr = operationList.slice(1)
            for (var i = 0; i < arr.length; i++) {
                if (!arr[i].operationInfo) {
                    flag = false
                    break;
                }
            }
            if (!flag) {
                top.mesShow("温馨提示", "请选择是或否", 1000, 'red');
                return false
            }

            var itemInfoList = [];
            $("table.ctable-2 tbody").find("tr").each(function (x, y) {
                var listj = {};
                $(y).find("input,textarea").each(function (a, b) {
                    if ($(b).attr("name")) {
                        listj[b.name] = b.value || "";
                    }
                });
                if (listj.opinionAnnex) {
                    listj.opinionAnnex = JSON.parse(listj.opinionAnnex)
                }
                itemInfoList.push(listj);
            });

            ajaxgeneral({
                url: 'action/usLhgzhdInfo/insertInfo',
                data: {
                    "id": '',
                    "operationList": operationList,
                    "itemInfoList": itemInfoList,
                    "gridName": $('#gridName').val()
                },
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    top.mesShow("温馨提示", "新增成功", 1000, 'red');
                }
            });

            return { 'state': 1 };
        };

        $(document).on('click', '.look-file', function () {
            $.messager.defaults = {ok: '已阅知'}
            var dlg = $.messager.alert('装维人员安全工作须知',
                '<div><span style="color: #dd2f17;">关键场景一：</span><span style="font-weight: bold;">登高。关键字：防护、牢靠</span></div>' +
                '1.防  护。戴好安全帽、系好帽子带、系紧安全带、挂好安全绳、穿好绝缘鞋、挎好工具包、查看梯子垫；<br />' +
                '2.牢  靠。检查脚爬胶皮、查看梯子强度、扶梯人员到位。' +
                '<div><span style="color: #dd2f17;">关键场景二：</span><span style="font-weight: bold;">涉电。关键字：防护、验电</span></div>' +
                '1.防  护。戴好安全帽、系好帽子带、检验告警器、穿好绝缘鞋、带好验电笔、判断高压电；<br />' +
                '2.验  电。高压安全距离不足拒绝作业！近电告警器告警必须查明原因！金属物和线缆带电必须消除强电后方可作业！' +
                '<div><span style="color: #dd2f17;">关键场景三：</span><span style="font-weight: bold;">交通。关键字：检查、超速、安全带</span></div>' +
                '1.检  查。看轮胎、查车灯、试刹车、验方向盘。<br />' +
                '2.安全带。乘车人员扎好安全带，电动车必须戴头盔。<br />' +
                '3.超  速。道路不超速、路口勤观察、村镇慢通行！' +
                '<div><span style="color: #dd2f17;">三类人员：</span><span style="font-weight: bold;">新入职、一线、乡镇员工是关键</span></div>' +
                '新入职和转岗人员没通过安全培训和安全考试不得单独作业！乡镇员工安全教育无盲点！维护、工程生产一线作业施工人员必须签订安全责任书、熟悉作业场景、办理雇主责任险、防护用品配置到位！特殊工种必须具备作业上岗证（登高、电工、驾驶）。',
                'none', function (r) {
                    if (r) {} else {}
            })
            dlg.window({
                width: 800
            })
            dlg.dialog('center');
        })


    </script>
    <style>
        textarea { white-space: normal !important; }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }
        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }
        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text { border: none; font-size: 13px; }
        .formTable td.lable { background-color: #ddf1fe; padding: 5px;  max-width: 100px; }
        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly { background-color: #fff; }
        /* input:read-only { background-color: #f7f7f7; } */

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] { right: 3px; top: -15px; }
        .cselectorImageUL input[type='file'] {display: inline-block;width: 60px !important;}
        textarea { line-height: 20px; letter-spacing: 1px; }
        .cselectorImageUL{width: 100%;}

        .dialog-button {
            text-align: center;
        }
        .dialog-button .l-btn {
            margin-left: 30px;
        }
        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }
        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .input-select-td .textbox {
            width: 100% !important;
        }

        .blockTitle {
            border-left: 5px solid #f79e40;
            padding: 5px 0px 5px 10px;
            margin: 10px 0px;
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
        }

        .radio-td .cselectorRadioUL {
            text-align: center;
        }

        .radio-td .cselectorRadioUL a {
            font-size: 17px;
            margin: 0 10px;
        }

        .tip-box {
            background: #eeeeee;
            padding: 10px;
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 10px;
        }

        .tip-title {
            font-weight: bold;
            color: #dd2f17;
        }

        .look-file {
            display: inline-block;
            width: 100%;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
        }

        .messager-window .messager-body tr td:first-child {
            width: 0 !important;
        }

        .messager-window .messager-body .messager-none {
            width: 0;
            height: 0;
            margin: 0;
        }
        .last-row {
            width: 100%;
            display: flex;
            margin-bottom: 40px;
        }

        .col-1 {
            width: 84px;
            border: 1px solid #e6e6e6;
            border-top: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .col-2 {
            width: 187px;
            border-bottom: 1px solid #e6e6e6;
            border-top: 1px solid #e6e6e6;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .col-3 {
            flex: 1;
        }

        .body_page {
            padding: 15px 0 0;
        }

        .ctable-2 .combo {
            width: 100% !important;
        }

        .opinionType-type .validatebox-text {
            width: 90% !important;
        }
    </style>
</head>

<body class="body_page">
    <form id="glyTableAddForm">
        <table border="0" cellpadding="0" cellspacing="10" class="formTable">
            <tr>
                <td colspan="6" style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding:
                    10px;">
                    联合跟装活动台账
                </td>
            </tr>
            <tr>
                <td width="10%" class="lable">
                    装维人姓名
                </td>
                <td width="23%">
                    <input id="truename" name="truename" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    联系电话
                </td>
                <td width="23%">
                    <input id="phone" name="phone" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    归属单位
                </td>
                <td width="23%">
                    <input id="belongCompanyName" name="belongCompanyName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
            </tr>
            <tr>
                <td width="10%" class="lable">
                    归属本部门
                </td>
                <td width="23%">
                    <input id="belongDepartmentName" name="belongDepartmentName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    归属网格
                </td>
                <td width="23%">
                    <input id="gridName" name="gridName" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
                <td width="10%" class="lable">
                    上报时间
                </td>
                <td width="23%">
                    <input id="createdTime" name="createdTime" type="text" class="easyui-validatebox" readonly="readonly" />
                </td>
            </tr>
        </table>
        <div class="block" style="width: 100%;">
            <span class="blockTitle">省专协同任务2-联合跟装活动</span>
            <div class="tip-box">
                <div>
                    <span class="tip-title">七务必：</span>
                    接单务必及时预约、上门务必准时到达、着装务必规范统一、进门务必自我介绍、激活务必光功达标、完工务必测速展示、离开务必清理现场。
                </div>
                <div>
                    <span class="tip-title">八不准：</span>
                    不准虚假预约/改约、不准提前结束工单、不准服务态度恶劣、不准违反施工标准、不准强行推销终端、不准资源缺失作假、不准虚假工程挂测、不准验收数行了事。
                </div>
                <div>
                    <span class="tip-title">上门十步法：</span>
                    上门之前需预约、入室之后需沟通、现场实施需谨慎、综合布线需规范、安全规范需道守、实施完毕需认证、准确无误建档案、指导用户很关键、结束莫忘再提醒、清理现场显素质。
                </div>
            </div>
            <table border="0" cellpadding="0" cellspacing="0" class="ctable ctable-1 w100">
                <thead>
                <tr>
                    <td align="center" width="2">序号</td>
                    <td align="center" width="100">项目</td>
                    <td align="center" width="400">规范</td>
                    <td align="center" width="100">操作</td>
                </tr>
                </thead>
                <thead class="trow hide">
                <tr path="0">
                    <input id="dictId" name="dictId" type="hidden"/>
                    <input id="dictValue" name="dictValue" type="hidden"/>
                    <input id="dictName" name="dictName" type="hidden"/>
                    <td align="center" width="2" path="xh"></td>
                    <td align="center" width="100" style="border-left: 1px solid #e6e6e6;" path="item"></td>
                    <td align="center" width="400" style="border-left: 1px solid #e6e6e6;border-right: 1px solid
                    #e6e6e6;white-space: normal;" path="guifan"></td>
                    <td class="radio-td" width="100" style="border-left: 1px solid #e6e6e6;">
                        <input id="operationInfo" name="operationInfo" classpath="cselectorRadio" required="required"
                               values="1|是,2|否" style="height: 32px;"/>
                        <span class="look-file">查看(内容附件)</span>
                    </td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="last-row">
                <div class="col-1"></div>
                <div class="col-2">意见建议</div>
                <div class="col-3">
                    <table border="0" cellpadding="0" cellspacing="0" class="ctable ctable-2 w100" style="border-left: 1px solid #e6e6e6;">
                        <thead>
                        <tr>
                            <td align="center" width="150">类别</td>
                            <td align="center" width="400">附件</td>
                            <td align="center" width="300">填报</td>
                            <td align="center" width="15" class="add-handle">操作</td>
                        </tr>
                        </thead>
                        <thead class="trow hide">
                        <tr path="0">
                            <td width="150" style="border-left: 1px solid #e6e6e6;" class="opinionType-type">
                                <input name="opinionType" classpath="validatebox opinionType" style="height:
                                32px;"/>
                            </td>
                            <td width="400" style="border-left: 1px solid #e6e6e6;border-right: 1px solid #e6e6e6;">
                                <input name="opinionAnnex" type="text" file="true" classpath="cselectorImageUpload"
                                       mulaccept="true" btnmsg="<i class='iconfont' title='附件上传'>&#xe641;</i>"
                                       href="sys/file/uploadProcessFiles" />
                            </td>
                            <td width="300" style="border-left: 1px solid #e6e6e6;">
                                <textarea name="opinionRemark" classpath="validatebox"></textarea>
                            </td>
                            <td align="center" width="15" class="add-handle">
                                <div class='a_add_btn col_r divBtn' style="cursor: pointer;">
                                    <span style="color: #00b4f1;">新增</span>
                                </div>
                                <div class='a_del_btn col_r divBtn' style="cursor: pointer;">
                                    <span style="color: #dd2f17;">删除</span>
                                </div>
                            </td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</body>

</html>