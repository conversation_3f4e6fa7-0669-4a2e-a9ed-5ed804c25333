package com.simbest.boot.djfupt.assiatant.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_History_Record")
@ApiModel(value = "智能助手-历史记录查询")
public class UsHistoryRecord extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 200)
    @ApiModelProperty(value = "pmInsId")
    private String pmInsId;

    @Column(length = 200)
    @ApiModelProperty(value = "类型")
    private String type; //1问  0答

    @Column(columnDefinition = "CLOB")
    @ApiModelProperty(value = "文本")
    private String textContent;

    @Column(columnDefinition = "CLOB")
    @ApiModelProperty(value = "分词信息")
    private String info;

    @Column(length = 200)
    @ApiModelProperty(value = "userName")
    private String userName;

    @Column(length = 200)
    @ApiModelProperty(value = "trueName")
    private String trueName;
}

