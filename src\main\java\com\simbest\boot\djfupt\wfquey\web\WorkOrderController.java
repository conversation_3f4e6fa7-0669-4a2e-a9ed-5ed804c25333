package com.simbest.boot.djfupt.wfquey.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.wfquey.service.IWorkOrderService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.xml.bind.util.JAXBSource;

@Api(description = "查询待办,已办,待阅,已阅,wide申请相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/workOrder")
public class WorkOrderController {

    @Autowired
    private IWorkOrderService workOrderService;

    /**
     *工单查询
     * @return
     */
    @PostMapping(value = {"/queryWorkOrder", "/api/queryWorkOrder","/queryWorkOrder/sso/","/anonymous/queryWorkOrder"})
    public JsonResponse queryWorkOrder(@RequestParam Integer page,
                                       @RequestParam Integer rows){
        return  workOrderService.queryWorkOrder(page,rows);
    }
}
