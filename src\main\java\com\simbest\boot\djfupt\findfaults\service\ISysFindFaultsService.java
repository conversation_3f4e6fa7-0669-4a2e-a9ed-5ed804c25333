package com.simbest.boot.djfupt.findfaults.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ISysFindFaultsService extends ILogicService<UsFindFaultsModel,String> {


    JsonResponse saveDraft(String source, String currentUserCode, UsFindFaultsModel usFindFaultsModel);


    JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId);

    JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String currentUserCode, String pmInsId, String location);

    JsonResponse queryAllFaultsInfo(Map<String, String> map, Integer page, Integer size, String faultsType);

    void exportParameter(String companyName, String startDate, String endDate, String appName, String gridName, HttpServletRequest request, HttpServletResponse response, String faultsType);

    JsonResponse queryAllFaultsCountInfo(Map<String, String> map, Integer page, Integer size, String faultsType);

    void exportAllFaultsCountInfo(String companyName, String startDate, String endDate, HttpServletRequest request, HttpServletResponse response, String faultsType);

    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsFindFaultsModel usFindFaultsModel);

    JsonResponse getFormDetailByPmInsId(String pmInsId);

    JsonResponse getUserGridName(String username);
}
