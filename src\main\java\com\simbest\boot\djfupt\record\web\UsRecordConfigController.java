package com.simbest.boot.djfupt.record.web;


import cn.hutool.core.util.ObjectUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;
import com.simbest.boot.djfupt.record.service.IUsRecordConfigService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 思政纪实配置相关接口
 */
@Api(description = "思政纪实配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/usRecordConfig")
public class UsRecordConfigController extends LogicController<UsRecordConfig, String> {

    private IUsRecordConfigService usRecordConfigService;

    @Autowired
    public UsRecordConfigController(IUsRecordConfigService usRecordConfigService) {
        super(usRecordConfigService);
        this.usRecordConfigService = usRecordConfigService;
    }

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private PaginationHelps paginationHelp;


    /**
     * 思政工作纪实列表信息
     */
    @ApiOperation(value = "思政工作纪实列表信息", notes = "思政工作纪实列表信息")
    @PostMapping(value = {"/findAllInfo", "/api/findAllInfo", "/findAllInfo/sso"})
    public JsonResponse findAllInfo(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                    @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                    @RequestParam(required = false) String direction, //排序规则（asc/desc）
                                    @RequestParam(required = false) String properties, //排序规则（属性名称）
                                    @RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usRecordConfigService.findAllInfo(resultMap);
        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "暂不支持改流程类型");

    }


    /**
     * 新增思政工作纪实
     */
    @ApiOperation(value = "新增思政工作纪实", notes = "新增思政工作纪实")
    @PostMapping(value = {"/insertUsRecordConfig", "/api/insertUsRecordConfig", "/insertUsRecordConfig/sso"})
    public JsonResponse insertUsRecordConfig(@RequestParam(required = false) String source,
                                             @RequestParam(required = false) String currentUserCode,
                                             @RequestBody UsRecordConfig usRecordConfig) {
        UsRecordConfig newUsRecordConfig = null;
        if (StringUtils.isNotEmpty(source)&&source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        if (ObjectUtil.isNotEmpty(usRecordConfig)) {
            newUsRecordConfig = usRecordConfigService.insert(usRecordConfig);
        }
        if (ObjectUtil.isNotEmpty(newUsRecordConfig)) {
            return JsonResponse.success("添加成功");
        } else {
            return JsonResponse.success("添加失败");
        }
    }

    /**
     * 删除思政工作纪实
     */
    @ApiOperation(value = "删除思政工作纪实", notes = "删除思政工作纪实")
    @PostMapping(value = {"/deleteUsRecordConfig", "/api/deleteUsRecordConfig", "/deleteUsRecordConfig/sso"})
    public JsonResponse deleteUsRecordConfig(@RequestParam(required = false) String source,
                                             @RequestBody UsRecordConfig usRecordConfig) {
        if (StringUtils.isNotEmpty(usRecordConfig.getId())) {
            usRecordConfigService.deleteById(usRecordConfig.getId());
        }
        return JsonResponse.defaultSuccessResponse();
    }


    /**
     * 导出模板
     */
    @GetMapping(value = {"/downloadTemplate", "/api/downloadTemplate", "/downloadTemplate/sso", "/anonymous/downloadTemplate"})
    public JsonResponse downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        usRecordConfigService.downloadTemplate(request, response);
        return JsonResponse.defaultSuccessResponse();
    }





    @ApiOperation(value = "导入思政数据", notes = "导入思政数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pmInsId", value = "主单据编码", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "workNumber", value = "订单编码", dataType = "string", paramType = "query"),
    })

    @PostMapping(value = {"/importInfo", "/api/importInfo", "/importInfo/sso"})
    public void importInfo(HttpServletRequest request, HttpServletResponse response) {
        usRecordConfigService.importInfo(request, response);
    }
}


