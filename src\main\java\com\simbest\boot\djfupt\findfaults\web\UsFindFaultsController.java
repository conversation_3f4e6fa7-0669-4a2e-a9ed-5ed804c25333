package com.simbest.boot.djfupt.findfaults.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.findfaults.model.UsAppNameModel;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsModel;
import com.simbest.boot.djfupt.findfaults.service.ISysAppNameService;
import com.simbest.boot.djfupt.findfaults.service.ISysFindFaultsService;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Api(description = "找茬上报流程")
@Slf4j
@RestController
@RequestMapping(value = "/action/findFaults")
public class UsFindFaultsController extends LogicController<UsFindFaultsModel, String> {

    private ISysFindFaultsService service;

    @Autowired
    public UsFindFaultsController(ISysFindFaultsService service) {
        super(service);
        this.service = service;
    }

    String param1 = "/action/findFaults";

    @Autowired
    private ICommonService iCommonService;


    @Autowired
    private ISysAppNameService appNameService;

    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/api/saveDraft", "/saveDraft/sso"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody UsFindFaultsModel usFindFaultsModel) {
        return service.saveDraft(source, currentUserCode, usFindFaultsModel);
    }

    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param notificationId  待阅id
     * @param bodyParam       提交参数
     * @param formId          表单id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "nextUserName", value = "审批人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "copyLocation", value = "抄送下一环节", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyMessage", value = "抄送意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyNextUserNames", value = "抄送人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
    })
    @PostMapping(value = {"/startSubmitProcess", "/api/startSubmitProcess", "/startSubmitProcess/sso", "/anonymous/startSubmitProcess"})
    public JsonResponse startSubmitProcess(@RequestParam String currentUserCode,
                                           @RequestParam String source,
                                           @RequestParam(required = false) String workItemId,
                                           @RequestParam(required = false) String outcome,
                                           @RequestParam(required = false) String location,
                                           @RequestParam(required = false) String copyLocation,
                                           @RequestParam(required = false) String notificationId,
                                           @RequestParam(required = false) String formId,
                                           @RequestBody Map<String, Object> bodyParam) throws Exception {
        return service.startSubmitProcess(source, currentUserCode, workItemId, outcome, location, copyLocation, bodyParam, formId, notificationId);
    }

    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation(value = "查询决策", notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getDecisions", "/api/getDecisions", "/getDecisions/sso", "/anonymous/getDecisions"})
    public JsonResponse getDecisions(
            @RequestParam(required = false) String processInstId,
            @RequestParam(required = false) String processDefName,
            @RequestParam String location,
            @RequestParam String source,
            @RequestParam(required = false) String currentUserCode,
            @RequestParam String processType,
            @RequestParam(required = false) String questionMode) {

        return iCommonService.getDecisions(processInstId, processDefName, location, source, currentUserCode, processType, param1, questionMode);
    }

    /**
     * 获取到决策下组织人员
     *
     * @param processInstId 流程实例id
     * @param appDecision   决策对象
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getOrgAndUser", "/api/getOrgAndUser", "/getOrgAndUser/sso"})
    public JsonResponse getOrgAndUser(@RequestParam String source,
                                      @RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String faultsAppName,
                                      @RequestBody SimpleAppDecision appDecision) {
        return iCommonService.getOrgAndUser(processInstId, source, currentUserCode, appDecision, param1,faultsAppName);
    }

    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "userCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getFormDetail", "/api/getFormDetail", "/getFormDetail/sso", "/anonymous/getFormDetail"})
    public JsonResponse getFormDetail(@RequestParam(required = false) Long processInstId,
                                      @RequestParam(required = false) String workFlag,
                                      @RequestParam(required = false) String source,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String pmInsId,
                                      @RequestParam(required = false) String currentUserCode) {
        return service.getFormDetail(processInstId, workFlag, source, currentUserCode, pmInsId, location);
    }



    /**
     * @description 查詢所有的系统名称
     * <AUTHOR> enlai
     * @time
     */
    @PostMapping(value = {"/getAllAppName", "/api/getAllAppName", "/getAllAppName/sso", "/anonymous/getAllAppName"})
    public JsonResponse findAllAppName(@RequestParam(required = false,defaultValue = "1") Integer page,
                                      @RequestParam(required = false,defaultValue = "10") Integer size,
                                      @RequestBody Map<String,String> map) {
        return appNameService.findAllAppName(page,size,map);
    }

    @PostMapping(value = {"/getAllAppNameNoPage", "/api/getAllAppNameNoPage", "/getAllAppNameNoPage/sso", "/anonymous/getAllAppNameNoPage"})
    public JsonResponse getAllAppNameNoPage() {
        return appNameService.getAllAppNameNoPage();
    }

    /**
     * @description 变更系统名称
     * <AUTHOR> enlai
     * @time
     */
    @PostMapping(value = {"/updateAppName", "/api/updateAppName", "/updateAppName/sso", "/anonymous/updateAppName"})
    public JsonResponse updateAppName(@RequestBody UsAppNameModel appNameModel) {
        UsAppNameModel update = appNameService.update(appNameModel);
        return JsonResponse.success(update,"修改成功");
    }

    /**
     * @description 删除系统名称
     * <AUTHOR> enlai
     * @time
     */
    @PostMapping(value = {"/deleteAppName", "/api/deleteAppName", "/deleteAppName/sso", "/anonymous/deleteAppName"})
    public JsonResponse deleteAppName(@RequestParam String id) {
        appNameService.deleteById(id);
        return JsonResponse.success("删除成功");
    }

    /**
     * @description 新增系统名称
     * <AUTHOR> enlai
     * @time
     */
    @PostMapping(value = {"/createAppName", "/api/createAppName", "/createAppName/sso", "/anonymous/createAppName"})
    public JsonResponse createAppName(@RequestBody UsAppNameModel appNameModel) {

        IUser currentUser = SecurityUtils.getCurrentUser();
        appNameModel.setCreator(currentUser.getUsername());
        appNameModel.setModifier(currentUser.getUsername());
        appNameModel.setCreateTruename(currentUser.getTruename());
        appNameModel.setCreateUsername(currentUser.getUsername());
        appNameModel.setEnabled(Boolean.TRUE);
        UsAppNameModel insert = appNameService.insert(appNameModel);
        return JsonResponse.success(insert,"新增成功");
    }

    @PostMapping(value = {"/queryAllFaultsInfo", "/api/queryAllFaultsInfo", "/queryAllFaultsInfo/sso", "/anonymous/queryAllFaultsInfo"})
    public JsonResponse queryAllFaultsInfo(@RequestParam(required = false,defaultValue = "1") Integer page,
                                           @RequestParam(required = false,defaultValue = "10") Integer size,
                                           @RequestParam(required = false) String faultsType,
                                           @RequestBody Map<String,String> map) {
        return service.queryAllFaultsInfo(map,page,size,faultsType);
    }

    @PostMapping(value = {"/exportParameter", "/api/exportParameter", "/sso/exportParameter"})
    public void exportParameter( HttpServletRequest request, HttpServletResponse response,
                                 @RequestParam(required = false) String companyName,
                                 @RequestParam(required = false) String startDate,
                                 @RequestParam(required = false) String endDate,
                                 @RequestParam(required = false) String appName,
                                 @RequestParam(required = false) String gridName,
                                 @RequestParam(required = false) String faultsType) {
        service.exportParameter(companyName,startDate,endDate,appName,gridName, request, response,faultsType);
    }

    @PostMapping(value = {"/exportAllFaultsCountInfo", "/api/exportAllFaultsCountInfo", "/sso/exportAllFaultsCountInfo"})
    public void exportAllFaultsCountInfo(HttpServletRequest request, HttpServletResponse response,
                                         @RequestParam(required = false) String companyName,
                                         @RequestParam(required = false) String startDate,
                                         @RequestParam(required = false) String endDate,
                                         @RequestParam(required = false) String faultsType) {
        service.exportAllFaultsCountInfo(companyName,startDate,endDate, request, response,faultsType);
    }

    @PostMapping(value = {"/queryAllFaultsCountInfo", "/api/queryAllFaultsCountInfo", "/queryAllFaultsCountInfo/sso", "/anonymous/queryAllFaultsCountInfo"})
    public JsonResponse queryAllFaultsCountInfo(@RequestParam(required = false,defaultValue = "1") Integer page,
                                           @RequestParam(required = false,defaultValue = "18") Integer size,
                                           @RequestParam(required = false) String faultsType,
                                           @RequestBody Map<String,String> map) {
        return service.queryAllFaultsCountInfo(map,page,size,faultsType);
    }

    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/api/deleteDraft", "/deleteDraft/sso"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody UsFindFaultsModel usFindFaultsModel) {
        return service.deleteDraft(source, currentUserCode, pmInsId, usFindFaultsModel);
    }

    @PostMapping(value = {"/getFormDetailByPmInsId", "/api/getFormDetailByPmInsId", "/getFormDetailByPmInsId/sso", "/anonymous/getFormDetailByPmInsId"})
    public JsonResponse getFormDetailByPmInsId(@RequestParam String pmInsId) {
        return service.getFormDetailByPmInsId(pmInsId);
    }

    @PostMapping(value = {"/getUserGridName", "/api/getUserGridName", "/getUserGridName/sso", "/anonymous/getUserGridName"})
    public JsonResponse getUserGridName(@RequestParam String username) {
        return service.getUserGridName(username);
    }
}
