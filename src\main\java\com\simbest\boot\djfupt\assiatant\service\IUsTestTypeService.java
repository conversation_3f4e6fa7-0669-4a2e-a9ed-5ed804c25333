package com.simbest.boot.djfupt.assiatant.service;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.assiatant.model.UsTestType;

import java.util.List;
import java.util.Map;

public interface IUsTestTypeService extends ILogicService<UsTestType, String> {

    JsonResponse send(Map<String,Object> paramMap);

    JsonResponse test01(String content);
}
