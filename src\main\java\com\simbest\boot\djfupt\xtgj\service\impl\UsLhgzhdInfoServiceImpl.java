package com.simbest.boot.djfupt.xtgj.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.djfupt.attachment.service.impl.FileExtendServiceImpl;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.FileTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.xtgj.model.UsLhgzhdInfo;
import com.simbest.boot.djfupt.xtgj.model.XtgjDictValueVO;
import com.simbest.boot.djfupt.xtgj.repository.UsLhgzhdInfoRepository;
import com.simbest.boot.djfupt.xtgj.service.IUsLhgzhdInfoService;
import com.simbest.boot.security.IRole;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class UsLhgzhdInfoServiceImpl extends LogicService<UsLhgzhdInfo, String> implements IUsLhgzhdInfoService {

    private final FileExtendServiceImpl fileExtendServiceImpl;
    private final SysDictValueService sysDictValueService;
    private final UsLhgzhdItemInfoServiceImpl usLhgzhdItemInfoServiceImpl;
    private UsLhgzhdInfoRepository usLhgzhdInfoRepository;

    public UsLhgzhdInfoServiceImpl(UsLhgzhdInfoRepository usLhgzhdInfoRepository, FileExtendServiceImpl fileExtendServiceImpl, SysDictValueService sysDictValueService, UsLhgzhdItemInfoServiceImpl usLhgzhdItemInfoServiceImpl) {
        super(usLhgzhdInfoRepository);
        this.usLhgzhdInfoRepository = usLhgzhdInfoRepository;
        this.fileExtendServiceImpl = fileExtendServiceImpl;
        this.sysDictValueService = sysDictValueService;
        this.usLhgzhdItemInfoServiceImpl = usLhgzhdItemInfoServiceImpl;
    }

    @Value("${app.file.upload.path}")
    private String UPLOAD_PATH;

    @Override
    public UsLhgzhdInfo insertInfo(UsLhgzhdInfo o) {
        IUser user = SecurityUtils.getCurrentUser();
        o.setUsername(user.getUsername());
        o.setTruename(user.getTruename());
        o.setPhone(user.getPreferredMobile());
        BelongInfoTool.setBelongCompanyAndDepartment(o);

        o.setOperationInfo(this.list2StringByOperationInfo(o.getOperationList()));

        // 保存意见
        UsLhgzhdInfo insert = super.insert(o);
        usLhgzhdItemInfoServiceImpl.deleteAndAddInfo(insert.getId(), o.getItemInfoList());

        return insert;
    }

    @Override
    public UsLhgzhdInfo updateInfo(UsLhgzhdInfo o) {
        IUser user = SecurityUtils.getCurrentUser();
        o.setUsername(user.getUsername());
        o.setTruename(user.getTruename());
        o.setPhone(user.getPreferredMobile());
        BelongInfoTool.setBelongCompanyAndDepartment(o);

        o.setOperationInfo(this.list2StringByOperationInfo(o.getOperationList()));

        // 保存意见
        usLhgzhdItemInfoServiceImpl.deleteAndAddInfo(o.getId(), o.getItemInfoList());

        return super.update(o);
    }

    @Override
    public UsLhgzhdInfo findByIdInfo(String id) {
        UsLhgzhdInfo byId = super.findById(id);

        byId.setOperationList(this.string2List(byId.getOperationInfo()));

        byId.setItemInfoList(usLhgzhdItemInfoServiceImpl.findAllByPid(byId.getId()));

        return byId;
    }

    /**
     * 通用查询列表
     */
    public List<UsLhgzhdInfo> findAllInfoList(UsLhgzhdInfo o) {
        o = Optional.ofNullable(o).orElse(new UsLhgzhdInfo());

        // 获取查询条件
        Specification<UsLhgzhdInfo> and = Specifications.<UsLhgzhdInfo>and()
                .eq(StringUtils.isNotBlank(o.getBelongCompanyCode()), "belongCompanyCode", o.getBelongCompanyCode())
                .eq(StringUtils.isNotBlank(o.getBelongDepartmentCode()), "belongDepartmentCode", o.getBelongDepartmentCode())
                .eq(StringUtils.isNotBlank(o.getBelongOrgCode()), "belongOrgCode", o.getBelongOrgCode())
                .like(StringUtils.isNotBlank(o.getGridName()), "gridName", String.format("%%%s%%", o.getGridName()))
                .like(StringUtils.isNotBlank(o.getBelongOrgName()), "belongOrgName", String.format("%%%s%%", o.getBelongOrgName()))
                .like(StringUtils.isNotBlank(o.getTruename()), "truename", String.format("%%%s%%", o.getTruename()))
                .ge(Objects.nonNull(o.getSdate()), "createdTime", o.getSdate())
                .le(Objects.nonNull(o.getEdate()), "createdTime", o.getEdate())
                .build();
        IUser user = SecurityUtils.getCurrentUser();
        Set<? extends IRole> roles = user.getAuthRoles();
        // 省公司党建管理员
        if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_PRO) || Objects.equals(v.getId(), Constants.FJFUPT_PRO))) {
        } else if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_City) || Objects.equals(v.getId(), Constants.FJFUPT_City))) {
            // 分公司党建管理员
            and = Specifications.<UsLhgzhdInfo>and()
                    .eq("belongCompanyCode", user.getBelongCompanyCode()).build()
                    .and(and);
        } else if (roles.stream().anyMatch(v -> Objects.equals(v.getRoleCode(), Constants.FJFUPT_BRO) || Objects.equals(v.getId(), Constants.FJFUPT_BRO))) {
            // 党建管理员
            and = Specifications.<UsLhgzhdInfo>and()
                    .eq("username", user.getUsername()).build()
                    .and(and);
        }

        return super.findAllNoPage(and, Sort.by(Sort.Direction.DESC, "createdTime"));
    }

    /**
     * 信息查询 通用分页
     * <br/>params [o, pageable]
     */
    @Override
    public Page<UsLhgzhdInfo> findAllInfo(UsLhgzhdInfo o, Pageable pageable) {
        List<UsLhgzhdInfo> list = this.findAllInfoList(o);

        return PageTool.getPage(list, pageable);
    }

    /**
     * 信息导出
     * <br/>params [o, pageable]
     */
    @Override
    public void exportInfo(HttpServletRequest request, HttpServletResponse response, UsLhgzhdInfo o) {
        List<UsLhgzhdInfo> list = this.findAllInfoList(o);
        if (CollectionUtils.isEmpty(list)) return;

        String modelPath = "model/联合跟装台活动账导出模版.xls";
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(modelPath);
             Workbook workbook = new HSSFWorkbook(in);) {
            Sheet sheet = workbook.getSheetAt(0);

            //设置数据列数据格式
            CellStyle dataStyle = getCellStyle(workbook);
            List<SysDictValue> dictValues = sysDictValueService.findByDictType(Constants.XTGJ_LHGZHD01_TYPE);
            // 相关数据写入
            int rowNum = 3;
            int maxColNum = 0;
            for (UsLhgzhdInfo info : list) {
                Row row = CellUtil.getRow(rowNum++, sheet);

                row.createCell(0).setCellValue(info.getBelongCompanyName());
                row.createCell(1).setCellValue(info.getBelongDepartmentName());
                row.createCell(2).setCellValue(info.getTruename());
                row.createCell(3).setCellValue(info.getPhone());
                row.createCell(4).setCellValue(info.getGridName());
                row.createCell(5).setCellValue(info.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                info.setOperationList(this.string2List(info.getOperationInfo()));

                row.createCell(6).setCellValue(info.getOperationList().get(0).getOperationInfo());
                row.createCell(7).setCellValue(info.getOperationList().get(1).getOperationInfo());
                row.createCell(8).setCellValue(info.getOperationList().get(2).getOperationInfo());
                row.createCell(9).setCellValue(info.getOperationList().get(3).getOperationInfo());
                row.createCell(10).setCellValue(info.getOperationList().get(4).getOperationInfo());
                row.createCell(11).setCellValue(info.getOperationList().get(5).getOperationInfo());
                row.createCell(12).setCellValue(info.getOperationList().get(6).getOperationInfo());
                row.createCell(13).setCellValue(info.getOperationList().get(7).getOperationInfo());
                row.createCell(14).setCellValue(info.getOperationList().get(8).getOperationInfo());
                row.createCell(15).setCellValue(info.getOperationList().get(9).getOperationInfo());
                row.createCell(16).setCellValue(info.getOperationList().get(10).getOperationInfo());
                row.createCell(17).setCellValue(info.getOperationList().get(11).getOperationInfo());
                row.createCell(18).setCellValue(info.getOperationList().get(12).getOperationInfo());
                row.createCell(19).setCellValue(info.getOperationList().get(13).getOperationInfo());
                row.createCell(20).setCellValue(info.getOperationList().get(14).getOperationInfo());
                row.createCell(21).setCellValue(info.getOperationList().get(15).getOperationInfo());

                // 意见
                info.setItemInfoList(usLhgzhdItemInfoServiceImpl.findAllByPid(info.getId()));
                AtomicInteger num = new AtomicInteger(22);
                info.getItemInfoList().forEach(v -> {
                    String type = dictValues.stream().filter(d -> d.getValue().equals(v.getOpinionType())).findFirst()
                            .map(SysDictValue::getName).orElse("");
                    String remark = v.getOpinionRemark();
                    row.createCell(num.getAndIncrement()).setCellValue(type);
                    row.createCell(num.getAndIncrement()).setCellValue(remark);
                });

                maxColNum = Math.max(num.get(), maxColNum);

                // 处理样式
                row.setHeightInPoints(60f);
                IntStream.range(0, num.get()).forEach(v -> Optional.ofNullable(row.getCell(v)).ifPresent(op -> op.setCellStyle(dataStyle)));
            }

            // 处理表头
            if (maxColNum > 24) {
                mergedRegions(0, 0, 6, maxColNum - 1, sheet);
                mergedRegions(1, 1, 22, maxColNum - 1, sheet);

                Row tableRow = CellUtil.getRow(2, sheet);
                String type = tableRow.getCell(22).getStringCellValue();
                String remark = tableRow.getCell(23).getStringCellValue();
                for (int v = 24; v < maxColNum; v += 2) {
                    tableRow.createCell(v).setCellValue(type);
                    tableRow.createCell(v + 1).setCellValue(remark);
                }

                // 配置黑线样式
                for (int i = 0; i < list.size(); i++) {
                    Row row = sheet.getRow(i);
                    for (int j = 0; j < maxColNum; j++) {
                        Cell cell = CellUtil.getCell(row, j);
                        CellStyle blackCellStyle = cell.getCellStyle();
//                        blackCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SKY_BLUE.getIndex());
//                        blackCellStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.GREY_40_PERCENT.getIndex());
                        blackCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
                        blackCellStyle.setBorderLeft(BorderStyle.THIN);//左边框
                        blackCellStyle.setBorderTop(BorderStyle.THIN);//上边框
                        blackCellStyle.setBorderRight(BorderStyle.THIN);//右边框

                        // 设置边框颜色为黑色
                        blackCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                        blackCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                        blackCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                        blackCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

                        cell.setCellStyle(blackCellStyle);
                    }
                }
            }

            // # 输出到新文件，保持模板不变
            // 临时文件目录
            String dataDir = UPLOAD_PATH + "/export/";
            String filename = String.format("%s_%s.xls", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE), "联合跟装活动台账导出信息");
            File file = new File(String.format("%s\\%s", dataDir, filename));
            FileUtils.touch(file);
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
            }
            FileTool.download(file.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 数据转换
     */
    private String list2StringByOperationInfo(List<XtgjDictValueVO> list) {
        return list.stream().map(v -> String.format("%s%s%s", v.getDictId(), Constants.XTGJ_CONNECT_SIGN, v.getOperationInfo()))
                .collect(Collectors.joining(Constants.XTGJ_SEPARATE_SIGN));
    }

    /**
     * 数据转换
     */
    private List<XtgjDictValueVO> string2List(String operationInfo) {
        List<XtgjDictValueVO> collect = Arrays.stream(operationInfo.split(Constants.XTGJ_SEPARATE_SIGN)).map(v -> {
            String[] split = v.split(Constants.XTGJ_CONNECT_SIGN);
            XtgjDictValueVO vo = new XtgjDictValueVO();
            vo.setDictId(split[0]);
            vo.setOperationInfo(split[1]);
            return vo;
        }).collect(Collectors.toList());

        List<SysDictValue> dictValues = sysDictValueService.findByDictType(Constants.XTGJ_LHGZHD_TYPE);
        collect.forEach(v1 -> {
            dictValues.forEach(v2 -> {
                if (v1.getDictId().equals(v2.getId())) {
                    v1.setDictName(v2.getName());
                    v1.setDictValue(v2.getValue());
                }
            });
        });
        return collect;
    }

    /**
     * 获取导出单元格样式
     *
     * @param workbook 工作薄
     * @return 单元格样式
     */
    public static CellStyle getCellStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SKY_BLUE.getIndex());
        dataStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.GREY_40_PERCENT.getIndex());
        dataStyle.setBorderBottom(BorderStyle.THIN); //下边框
        dataStyle.setBorderLeft(BorderStyle.THIN);//左边框
        dataStyle.setBorderTop(BorderStyle.THIN);//上边框
        dataStyle.setBorderRight(BorderStyle.THIN);//右边框
        dataStyle.setAlignment(HorizontalAlignment.CENTER); // 左右居中
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        dataStyle.setWrapText(true); // 自动换行
        Font dataFont = workbook.createFont();
        dataFont.setFontName("华文中宋");
        dataFont.setFontHeightInPoints((short) 10); //字体大小
        dataStyle.setFont(dataFont);
        return dataStyle;
    }

    /**
     * 合并单元格操作
     *
     * @param firstRow 起始行
     * @param lastRow  结束行
     * @param firstCol 起始列
     * @param lastCol  结束列
     * @param sheet    sheet
     */
    private static void mergedRegions(int firstRow, int lastRow, int firstCol, int lastCol, Sheet sheet) {
        if (firstCol >= lastCol) return;

        CellRangeAddress cellAddresses = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        // 消除合并单元格
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
        for (int i = 0; i < mergedRegions.size(); i++) {
            CellRangeAddress v = mergedRegions.get(i);
            if (v.intersects(cellAddresses)) {
                sheet.removeMergedRegion(i);
                log.debug("移除合并单元格：{}", v);
            }
        }
        sheet.addMergedRegion(cellAddresses);
        log.debug("添加合并单元格：{}", cellAddresses);
    }

}
