package com.simbest.boot.djfupt.record.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.record.model.UsTalkContent;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.beans.Transient;

public interface UsTalkContentRepository extends LogicRepository<UsTalkContent, String> {
    @Transient
    @Modifying
    @Query(
            value = " update Us_Talk_Content t set t.enabled = 0  where t.main_Id = :mainId ",
            nativeQuery = true
    )
    int deleteByMainId(@Param("mainId") String mainId);
}
