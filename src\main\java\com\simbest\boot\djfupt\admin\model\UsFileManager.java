package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_file_manager")
@ApiModel(value = "文件管理试题")
public class UsFileManager extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UFM")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 200)
    @ApiModelProperty(value = "文件标题")
    protected String title;

    @Column(length = 2000)
    @ApiModelProperty(value = "文件描述")
    protected String describe;

    @Column(length = 2000)
    @ApiModelProperty(value = "文件id,多个用英文逗号拼接")
    protected String fileIds;

    @Transient
    List<SysFile> sysFileList;
}
