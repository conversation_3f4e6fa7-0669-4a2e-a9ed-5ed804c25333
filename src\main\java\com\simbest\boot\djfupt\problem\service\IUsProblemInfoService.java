package com.simbest.boot.djfupt.problem.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IUsProblemInfoService extends ILogicService<UsProblemInfo, String> {

    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param copyLocation    抄送环节
     * @param bodyParam       提交审批参数
     * @param formId          表单id
     * @param notificationId  待阅id
     * @return
     */
    JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId);


    /**
     * 获取表单详情 (location 当前环节 workItemId 工作项id 暂无用到)
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @param location      当前环节
     * @return
     */
    JsonResponse getFormDetail(String processInstId, String workFlag, String source, String userCode, String pmInsId, String location,String type);


    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(String source, String currentUserCode, UsProblemInfo usProblemInfo);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, UsProblemInfo usProblemInfo);

    /**
     * 经验推广台账
     *
     * @param startTime
     * @param endTime
     * @param problemName
     * @param belongDepartmentCode
     * @return
     */
    JsonResponse queryApplication(Integer page, Integer rows, String source, String userCode, String startTime, String endTime, String problemName, String belongDepartmentCode,String state);


    void exportParameter(Integer page, Integer rows,
                         String source, String userCode, String startTime,
                         String endTime, String problemName, String belongDepartmentCode,String state,
                         HttpServletResponse response,
                         HttpServletRequest request);


    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 问题数量
     *
     * @return
     */
    List<Map<String, Object>> problemCount(Map<String, Object> resultMap);


    /**
     * 问题数量
     *
     * @return
     */
    List<Map<String, Object>> problemAllCount(Map<String, Object> resultMap);


    JsonResponse test();

    Page<List<Map<String, Object>>> conditionQuery(Integer page,
                                                   Integer rows,
                                                   String source,
                                                   String userCodeString,
                                                   String startTime,
                                                   String endTime,
                                                   String problemName,
                                                   String belongDepartmentCode,
                                                   String state
    );

    List<Map<String, Object>> conditionQuerys(Integer page,
                                              Integer rows,
                                              String source,
                                              String userCodeString,
                                              String startTime,
                                              String endTime,
                                              String problemName,
                                              String belongDepartmentCode,
                                              String state
    );

    /**
     * 问题上报数据统计
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    List<ProblemStatisticsVo> problemStatistics(int page, //当前页码
                                                int rows, //每页数量
                                                Map<String, Object> resultMap);


    void exportProblemStatistics(  Map<String, Object> resultMap,
                                  HttpServletResponse response,
                                  HttpServletRequest request);

    /**
     * 问题上报数据统计
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    List<ProblemStatisticsVo> problemStatisticsOther(int page, //当前页码
                                                int rows, //每页数量
                                                Map<String, Object> resultMap);



    JsonResponse updateProblemInfo( UsProblemInfo problemInfo );

    void updateFileByPmInsId(UsProblemInfo usProblemInfo, String pmInsType);


    JsonResponse getCurrentProblemInfo();
}
