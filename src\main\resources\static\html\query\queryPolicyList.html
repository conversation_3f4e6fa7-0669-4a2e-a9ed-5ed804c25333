<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>政策宣讲台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent();
        var myinfo = {};
        var abol = false  //纪检身份
        abolFun()
        $(function () {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var yearArr = [year-2,year-1,year]
            var monthArrs = []
            var  titleYearData=[]
            var  titleMonthData=[]

            for(var i=year-2;i<=year;i++){
                titleYearData.push({
                    value:i+'',
                    name:i+''
                })
            }
            $('#year').combobox({ 
                value:'',   
                valueField:'value',    
                textField:'name',
                panelHeight:'auto',
                editable:false,
                onSelect:yearFun,
                data:titleYearData
            });

            function yearFun(data) {
                titleMonthData=[]
                if (data.name == year) {
                    for (var i = 1; i <= month; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }else{
                    for (var i = 1; i <= 12; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }
                $('#month').combobox({ 
                value:'',   
                valueField:'value',    
                textField:'name',
                panelHeight:'auto',
                editable:false,
                data:titleMonthData
            });
                $('#month').combobox('setValue', '01');
            } 
            var timedata = {year:year+'',month:month>9 ? (month+'') : ( '0' + month )}
            formval(timedata,"taskTableQueryForm");

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usPolicyInfo/findPolicyLeder", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "queryParams":{},
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "单位", field: "belongCompanyName", width: 200, tooltip: true, align: "center" },
                            { title: "应宣讲次数", field: "preach", width: 80, tooltip: true, align: "center" },
                            { title: "已完成宣讲次数", field: "dopreach", width: 80, tooltip: true, align: "center" },
                            { title: "完成率", field: "rate", width: 80, tooltip: true, align: "center", formatter: function (value, row, index) {
                                    return '<span class="check" index="' + index + '">' + Number(value.replace('%', '')).toFixed(2) + '%</span>'
                                }
                            },
                            { title: "宣讲内容数量", field: "contentcount", width: 80, tooltip: true, align: "center" },
                        ]
                    ]
                }
            };
            pageparam.listtable.queryParams = timedata
            loadGrid(pageparam);

            // 查看宣讲完成详情
            $(document).on('click', 'a.check', function () {
                var index = $(this).attr('index');
                var row = $('#taskTable').datagrid('getRows')[index];
                var param = {
                    city: encodeURI(row.belongCompanyName)
                }
                var url = tourl('html/query/policyRate.html', param)
                top.dialogP(url, window.name, '宣讲完成情况', 'groupCheck', true, 'maximized', 'maximized')
            })

            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir + "action/usPolicyInfo/exportPolicyLeder?currentUserCode=" + web.currentUser.username);
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });
              //选择组织
              $(".chooseOrgs").on("click",function(){
                var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'C'};
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                var url=tourl('html/choose/chooseCompanyII.html',href);
                top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
            });

            if(web.currentUser.belongCompanyTypeDictValue == '01'||abol){
                $('.c02').show()
            }else{
                $('.c01').show()
            }

            //分公司人员新增接口做个判断，控制是否显示c03
            if(web.currentUser.belongCompanyTypeDictValue == '02' &&!abol){
                ajaxgeneral({
                    url: 'action/index/judgeUser',
                    contentType: 'application/json; charset=utf-8',
                    success: function (res) { 
                        if(!res.data.isAdmin){
                            $('.c03').hide()
                        }
                    }
                });
            }

        });

        function abolFun(){
            for(var i in web.currentUser.authRoles){
                if(web.currentUser.authRoles[i].authority == 'djfupt_001'){
                    return abol = true
                }
            }
        }

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };
        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [],comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#taskTableQueryForm input[name=companyCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $('#companyName').combobox('setValue',names.join(","));
        };
        function companyFun(data){
            $("#taskTableQueryForm input[name=companyCode]").val(data.value);
        }

    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm" contentType="application/json; charset=utf-8">
        <input name="companyCode" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right" class="c03">单位</td>
                <td width="230" class="c01 c03 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" value="" />
                </td>

                <td width="250" class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>
                <td width=" 90" align="right">宣讲时间</td>
                <td width="520">
                        <input class="easyui-combobox" id="year" name="year" data-options="
                        valueField: 'label',
                        textField: 'value',
                        editable:false,"
                        style="width: 41%; height: 32px;" />
                    年
                    <input class="easyui-combobox" id="month" name="month" data-options="
                        valueField: 'label',
                        textField: 'value',
                        editable:false,
                        data: [{ label: 'java', value: 'Java' },{ label: 'perl', value: 'Perl' },{ label: 'ruby', value: 'Ruby' }]"
                        style="width: 41%; height: 32px;" />
                    月
                </td>
                <td>
                    <div class=" w200">
                        <a class="btn fr ml10 exporttable ">
                            <font>导出</font>
                        </a>
                        <a class="btn fr searchtable">
                            <font>查询</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>