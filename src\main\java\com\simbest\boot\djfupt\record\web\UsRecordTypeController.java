package com.simbest.boot.djfupt.record.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.record.model.UsRecordType;
import com.simbest.boot.djfupt.record.service.IUsRecordTypeService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Api(description = "思政纪实类型")
@Slf4j
@RestController
@RequestMapping(value = "/action/usRecordType")
public class UsRecordTypeController extends LogicController<UsRecordType, String> {

    private IUsRecordTypeService service;

    @Autowired
    public UsRecordTypeController(IUsRecordTypeService UsRecordTypeService) {
        super(UsRecordTypeService);
        this.service = UsRecordTypeService;
    }



    @PostMapping(value = {"/findInfoPage", "/api/findInfoPage", "/findInfoPage/sso"})
    public JsonResponse findInfoPage(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int rows,
                                    @RequestBody(required = false) Map<String, String> resultMap) {
        if(resultMap == null){
            resultMap = new HashMap<>();
        }
       return service.findInfoPage(page,rows,resultMap);
    }

    @PostMapping(value = {"/findInfoNoPage", "/api/findInfoNoPage", "/findInfoNoPage/sso"})
    public JsonResponse findInfoNoPage(@RequestBody(required = false) Map<String, String> resultMap) {
        if(resultMap == null){
            resultMap = new HashMap<>();
        }
        return JsonResponse.success(service.findInfoNoPage(resultMap));
    }
}


