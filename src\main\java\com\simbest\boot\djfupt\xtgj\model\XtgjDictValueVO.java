package com.simbest.boot.djfupt.xtgj.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "协同工具字典值映射")
public class XtgjDictValueVO {

    @ApiModelProperty(value = "字典id")
    private String dictId;

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典值")
    private String dictValue;

    @ApiModelProperty(value = "操作")
    private String operationInfo;

    @ApiModelProperty(value = "备注说明")
    private String remarkInfo;

}
