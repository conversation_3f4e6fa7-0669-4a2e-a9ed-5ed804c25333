<!-- 数据总览 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>数据总览</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        var pageparam;
        getCurrent()
        $(function () {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var yearArr = [year-2,year-1,year]
            var monthArrs = []
            var  titleYearData=[]
            var  titleMonthData=[]

            for(var i=year-2;i<=year;i++){
                titleYearData.push({
                    value:i+'',
                    name:i+''
                })
            }
            $('#year').combobox({ 
                value:'',   
                valueField:'value',    
                textField:'name',
                panelHeight:'auto',
                editable:false,
                onSelect:yearFun,
                data:titleYearData
            });

            function yearFun(data) {
                titleMonthData=[]
                if (data.name == year) {
                    for (var i = 1; i <= month; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }else{
                    for (var i = 1; i <= 12; i++) {
                        titleMonthData.push({
                            value: i>9 ? (i+'') : ( '0' + i ),
                            name: i>9 ? (i+'') : ( '0' + i )
                        })
                    }
                }
                $('#month').combobox({ 
                value:'',   
                valueField:'value',    
                textField:'name',
                panelHeight:'auto',
                editable:false,
                data:titleMonthData
            });
                $('#month').combobox('setValue', '01');
            } 

            var strLink = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            // if (month >= 1 && month <= 9) { month = "0" + month; }
            if (day >= 1 && day <= 9) { day = "0" + day; }
            var firstDate = year + strLink + '01' + strLink + '01';
            var sysDate = year + strLink + month + strLink + day;
            var lastDay = getLastDay(year, month);
            var lastDate = year + strLink + month + strLink + lastDay;
            var returnArr = [firstDate, sysDate, lastDate];

            function getLastDay(year, month) { //获取某年某月最后一天是几号
                var new_year = year;
                var new_month = month++;//取下一个月的第一天，方便计算（最后一天不固定）
                if (month > 12) {//如果当前大于12月，则年份转到下一年
                    new_month -= 12;//月份减
                    new_year++;//年份增
                }
                var last_date = new Date(new_year, new_month, 0).getDate();
                return last_date;
            }






            var timedata = {year:year+'',month:month>9 ? (month+'') : ( '0' + month ),endTime:returnArr[2],startTime:returnArr[0]}
            formval(timedata,"taskTableQueryForm");

            pageparam = {
                "listtable": {
                    "listname": "#taskTable", 
                    "querycmd": "action/index/dataScreening", 
                    "contentType": "application/json; charset=utf-8", 
                    "queryParams":{},
                    "styleClass": "noScroll",
                    "nowrap": true,
                    "columns": [[ 
                            {title: "单位", field: "companyName", width: 80, tooltip: true, align: "center"},
                            {title: "网格数量", field: "gridNum", width: 80, tooltip: true, align: "center"},
                            {title: "<div class='rowWrap'>政策宣讲完成率</div>", field: "policy", width: 120, tooltip: true, align: "center"},
                            {title: "<div class='rowWrap'>思政工作完成率</div>", field: "recordFill", width: 120, tooltip: true, align: "center"},
                            {title: "<div class='rowWrap'>问题解决完成率</div>", field: "problemInfo", width: 120, tooltip: true, align: "center"},
                            {title:  "<div class='rowWrap'>经验总结完成率</div>", field: "caseInfo", width: 120, tooltip: true, align: "center"},
                        ]]
                }
            };
          
            // loadGrid(pageparam);
            $(".exporttable").on("click", function () {
                var data = getFormValue('taskTableQueryForm')
               if(year){
                data.time = data.year +'-'+ data.month
               }else{
                data.time = ''
               }
               delete data.chooseOrgs
               delete data.year
               delete data.month
               formval(data,'taskForm')
                $("#taskForm").attr("action", web.rootdir + "action/index/exportDataScreening");
                $("#taskForm").attr("method", "post");
                $("#taskForm").submit();
            });
            //选择组织
            $(".chooseOrgs").on("click",function(){
                var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'C'};
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                var url=tourl('html/choose/chooseCompanyII.html',href);
                top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
            });
            if(web.currentUser.belongCompanyTypeDictValue == '01'){
                $('.c02').show()
            }else{
                $('.c01').show()
            }

            $(".searchBtn").on("click",function(){
               var data = getFormValue('taskTableQueryForm')
               if(year){
                data.time = data.year +'-'+ data.month
               }else{
                data.time = ''
               }
               delete data.chooseOrgs
               delete data.year
               delete data.month
               pageparam.listtable.queryParams = data
               loadGrid(pageparam);
            });
            $('.searchBtn').trigger('click')
        });
        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
        };
        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [],comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#taskTableQueryForm input[name=orgCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $('#companyName').combobox('setValue',names.join(","));
        };
        function companyFun(data){
            $("#taskTableQueryForm input[name=orgCode]").val(data.value);
        }


    </script>
    <style>
         .rowWrap{ word-wrap: normal; white-space: normal; line-height: 14px; }
         .tdStyle {
             display: flex;
             justify-content: space-between;
             align-items: center;
         }
         .date{
             display: flex;
             justify-content: space-between;
             align-items: center;
         }
    </style>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <input name="orgCode" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="50" align="right">单位</td>
                <td width="200" class="c01 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" value="" />
                </td>
                <td width="200" class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>

                <td width="170" align="right">问题/经验推广时间</td>
                <td width="350" class="tdStyle date">
                    <input id="startTime" name="startTime" type="text" class="easyui-datebox" style="width:calc(45% - 10px);height:32px;" validType="startDateCheck['endTime','startTime']" data-options="panelHeight:'auto', editable:false" />
                    至
                    <input id="endTime" name="endTime" type="text" class="easyui-datebox" style="width:calc(45% - 10px);height:32px;" validType="endDateCheck['startTime','endTime']" data-options="panelHeight:'auto', editable:false" />
                </td>



<!--                <td width="100" align="right">创建日期：</td>-->
<!--                <td width="300" class="date">-->
<!--                    <input id="startTime" name="startTime" type="text" class="easyui-datebox"-->
<!--                           data-options="panelHeight:'auto',editable:false" style="width:calc(50% - 10px);height:32px;"/>-->
<!--                    - -->
<!--                    <input id="endTime" name="endTime" type="text" class="easyui-datebox"-->
<!--                           data-options="panelHeight:'auto',editable:false" style="width:calc(50% - 10px);height:32px;"/>-->
<!--                </td>-->



                <td width="120" align="right">宣讲/思政时间</td>
                <td width="350" class="tdStyle date">
                        <input class="easyui-combobox" id="year" name="year" data-options="
                        valueField: 'label',
                        textField: 'value',
                        editable:false,"
                        style="width:calc(45% - 10px); height: 32px;" />
                    年
                    <input class="easyui-combobox" id="month" name="month" data-options="
                        valueField: 'label',
                        textField: 'value',
                        editable:false,"
                        style="width:calc(45% - 10px); height: 32px;" />
                    月
                </td>
                <td width="15%">
                    <a class="btn fr ml10 exporttable ">
                        <font>导出</font>
                    </a>
                    <a class="btn fr searchBtn">
                        <font>查询</font>
                    </a>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>

    <form id="taskForm" class="hide">
          <input type="hidden" name="orgCode" id="orgCode" />
          <input type="hidden" name="companyName" id="companyName" />
          <input type="hidden" name="startTime" id="startTime" />
          <input type="hidden" name="endTime" id="endTime" />
          <input type="hidden" name="time" id="time" />
    </form>
</body>

</html>