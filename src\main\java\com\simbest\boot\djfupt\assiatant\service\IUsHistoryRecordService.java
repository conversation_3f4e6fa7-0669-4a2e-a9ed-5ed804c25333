package com.simbest.boot.djfupt.assiatant.service;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.assiatant.model.UsHistoryRecord;

import java.util.List;
import java.util.Map;

public interface IUsHistoryRecordService extends ILogicService<UsHistoryRecord, String> {
    List<Map<String, Object>> getAllNoPage(String source, String currentUserCode, Map<String, String> map);

    List<Map<String, Object>> getAllDetailNoPage(String source, String currentUserCode, String pmInsId);

    JsonResponse clearByPmInsId(String pmInsId);

    JsonResponse clearAll();
}
