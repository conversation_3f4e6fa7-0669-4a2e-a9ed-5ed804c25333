package com.simbest.boot.djfupt.record.model;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "Us_Talk_Content")
@ApiModel(value = "谈话内容")
public class UsTalkContent extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 500)
    @ApiModelProperty(value = "类型")
    private String type;

    @Column(length = 500)
    @ApiModelProperty(value = "问题简介")
    private String remark;

    @Column(length = 50)
    @ApiModelProperty(value = "主表id")
    private String mainId;
}
