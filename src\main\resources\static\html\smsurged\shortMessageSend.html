<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>党建指导员支撑服务平台</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/product.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        // 流程类型
        //dictionary.pmInsType= "A|设备新增流程(系统推送),B|设备新增流程(手动起草),C|设备信息变更流程";
        dictionary.pmInsType= "A|党建指导员支撑服务平台审批流程";
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable",//table列表的id名称，需加#
                    "querycmd": "action/smsUrged/queryAllTaskToDo?source=PC",//table列表的查询命令
                    "nowrap": true,//把数据显示在一行里,默认true
                    "fitColumns": false,
                    "checkboxall": true,
                    "remoteSort": false,
                    "idField":"ID",
                    "frozenColumns": [[
                        {field: "ck", checkbox: true}
                    ]],//固定在左侧的列
                    "columns": [[//列
                        {
                            title: "任务标题", field: "RECEIPT_TITLE", width: 350, rowspan: 1, tooltip: true//align：对齐此列的数据，可以用left、right、center
                        },
                        {
                            title: "流程类型", field: "PM_INS_TYPE", width: 200, rowspan: 1, tooltip: true,
                            formatter: function (value, row, index) {
                                return getvals("pmInsType", value);
                            }
                        },
                        {title: "创建部门", field: "CREATE_ORG_NAME", width: 200},
                        {title: "创建人", field: "CREATE_USER_NAME", width: 100},
                        {title: "创建时间", field: "CREATE_TIME", width: 150, sortable: true},//排序sortable: true
                        {title: "当前环节", field: "ACTIVITYINSTNAME", width: 150, sortable: true},//排序sortable: true
                        {title: "待处理人", field: "PARTI_NAME", width: 100}
                    ]],
                    "rowStyler": function (index, row) {
                    }
                },
            };
            loadGrid(pageparam);
            // 批量发送短信催办
            $(document).on("click", ".branchSend", function () {
                var datas = $(pageparam.listtable.listname).datagrid("getChecked");
                if (datas.length > 0) {
                    $.messager.confirm("批量催办", "确定要发送短信催办吗？", function (r) {
                        if (r) {
                            var ajaxopts = {
                                url: "action/smsUrged/sendShortMessage?source=PC",
                                contentType: "application/json; charset=utf-8",
                                data: datas,
                                success: function (data) {
                                    top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                }
                            };
                            ajaxgeneral(ajaxopts);
                        }
                    });
                }
                else {
                    top.mesShow("温馨提示", "请选择要发送催办短信的用户！", 2000, 'red');
                }
            });
            // 全部催办
            $(document).on("click", ".allSend", function () {
                // var datas = $(pageparam.listtable.listname).datagrid("getChecked");
                $.messager.confirm("全部催办", "确定要发送全部短信催办吗？", function (r) {
                        if (r) {
                            var ajaxopts = {
                                url: "action/smsUrged/sendAllShortMessage?source=PC",
                                contentType: "application/json; charset=utf-8",
                                success: function (data) {
                                    top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                }
                            };
                            ajaxgeneral(ajaxopts);
                        }
                    });
            });
        });
    </script>
</head>
<body class="body_page">
<form id="taskTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">任务标题：</td>
            <td width="200"><input name="title" type="text" value=""/></td>
            <!--<td width="90" align="right">流程名称：</td>-->
            <!--<td width="200">-->
                <!--<input class="pmInsType easyui-combobox" name="processType"  style="width: 100%; height: 32px;"-->
                       <!--data-options="-->
                       <!--valueField: 'value',-->
                       <!--ischooseall:true,-->
                       <!--textField: 'name',-->
                       <!--queryParams:{'dictType':'processType'},-->
                       <!--url: web.rootdir+'action/queryDictValue/queryByType'"/>-->
            <!--</td>-->
            <td>
                <div class="w100">
                    <a class="btn fl searchtable "><font>查询</font></a>
                    <a class="btn fl a_warning ml10 branchSend"><font>催办</font></a>
                    <a class="btn fl a_primary ml10 allSend"><font>全部催办</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="taskTable">
    <table id="taskTable"></table>
</div>
</body>
</html>
