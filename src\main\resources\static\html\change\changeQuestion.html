<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>变更课题</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent();

        $(function () {
            if (gps.type == 'check' || gps.type == 'edit') {
                $('.pageInfo').hide();
                $('.body_page').attr('style', 'padding-top: 20px')
            } else {
                $('.body_page').attr('style', 'padding-top: 85px')
            }
            if (gps.type == "draft") {
                if (gps.location) {
                    $(".reprealDraft").show();
                }
            }
            if (gps.type == 'join') {
                loadForm('questionForm', {currentUserCode: web.currentUser.username, pmInsId: gps.pmInsId})
                $('.pageInfo').hide();
                $('.body_page').attr('style', 'padding-top: 20px')
            } else {
                $('#questionForm').attr('cmd-select', 'action/usProblemInfo/getCurrentProblemInfo')
                loadForm('questionForm', {currentUserCode: web.currentUser.username})
            }
            var param = {
                "htmlName": "changeQuestion",
                "formId": "questionForm",
                "processNextCmd": "action/usProblemInfo/startSubmitProcess",
                "processNextBeforeSubmit": function (data) {
                    // if ( gps.location == 'djfupt.proAdminFeedback') {
                    //     if (data.formData.problemFeedback == '') {
                    //         return top.mesShow("温馨提示！", "请将问题处理结果反馈填写完整后流转！", 2000, "red")
                    //     }
                    // }
                    // if (data.decisionId == 'djfupt.proAdminToAsk' && gps.location == 'djfupt.braAdminFeedback') {
                    //     if (data.formData.problemFeedback == '') {
                    //         return top.mesShow("温馨提示！", "请将问题处理结果反馈填写完整后流转！", 2000, "red")
                    //     }
                    // }

                    // if (data.decisionId == 'djfupt.proAdminToAsk' && gps.location == 'djfupt.addressBookFeedback') {
                    //     if (data.formData.problemFeedback == '') {
                    //         return top.mesShow("温馨提示！", "请将问题处理结果反馈填写完整后流转！", 2000, "red")
                    //     }
                    // }
                    return true;
                }
            };
            // loadProcess(param); // 去除流程

            var today = new Date();
            var year = today.getFullYear();
            var month = (today.getMonth() + 1) > 9 ? today.getMonth() + 1 : '0' + (today.getMonth() + 1);
            var day = today.getDate() > 9 ? today.getDate() : '0' + today.getDate();
            today = year + '-' + month + '-' + day;

            $('#applyUser').val(web.currentUser.truename)
            $('#belongOrgName').val(web.currentUser.belongCompanyName)
            $('#applyUserPhone').val(web.currentUser.preferredMobile)
            $('#belongDepartmentName').val(web.currentUser.belongDepartmentName);
            if (!gps.location) {
                $("#expectedCompletionTime").datetimebox('calendar').calendar({
                    styler: function (date) {
                        var now = new Date();
                        var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        return date < d1 ? "color:#eee" : "";
                    },
                    validator: function (date) {
                        var now = new Date();
                        var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        return date >= d1;
                    }
                });
            }

            //hadmin修改
            $(document).on("click", ".hadminSave", function () {
                var datas = getFormValue("questionForm")
                // if (formValidate("questionForm")) {
                //     ajaxgeneral({
                //         url: "action/usProblemInfo/updateProblemInfo",
                //         data: datas,
                //         contentType: "application/json; charset=utf-8",
                //         success: function (res) {
                //             top.window.queryQuestionListDialog.window.dialogClosed()
                //             top.tabClick("queryQuestionListDialog")
                //         }
                //     });
                // }
                var ajaxopts = {
                    url: "action/usProblemInfo/saveUsProblemInfo",
                    contentType: "application/json; charset=utf-8",
                    loading: true,
                    data: datas,
                    success: function (data) {
                        top.dialogClose(gps.fromIfr);
                    }
                };
                ajaxgeneral(ajaxopts);
            });
        });

        function beforeSubmit(data) {
            return data
        }

        function groupNameSelect(data) {
            if (!data) return;
            $('#groupRegistDate').val(data.groupRegistDate || '')
            $('#groupApplyDate').val(data.groupApplyDate || '')
            $('#groupPhone').val(data.groupPhone || '')
            $('#groupApplyUser').val(data.groupApplyUser || '')
            $('#groupBelongDepartmentName').val(data.groupBelongDepartmentName || '')
            $('#groupBelongCompanyName').val(data.groupBelongCompanyName || '')
            $('#groupNumber').val(data.groupNumber || '')
        }

        function submitcallback(data) {
        }

        function getcallback(data) {
            // $('#id').val(data.id)
            // $('#pmInsId').val(data.pmInsId)
            // $('#type').val(data.type)
            // if (gps.location) {
            //     $('.optClose').show()
            //     $('.queSave').show()
            //     $('#feedBackApplyUser').val(web.currentUser.truename)
            //     $('#feedApplyUserPhone').val(web.currentUser.preferredMobile)
            //     $('#feedBackBelongOrgName').val(web.currentUser.belongDepartmentName)
            //     $($('#problemDescribe').parent().prev('span')).text('问题描述')
            //     idReadonlyNo('problemFeedback')
            //     idReadonlyNo('feedBackFiles')
            // }
            // 去除流程
            // if (gps.location == "djfupt.proAdminFeedback") {
            //     $('#problemFeedback').validatebox({required: true})
            //     idReadonlyNo('problemFeedback')
            //     idReadonlyNo('feedBackFiles')
            // }
            // if (gps.location == "djfupt.proAdminFeedbackAsk") {
            //     $('#problemFeedback').validatebox({required: false})
            //     idReadonly('problemFeedback')
            //     idReadonly('feedBackFiles')
            // }
            // if (gps.type == 'join') {
            //     idReadonly('problemFeedback')
            //     idReadonly('feedBackFiles')
            // }
            if (gps.otherType == "hadminUpdata") {
                $('.formReset').hide()
                $('.saveDraft').hide()
                $('.nextBtn').hide()
                $('.reprealDraft').hide()
                $('.hadminSave').show()
                idReadonly("questionMode")
            }

        }

        window.getchoosedata = function () {
            if (!formValidate('questionForm')) {
                return false
            }
            var datas = getFormValue('questionForm');
            return {'data': datas, 'state': 1};
        };


        function loadProcess(param) {
            var processNext_flag = true;//防止接口慢时用户再次流转
            if (gps.from) {
                $("#" + param.formId).attr("cmd-select", $("#" + param.formId).attr("cmd-select") + "/sso");
            }
            if (!gps.location) {
                if (!param.startNoLoadForm) loadForm(param.formId);
                getCurrent(loadF);
                $(".nextBtn,.saveDraft").show();
            } else {
                if (gps.type) gps.workFlag = gps.type;
                gps.source = "PC";
                loadForm(param.formId, gps);
                if (!gps.modify) {
                    if (gps.location != $("#" + param.formId).attr("formLocation") || gps.type == 'task' || gps.type == 'join')
                        formReadonly(param.formId);
                } else {
                    $("#" + param.formId + " input").removeAttr("readonly");
                }
                if (!gps.modify && (gps.type == "task" || gps.type == "toRead")) $(".nextBtn,.flowTrack,.viewComments,.processImg").show();
                if (gps.type == "join" || gps.type == "doRead") $(".flowTrack,.viewComments,.processImg").show();
                if (gps.modify) $(".wfmgModifyBtn").show();
                // 草稿
                if (gps.type == "draft") {
                    $(".nextBtn,.saveDraft").show();
                }
            }

            //注销
            function cancelSure() {
                var href = {"processInstId": gps.processInstId, "source": "PC"};
                var url = tourl(param.processDeleteCmd, href);//(+gps.from?"/sso":"")
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.from) {
                                window.colse();
                            } else {
                                top.dialogClose(gps.initForm || "audit");
                            }
                        }
                    });
                }
            };

            //流程下一步
            window.processNext = function (data) {
                var href = {};
                href.outcome = data.data.outcome;
                href.outcomeName = encodeURI(data.data.outcomeName);
                href.copyLocation = data.data.copyLocation;
                if (gps.workItemId) href.workItemId = gps.workItemId;
                if (gps.processInstId) href.processInstId = gps.processInstId;
                if (gps.location) href.location = gps.location;
                if (gps.type == "toRead") href.notificationId = gps.notificationId;
                href.source = "PC";
                var url = tourl(param.processNextCmd, href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                formD.type = "B"
                var fData = {"formData": formD};
                if (data.data.nextUserName) fData.nextUserName = data.data.nextUserName;
                if (data.data.message) fData.message = data.data.message;
                fData.copyNextUserNames = data.data.copyNextUserNames;
                fData.copyMessage = data.data.copyMessage;
                fData.decisionId = data.data.outcome;
                fData.outcomeName = data.data.outcomeName;
                if (formD) {
                    /**
                     编辑者：申振楠
                     修改时间：2022/5/24 - 2022/9/15
                     修改内容：点击确定按钮置灰
                     */
                    $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                    processNext_flag = false;
                    if (param.processNextBeforeSubmit) {
                        var pnbs = eval(param.processNextBeforeSubmit(fData));
                        // if (!pnbs) return false;   这里修改了可以二次点击流转下一步
                        if (!pnbs) {
                            processNext_flag = true;
                            return false
                        }
                        ;

                    }
                    top.dialogClose("processNext");
                    ajaxgeneral({
                        url: url,
                        data: {"flowParam": fData},
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        loading: true,
                        success: function (data) {
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                            processNext_flag = true;
                            if (gps.dialogClose) {
                                top.dialogClose(gps.dialogClose);
                            }
                            if (gps.location || gps.initForm) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                } else if(gps.flushPortalUrl){
                                    // 集团单点流转
                                    var flushPortalUrl = decodeURIComponent(gps.flushPortalUrl);
                                    var params = {
                                        "appcode": gps.appcode,
                                        "uniqueId": gps.uniqueId,
                                        "itemId": gps.itemId,
                                    }
                                    var pageUrlNew = tourl(flushPortalUrl,params);
                                    window.location.replace(pageUrlNew)
                                }else if (gps.formToTab) {
                                    top.tabClose("li_" + param.htmlName);
                                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                                    var iframs = gps.initForm.split("@");
                                    setTimeout(function () {
                                        if (iframs[0].indexOf("_") > -1) {
                                            var ifi = iframs[0].split("_");
                                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                                        } else {
                                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                                            }
                                        }
                                    }, 1500);
                                } else if (gps.fromIfr) {
                                    top.dialogClose(gps.fromIfr);
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                if (gps.fromIfr) {//从列表打开的工单详情
                                    top.dialogClose(gps.fromIfr);
                                } else if (gps.newPage) {
                                    top.tabClick("li_processTask");
                                } else if (gps.openMenu) {//打开指定菜单
                                    top.tabClick(gps.openMenu);
                                } else {
                                    //window.location.href=window.location.href;
                                    setTimeout(function () {
                                        top.tabClick("processTask");
                                        top.tabClose("li_" + param.htmlName);
                                    }, 1500);
                                }
                            }
                        }, sError: function () {
                            processNext_flag = true;
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                        }, error: function () {
                            processNext_flag = true;
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                        }
                    });
                }
            };
            //废除草稿
            $(document).on("click", ".reprealDraft", function () {
                var data = getFormValue('questionForm');

                var ajaxopts = {
                    url: "action/usProblemInfo/deleteDraft?pmInsId=" + data.pmInsId + '&currentUserCode=' + web.currentUser.username,
                    contentType: "application/json; charset=utf-8",
                    data: data,
                    success: function (data) {
                        if (gps.type == "draft") {
                            top.dialogClose("audit");
                        } else {
                            top.tabClick("processDraft");
                            top.tabClose("li_applicationFrom");
                        }
                    }
                };
                ajaxgeneral(ajaxopts);
            });

            //流程下一步
            $(document).on("click", ".nextBtn", function () {
                if (!processNext_flag) return false;
                $(".nextBtn").removeClass("activeNextBtn");
                if (!$(this).hasClass("activeNextBtn")) $(this).addClass(" activeNextBtn");//标识用
                var $form = $(this).parents("form");
                if (formValidate($form.attr("id"))) {
                    var href = {};
                    if (gps.location) {
                        href.location = gps.location;
                        if (gps.processInstId) href.processInstId = gps.processInstId;
                        if (gps.processDefName) href.processDefName = gps.processDefName;
                    } else {
                        href.location = $(this).parents("form").attr("formLocation");
                    }
                    if (gps.from) href.from = gps.from;
                    if (gps.type) href.type = gps.type;
                    var otherhref = {};
                    if ($form.attr("nextBtnOther")) {
                        if ($form.attr("nextBtnOther").indexOf("()") > -1)
                            otherhref = eval($form.attr("nextBtnOther"));
                        else
                            otherhref = eval($form.attr("nextBtnOther") + "()");
                    }
                    for (var i in otherhref) {
                        href[i] = otherhref[i];
                    }

                    href.source = "PC";
                    href.processType = 'quetsion'

                    href.questionMode = getFormValue("questionForm").questionMode
                    var url = tourl('html/process/processNext.html', href);
                    var s = getBrowserInfo();
                    var w = 1000;
                    if ((s.browser == "msie" && parseInt(s.ver) < 9) || $(window.parent.window).height() < 550) {
                        w = 1017;
                    }

                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流转下一步', 'processNext', false, w);
                }
            });

            //流程跟踪
            $(document).on("click", ".flowTrack", function () {
                var href = {"location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation")};
                var flowTrackText = $(".flowTrack font").text();
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.flowTrackBefore) {
                    var ft = eval(param.flowTrackBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processTrack.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), flowTrackText, 'processTrack', true);
            });
            //查看意见
            $(document).on("click", ".viewComments", function () {
                var href = {"location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation")};
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.viewCommentsBefore) {
                    var ft = eval(param.viewCommentsBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processComments.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '审批意见', 'processComments', true);
            });
            //流程图
            $(document).on("click", ".processImg", function () {
                var href = {"processinstid": gps.processInstId, "tenantId": web.appCode};
                if (param.processImgBefore) {
                    var ft = eval(param.processImgBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }

                if (web.appCode == "dict") {
                    // dict流程图
                    var url = tourl('html/process/flowImage.html', href);
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流程图', 'flowImage', true, 'maximized', 'maximized');
                } else {
                    var url = 'http://10.92.82.44:8888/nbps/processGraphic.jsp';
                    if (window.location.href.indexOf("10.92.81.163") > -1 || window.location.href.indexOf("10.92.82.140") > -1 || window.location.href.indexOf("10.92.82.141") > -1) url = 'http://10.92.81.163:8088/nbps/processGraphic.jsp';
                    url = tourl(url, href);//正式:http://10.92.81.163:8088/nbps/processGraphic.jsp  测试:http://10.92.82.44:8888/nbps/processGraphic.jsp
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流程图', 'processImg', true, $(window).width() - 50, $(window).height() - 20);
                }
            });
            //修改提交用于wfmg
            $(document).on("click", ".wfmgModifyBtn", function () {
                var href = {};
                href.businessKey = gps.businessKey;
                var url = tourl("action/workOrderMg/updateWorkDetail", href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.location) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                }else if(gps.flushPortalUrl){
                                    // 集团单点流转
                                window.opener=null;
                                    window.open('','_self');
                                    window.close();
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                //window.location.href=window.location.href;
                                setTimeout(function () {
                                    top.tabClose("li_" + param.htmlName);
                                }, 1500);
                            }
                        }
                    });
                }
            });
        };

        function gradChange(n, o) {
            if (n == 0) {
                $('.queSave').hide()
                $($('#problemDescribe').parent().prev('span')).text('问题具体描述')
                $('#problemFeedback').validatebox({required: false})
                $('#feedBackApplyUser').val('')
                $('#feedApplyUserPhone').val('')
                $('#feedBackBelongOrgName').val('')
            } else if (n == 1) {
                $('.queSave').show()
                $($('#problemDescribe').parent().prev('span')).text('问题描述')
                $('#problemFeedback').validatebox({required: true})
                $('#feedBackApplyUser').val(web.currentUser.truename)
                $('#feedApplyUserPhone').val(web.currentUser.preferredMobile)
                $('#feedBackBelongOrgName').val(web.currentUser.belongDepartmentName)
            }
        };

        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            var data = getFormValue('questionForm');
            // var ajaxopts = {
            //     url: "action/usProblemInfo/saveDraft",
            //     contentType: "application/json; charset=utf-8",
            //     loading: true,
            //     data: data,
            //     success: function (data) {
            //         if (gps.type == "draft") {
            //             top.dialogClose("audit");
            //         } else {
            //             top.dialogClose(gps.fromIfr);
            //             top.tabClick("processDraft");
            //             top.tabClose("li_applicationFrom");
            //         }
            //     }
            // };
            var ajaxopts = {
                url: "action/usProblemInfo/saveUsProblemInfo",
                contentType: "application/json; charset=utf-8",
                loading: true,
                data: data,
                success: function (data) {
                    top.dialogClose(gps.fromIfr);
                }
            };
            ajaxgeneral(ajaxopts);
        });

        var processNext_flag = true;//防止接口慢时用户再次流转
        //流程下一步
        $(document).on("click", ".nextBtn", function () {
            if (!processNext_flag) return false;
            processNext_flag = false
            $(".nextBtn").removeClass("activeNextBtn");
            if (!$(this).hasClass("activeNextBtn")) $(this).addClass(" activeNextBtn");//标识用
            var $form = $(this).parents("form");
            if (formValidate($form.attr("id"))) {
                var data = getFormValue('questionForm');
                var ajaxopts = {
                    url: "action/usProblemInfo/insertUsProblemInfo",
                    contentType: "application/json; charset=utf-8",
                    data: data,
                    loading: true,
                    success: function (data) {
                        processNext_flag = true;
                        formreset($form.attr("id"))
                    }, error: function (err) {
                        processNext_flag = true;
                    }
                }
                ajaxgeneral(ajaxopts)
            } else {
                processNext_flag = true
            }
        });

        $(document).on("click", "a.openOther", function () {
            window.open('/djfupt/html/dataScreen/partyBuildingScreen.html')
        })
        //关闭
        $(document).on("click", ".optClose", function () {
            var param = {htmlName: 'changeQuestion'}
            if (gps.location || gps.initForm) {
                if (gps.from) {
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                }else if(gps.flushPortalUrl){
                            // 集团单点流转
                           window.opener=null;
                            window.open('','_self');
                            window.close();
                        } else if (gps.formToTab) {
                    top.tabClose("li_" + param.htmlName);
                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                    var iframs = gps.initForm.split("@");
                    setTimeout(function () {
                        if (iframs[0].indexOf("_") > -1) {
                            var ifi = iframs[0].split("_");
                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                        } else {
                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                            }
                        }
                    }, 1500);
                } else if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.dialogClose("audit");
                }
            } else {
                if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.newPage) {
                    top.tabClick("li_processTask");
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.tabOpen("html/process/processTask.html", "我的待办");
                    top.tabClose("li_" + param.htmlName);
                }
            }
        });

    </script>
    <style>
        textarea {
            white-space: normal !important;
        }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }

        .formTable > tbody > tr > td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }

        .formTable > tbody > tr > td input,
        .formTable > tbody > tr > td span,
        .formTable > tbody > tr > td textarea,
        .formTable > tbody > tr > td .textbox .textbox-text {
            border: none;
            font-size: 13px;
        }

        .formTable td.lable {
            background-color: #ddf1fe;
            padding: 5px;
            text-align: left;
            max-width: 100px;
        }

        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly {
            background-color: #fff;
        }

        input:read-only {
            background-color: #fff;
        }

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] {
            right: 3px;
            top: -15px;
            width: 55px;
        }

        textarea {
            line-height: 20px;
            letter-spacing: 1px;
        }

        .queSave {
            display: none;
        }

        /* .cselectorImageUL .btn,.cselectorImageUL input[type='file']{
            width: 55px !important;
            right: 3px;
            top: 3px;
        } */

        .tabForm {
            border-top: none;
        }

        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }

        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</head>

<body class="body_page">
<!--noNextUserDecisionId无下一审批人的决策id比如归档，可以为多个节点中间用|隔开；mulitNextUserDecisionId审批人多选的决策项-->
<form id="questionForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
      cmd-select="action/usProblemInfo/getFormDetail" beforeSubmit="beforeSubmit()" submitcallback="submitcallback()"
      getcallback="getcallback()">
    <input id="pmInsId" name="pmInsId" value="" hidden>
    <input id="id" name="id" value="" hidden>
    <input id="type" name="type" value="" hidden>
    <div class="pageInfo" style="min-width: 0">
        <div class="pageInfoD">
            <a class="btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i>
                <font>流转下一步</font>
            </a>
            <a class="btn small fl mr15 saveDraft"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 hadminSave hide mb20"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 optClose"><i class="iconfont">&#xe690;</i>
                <font>关闭</font>
            </a>
            <!--            <a class="btn small fl mr15 reprealDraft hide"><i class="iconfont">&#xe6bd;</i>-->
            <!--                <font>废除草稿</font>-->
            <!--            </a>-->
            <!--            <a class="btn small fl mr15 flowTrack hide"><i class="iconfont">&#xe68c;</i>-->
            <!--                <font>流程跟踪</font>-->
            <!--            </a>-->
            <!--            <a class="btn small fl mr15 viewComments hide"><i class="iconfont">&#xe629;</i>-->
            <!--                <font>查看意见</font>-->
            <!--            </a>-->
            <!--            <a class="btn small fl mr15 processImg hide"><i class="iconfont">&#xe6bd;</i>-->
            <!--                <font>流程图</font>-->
            <!--            </a>-->
            <!--            <a class="btn small fl mr15 optClose"><i class="iconfont">&#xe690;</i>-->
            <!--                <font>关闭</font>-->
            <!--            </a>-->
        </div>
    </div>

    <div style="width: 100%;">
        <table class="tabForm formTable w100" cellpadding="0" cellspacing="10">
            <tr>
                <td width="10%"></td>
                <td width="23.3%"></td>
                <td width="10%"></td>
                <td width="23.3%"></td>
                <td width="10%"></td>
                <td width="23.3%"></td>
            </tr>
            <tr>
                <td colspan="6" style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding: 5px;">
                    问题上报
                </td>
            </tr>
            <tr>
                <td class="lable">问题名称<font class="col_r">*</font>
                </td>
                <td colspan="5"><input id="problemName" class="easyui-validatebox" name="problemName" type="text"
                                       required="true" validType="maxLength[50]" style="width:100%; height: 32px;"/>
                </td>
            </tr>
            <tr>
                <td class="lable"> 发起人</td>
                <td><input id="applyUser" name="applyUser" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">发起人组织</td>
                <td><input id="belongOrgName" name="belongOrgName" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">发起人部门</td>
                <td><input id="belongDepartmentName" name="belongDepartmentName" type="text" readonly="readonly"
                           noReset="true"
                           style="width:100%; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td class="lable">发起人联系方式</td>
                <td><input id="applyUserPhone" name="applyUserPhone" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">期望完成时间<font class="col_r">*</font>
                </td>
                <td>
                    <input id="expectedCompletionTime" name="expectedCompletionTime" type="text"
                           class="easyui-datebox" required="true" data-options="editable:false"
                           style="width:100%;height:32px;"/>
                </td>
                <td class="lable">问题上报方式</td>
                <td>
                    <select class="easyui-combobox" id="questionMode" name="questionMode"
                            style="width:100%; height: 32px;"
                            data-options="editable:false,onChange:gradChange,panelHeight:'auto'" noReset="true">
<!--                        <option selected value="0">问题上报</option>-->
                        <option value="1">问题存储</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="lable"><span>问题及解决情况描述</span> <font class="col_r">*</font></td>
                <td colspan="5" width="90%">
                        <textarea id="problemDescribe" name="problemDescribe" class="easyui-validatebox" required="true"
                                  validType="maxLength[1000,'talkContentTips']" style="min-height:120px;"></textarea>
                    <p class="talkContentTips"></p>
                </td>
            </tr>
            <tr style="height: 40px">
                <td class="lable">附件</td>
                <td colspan="5" width="90%" style="padding-left: 7px">
                    <input id="drawFiles" name="drawFiles" type="text" file="true" mulaccept="true"
                           class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=1"/>
                </td>
            </tr>
            <tr class="queSave" style="display:none;">
                <td colspan="6" style="font-weight: 700;font-size: 16px;padding: 5px;"> 问题处理反馈</td>
            </tr>
            <tr class="queSave" style="display:none;">
                <td class="lable"> 问题处理结果反馈</td>
                <td colspan="5" width="90%">
                        <textarea id="problemFeedback" name="problemFeedback" class="easyui-validatebox"
                                  data-options="required: false" validType="maxLength[1000]"
                                  style="min-height:120px;"></textarea>
                </td>
            </tr>
            <tr class="queSave">
                <td class="lable">附件</td>
                <td colspan="5" width="90%">
                    <input id="feedBackFiles" name="feedBackFiles" type="text" file="true" mulaccept="true"
                           class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsTypePart=2"/>
                </td>
            </tr>
            <tr class="queSave">
                <td class="lable"> 反馈人</td>
                <td><input id="feedBackApplyUser" name="feedBackApplyUser" type="text" noReset="true"
                           style="width:100%; height: 32px;" readonly="true"/>
                </td>
                <td class="lable">反馈人组织</td>
                <td><input id="feedBackBelongOrgName" name="feedBackBelongOrgName" type="text"
                           noReset="true" style="width:100%; height: 32px;" readonly="true"/>
                </td>
                <td class="lable">反馈人联系方式</td>
                <td><input id="feedApplyUserPhone" name="feedApplyUserPhone" type="text"
                           noReset="true" style="width:100%; height: 32px;" readonly="true"/>
                </td>
            </tr>
        </table>
    </div>
</form>

<div class="ces hide">
    <a href="#" class="btn  openOther"> 党建智慧大屏</a>
</div>
</body>

</html>