package com.simbest.boot.djfupt.wfquey.service.impl;

import com.google.common.collect.Maps;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.djfupt.wfquey.service.IQueryOpinionHistoryService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2018/06/12
 * @Description 流程意见
 */
@Slf4j
@Service(value = "queryOpinionHistory")
public class QueryOpinionHistoryImpl  implements IQueryOpinionHistoryService {

    @Autowired
    private IWfOptMsgService wfOptMsgService;


    /**
     * 查询工单审批意见
     * @param processInstId 流程实例id
     * @param currentUserCode 当前人
     * @return
     */
    @Override
    public List<Map<String,Object>> getWfOptMags(Long processInstId,String currentUserCode ) {
        /**查询流程审批意见**/
        Map<String,Object> mapParam = Maps.newHashMap();
        mapParam.put("processInsId",processInstId);
        if (StringUtils.isEmpty( currentUserCode )){
            mapParam.put("currentUser",SecurityUtils.getCurrentUserName());
        }else {
            mapParam.put("currentUser",currentUserCode);
        }
        return wfOptMsgService.queryProcessOptMsgDataMap( mapParam );
    }


}
