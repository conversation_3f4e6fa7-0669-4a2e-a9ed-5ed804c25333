<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>装维入格调研台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent()
        $(function () {
            var orgList = []
            ajaxgeneral({
                url: 'uums/sys/org/findPOrgAndCityOrg?appcode=' + web.appCode,
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    data.data.forEach(function (item) {
                        if (item.styleDictValue == '02') {
                            orgList.push({
                                belongCompanyCode: item.belongCompanyCode,
                                orgName: item.orgName
                            })
                        }
                    })
                    orgList = orgList.slice(1, 19)
                    $('#belongCompanyCode').combobox({
                        "valueField": 'belongCompanyCode',
                        "ischooseall": false,
                        "textField": 'orgName',
                        "editable": false,
                        "panelHeight": '200',
                        "data": orgList
                    });
                }
            });

            var pageparam = {
                "listtable": {
                    "listname": "#glyTable", //table列表的id名称，需加#
                    "querycmd": "action/usZwrgdyInfo/findAllInfo", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "归属单位", field: "belongCompanyName", width: 60, tooltip: true, align: "center" },
                            { title: "归属部门", field: "belongDepartmentName", width: 120, tooltip: true, align: "center" },
                            { title: "填报人姓名", field: "truename", width: 60, tooltip: true, align: "center"},
                            { title: "联系电话", field: "phone", width: 80, tooltip: true, align: "center" },
                            { title: "归属同格", field: "gridName", width: 120, tooltip: true, align: "center" },
                            { title: "上报时间", field: "createdTime", width: 80, tooltip: true, align: "center" },
                            { field: "opt", title: "操作", width: 60, rowspan: 1, align: "center",
                                formatter: function (value, row, index) {    //单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                    var g = ""
                                    g += "<a href='#' class='look' id='" + row.id + "' title='查看'>【查看】</a>";
                                    return g
                                }
                            }
                        ]
                    ]
                }
            };
            loadGrid(pageparam);
        });

        $(document).on('click', 'a.addBtn', function () {
            top.dialogP("html/xtgj/zwrgdyAdd.html?type=add", window.name, "新增", "addCallBack", false, 1340, 600)
        })
        
        window.addCallBack = function (data) {
            setTimeout(function () {
                $("#glyTable").datagrid("reload");
            }, 500)
        }

        $(document).on('click', 'a.look', function () {
            var id = $(this).attr('id');
            top.dialogP("html/xtgj/zwrgdyAdd.html?type=look&id="+id, window.name, "查看", "addCallBack", true, 1340, 600)
        })

        $(document).on('click', 'a.exporttable', function () {
            $("#glyTableQueryForm").attr("action", web.rootdir + "action/usZwrgdyInfo/exportInfo");
            $("#glyTableQueryForm").attr("method", "post");
            $("#glyTableQueryForm").submit();
        })

        //重置
        $(document).on("click", ".formreset", function () {
            formreset('glyTableQueryForm')
            $("#belongCompanyCode").combobox("setValue",null);
            $('.searchtable').trigger('click')
        });
    </script>
    <style>
        textarea { white-space: normal !important; }

        .formTable {
            width: 100%;
            margin-top: 0px;
            border-spacing: 0;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
        }
        .formTable>tbody>tr>td {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            color: #356885;
            font-weight: bold;
        }
        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text { border: none; font-size: 13px; }
        .formTable td.lable { background-color: #ddf1fe; padding: 5px;  max-width: 100px; }
        .formTable td .textAndInput_readonly,
        .formTable td .textAndInput_readonly .validatebox-readonly { background-color: #fff; }
        /* input:read-only { background-color: #f7f7f7; } */

        .cselectorImageUL .btn,
        .cselectorImageUL input[type='file'] { right: 3px; top: -15px; }
        .cselectorImageUL input[type='file'] {display: inline-block;width: 60px !important;}
        textarea { line-height: 20px; letter-spacing: 1px; }
        .cselectorImageUL{width: 100%;}

        .dialog-button {
            text-align: center;
        }
        .dialog-button .l-btn {
            margin-left: 30px;
        }
        .uploadImageI {
            padding-top: 0px;
            font-weight: normal !important;
        }
        .cselectorImageUL .btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .input-select-td .textbox {
            width: 100% !important;
        }

        .blockTitle {
            border-left: 5px solid #f79e40;
            padding: 5px 0px 5px 10px;
            margin: 10px 0px;
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
        }

        .radio-td .cselectorRadioUL {
            text-align: center;
        }

        .radio-td .cselectorRadioUL a {
            font-size: 17px;
            margin: 0 10px;
        }
    </style>
</head>

<body class="body_page">
    <form id="glyTableQueryForm" enctype="multipart/form-data" charset=utf-8" method="post">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="8%"></td>
                <td width="20%"></td>
                <td width="8%"></td>
                <td width="20%"></td>
                <td width="40%"></td>
            </tr>
            <tr>
                <td width="100" align="right">归属组织：</td>
                <td width="200" class="input-select-td">
                    <input id="belongCompanyCode" name="belongCompanyCode" type="text" style="height:
                    32px;" />
                </td>
                <td width="200" align="right">上报人姓名：</td>
                <td width="100"><input id="truename" name="truename" type="text" /></td>
                <td width="360"></td>
            </tr>
            <tr>
                <td width="100" align="right">网格名称：</td>
                <td width="200">
                    <input id="gridName" name="gridName" type="text"/>
                </td>
                <td width="100" align="right">上报时间：</td>
                <td width="200">
                    <input id="sdate" name="sdate" style="width:42%;height: 32px;" type="text"
                           validType="startDateCheck['edate','sdate']"
                           class="easyui-datetimebox"> </input> &nbsp;至&nbsp;
                    <input id="edate" name="edate" style="width:42%;height: 32px;" type="text"
                           validType="endDateCheck['sdate','edate']"
                           class="easyui-datetimebox"> </input>
                </td>
                <td width="360">
                    <div class="fr">
                        <a class="btn fl searchtable"><span>查询</span></a>
                        <a class="btn fl ml10 formreset"><span>重置</span></a>
                        <a class="btn fl ml10 addBtn"><span>新增</span></a>
                        <a class="btn fl ml10 exporttable">
                            <font>导出</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="glyTable">
        <table id="glyTable"></table>
    </div>

</body>

</html>