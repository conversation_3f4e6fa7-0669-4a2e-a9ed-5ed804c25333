package com.simbest.boot.djfupt.util;

import com.google.common.collect.Maps;
import com.simbest.boot.djfupt.util.BpsConfig;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.wf.login.WorkFlowBpsLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class BpsLoginUtil {

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private WorkFlowBpsLoginService workFlowBpsLoginService;

    public void bpsLogin(String username) {
        boolean bpsTenant = Boolean.valueOf(bpsConfig.bpsTenant);
        String bpsTenantId = bpsConfig.bpsTenantId;
        Map<String,Object> map = Maps.newConcurrentMap();
        map.put("tenant", bpsTenant );
        map.put("userName",  username );
        map.put("tenantId", bpsTenantId );
        workFlowBpsLoginService.bpsLogin( map );
    }

}