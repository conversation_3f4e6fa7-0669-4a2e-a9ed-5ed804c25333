﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>选择人</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<div style="padding-right:240px;">
<!--searchform-->
<form id="userTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">用户姓名：</td><td width="150"><input name="username" type="text" value="" /></td>
            <td width="90" align="right">移动电话：</td><td width="150"><input name="preferredMobile" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>  
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="userTable"><table id="userTable"></table></div>
</div>
<div class="role orgC"></div>
<script type="text/javascript">
	var gps=getQueryString();
	$(function(){
		//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
		var pageparam={
			"listtable":{
				"listname":"#userTable",//table列表的id名称，需加#
				"querycmd":"uums/sys/userinfo/findRoleNameIsARoleDim?appcode="+web.appCode,//table列表的查询命令
				"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
				"nowrap": true,//把数据显示在一行里,默认true
				"idField":"username",
				"queryParams":{"appcode":web.appCode},
				"checkboxall":true,
				"frozenColumns":[[
					{ field: "ck",checkbox:true}
				]],//固定在左侧的列
				"columns":[[//列   
					{ title: "登录标识", field: "username", width: 60},
					{ title: "用户姓名", field: "truename", width: 60},
					{ title: "员工号", field: "employeeNumber", width: 60 },//排序sortable: true
					{ title: "移动电话", field: "preferredMobile", width: 60 },
					{ title: "所属公司", field: "orgName", width: 120}
				] ],
				"onLoadSuccess":function(data){
					loadSuccess();
				},
				"onBeforeCheck":function(index, row){
					if(gps.multi==0){
						$("#userTable").datagrid("clearChecked");
						$(".role").html("");
					}
				},
				"onCheck":function(index, row){
					if($(".role a#"+row.username).length==0) $(".role").append("<a id='"+row.username+"'><font>"+row.truename+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
				},
				"onUncheck":function(index, row){
					if($(".role a#"+row.username).length>0) $(".role a#"+row.username).remove();
				}
			}
		};
		loadGrid(pageparam);
		$(document).on("click",".role a i",function(){
			$(this).parent("a").remove();
		});
	});
	//是否有该条数据
	function getObjects(idas,keyas){
		var a=false;
		var ids=idas.split(",");
		var idkeys=keyas.split(",");
		$(".role a").each(function(i,v){	
			var b=0;
			for(var i in ids){			
				if ($(v).attr(idkeys[i]) == ids[i]) {
					b++;
				}
			}
			if(b==ids.length) {
				a=true;
				return false;
			}
		});
		return a;
	}; 
	//数据加载成功
	function loadSuccess(){
		var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
		for(var i in chooseRow){
			if($(".role a#"+chooseRow[i].id+"[name="+chooseRow[i].name+"]"))$(".role").append("<a name='"+chooseRow[i].name+"' id='"+chooseRow[i].id+"'><font>"+chooseRow[i].name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");	
			var a=$("#userTable").datagrid("getRowIndex",chooseRow[i].id);
			$("#userTable").datagrid("checkRow",a);
		}
	};
	window.getchoosedata=function(){
		var datas=[];
		$(".role a").each(function(i,v){
			var data={};
			data.id=$(v).attr("id");
			data.name=$(v).children("font").html();
			datas.push(data);
		});
		return {"data":datas,"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
	};
</script>
</body>
</html>
