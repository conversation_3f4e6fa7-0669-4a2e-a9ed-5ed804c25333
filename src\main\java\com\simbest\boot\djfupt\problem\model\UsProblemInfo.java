package com.simbest.boot.djfupt.problem.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_problem_info")
@ApiModel(value = "问题上报")
public class UsProblemInfo extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;


    @Column(length = 40)
    @ApiModelProperty(value = "单据ID", required = true)
    protected String pmInsId;               //主单据ID  关联us_pm_instence表中的单据ID


    @Column(length = 40)
    @ApiModelProperty(value = "发起人", required = true)
    private String applyUser;                   //发起人

    @Column(length = 500)
    @ApiModelProperty(value = "发起人组织", required = true)
    private String belongOrgName;                    //发起人组织

    @Column(length = 40)
    @ApiModelProperty(value = "发起人联系方式", required = true)
    private String applyUserPhone;                   //发起人联系方式


    @Column(length = 40)
    @ApiModelProperty(value = "问题名称", required = true)
    private String problemName;                   //问题名称


    @Column(length = 40)
    @ApiModelProperty(value = "问题具体描述", required = true)
    private String problemDescribe;                   //问题具体描述

    @Column(length = 40)
    @ApiModelProperty(value = "流程类型", required = true)
    private String type;

    @Column(length = 40)
    @ApiModelProperty(value = "问题上报方式", required = true)
    private String questionMode;   // 0问题上报   1问题存储

    @Column(length = 500)
    @ApiModelProperty(value = "问题处理反馈", required = true)
    private String problemFeedback;


    @Column(length = 500)
    @ApiModelProperty(value = "协调推动情况", required = true)
    private String situation;

    @Column(length = 40)
    @ApiModelProperty(value = "反馈发起人", required = true)
    private String feedBackApplyUser;                   //发起人

    @Column(length = 500)
    @ApiModelProperty(value = "反馈发起人组织", required = true)
    private String feedBackBelongOrgName;                    //发起人组织

    @Column(length = 40)
    @ApiModelProperty(value = "反馈发起人联系方式", required = true)
    private String feedApplyUserPhone;                   //发起人联系方式



    @Column(length = 500)
    @ApiModelProperty(value = "期望完成时间", required = true)
    private String expectedCompletionTime;


    @Column(length = 500)
    @ApiModelProperty(value = "state", required = true)
    private int state=0;   //0不可判断定时   1可判断定时任务

    @Column(length = 30)
    @ApiModelProperty(value = "是否是草稿", required = true)
    private String isDraft; //2是，1不是



    @Transient
    List<SysFile> drawFiles;                //附件


    @Transient
    List<SysFile> feedBackFiles;                //协调反馈附件

    @Column(length = 500)
    private  String belongCompanyCodeParent;//上级code

    @Transient
    private  String startTime;

    @Transient
    private  String endTime;

    @Transient
    private  String companyName;



}
