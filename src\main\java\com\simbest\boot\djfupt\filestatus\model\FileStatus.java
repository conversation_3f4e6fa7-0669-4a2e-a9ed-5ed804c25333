package com.simbest.boot.djfupt.filestatus.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * <strong>Title : FileStatus</strong><br>
 * <strong>Description : 文件状态实体类</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_file_status")
@ApiModel(value = "文件状态")
public class FileStatus extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "FIST")         //主键前缀，此为可选项注解
    private String id;

    @Column(name = "file_id", length = 40)
    @ApiModelProperty(value = "文件ID")
    private String fileId;

    @Column(name = "status", length = 20)
    @ApiModelProperty(value = "文件状态")
    private String status;

    @Column(name = "file_name", length = 200)
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @Column(name = "file_type", length = 50)
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @Column(name = "file_size", length = 20)
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    @Column(name = "download_count")
    @ApiModelProperty(value = "下载次数")
    private Integer downloadCount;

    @Column(name = "upload_time")
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    @Column(name = "is_public")
    @ApiModelProperty(value = "是否公开")
    private Boolean isPublic;
}
