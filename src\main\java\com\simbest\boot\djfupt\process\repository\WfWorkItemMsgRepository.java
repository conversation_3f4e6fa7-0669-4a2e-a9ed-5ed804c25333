package com.simbest.boot.djfupt.process.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <strong>Title :SysProcessInfoRepository </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/6/22</strong><br>
 * <strong>Modify on : 2022/6/22</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */

public interface WfWorkItemMsgRepository extends LogicRepository<WfOptMsgModel, String> {


    @Query(
            value = "select t.*" +
                    "  from WF_OPTMSG_MODEL t" +
                    " where t.enabled = 1" +
                    "   and t.receipt_code =:pmInsId" +
                    "   and t.workitemid in (:workItemList)",
            nativeQuery = true
    )
    List<WfOptMsgModel> findAllMsg(@Param("pmInsId") String pmInsId, @Param("workItemList") List<String> workItemList);

}
