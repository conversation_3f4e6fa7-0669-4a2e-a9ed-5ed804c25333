package com.simbest.boot.djfupt.findfaults.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.findfaults.model.UsAppNameModel;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsIdea;
import com.simbest.boot.djfupt.findfaults.repository.UsAppNameRepository;
import com.simbest.boot.djfupt.findfaults.service.ISysAppNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SysAppNameServiceImpl extends LogicService<UsAppNameModel, String> implements ISysAppNameService {

    private UsAppNameRepository repository;

    @Autowired
    public SysAppNameServiceImpl(UsAppNameRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public JsonResponse findAllAppName(Integer page, Integer size, Map<String, String> map) {
        String appName = MapUtil.getStr(map, "appName");
        Specification<UsAppNameModel> specification = Specifications.<UsAppNameModel>and()
                .eq("enabled", Boolean.TRUE)
                .like(StrUtil.isNotEmpty(appName), "appName", "%" + appName + "%")
                .build();
        Pageable pageable = getPageable(page, size, String.valueOf(Sort.Direction.DESC), "createdTime");
        Page<UsAppNameModel> all = this.findAll(specification, pageable);
        return JsonResponse.success(all);
    }

    @Override
    public JsonResponse getAllAppNameNoPage() {
        Specification<UsAppNameModel> specification = Specifications.<UsAppNameModel>and()
                .eq("enabled", Boolean.TRUE)
                .build();
        return JsonResponse.success(this.findAllNoPage(specification));
    }
}
