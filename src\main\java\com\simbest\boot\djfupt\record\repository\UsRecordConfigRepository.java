package com.simbest.boot.djfupt.record.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.record.model.UsRecordConfig;
import com.simbest.boot.djfupt.record.model.UsRecordFill;

import java.util.List;

public interface UsRecordConfigRepository extends LogicRepository<UsRecordConfig, String> {


    List<UsRecordConfig> findAllByEnabledOrderByCreatedTimeDesc(Boolean enabled);

}
