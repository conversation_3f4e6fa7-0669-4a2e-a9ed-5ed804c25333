package com.simbest.boot.djfupt.record.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.djfupt.record.model.UsTalkContent;
import com.simbest.boot.djfupt.record.service.IUsTalkContentService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(description = "谈话内容")
@Slf4j
@RestController
@RequestMapping(value = "/action/uTalkContent")
public class UsTalkContentController extends LogicController<UsTalkContent, String> {
    private IUsTalkContentService service;

    @Autowired
    public UsTalkContentController(IUsTalkContentService UsTalkContentService) {
        super(UsTalkContentService);
        this.service = UsTalkContentService;
    }
}


