package com.simbest.boot.djfupt.wfquey.service.impl;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.wfquey.service.IQueryProcessHistoryService;
import com.simbest.boot.djfupt.wfquey.service.IWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WorkOrderServiceImpl implements IWorkOrderService {
    /**
     * 工单查询
     * @param page
     * @param rows
     * @return/
     */
    @Override
    public JsonResponse queryWorkOrder(Integer page, Integer rows) {
        String sql="";
        return null;
    }
}
