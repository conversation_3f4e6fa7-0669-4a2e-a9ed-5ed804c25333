package com.simbest.boot.djfupt.filestatus.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.filestatus.model.FileStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <strong>Title : IFileStatusService</strong><br>
 * <strong>Description : 文件状态服务层接口</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
public interface IFileStatusService extends ILogicService<FileStatus, String> {

    /**
     * 更新文件状态
     * @param fileStatus 文件状态对象
     * @return 更新后的文件状态
     */
    FileStatus updateFileStatus(FileStatus fileStatus);

    /**
     * 根据文件ID查询文件状态
     * @param fileId 文件ID
     * @return 文件状态对象
     */
    FileStatus findByFileId(String fileId);

    /**
     * 分页查询文件状态
     * @param fileStatus 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<FileStatus> findAllInfo(FileStatus fileStatus, Pageable pageable);

    /**
     * 切换文件状态
     * @param fileId 文件ID
     * @return 更新后的文件状态
     */
    FileStatus toggleFileStatus(String fileId);
}
