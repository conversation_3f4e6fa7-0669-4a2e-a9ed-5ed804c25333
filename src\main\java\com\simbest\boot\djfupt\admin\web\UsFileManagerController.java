package com.simbest.boot.djfupt.admin.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsFileManager;
import com.simbest.boot.djfupt.admin.service.IUsFileManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;



/**
 * 网格监督员管理相关接口
 */
@Api(description = "文件管理相关接口")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/usFileManager")
public class UsFileManagerController extends LogicController<UsFileManager, String> {

    private IUsFileManagerService usFileManagerService;

    @Autowired
    public UsFileManagerController(IUsFileManagerService usFileManagerService) {
        super(usFileManagerService);
        this.usFileManagerService = usFileManagerService;
    }

    @ApiOperation(value = "文件管理(分页)", notes = "文件管理(分页)")
    @PostMapping(value = {"/findListByPage", "/sso/findListByPage", "/api/findListByPage"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页容量", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "direction", value = "排序方向asc/desc", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序属性", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "文件标题", dataType = "String", paramType = "query"),

    })
    public JsonResponse findListByPage(@RequestParam(required = false, defaultValue = "1") int page,
                                       @RequestParam(required = false, defaultValue = "10") int size,
                                       @RequestParam(required = false, defaultValue = "desc") String direction,
                                       @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                       @RequestParam(required = false) String title

    ) {
        try {
            Page<UsFileManager> pageList = usFileManagerService.findListByPage(page, size, direction, properties,title);
            return JsonResponse.success(pageList);
        } catch (Exception e) {
            log.error("文件管理分页查询接口异常，{}", e);
            return JsonResponse.fail("接口异常！");
        }
    }

    @ApiOperation(value = "文件新增")
    @PostMapping(value = {"/insert", "/insert/sso", "/insert/api"})
    public JsonResponse insert(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                               @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                               @RequestBody UsFileManager usFileManager) {
        return usFileManagerService.insertInfo(source,currentUserCode,usFileManager);
    }

    @ApiOperation(value = "文件修改")
    @PostMapping(value = {"/updateInfo", "/updateInfo/sso", "/updateInfo/api"})
    public JsonResponse updateInfo(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                   @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                   @RequestBody UsFileManager usFileManager) {
        return usFileManagerService.updateInfo(source,currentUserCode,usFileManager);
    }

    @ApiOperation(value = "文件信息删除")
    @PostMapping(value = {"/delInfo", "/delInfo/sso", "/delInfo/api"})
    public JsonResponse delInfo(@RequestParam String id) {
        return usFileManagerService.delInfo(id);
    }

    @ApiOperation(value = "根据id获取文件信息")
    @PostMapping(value = {"/getById", "/getById/sso", "/getById/api"})
    public JsonResponse getById(@RequestParam String id) {
        return JsonResponse.success(usFileManagerService.getById(id));
    }
}
