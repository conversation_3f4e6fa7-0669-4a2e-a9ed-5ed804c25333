package com.simbest.boot.djfupt.admin.service;


import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;

import java.util.List;
import java.util.Map;

public interface WorkProgressDashboardService  {
    Map<String, Map<String, Object>> findPolicyLederTotal(String currentUserCode, String source);

    DataScreeningVo dataScreening(String currentUserCode, String source, Map<String, Object> resultMap);

    JsonResponse workProgress();
}
