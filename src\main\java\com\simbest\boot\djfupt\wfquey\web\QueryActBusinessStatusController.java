package com.simbest.boot.djfupt.wfquey.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.wfquey.service.IQueryActBusinessStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用途：查询代办已办
 * 作者： zsf
 * 时间： 2018/07/05
 */
@Api(description = "查询待办,已办,待阅,已阅,wide申请相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/queryActBusinessStatus")
public class QueryActBusinessStatusController {

    @Autowired
    private IQueryActBusinessStatusService queryActBusinessStatus;

    /**
     * 查询我的待办
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询待办", notes = "查询我的待办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/myTaskToDo", "/api/myTaskToDo"})
    public JsonResponse myTaskToDo(@RequestParam Integer page,
                                   @RequestParam Integer rows,
                                   @RequestParam String source,
                                   @RequestParam(required = false) String title,
                                   @RequestParam(required = false) String workCode,
                                   @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.myTaskToDo(page, rows, title, workCode, source, currentUserCode);
    }


    /**
     * 查询我的待办
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询待办", notes = "查询我的待办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/myTaskToDoDj", "/api/myTaskToDoDj"})
    public JsonResponse myTaskToDoDj(@RequestParam Integer page,
                                   @RequestParam Integer rows,
                                   @RequestParam String source,
                                   @RequestParam(required = false) String title,
                                   @RequestParam(required = false) String workCode,
                                   @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.myTaskToDoDj(page, rows, title, workCode, source, currentUserCode);
    }




    /**
     * 查询我的已办
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询已办", notes = "查询我的已办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyJoin", "/api/queryMyJoin"})
    public JsonResponse queryMyJoin(@RequestParam Integer page,
                                    @RequestParam Integer rows,
                                    @RequestParam String source,
                                    @RequestParam(required = false) String title,
                                    @RequestParam(required = false) String workCode,
                                    @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.queryMyJoin(page, rows, title,workCode, source, currentUserCode);
    }

    /**
     * 查询我的申请
     *
     * @param page
     * @param rows
     * @param title
     * @return
     */
    @ApiOperation(value = "查询我的申请", notes = "查询我的申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = "/queryMyApply")
    public JsonResponse queryMyApply(@RequestParam Integer page, @RequestParam Integer rows, @RequestParam(required = false) String title) {
        return JsonResponse.success(queryActBusinessStatus.queryMyApply(page, rows, title));
    }


    /**
     * 查询我的待阅
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询我的待阅", notes = "查询我的待阅")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyPending", "/api/queryMyPending"})
    public JsonResponse queryMyPending(@RequestParam Integer page,
                                       @RequestParam Integer rows,
                                       @RequestParam String source,
                                       @RequestParam(required = false) String title,
                                       @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.queryMyPending(page, rows, title, source, currentUserCode);
    }

    /**
     * 查询我的已阅
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询我的已阅", notes = "查询我的已阅")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyRead", "/api/queryMyRead"})
    public JsonResponse queryMyRead(@RequestParam Integer page,
                                    @RequestParam Integer rows,
                                    @RequestParam String source,
                                    @RequestParam(required = false) String title,
                                    @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.queryMyRead(page, rows, title, source, currentUserCode);
    }


    /**
     * 查询草稿
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "查询草稿", notes = "查询草稿")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/myDraftToDo", "/api/myDraftToDo"})
    public JsonResponse myDraftToDo(@RequestParam Integer page,
                                    @RequestParam Integer rows,
                                    @RequestParam String source,
                                    @RequestParam(required = false) String title,
                                    @RequestParam(required = false) String currentUserCode) {
        return queryActBusinessStatus.myDraftToDo(page, rows, title, source, currentUserCode);
    }

}
