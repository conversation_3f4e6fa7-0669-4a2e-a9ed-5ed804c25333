package com.simbest.boot.djfupt.xtgj.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.djfupt.xtgj.model.UsLhgzhdItemInfo;

import java.util.List;

public interface IUsLhgzhdItemInfoService extends ILogicService<UsLhgzhdItemInfo, String> {

    UsLhgzhdItemInfo insertInfo(UsLhgzhdItemInfo o);

    void deleteAllByPid(String pmInsId);

    List<UsLhgzhdItemInfo> deleteAndAddInfo(String pid, List<UsLhgzhdItemInfo> list);

    List<UsLhgzhdItemInfo> findAllByPid(String pid);
}
