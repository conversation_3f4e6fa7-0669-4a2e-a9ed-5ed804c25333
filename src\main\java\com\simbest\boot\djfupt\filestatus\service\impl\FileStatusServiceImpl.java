package com.simbest.boot.djfupt.filestatus.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.djfupt.filestatus.model.FileStatus;
import com.simbest.boot.djfupt.filestatus.repository.FileStatusRepository;
import com.simbest.boot.djfupt.filestatus.service.IFileStatusService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <strong>Title : FileStatusServiceImpl</strong><br>
 * <strong>Description : 文件状态服务层实现</strong><br>
 * <strong>Create on : 2025-05-23</strong><br>
 */
@Slf4j
@Service
public class FileStatusServiceImpl extends LogicService<FileStatus, String> implements IFileStatusService {

    private final FileStatusRepository repository;

    @Autowired
    public FileStatusServiceImpl(FileStatusRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    @Transactional
    public FileStatus updateFileStatus(FileStatus fileStatus) {
        if (fileStatus == null || fileStatus.getFileId() == null) {
            throw new RuntimeException("文件ID不能为空");
        }

        // 先查询是否存在该文件ID的记录
        FileStatus existingFileStatus = findByFileId(fileStatus.getFileId());
        IUser iuser = SecurityUtils.getCurrentUser();
        if (fileStatus.getCreator() == null){
            fileStatus.setCreator(iuser.getUsername());
        }
        if (fileStatus.getModifier() == null){
            fileStatus.setModifier(iuser.getUsername());
        }
        if (fileStatus.getCreatedTime() ==null){
            fileStatus.setCreatedTime(LocalDateTime.now());
        }
        fileStatus.setEnabled(true);

        if (existingFileStatus != null) {
            // 如果存在，更新状态
            existingFileStatus.setStatus(fileStatus.getStatus());
            return repository.save(existingFileStatus);
        } else {
            // 如果不存在，新增记录
            return repository.save(fileStatus);
        }
    }

    @Override
    public FileStatus findByFileId(String fileId) {
        return repository.findByFileId(fileId);
    }

    @Override
    public Page<FileStatus> findAllInfo(FileStatus fileStatus, Pageable pageable) {
        return repository.findAll((root, query, cb) -> {
            if (fileStatus == null) {
                return null;
            }
            return cb.and(
                fileStatus.getFileId() != null ? cb.equal(root.get("fileId"), fileStatus.getFileId()) : null,
                fileStatus.getStatus() != null ? cb.equal(root.get("status"), fileStatus.getStatus()) : null
            );
        }, pageable);
    }

    @Override
    @Transactional
    public FileStatus toggleFileStatus(String fileId) {
        FileStatus fileStatus = findByFileId(fileId);
        if (fileStatus == null) {
            throw new RuntimeException("文件状态不存在");
        }
        // 切换状态
        fileStatus.setStatus("true".equals(fileStatus.getStatus()) ? "false" : "true");
        return repository.save(fileStatus);
    }
}
