package com.simbest.boot.djfupt.util;

import java.security.MessageDigest;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * 〈〉
 * 获取密钥工具类
 * <AUTHOR>
 * @create 2023/7/5
 * @since 1.0.0
 */
public class SignatureUtil {
    private static final String APP_KEY = "dee756dd2b78c712e395c8668134915c";
    private static final String APP_SECRET = "f0fe944abbf9a63f6028047c5972b868";


    /**
     *
     * @param timestamp yyyyMMddHHmmssSSS
     * @return
     */
    public static String signature(String timestamp) throws Exception {
        Map<String,Object> map=new TreeMap<>();
        map.put("appkey",APP_KEY);
        map.put("timestamp",timestamp);
        StringBuffer s = new StringBuffer();
        String s1 = transMapToString(map);
        s.append(s1).append(APP_SECRET);
        return shaEncode(s.toString());

    }
    private static String transMapToString(Map map){
        Map.Entry entry;
        StringBuffer sb = new StringBuffer();
        for(Iterator iterator = map.entrySet().iterator(); iterator.hasNext();)
        {
            entry = (Map.Entry)iterator.next();
            sb.append(entry.getKey().toString()).append( "'" ).append(null==entry.getValue()?"":
                    entry.getValue().toString()).append (iterator.hasNext() ? "^" : "");
        }
        return sb.toString();
    }
    private static String shaEncode(String inStr) throws Exception {
        MessageDigest sha = null;
        try {
            sha = MessageDigest.getInstance("SHA");
        } catch (Exception e) {
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }

        byte[] byteArray = inStr.getBytes("UTF-8");
        byte[] md5Bytes = sha.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < md5Bytes.length; i++) {
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
    }

}
