package com.simbest.boot.djfupt.findfaults.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.findfaults.model.UsFindFaultsModel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface UsFindFaultsRepository extends LogicRepository<UsFindFaultsModel,String> {

    @Transactional
    @Query(
            value = "select a.* from Us_Find_Faults_Model a where a.enabled = 1 and a.pm_ins_id =:id",
            nativeQuery = true
    )
    UsFindFaultsModel getFromDetail(@Param("id") String id);


}
