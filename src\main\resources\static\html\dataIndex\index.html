
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <title>访客统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <meta charset="UTF-8">
    <meta name="ctx" th:content="${#httpServletRequest.getContextPath()}" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=69562" rel="stylesheet"/>
    -->
    <link rel="stylesheet" href="../../fonts/iconfont/iconfont.css?v=69562" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=69562" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=69562" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=69562" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/index.css?v=69562" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=69562" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=69562" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=69562" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.calendar.js?v=69562" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=69562" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=69562" type="text/javascript"></script>
</head>
<style>
    .top {
			background-image: linear-gradient(to right, #d61a07, #f63521);
		}

		.top_right strong {
			color: white;
		}

		.top_right i {
			color: white;
		}

		ul.nav1 li a font {
			color: #000;
			font-weight: normal;
		}

		ul.nav1 li a.a_hover {
			background: #e02020;
			color: #fff;
		}

		ul.nav1 li a:hover font,
		ul.nav1 li a.a_hover font {
			color: #fff;
		}

		ul.nav1 li ul.nav2 {
			background: rgb(250, 250, 250);
		}

		ul.nav1 li a:hover {
			background: #e02020;
			border-left: 5px solid #e02020;
		}

		ul.nav1 li a i {
			color: #e02020
		}

		ul.nav1 li a:hover i,
		ul.nav1 li a.a_hover i {
			color: #fff
		}

		ul.nav2 li a:hover i {
			color: #e02020
		}

		.right_tab a {
			/* background: #FAEFEF; */
			background: #fff;
		}

		.right_tab a.a_hover {
			background: #fff;
			color: #333;
			border-top: 2px solid #e02020;
		}

		.datagrid-header {
			background: #F9E1E1 !important;
		}

		.datagrid-row-alt {
			background: #FFF9F1 !important;
		}

		.datagrid-row {
			background: #FAFAFA !important;
		}

		ul.nav1 li ul.nav2 li a font {
			color: #333333;
		}

		ul.nav1 li ul.nav2 li a {
			margin-left: 0px;
			padding-left: 28px;
			width: 100%;
			border: 0;
			border-bottom: 1px solid rgb(243, 243, 243);
			background: #ffffff;
		}

		ul.nav1 li ul.nav2 li a:hover {
			padding-left: 28px;
		}

		ul.nav1 li ul.nav2 li a.a_hover i {
			color: #e02020;
		}
		ul.nav1 li ul.nav2 li a.a_hover font{
			color: #e02020;
		}

		.dialog-button .l-btn{
			background-color: #e02020;
		}

		.green{border-color:#eea236;padding:0;}
		.green .window-header{background:#eea236;padding-top:10px;}
		.green .panel-header{border-color:#eea236;}
		.green .panel-tool-close{background: url('images/panel_toolsO.png') no-repeat -16px 0px;}
		.tooltip {
			background-color: #f9e1e1;
			border-color: #d31805;
			color: #000000;
		}

		ul.nav1 li ul.nav2 li ul.nav3{background: #fff;}
</style>
<body>
<div class="center">
    <!--left-->
    <div class="left">
        <div class="left_menu">
            <ul class="nav1">
            </ul>
        </div>
    </div>
    <!--right-->
    <div class="right">
        <div class="right_tab">
            <!-- <a id="li_home" class="a_hover"><i class="fl iconfont">&#xe691;</i><font>首页</font></a> -->
        </div>
        <div>
            <!-- <iframe id="li_home_if" name="welcome" class="iframe_first" th:src="@{/html/index/wgDetail.html}" frameborder="0" scrolling="auto"></iframe> -->
            <iframe id="li_home_if" name="welcome" class="iframe_first"  frameborder="0" scrolling="auto"></iframe>
        </div>
    </div>

</div>

<script type="text/javascript">
    var gps=getQueryString();
    var ih;
    var id=gps.id;
    var InteriorPlanSub;
    var isManager;
    var supervisor;
    top.indexData = gps  //存到gps里
    $(function () {
        getCurrent();
        var username=web.currentUser.username;
        ajaxgeneral({
            url:"uums/sys/userinfo/findPermissionByAppUser?appcode="+web.appCode+"&username="+username,
            loading:true,
            success:function(data){
                if(data.data) {
                    var datai = data.data.sort(function (a, b) {
                        return (a.displayOrder > b.displayOrder) ? 1 : -1;
                    });
                    var dataMenu = []
                    for (var i in  datai){
                        if(datai[i].url =='html/dataIndex/VisitorAnalysis.html' || datai[i].url =='html/dataIndex/adminManagement.html'){
                            dataMenu.push(datai[i])
                        }
                    }
                    datai = dataMenu
                    // console.log(datai);

                    if(datai.length == 0 ){
                        return top.mesShow("温馨提示！", "暂无权限！", 2000, "red")
                    }



                    var datas = toTreeData(datai, "id", "parentId", "id,parentId,description,url,icon");
                    var htmls = menuH(datas, 1);
                    $(".left_menu").html(htmls);
                    $('.left_menu .nav1 li:eq(0) a').trigger('click')
                }
            }
        });
        //打开首页计算高度
        $(".right").css("width", $(window).width() - 218);
        calculateHeight();
        $(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));
        //菜单事件
        $(document).on("click",".nav1 a",function (e) {
            // 重置布局位置，防止偏移
            resetLayout();

            var path = $(this).attr("path");

            //为空表示有多级菜单
            var $sib = $(this).parent().siblings();
            $(".nav1 a").removeClass("a_active");
            $sib.children("a").removeClass("a_hover").children("i.down").html("&#xe673;");
            $sib.children("ul").hide();
            $(this).addClass("a_active a_hover");
            if (path != "") {
                $(this).parents("ul").prev("a").addClass("a_hover");
                if ($("#li_" + $(this).attr("id")).length > 0) {
                    $("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
                    $("#li_" + $(this).attr("id") + "_if").show().siblings().hide();
                    $(".right_tab a i.fr").removeClass("i_color");
                    $("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");

                    $("#li_" + $(this).attr("id")).prependTo(".right_tab");
                    // 只有当iframe不存在或src为空时才重新加载
                    var $iframe = $("#li_" + $(this).attr("id") + "_if");
                    if ($iframe.length === 0 || !$iframe.attr("src")) {
                        var tourl = web.rootdir+path + (path.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
                        $iframe.insertAfter("#li_home_if").attr("src", tourl);
                    } else {
                        $iframe.insertAfter("#li_home_if");
                    }
                }
                else {
                    $(".right iframe").hide();
                    //IE6、IE7样式
                    if ($.support.msie) {//($.browser.msie && ($.browser.version == "6.0") && !$.support.style) || ($.browser.msie && ($.browser.version == "7.0"))
                        $("<a id='li_" + $(this).attr("id") + "' class='a_hover'><i class='iconfont fr i_color'>&#xe63e;</i>" + $(this).html() + "</a>").prependTo(".right_tab");
                        $("#li_" + $(this).attr("id")).css("width", ($(this).children("font").text().length) * 18 + 15);
                    } else {
                        $("<a id='li_" + $(this).attr("id") + "' class='a_hover'>" + $(this).html() + "<i class='iconfont fr i_color'>&#xe63e;</i></a>").prependTo(".right_tab");
                    }
                    $("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
                    $(".right_tab a i.fr").removeClass("i_color");
                    $("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");
                    var url = $(this).attr("path");
                    var tourl = web.rootdir+url + (url.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
                    $("<iframe name='" + $(this).attr("id") + "' id='li_" + $(this).attr("id") + "_if' style='height:"+ih+"px;' src='" + tourl + "' frameborder='0'></iframe>").insertAfter("#li_home_if");
                    //$("#rightiframe").attr("src","${ctx}/" + tourl);
                }
            } else {
                $(this).children("i.down").html("&#xe674;");
                var $nextli = $(this).next("ul").children("li");
                $nextli.children("ul").hide();
                $nextli.children("a").removeClass("a_hover").children("i.down").html("&#xe674;");
                $next = $(this).next("ul");
                $next.slideDown(600, function () {
                    $next.show();
                });
            }
            e.stopPropagation();
        });
        //单击右侧顶部菜单选项
        $(document).on("click",".right_tab a", function () {
            // 重置布局位置，防止偏移
            resetLayout();

            $(".nav1 a").removeClass("a_hover");
            $(".nav1 a#" + $(this).attr("id").substr(3)).addClass("a_hover");
            $(".nav1 li ul").hide();
            $(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").show();
            $(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").prev().addClass("a_hover");
            $(this).addClass("a_hover").siblings().removeClass("a_hover");
            $(".right_tab a i.fr").removeClass("i_color");
            $("#" + $(this).attr("id") + " i.fr").addClass("i_color");
            $("#" + $(this).attr("id") + "_if").show().siblings().hide();
            //$(".nav1 a#" + $(this).attr("id").substr(3)).trigger("click");
        });

        //右键菜单强制刷新页面
        $(document).on("contextmenu", ".right_tab a", function (e) {
            e.preventDefault();
            var tabId = $(this).attr("id");
            if (tabId && tabId !== "li_home") {
                if (confirm("是否要强制刷新此页面？这将丢失页面上未保存的数据。")) {
                    var $iframe = $("#" + tabId + "_if");
                    if ($iframe.length > 0) {
                        var currentSrc = $iframe.attr("src");
                        if (currentSrc) {
                            // 移除旧的时间戳并添加新的时间戳
                            var baseSrc = currentSrc.split("?")[0];
                            var newSrc = baseSrc + "?tm=" + (new Date()).getTime();
                            $iframe.attr("src", newSrc);
                        }
                    }
                }
            }
            return false;
        });
        //单击右侧顶部菜单关闭按钮
        $(document).on("click",".right_tab a i.fr", function (e) {
            if ($(".right iframe:visible").attr("id") == $(this).parent().attr("id") + "_if") {
                if ($(this).parent().next().length > 0) {
                    $(this).parent().next().trigger("click");
                }
                else if ($(this).parent().prev().length > 0) {
                    $(this).parent().prev().trigger("click");
                }
            }
            $(this).parent().remove();
            $("#" + $(this).parent().attr("id") + "_if").remove();
            e.stopPropagation();
        });

    });
    //重置布局位置
    function resetLayout() {
        // 确保右侧内容区域位置正确
        $(".right").css({
            "left": "218px",
            "width": $(window).width() - 218
        });
        // 确保左侧菜单位置正确
        $(".left").css({
            "left": "0px",
            "width": "218px"
        });
    }

    //计算高度
    function calculateHeight() {
        // 先重置布局位置
        resetLayout();

        //var a = $(".left").height();
        var a = 335;
        var b = $(".right").height();
        var c = $(window).height() - 60;
        if (c < a || c < b) {
            if (a < b) {
                $(".center").css("height", b);
                $(".left").css("height", b);
                $(".left_menu").css("height", b);
                $(".right").css("height", b);
                $(".right iframe").css("height", b-47);
                ih=b-47;
            } else {
                $(".center").css("height", a);
                $(".left").css("height", a);
                $(".left_menu").css("height", a);
                $(".right").css("height", a);
                $(".right iframe").css("height", a-47);
                ih=a-47;
            }
        } else {
            $(".center").css("height", c);
            $(".left").css("height", c);
            $(".left_menu").css("height", c);
            $(".right").css("height", c);
            $(".right iframe").css("height", c-47);
            ih=c-47;
        }
    };
    $(window).resize(function () {
        $(".right").css("width", $(window).width() - 218);
        calculateHeight();
        $(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));
    });
</script>
</body>
</html>