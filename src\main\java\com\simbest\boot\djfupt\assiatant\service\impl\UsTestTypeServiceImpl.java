package com.simbest.boot.djfupt.assiatant.service.impl;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.assiatant.model.UsHistoryRecord;
import com.simbest.boot.djfupt.assiatant.model.UsTestType;
import com.simbest.boot.djfupt.assiatant.repository.UsTestTypeRepository;
import com.simbest.boot.djfupt.assiatant.service.IUsHistoryRecordService;
import com.simbest.boot.djfupt.assiatant.service.IUsTestTypeService;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.kms.KMSUtils;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleConfig;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UsTestTypeServiceImpl extends LogicService<UsTestType, String> implements IUsTestTypeService {
    private UsTestTypeRepository repository;

    @Autowired
    public UsTestTypeServiceImpl(UsTestTypeRepository repository) {
        super(repository);
        this.repository = repository;
    }
    @Autowired
    private KMSUtils kmsUtils;

    @Autowired
    private RsaEncryptor encryptor;

    @Resource
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    @Autowired
    private IUsHistoryRecordService iUsHistoryRecordService;

    private SimpleConfig djzs_Config;
    private SimpleConfig fdzl_Config;
    @PostConstruct
    private void init() {
        djzs_Config = uumsSysAppConfigApi.findAppConfigByStyle("djzs", "hadmin", Constants.APP_CODE);
        fdzl_Config = uumsSysAppConfigApi.findAppConfigByStyle("fdzl", "hadmin", Constants.APP_CODE);
    }

    @Override
    public JsonResponse send(Map<String,Object> paramMap) {
        List<String> returnList = new ArrayList<>();
        String content = MapUtil.getStr(paramMap,"content");
        String pmInsId = MapUtil.getStr(paramMap,"pmInsId");
        List<String> newKeyWords = kmsUtils.getKeyWords(content); //获取关键词分词数组
        Map<String,String> inputMap = new HashMap<>();
        IUser currentUser = SecurityUtils.getCurrentUser();

        UsHistoryRecord inputHistoryRecord = new UsHistoryRecord();
        inputHistoryRecord.setPmInsId(pmInsId);
        inputHistoryRecord.setType("1");
        inputHistoryRecord.setTextContent(content);
        inputHistoryRecord.setUserName(currentUser.getUsername());
        inputHistoryRecord.setTrueName(currentUser.getTruename());
        if(CollectionUtil.isNotEmpty(newKeyWords)){
            inputHistoryRecord.setInfo(newKeyWords.toString());
        }
        iUsHistoryRecordService.insert(inputHistoryRecord);


        //先判断组合
        if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("派发") && newKeyWords.contains("管理员")){ //查询党建助手派发管理员信息
            //提取公司
             String companyName = this.getCompanyName(newKeyWords);
             inputMap.put("companyName",companyName);
            returnList = this.callInterface("1", inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("系统管理员")){ //查询党建助手派发管理员信息
            returnList = this.callInterface("2",inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("接口")){ //查询党建助手派发管理员信息
            //提取公司
            String companyName = this.getCompanyName(newKeyWords);
            inputMap.put("companyName",companyName);
            //提取接口人类型
            String businessType = this.getBusinessType(newKeyWords);
            inputMap.put("businessType",businessType);
            returnList = this.callInterface("3",inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("工单") &&newKeyWords.contains("数量")){ //查询党建助手我发起的工单数量及工单标题
            inputMap.put("creator",SecurityUtils.getCurrentUserName());
            returnList = this.callInterface("4",inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("数量")
                && (newKeyWords.contains("接收") || newKeyWords.contains("完成") || newKeyWords.contains("超时") || newKeyWords.contains("率")) ) {
            //查询党建助手、公司接收任务、完成任务、超时任务、完成率数据
            //提取公司
            String companyName = this.getCompanyName(newKeyWords);
            inputMap.put("companyName",companyName);
            if(StringUtils.isNotEmpty(companyName)){
                if(companyName.equals("省公司")){
                    inputMap.put("type","1");
                } else {
                    inputMap.put("type","2");
                }
            }
            //提取接口人类型
            String companyCode = this.getCompanyCode(companyName);
            inputMap.put("companyCode",companyCode);
            returnList = this.callInterface("5",inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("助手") && newKeyWords.contains("详细信息")) { //查询党建工作助手、工单详细表单信息
             String titleList = newKeyWords.stream().collect(Collectors.joining(","));
             inputMap.put("titleList",titleList);
            returnList = this.callInterface("6",inputMap);
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("资源") && newKeyWords.contains("共享")) { //查询党建资源共享的一种新闻
            String newsType = this.getNewsType(newKeyWords);
            if(StringUtils.isNotEmpty(newsType)){
                inputMap.put("newsType",newsType);
                returnList = this.callInterface("7",inputMap);
            }
        } else if(newKeyWords.contains("附件")) { //根据附件名称，模糊查询附件
            String titleList = newKeyWords.stream().collect(Collectors.joining(","));
            inputMap.put("titleList",titleList);
                returnList = this.callInterface("8", inputMap);
        }

        if(CollectionUtil.isEmpty(returnList)){
            String returnStr = "请您具体描述问题~";
            returnList.add(returnStr);
        }
        for(String item:returnList){
            UsHistoryRecord outHistoryRecord = new UsHistoryRecord();
            outHistoryRecord.setPmInsId(pmInsId);
            outHistoryRecord.setType("0");
            outHistoryRecord.setTextContent(item);
            outHistoryRecord.setUserName(currentUser.getUsername());
            outHistoryRecord.setTrueName(currentUser.getTruename());
            iUsHistoryRecordService.insert(outHistoryRecord);
        }
        return JsonResponse.success(returnList);
    }



    @Override
    public JsonResponse test01(String content) {
        List<String> keyWords = kmsUtils.getKeyWords(content); //获取关键词分词数组
        return JsonResponse.success(keyWords);
    }

    public List<String> callInterface(String type ,Map<String,String> paramMap){
        List<String> returnList = new ArrayList<>();
        JsonResponse jsonResponse = null;
        StringBuffer strBuffer = new StringBuffer("");
        if("1".equals(type)){ //派发管理员
            jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getDistributeManagerList/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            int size = data.size();
            if(size==0){
                returnList.add("暂无人员信息");
                return returnList;
            }
            strBuffer.append("党建助手派发管理员一共"+size+"人，人员名单：");
            String perBuf = data.stream().map(item -> MapUtil.getStr(item, "disName")).collect(Collectors.joining(","));
            strBuffer.append(perBuf);
            strBuffer.append("。");
            returnList.add(strBuffer.toString());
        } else if("2".equals(type)) { //系统管理员
             jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getSysManagerList/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            int size = data.size();
            if(size==0){
                returnList.add("暂无人员信息");
                return returnList;
            }
            strBuffer.append("党建助手系统管理员一共"+size+"人，人员名单：");
            String perBuf = data.stream().map(item -> MapUtil.getStr(item, "name")).collect(Collectors.joining(","));
            strBuffer.append(perBuf);
            strBuffer.append("。");
            returnList.add(strBuffer.toString());
        } else if("3".equals(type)){ //接口人管理员
             jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getInterfaceManagerList/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            int size = data.size();
            if(size==0){
                returnList.add("暂无人员信息");
                return returnList;
            }
            strBuffer.append("党建助手"+paramMap.get("businessType") == null?"":paramMap.get("businessType")+"接口人"+"一共"+size+"人，人员名单：");
            String perBuf = data.stream().map(item -> MapUtil.getStr(item, "name")).collect(Collectors.joining(","));
            strBuffer.append(perBuf);
            strBuffer.append("。");
            returnList.add(strBuffer.toString());
        } else if ("4".equals(type)){
            jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getMeApplyWork/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            int size = data.size();
            if(size==0){
                returnList.add("您还没有申请过党建助手工单~");
                return returnList;
            }
            strBuffer.append("您一共发起了"+size+"个工单。");
            returnList.add(strBuffer.toString());
        } else if ("5".equals(type)){
            String companyName = paramMap.get("companyName");
            jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getReportInfo/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            Map<String,Object> data = (Map<String,Object>)jsonResponse.getData();
            if(data == null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            String auth = MapUtil.getStr(data, "auth");
            if("0".equals(auth)){
                returnList.add("暂无权限~");
                return returnList;
            }
            String receive = MapUtil.getStr(data, "receive");
            String finish = MapUtil.getStr(data, "finish");
            String over = MapUtil.getStr(data, "over");
            String complete = MapUtil.getStr(data, "complete");
            strBuffer.append("党建助手");
            strBuffer.append(StringUtil.isEmpty(companyName)?"":companyName);
            strBuffer.append("，");
            strBuffer.append("接收任务数量：");
            strBuffer.append(receive);
            strBuffer.append("，");
            strBuffer.append("完成任务数量：");
            strBuffer.append(finish);
            strBuffer.append("，");
            strBuffer.append("超时任务数量：");
            strBuffer.append(over);
            strBuffer.append("，");
            strBuffer.append("完成率");
            strBuffer.append(complete);
            strBuffer.append("。");
            returnList.add(strBuffer.toString());
        } else if ("6".equals(type)){
            jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getWorkInfo/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            int size = data.size();
            if(size==0){
                returnList.add("暂无结果~");
                return returnList;
            }
            int xh = 1;
            for(Map<String,Object> item:data){
                String context = xh+"、标题：${title}、填报状态：${state}、完成进度：${jd}、发起时间：${createdTime}、截至时间：${endTime};";
                String title = MapUtil.getStr(item,"title");
                int isSubmitNumber = MapUtil.getInt(item,"isSubmitNumber");
                int numbers = MapUtil.getInt(item,"numbers");
                String state = "";
                if(isSubmitNumber == numbers){
                    state = "已完成";
                } else {
                    state = "未完成";
                }
                String jd = isSubmitNumber+"/"+numbers;
                Long createdTimeLong = MapUtil.getLong(item,"createdTime");
                String createdTime = DateUtil.getDate(new Date(createdTimeLong), DateUtil.timestampPattern1);
                String endTime = MapUtil.getStr(item,"endTime");
                context =  context.replace("${title}", title)
                                    .replace("${state}", state)
                                    .replace("${jd}", jd)
                                    .replace("${createdTime}", createdTime)
                                    .replace("${endTime}", endTime);
                strBuffer.append(context);
                xh++;
            }
            returnList.add(strBuffer.toString());
        } else if ("7".equals(type)) {
            jsonResponse = HttpClient.textBody(fdzl_Config.getAddress() + "/action/Video/getNewsList/sso" + "?loginuser=" + encryptor.encrypt(SecurityUtils.getCurrentUserName()) + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            List<Map<String,Object>> data = (List<Map<String,Object>>)jsonResponse.getData();
            if(data==null){
                returnList.add("查询异常，请稍后再试~");
                return returnList;
            }
            if(data.size()==0){
                returnList.add("暂无结果~");
                return returnList;
            }
            StringBuffer returnStr = new StringBuffer("");
            int xh=1;
            for(Map<String,Object> item : data){
                String url = fdzl_Config.getAddress()+"/html/fontHome/detail.html?id="+MapUtil.getStr(item,"id")+"&isVideo=1";
                String title = MapUtil.getStr(item,"title");
                String rowStr = xh+"、<a href='" +url+ "' class='applyPath' >" +title+ "</a>";
                if(xh!=1){
                    returnStr.append("<br>");
                }
                returnStr.append(rowStr);
                xh++;
            }
            returnList.add(returnStr.toString());
        } else if ("8".equals(type)) {
             List<Map<String, Object>> data = this.syncMethod(Arrays.asList("1,2".split(",")), paramMap);
            if(data.size()==0){
                returnList.add("暂无结果~");
                return returnList;
            }
            StringBuffer returnStr = new StringBuffer("");
            int xh=1;
            for(Map<String,Object> item : data){
                String url = MapUtil.getStr(item,"anonymousFilePath");
                String fileName = MapUtil.getStr(item,"fileName");
                String rowStr = xh+"、<a href='" +url+ "' class='applyPath' >" +fileName+ "</a>";
                if(xh!=1){
                    returnStr.append("<br>");
                }
                returnStr.append(rowStr);
                xh++;
            }
            returnList.add(returnStr.toString());
        }
        return returnList;
    }

    public List<Map<String,Object>>  syncMethod(List<String> typeList,Map<String,String> paramMap){
        // 创建一个任务列表
        // 使用CompletableFuture异步执行任务并收集结果
        List<CompletableFuture<List<Map<String,Object>>>> futures = typeList.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> callApiMethod(task,paramMap)))
                .collect(Collectors.toList());
        // 等待所有任务完成并合并结果
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        CompletableFuture<List<Map<String,Object>>> combinedResult = allOf.thenApply(v ->
                futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList())
        );
        // 获取合并后的结果
        List<Map<String,Object>> result = null;
        try {
            result = combinedResult.get();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
    public  List<Map<String,Object>> callApiMethod(String type,Map<String,String> paramMap) {
        JsonResponse jsonResponse = null;
        List<Map<String,Object>> returnList = new ArrayList<>();
        if("1".equals(type)){ //查询fdzl附件
            jsonResponse = HttpClient.textBody(fdzl_Config.getAddress() + "/action/Video/getfileList/sso" + "?loginuser=" + encryptor.encrypt("hadmin") + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            returnList = (List<Map<String,Object>>)jsonResponse.getData();
        } else if ("2".equals(type)){ //查询djzs附件
            jsonResponse = HttpClient.textBody(djzs_Config.getAddress() + "/action/distributePersonManager/getfileList/sso" + "?loginuser=" + encryptor.encrypt("hadmin") + "&appcode=" + Constants.APP_CODE)
                    .json(JSONUtil.toJsonStr(paramMap))
                    .asBean(JsonResponse.class);
            returnList = (List<Map<String,Object>>)jsonResponse.getData();
        }
        return returnList;
    }

    public String getCompanyName(List<String> newKeyWords){
        String companyName = "";
        if(newKeyWords.contains("省")){
            companyName = "省公司";
        } else if(newKeyWords.contains("郑州")) {
            companyName = "郑州分公司";
        } else if(newKeyWords.contains("南阳")) {
            companyName = "南阳分公司";
        } else if(newKeyWords.contains("周口")) {
            companyName = "周口分公司";
        } else if(newKeyWords.contains("洛阳")) {
            companyName = "洛阳分公司";
        } else if(newKeyWords.contains("商丘")) {
            companyName = "商丘分公司";
        } else if(newKeyWords.contains("信阳")) {
            companyName = "信阳分公司";
        } else if(newKeyWords.contains("新乡")) {
            companyName = "新乡分公司";
        } else if(newKeyWords.contains("驻马店")) {
            companyName = "驻马店分公司";
        } else if(newKeyWords.contains("安阳")) {
            companyName = "安阳分公司";
        } else if(newKeyWords.contains("开封")) {
            companyName = "开封分公司";
        } else if(newKeyWords.contains("平顶山")) {
            companyName = "平顶山分公司";
        } else if(newKeyWords.contains("许昌")) {
            companyName = "许昌分公司";
        } else if(newKeyWords.contains("濮阳")) {
            companyName = "濮阳分公司";
        } else if(newKeyWords.contains("三门峡")) {
            companyName = "三门峡分公司";
        } else if(newKeyWords.contains("漯河")) {
            companyName = "漯河分公司";
        } else if(newKeyWords.contains("鹤壁")) {
            companyName = "鹤壁分公司";
        } else if(newKeyWords.contains("济源")) {
            companyName = "济源分公司";
        }
        return companyName;
    }

    public String getCompanyCode(String companyName){
        String companyCode = "";
        switch (companyName) {
            case "省公司":
                companyCode = "4772338661636601428";
                break;
            case "郑州分公司":
                companyCode = "4772276805467173986";
                break;
            case "南阳分公司":
                companyCode = "4772416258988872377";
                break;
            case "周口分公司":
                companyCode = "4772294881701191626";
                break;
            case "洛阳分公司":
                companyCode = "4772398387391759323";
                break;
            case "商丘分公司":
                companyCode = "4772190764623477349";
                break;
            case "信阳分公司":
                companyCode = "4772237158131728644";
                break;
            case "新乡分公司":
                companyCode = "4772222692656252440";
                break;
            case "驻马店分公司":
                companyCode = "4772309833745710077";
                break;
            case "安阳分公司":
                companyCode = "4772319337553908874";
                break;
            case "开封分公司":
                companyCode = "4772385069455370245";
                break;
            case "平顶山分公司":
                companyCode = "4772159046344071595";
                break;
            case "许昌分公司":
                companyCode = "4772253677731688102";
                break;
            case "濮阳分公司":
                companyCode = "4772436821803101973";
                break;
            case "三门峡分公司":
                companyCode = "4772173802809103642";
                break;
            case "漯河分公司":
                companyCode = "4772428442783965979";
                break;
            case "鹤壁分公司":
                companyCode = "4772356884662698724";
                break;
            case "济源分公司":
                companyCode = "4772365719866026009";
                break;
            case "焦作分公司":
                companyCode = "4772371646798367640";
                break;
            default:
                break;
        }
        return companyCode;
    }
    public String getBusinessType(List<String> newKeyWords){
        String businessType = "";
        if(newKeyWords.contains("默认") && newKeyWords.contains("接口")  ){
            businessType = "默认接口人";
        } else if(newKeyWords.contains("党费")) {
            businessType = "党费";
        } else if(newKeyWords.contains("团") && newKeyWords.contains("青")) {
            businessType = "团青";
        } else if(newKeyWords.contains("统战")) {
            businessType = "统战";
        } else if(newKeyWords.contains("宣传")  && newKeyWords.contains("思想")) {
            businessType = "宣传思想文化";
        } else if(newKeyWords.contains("基础") && newKeyWords.contains("党建")) {
            businessType = "基础党建";
        } else if(newKeyWords.contains("合力") && newKeyWords.contains("攻坚")) {
            businessType = "合力攻坚";
        } else if(newKeyWords.contains("党员") && newKeyWords.contains("教育")) {
            businessType = "党员教育培训";
        } else if(newKeyWords.contains("巡视") && newKeyWords.contains("整改")) {
            businessType = "巡视巡察整改";
        } else if(newKeyWords.contains("党员")) {
            businessType = "党员发展";
        } else if(newKeyWords.contains("党廉")) {
            businessType = "党廉（分公司）";
        } else if(newKeyWords.contains("嵌入式") && newKeyWords.contains("风险")) {
            businessType = "嵌入式风险防控（本部）";
        } else if(newKeyWords.contains("党建") && newKeyWords.contains("考评")) {
            businessType = "党建考评迎检";
        } else if(newKeyWords.contains("红色") && newKeyWords.contains("基地")) {
            businessType = "红色文化教育基地";
        } else if(newKeyWords.contains("豫") && newKeyWords.contains("奋发")) {
            businessType = "豫起奋发 担当作为";
        }
        return businessType;
    }
    public String getNewsType(List<String> newKeyWords) {
        String newsType = "";
        //党章党规、巡视巡察、制度文件、热点问题、红色教育基地、团青统战、培训资料、视频图片
        if(newKeyWords.contains("党章") && newKeyWords.contains("党规")){
            newsType = "党章党规";
        } else if(newKeyWords.contains("巡视") && newKeyWords.contains("巡察")){
            newsType = "巡视巡察";
        } else if(newKeyWords.contains("制度") && newKeyWords.contains("文件")){
            newsType = "制度文件";
        } else if(newKeyWords.contains("热点") && newKeyWords.contains("问题")){
            newsType = "热点问题";
        } else if(newKeyWords.contains("红色") && newKeyWords.contains("教育")){
            newsType = "红色教育基地";
        } else if(newKeyWords.contains("团青") && newKeyWords.contains("统战")){
            newsType = "团青统战";
        } else if(newKeyWords.contains("培训") && newKeyWords.contains("资料")){
            newsType = "培训资料";
        } else if(newKeyWords.contains("视频") && newKeyWords.contains("图片")){
            newsType = "视频图片";
        } else if(newKeyWords.contains("集团") && newKeyWords.contains("培训")){
            newsType = "集团培训";
        } else if(newKeyWords.contains("主题") && newKeyWords.contains("教育")){
            newsType = "主题教育";
        }
        return newsType;
    }
}

