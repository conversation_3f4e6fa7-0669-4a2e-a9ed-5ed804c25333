package com.simbest.boot.djfupt.record.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

@Data
public class RecordVo {
    @ExcelVOAttribute(name = "编号", column = "A")
    @Excel(name = "编号")
    private  int num;

    @ExcelVOAttribute(name = "公司名称", column = "B")
    @Excel(name = "公司名称")
    private  String companyName;


    private  String companyCode;

    @ExcelVOAttribute(name = "已完成次数", column = "D")
    @Excel(name = "已完成次数")
    private int nums;


    @ExcelVOAttribute(name = "应开展次数", column = "C")
    @Excel(name = "应开展次数")
    private int grids;

    @ExcelVOAttribute(name = "完成率", column = "E")
    @Excel(name = "完成率")
    private String completionRate;//完成率
}
