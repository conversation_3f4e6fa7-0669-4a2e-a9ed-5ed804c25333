package com.simbest.boot.djfupt.policy.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.djfupt.policy.model.UsPolicyExport;
import com.simbest.boot.djfupt.policy.model.UsPolicyInfo;
import com.simbest.boot.security.SimpleAppDecision;
import org.springframework.data.domain.Page;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IUsPolicyInfoService extends ILogicService<UsPolicyInfo, String> {


    /**
     * 政策宣讲更新附件
     *
     * @param usPolicyInfo
     */
    public void updateFileInfoById(UsPolicyInfo usPolicyInfo);

    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(UsPolicyInfo usPolicyInfo);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(UsPolicyInfo usPolicyInfo);


    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param bodyParam
     * @param formId          表单id
     * @return
     */
    JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String processDefId, Map<String, Object> bodyParam, String formId, String notificationId);

    /**
     * 政策宣讲---启动流程
     */
    JsonResponse startProcess(UsPolicyInfo usPolicyInfo, String nextUserName, String outcome, String message, String source, String currentUserCode, String processDefId);


    /**
     * 政策宣讲---提交流程
     */
    JsonResponse saveSubmitTask(UsPolicyInfo usPolicyInfo, String workItemId, String outcome, String message, String nextUserName, String location, String processDefId, String notificationId, String source, String currentUserCode);


    /**
     * 根据创建人和主单据ID获取信息
     * 获取明细信息使用
     */
    UsPolicyInfo findUsPolicyInfoInfo(@Param("creator") String creator, @Param("pmInsId") String pmInsId);

    /**
     * 获取表单详情 (location 当前环节 workItemId 工作项id 暂无用到)
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @param location      当前环节
     * @return
     */
    JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location);



    /**
     * 获取表单详情 (location 当前环节 workItemId 工作项id 暂无用到)
     * 党建大屏使用
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @param location      当前环节
     * @return
     */
    JsonResponse findFormDetail(Long processInstId, String workFlag, String source, String userCode, String pmInsId, String location,String workItemId);

    /**
     * 政策宣讲台账信息
     */
     List<Map<String, Object>> findPolicyLeder(Map<String, Object> resultMap);

    /**
     * 政策宣讲台账信息
     */
    List<Map<String, Object>> findPolicyLederOther(Map<String, Object> resultMap);



     List<Map<String, Object>> findPolicyLederOlds(Map<String, Object> resultMap);


    /**
     * 党建指导员政策宣讲完成情况使用
     */
    List<Map<String, Object>> findDpPolicyLeder(Map<String, Object> resultMap);





    /**
     * 政策宣讲台账信息
     * 仅包含应宣讲信息和已宣讲信息以及百分占比
     */
    List<Map<String, Object>> newFindPolicyLeder(Map<String, Object> resultMap);
    /**
     *  政策宣讲台账信息
     *  宣讲内容数量信息
     *  查询的时间段内的各单位的宣讲事项的总数
     *  只统计第二部时候的数据
     */
    List<Map<String, Object>> findPolicyCount(Map<String, Object> resultMap);



    /**
     * 政策宣讲台账--点击百分号后的明细数据
     *
     * @param resultMap
     * @return
     */

    public List<Map<String, Object>> findPolicyDeatilLeder(Map<String, Object> resultMap);


    /**
     * 政策宣讲台账--点击百分号后的明细数据
     *
     * @param resultMap
     * @return
     */

    public List<Map<String, Object>> findPolicyDeatilLederOther(Map<String, Object> resultMap);


    /**
     * 导出数据
     *
     * @param currentUserCode
     * @param request
     * @param response
     */

    void exportPolicyLeder(String currentUserCode, HttpServletRequest request, HttpServletResponse response, UsPolicyExport usPolicyExport );


    /**
     * 导出数据
     *
     * @param currentUserCode
     * @param request
     * @param response
     */

    void exportPolicyDeatilLeder(String currentUserCode, HttpServletRequest request, HttpServletResponse response, UsPolicyInfo  policyInfo);


    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @param processType    流程类型
     * @return
     */
    JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String processType);

    /**
     * 根据决策查找人员
     *
     * @param processInstId  流程实例id
     * @param sysAppDecision 决策对象
     * @param source         来源
     * @param userCode       用户OA账户
     * @return
     */
    JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision);


    List<WfOptMsgModel> findAllMsg(String pmInsId, List<String> workItemList);


    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    //党建智慧大屏  地图数据

    List<Map<String, Object>> findMapDetail(Map<String, Object> resultMap);




    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 政策宣讲数量
     * @return
     */
    List<Map<String,Object>> policyCount(Map<String, Object> resultMap);




    /**
     * 政策宣讲数量
     * @return
     */
    List<Map<String,Object>> policyAllCount(Map<String, Object> resultMap);



    /**
     * 获取当前登录人的相关信息
     *
     * @return
     */
    JsonResponse getUser();

    /**
     * 获取第一议题数据
     *
     * @return
     */
    JsonResponse selectAllPro(String source,String currentUserCode);



    /**
     * 支部近期
     * @return
     */
    JsonResponse selectAll(String source,String currentUserCode);


    /**
     * 第一议题
     * @return
     */
    JsonResponse selectCountYt(String source,String currentUserCode);

    /**
     * 支部近期
     * @return
     */
    JsonResponse selectCountZb(String source,String currentUserCode);

    Page<List<Map<String, Object>>> policyOrder(Map<String, Object> resultMap);

     List<Map<String, Object>> findLatest(Map<String, Object> resultMap);


    List<Map<String, Object>> findLatestOld(Map<String, Object> resultMap);


    JsonResponse workDynamicsList(String source, String currentUserCode);

    JsonResponse overallProgress(String source, String currentUserCode);

    JsonResponse companyCompletionRate(String source, String currentUserCode);

    JsonResponse deptCompletionRate(String source, String currentUserCode);

    JsonResponse updatePolicyInfo( UsPolicyInfo policyInfo );

    JsonResponse test1();
}
