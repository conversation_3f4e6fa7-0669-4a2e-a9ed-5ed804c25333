package com.simbest.boot.djfupt.index.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

@Data
public class DataScreeningVo {

    @ExcelVOAttribute(name = "所在单位", column = "A")
    private String companyName; //单位
    @ExcelVOAttribute(name = "网格数量", column = "B")
    private int gridNum;//网格数量

    @ExcelVOAttribute(name = "政策宣讲完成率", column = "C")
    private String policy;//政策宣讲完成率
    private Integer policyNum;//政策宣讲次数

    @ExcelVOAttribute(name = "思政工作率", column = "D")
    private String recordFill;//思政工作率
    private Integer recordFillNum;//思政工作次数

    @ExcelVOAttribute(name = "问题解决完成率", column = "E")
    private String problemInfo;//问题解决完成率
    private Integer problemInfoNum;//问题解决次数

    @ExcelVOAttribute(name = "经验完成率", column = "F")
    private String caseInfo;//经验完成率
    private Integer caseInfoNum;//经验完成次数
}
