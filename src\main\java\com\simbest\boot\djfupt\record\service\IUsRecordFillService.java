package com.simbest.boot.djfupt.record.service;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.record.model.RecordVo;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IUsRecordFillService extends ILogicService<UsRecordFill, String> {
    /**
     * 查询思政纪实草稿状态工单
     * @param creator
     * @return
     */
    List<UsRecordFill> getFromDetail(String creator);

    /**
     * 更新附件
     * @param usRecordFill
     */
    public void updateFileInfoBy(UsRecordFill usRecordFill);





    /**
     * 展示所有思政纪实台账
     * @param resultMap
     * @return
     */
    List<Map<String,Object>> findAllUsRecordFill(Map<String, Object> resultMap);


    List<Map<String,Object>> findAllUsRecordFills(Map<String, Object> resultMap);





    /**
     * 导出思政纪实台账数据
     * @param currentUserCode
     * @param request
     * @param response
     */

    void exportUsRecordFillDate(String currentUserCode, HttpServletRequest request, HttpServletResponse response,UsRecordFill usRecordFill);










    //////////////////////////////////////////////////
    //党建大屏

    /**
     * 思政数量
     * @return
     */

    List<Map<String,Object>> findRecordCount(Map<String, Object> resultMap);



    /**
     * 思政数量
     * @return
     */

    List<Map<String,Object>> findAllRecordCount(Map<String, Object> resultMap);

    JsonResponse recordDetail(String id);

    List<RecordVo>  recordStatistics(int page, //当前页码
                                     int rows, //每页数量
                                     Map<String, Object> resultMap);

    List<RecordVo>  recordStatisticsOther(int page, //当前页码
                                     int rows, //每页数量
                                     Map<String, Object> resultMap);

    void exportProblemStatistics(  Map<String, Object> resultMap,
                                   HttpServletResponse response,
                                   HttpServletRequest request);

    JsonResponse configuration();
}
