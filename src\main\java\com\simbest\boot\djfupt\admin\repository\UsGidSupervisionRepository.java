package com.simbest.boot.djfupt.admin.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.djfupt.admin.model.UsGidSupervisionManager;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UsGidSupervisionRepository extends LogicRepository<UsGidSupervisionManager, String> {
    @Query(
            value = " SELECT t.* FROM us_gid_supervision_manager t  WHERE t.enabled=1  and ( t.county_code = :countyCode AND t.grid_code= :gridCode)   ",
            nativeQuery = true
    )
    List<UsGidSupervisionManager> findByCountyCodeOrGridNameAndActive(@Param("gridCode") String gridCode, @Param("countyCode")String countyCode);

    @Query(
            value = " SELECT t.* FROM us_gid_supervision_manager t  WHERE t.enabled=1 and t.id != :id  and ( t.grid_code = :gridCode OR t.grid_name= :gridName)  ",
            nativeQuery = true
    )
    List<UsGidSupervisionManager> findByGridCodeOrGridNameAndIdAndActive(@Param("id") String id, @Param("gridCode") String gridCode, @Param("gridName")String gridName);
    @Query(
            value = " SELECT t.* FROM us_gid_supervision_manager t  WHERE t.enabled=1 and t.id != :id  and  t.grid_user_name = :gridUserName   ",
            nativeQuery = true
    )
    List<UsGidSupervisionManager> findByGridUserNameAndIdAndActive(@Param("id") String id, @Param("gridUserName") String gridUserName);

    UsGidSupervisionManager  findUsGidSupervisionManagerByGridCodeAndEnabled(@Param("gridCode") String gridCode, @Param("enabled")Boolean Enabled);

    List<UsGidSupervisionManager> findUsGidSupervisionManagersByGridUserNameAndEnabled(@Param("gridUserName") String gridUserName, @Param("enabled")Boolean Enabled);


    List<UsGidSupervisionManager> findUsGidSupervisionManagersByGridNameAndEnabled(@Param("gridName") String gridName, @Param("enabled")Boolean Enabled);
}
