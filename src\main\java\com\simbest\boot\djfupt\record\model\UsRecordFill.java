package com.simbest.boot.djfupt.record.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_record_fill")
@ApiModel(value = "思政纪实填报")
public class UsRecordFill extends WfFormModel {

    @Id
    @Column(name = "id", length = 300)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "BWFA")         //主键前缀，此为可选项注解
    private String id;


    @Column(length = 300)
    @ApiModelProperty(value = "填报人", required = true)
    @ExcelVOAttribute(name = "填报人", column = "C")
    private String applyUser;               //主单据ID  关联us_pm_instence表中的单据ID

    @Column(length = 300)
    @ApiModelProperty(value = "填报组织", required = true)
    private String applyOrg;


    @Column(length = 300)
    @ApiModelProperty(value = "填报时间", required = true)
    private String applyTime;

    @Column(length = 300)
    @ApiModelProperty(value = "谈话或走访时间", required = true)
    @ExcelVOAttribute(name = "谈话或走访时间", column = "D")
    private String talkTime;



    @Column(length = 300)
    @ApiModelProperty(value = "谈话或走访地点", required = true)
    @ExcelVOAttribute(name = "谈话或走访地点", column = "E")
    private String talkAddress;

    @Column(length = 300)
    @ApiModelProperty(value = "座谈人数", required = true)
    @ExcelVOAttribute(name = "座谈人数", column = "F")
    private String numberOfPanel;



    @Column(length = 300)
    @ApiModelProperty(value = "谈话主题或走访事项", required = true)
    @ExcelVOAttribute(name = "谈话主题或走访事项", column = "G")
    private String configDeptName;


    @Column(length = 300)
    @ApiModelProperty(value = "谈话或走访对象", required = true)
    @ExcelVOAttribute(name = "谈话或走访对象", column = "H")
    private String configTime;

    @Column(length = 3000)
    @ApiModelProperty(value = "谈话或走访内容简述", required = true)
    private String talkContent;




    @Column(length = 30)
    @ApiModelProperty(value = "是否是草稿", required = true)
    private String isDraft; //页面提交：1：非草稿
                             // 页面保存 ：2：是草稿


    @Column(length = 500)
    private  String belongCompanyCodeParent;//上级code


    @Column(length = 300)
    @ApiModelProperty(value = "文件ID", required = true)
    private String fileIds;

    @Transient
    List<SysFile> drawFiles;                //附件


    @Transient
    private String startTime;                //


    @Transient
    private String endTime;                //


    @Transient
    private  String grids;


    @Transient
    private  String year;

    @Transient
    private  String month;

    @Transient
    @ApiModelProperty(value = "单位", required = true)
    @ExcelVOAttribute(name = "单位", column = "A")
    private  String companyName;


    @Transient
    @ApiModelProperty(value = "部门", required = true)
    @ExcelVOAttribute(name = "部门", column = "B")
    private  String depName;


    @Column(length = 40)
    // @ExcelVOAttribute(name = "任务描述", column = "L")
    private String taskDescription;//任务描述




    @Column(length = 40)
    // @ExcelVOAttribute(name = "任务描述", column = "L")
    private String taskDescriptionFileId;//任务描述附件id


    @Transient
    private List<SysFile> taskDescriptionFiles;


    @Transient
    private String type;

    @Transient
    private List<String> recordTypeList;//思政纪实类型列表

    @Transient
    private List<UsTalkContent> talkContentList;//谈话内容列表

    @ExcelVOAttribute(name = "谈话或走访内容", column = "I")
    private String talkContentListString;

}
