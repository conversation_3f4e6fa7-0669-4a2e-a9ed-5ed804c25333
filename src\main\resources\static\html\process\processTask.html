﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        getCurrent()
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/queryActBusinessStatus/myTaskToDo?source=PC", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [[//列
                        {
                            title: "工单标题", field: "receiptTitle", width: 200, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var th = appNameTH(web.appName, web.appHtml, row.pmInsType);
                                var g = "<span class='audit col_b titleTooltipA' index='" + index + "' ptitle='" + th.type + "' path='" + th.html + "'>" + value + "</span>";
                                return g;
                            }
                        },
                        { title: "创建部门", field: "createOrgName", width: 140, tooltip: true },
                        { title: "创建人", field: "createUserName", width: 100 },
                        {
                            title: "创建时间", field: "createTime", width: 150,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getTimeDate(row.createTime, "yyyy-MM-dd hh:mm:ss");
                            }
                        },//排序sortable: true
                        { title: "已办理人", field: "previousAssistantName", width: 100 },
                        {
                            title: "办理时间", field: "previousAssistantDate", width: 150,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return getTimeDate(row.previousAssistantDate, "yyyy-MM-dd hh:mm:ss");
                            }
                        },
                        {
                            title: "当前办理环节", field: "activityInstName", width: 160, tooltip: true,
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return row.currentState == "7" ? "已归档" : value;
                            }
                        }
                        //{ title: "当前状态", field: "currentState", width: 100},

                    ]],
                }
            };
            pageparam.listtable.columns[0].push({
                field: "opt", title: "操作", width: 160, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                    var th = appNameTH(web.appName, web.appHtml, row.pmInsType);
                    var g = "<a class='audit' index='" + index + "' title='办理' index='" + index + "' ptitle='" + th.type + "' path='" + th.html + "'>【办理】</a>";
                    return g;
                }
            });
            loadGrid(pageparam);

            $(document).on("click", "a.audit,span.audit", function () {
                var $t = $(this);
                var index = $t.attr("index");
                $("#taskTable").datagrid("clearSelections");//取消选择所有当前页中所有的行
                $("#taskTable").datagrid("clearChecked");//取消选择所有当前页中所有的行
                $("#taskTable").datagrid("selectRow", index);//选择一行，行索引从0开始
                var row = $("#taskTable").datagrid("getSelected");//返回第一个被选中的行或如果没有选中的行则返回null
                var url = ($t.attr("path")) + "?type=task&location=" + row.activityDefId + "&processInstId=" + (row.processInstId || '') + "&processDefName=" + row.processDefName + "&workItemId=" + row.workItemId + "&currentState=" + row.currentState + "&pmInsId=" + row.receiptCode + "&usPmInstenceId=" + row.businessKey;
                var ptitle = ($t.attr("ptitle")) + "-" + row.receiptTitle + "-审批";
                top.dialogP(url, 'processTask', ptitle, 'audit', true, "maximized", "maximized", taskListLoad);
            });

            var currentDate = new Date();
            var targetDate = new Date('2025-12-15');

            // 比较当前日期和目标日期
           if(currentDate > targetDate) {
                $('.activeTwo').parent().css('background-color','#aaa')
                $('.activeTwo').removeAttr('path')
            } 
        });
        //刷新页面
        function taskListLoad() {
            $("#taskTable").datagrid("reload");
        };

        //重置
        $(document).on("click", ".formreset", function () {
            formreset('taskTableQueryForm')
            $('.searchtable').trigger('click')
        });

        //时间转换
        function timestampToTime(timestamp) {
            var date = new Date(timestamp);
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
            var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
            var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + ':';
            var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
            var strDate = Y + M + D + h + m + s;
            return strDate;
        }

        // 找茬活动
        $(document).on("click", ".activeTwo", function () {
            var path = $(this).attr('path')
            if(path){
                top.tabClick(path);
            }else{
                return top.mesShow("温馨提示", "豫起奋发 网格调度来找茬'活动(第二期)活动已结束！", 2000, "red")
            }
        });
        $(document).on("click", ".close", function () {
            $(this).parent().hide()
        });


    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">工单编号：</td>
                <td width="230"><input name="workCode" type="text" value="" /></td>
                <td width="90" align="right">工单标题：</td>
                <td width="230"><input name="title" type="text" value="" /></td>
                <td>
                    <div class="w100">
                        <a class="btn ml10 searchtable">
                            <font>查询</font>
                        </a>
                        <a class="btn ml10 formreset">
                            <font>重置</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
<!--    <div class="pcBtn" style="top: 70%;">-->
<!--        <span class="apply activeThree" path="zwrgdyQuery">"省专协同任务1&#45;&#45;装维入格调研"活动入口</span>|-->
<!--        <span class="close">关闭</span>-->
<!--    </div>-->
    <div class="pcBtn" style="top: 80%;">
        <span class="apply" path="lhgzhdQuery">"省专同任务--联合跟装活动入口</span>|
        <span class="close">关闭</span>
    </div>
    <div class="pcBtn" style="top: 90%;">
        <span class="apply activeTwo" path="faultsApplyThree" style="padding-right: 25px;">“豫起奋发 政企运营来找茬”活动(第三期)活动入口</span>|
        <span class="close">关闭</span>
    </div>
</body>

</html>

<style>
    .pcBtn{
        background: #39aef5;
        color: #fff;
        padding: 5px 10px;
        text-align: center;
        border-radius: 32px;
        position: absolute;
        top: 80%;
        right: 0;
        font-weight: 700;
        font-size: 16px;
    }
    .pcBtn .apply,.pcBtn .close{
        padding:0 20px;
        cursor: pointer;
    }
</style>