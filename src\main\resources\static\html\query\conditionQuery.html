<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>工单查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var myinfo={};
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#taskTable",//table列表的id名称，需加#
                    "querycmd":"action/applicationForm/conditionQuery?source=PC",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "工单标题", field: "TITLE", width: 80,tooltip:true},
                        { title: "申请人", field: "APPLY_USER", width: 50 },
                        { title: "申请单位", field: "BELONG_COMPANY_NAME", width: 50 },
                        { title: "申请部门", field: "BELONG_DEPARTMENT_NAME", width: 50 },
                        { title: "选择类型", field: "TYPE_NAME", width: 50 },
                        { title: "当前环节", field: "WORK_ITEM_NAME", width: 60 ,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var str = row.CURRENT_STATE;
                                if(str == "7"){
                                    value = "已归档";
                                }
                                return value;
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 50, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return "<a class='audit' index='"+index+"' title='查看' ptitle='党建指导员支撑服务平台申请流程-"+row.TITLE+"' processInstId='"+row.PROCESS_INST_ID+"' path='html/apply/applicationForm.html?type=join&location=1&processInstId="+row.PROCESS_INST_ID+"'>【查看】</a>";
                            }
                        }
                    ] ]
                }
            };
            loadGrid(pageparam);
            $(document).on("click","a.audit",function(){
                var $t=$(this);
                var index=$t.attr("index");
                var path=$t.attr("path");
                top.dialogP(path, 'comprehensive', $t.attr("ptitle") + '查看', 'audit', true, $(window).width() + 200, $(window).height() + 80, listLoad);
            });
            //选择组织
            $(".chooseOrgs").on("click",function(){
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                var href={"multi":"0","name":"chooseOrgsVal"};
                if($("#taskTableQueryForm .chooseOrgs").val()!=""){
                    var datas=[];
                    var names=$("#taskTableQueryForm .chooseOrgs").val().split(",");
                    var codes=$("#taskTableQueryForm input[name=belongOrgCode]").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseOrgsVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                }
                var url=tourl('html/choose/chooseOrgs.html',href);
                top.dialogP(url,'conditionQuery','选择组织','chooseOrgs',false,'800');
            });
        });
        //刷新页面
        function listLoad(){
            $("#taskTable").datagrid("reload");
        };

        //选择组织
        window.chooseOrgs=function(data) {
            var names = [], codes = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
            }
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $("#taskTableQueryForm input[name=belongOrgCode]").val(codes.join(","));
        };
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="taskTableQueryForm">
    <input name="belongOrgCode" type="hidden"/>
    <input name="styleDictValue" type="hidden"/>
    <input name="companyTypeDictValue" type="hidden"/>
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">申请人：</td>
            <td width="150">
                <input class="applyUser"  name="applyUser" type="text" value="" />
            </td>
            <td width="90" align="right">申请部门：</td>
            <td width="150">
                <input class="chooseOrgs" name="orgName" type="text" value="" />
            </td>
            <td></td>
        </tr>
        <tr>
            <td width="90" align="right">工单标题：</td>
            <td width="150">
                <input name="title" type="text" value="" />
            </td>
            <td width="90" align="right">选择类型：</td>
            <td width="150">
                <input id="type" name="type" class="easyui-combobox"  style="width: 100%; height: 32px;" data-options="
                       valueField: 'value',
                       panelHeight:'auto',
                       ischooseall:true,
                       textField: 'name',
                       queryParams:{'dictType':'applyType'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="taskTable"><table id="taskTable"></table></div>
</body>
</html>
