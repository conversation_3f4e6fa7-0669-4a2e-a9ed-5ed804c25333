package com.simbest.boot.djfupt.record.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.record.model.UsRecordType;

import java.util.List;
import java.util.Map;

public interface IUsRecordTypeService extends ILogicService<UsRecordType, String> {
    List<Map<String, Object>> findInfoNoPage(Map<String, String> resultMap);

    JsonResponse findInfoPage(int page,int rows, Map<String, String> resultMap);
}
