package com.simbest.boot.djfupt.util.kms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分词检索 关键字返回响应体vo")
public class KeyWordResponseVo {

	@ApiModelProperty(value = "响应码 0-成功， 其他失败， 接口文档的示例报文给的是 -10")
	private Integer code;

	@ApiModelProperty(value = "数据")
	private innerData data;

	@ApiModelProperty(value = "ok/失败信息")
	private String msg;

	@ApiModelProperty(value = "暂留未知")
	private String path;

	@ApiModelProperty(value = "暂留未知")
	private String extra;

	@ApiModelProperty(value = "暂留未知")
	private String timestamp;

	@ApiModelProperty(value = "暂留未知")
	private String errorMsg;

	@ApiModelProperty(value = "暂留未知")
	private boolean isSuccess;

	public List<String> getKeyWords(){
		return this.data.getKeywordList();
	}
}

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
class innerData{

	@ApiModelProperty(value = "分词列表")
	private List<String> keywordList;

	@ApiModelProperty(value = "分词总数")
	private Integer title;
}
