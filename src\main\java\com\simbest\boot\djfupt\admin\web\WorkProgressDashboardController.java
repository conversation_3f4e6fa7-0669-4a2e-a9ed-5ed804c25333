package com.simbest.boot.djfupt.admin.web;


import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsFileManager;
import com.simbest.boot.djfupt.admin.service.IUsFileManagerService;
import com.simbest.boot.djfupt.admin.service.WorkProgressDashboardService;
import com.simbest.boot.djfupt.index.model.DataScreeningVo;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 档位工作进度大屏相关接口
 */
@Api(description = "文件管理相关接口")
@Slf4j
@SuppressWarnings("ALL")
@RestController
@RequestMapping(value = "/action/workProgressDashboard")
public class WorkProgressDashboardController {

    @Autowired
    private WorkProgressDashboardService workProgressDashboardService;

    @Autowired
    LoginUtils loginUtils;


    @PostMapping(value = {"/findPolicyLederTotal", "/api/findPolicyLederTotal", "/findPolicyLederTotal/sso", "/anonymous/findPolicyLederTotal"})
    public JsonResponse findPolicyLederTotal(
            @RequestParam(required = false) String currentUserCode,
            @RequestParam(required = false) String source) {
        Map<String, Map<String, Object>> resultMap = workProgressDashboardService.findPolicyLederTotal(currentUserCode, source);
        return JsonResponse.success(resultMap);
    }

    /**
     * 废弃
     * @param page
     * @param size
     * @param currentUserCode
     * @param source
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/dataScreening", "/api/dataScreening", "/sso/dataScreening"})
    public JsonResponse  dataScreening(@RequestParam int page,
                                       @RequestParam int size,
                                       @RequestParam (required = false)String currentUserCode,
                                       @RequestParam (required = false )String source,
                                       @RequestBody  Map<String, Object> resultMap){

        DataScreeningVo  dataScreeningVoList= workProgressDashboardService.dataScreening(currentUserCode,source,resultMap);
        return  JsonResponse.success(dataScreeningVoList);
    }

    @PostMapping(value = {"/workProgress", "/workProgress/api", "/workProgress/sso"})
    public JsonResponse  workProgress( @RequestParam (required = false)String currentUserCode,
                                                   @RequestParam (required = false )String source){
        Map<String, Map<String, Object>> resultList = new HashMap<>();
        if (Constants.MOBILE.equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        return  JsonResponse.success(workProgressDashboardService.workProgress());
    }

}
