﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>管理员管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable",//table列表的id名称，需加#
                    "querycmd": "action/queryActBusinessStatus/queryMyJoin?source=PC",//table列表的查询命令
                    // "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "fitColumns": true,//自动扩大或缩小列的尺寸以适应表格的宽度并且防止水平滚动，不配置默认false
                    "checkboxall": true,
                    //"sortName":'orgCode',
                    //"sortOrder": 'desc',
                    "idField": 'ID',
                    "remoteSort": false,
                    "columns": [[//列
                        {
                            title: "工单标题", field: "receiptTitle", width: 150, sortable: true, align: "center", formatter: function (value, row, index) {
                                var g = "";
                                if ("A" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changeExperience.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "' >" + value + "</a>";
                                } else if ("B" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changeQuestion.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "' >" + value + "</a>";
                                } else if ("C" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changePolicy.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >" + value + "</a>";
                                }
                                else if ("D" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/query/faultsApply.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >" + value + "</a>";
                                }
                                else if ("E" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/query/faultsApplyThree.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >" + value + "</a>";
                                }
                                return g
                            }
                        },
                        {
                            title: "创建部门", field: "createOrgName", width: 80, rowspan: 1, tooltip: true, sortable: true//align：对齐此列的数据，可以用left、right、center
                        },
                        { title: "创建人", field: "createUserName", width: 60, sortable: true, align: "center" },
                        { title: "创建时间", field: "createTime", width: 100, sortable: true, order: "asc" },//排序sortable: true
                        { title: "当前办理人", field: "previousAssistantName", width: 80, sortable: true, align: "center" },
                        { title: "办理时间", field: "previousAssistantDate", width: 130, sortable: true, align: "center" },
                        {
                            title: "当前办理环节", field: "activityInstName", width: 130, sortable: true, align: "center", formatter: function (value, row, index) {
                                var g = ''
                                if (row.pmInsType == 'C' && row.currentState == 8) {
                                    g = '超时自动归档'
                                } if (row.pmInsType == 'B' && row.currentState == 7) {
                                    g = '同意，已发布'
                                } if (row.currentState == 7) {
                                    g = '已归档'
                                } else {
                                    g = value
                                }
                                return g
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 100, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "";
                                if ("A" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changeExperience.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "' >【查看】</a>";
                                } else if ("B" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changeQuestion.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "' >【查看】</a>";
                                } else if ("C" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/change/changePolicy.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >【查看】</a>";
                                }
                                else if ("D" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/query/faultsApply.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >【查看】</a>";
                                }
                                else if ("E" == row.pmInsType) {
                                    g += "<a class='audit' index='" + index + "' title='查看' ptitle='" + row.receiptTitle + "' pmInsId='" + row.receiptCode + "' " +
                                        "path='html/query/faultsApplyThree.html?workFlag=join&type=join&location=" + row.activityDefId + "&source=PC&processInstId=" + row.processInstId + "&pmInsId=" + row.receiptCode + "&previousAssistant='" + row.previousAssistant + " >【查看】</a>";
                                }
                                return g
                            }
                        }

                    ]],
                    "rowStyler": function (index, row) {
                    }
                },
            };
            loadGrid(pageparam);

            //全部发送短信催办
            $(document).on("click", ".allSend", function () {
                var datas = $(pageparam.listtable.listname).datagrid("getRows");
                if (datas.length > 0) {
                    $.messager.confirm("批量催办", "请确认是否进行催办？", function (r) {
                        if (r) {
                            var ajaxopts = {
                                url: "action/smsUrged/sendShortMessage?source=PC",
                                contentType: "application/json; charset=utf-8",
                                data: datas,
                                success: function (data) {
                                    top.mesShow("温馨提示", data.message || "操作成功", 1500);
                                }
                            };
                            ajaxgeneral(ajaxopts);
                        }
                    });
                }
                else {
                    top.mesShow("温馨提示", "请选择要发送催办短信的工单！", 2000, 'red');
                }
            });

            //详情查看
            $(document).on("click", ".audit", function () {
                var item = $(this);
                var url = item.attr("path");
                top.dialogP(url, window.name, item.attr("ptitle"), 'getCheck', true, 'maximized', 'maximized');
            });

            //重置
            $(document).on("click", ".formreset", function () {
                formreset('taskTableQueryForm')
                $('.searchtable').trigger('click')
            });

            loadGrid(pageparam);
            var flag = true;
            if (top.joinSearch) {
                var url = "html/change/changePolicy.html?workFlag=join&type=join&location=" + top.joinSearch.location + "&source=PC&processInstId=" + top.joinSearch.processinstid + "&pmInsId=" + top.joinSearch.pmInsId + "&workItemId=" + top.joinSearch.workItemId
                delete top.joinSearch
                top.dialogP(url, window.name, '政策宣讲', 'getCheck', true, 'maximized', 'maximized');
            }

            var currentDate = new Date();
            var targetDate = new Date('2025-12-15');

            // 比较当前日期和目标日期
           if(currentDate > targetDate) {
                $('.activeTwo').parent().css('background-color','#aaa')
                $('.activeTwo').removeAttr('path')
            } 
        });

        //初始化界面
        // window.initsystem = function () { };

        // 找茬活动
        $(document).on("click", ".apply", function () {
            var path = $(this).attr('path')
            if(path){
                top.tabClick(path);
            }else{
                return top.mesShow("温馨提示", "'豫起奋发 网格调度来找茬'活动(第二期)活动已结束！", 2000, "red")
            }
        });

        $(document).on("click", ".close", function () {
            $(this).parent().hide()
        });
    </script>
</head>

<body class="body_page">
    <form id="taskTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">工单编号：</td>
                <td width="230"><input name="workCode" type="text" value="" /></td>
                <td width="90" align="right">工单标题：</td>
                <td width="230"><input name="title" type="text" value="" /></td>
                <td>
                    <div class="w100">
                        <a class="btn ml10 searchtable">
                            <font>查询</font>
                        </a>
                        <a class="btn ml10 formreset">
                            <font>重置</font>
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
    <div class="pcBtn" style="top: 70%;">
        <span class="apply" path="zwrgdyQuery">"省专协同任务1--装维入格调研"活动入口</span>|
        <span class="close">关闭</span>
    </div>
    <div class="pcBtn" style="top: 80%;">
        <span class="apply" path="lhgzhdQuery">"省专协同任务2--联合跟装活动"活动入口</span>|
        <span class="close">关闭</span>
    </div>
    <div class="pcBtn" style="top: 90%;">
        <span class="apply activeTwo" path="faultsApplyThree" style="padding-right: 55px;">“豫起奋发 政企运营来找茬”活动(第三期)活动入口</span>|
        <span class="close">关闭</span>
    </div>
</body>

</html>

<style>
    .pcBtn{
        background: #39aef5;
        color: #fff;
        padding: 5px 10px;
        text-align: center;
        border-radius: 32px;
        position: absolute;
        top: 80%;
        right: 0;
        font-weight: 700;
        font-size: 16px;
    }
    .pcBtn .apply,.pcBtn .close{
        padding:0 20px;
        cursor: pointer;
    }
</style>