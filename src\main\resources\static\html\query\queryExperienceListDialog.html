<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>经验推广台账</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}" type="text/javascript">
        </script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript">
        </script>
    <script type="text/javascript">
        var myinfo = {};
        getCurrent()
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#taskTable", //table列表的id名称，需加#
                    "querycmd": "action/usCaseInfo/findAllApplication", //table列表的查询命令
                    "contentType": "application/json; charset=utf-8", //table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    "queryParams":{},
                    "nowrap": true, //把数据显示在一行里,默认true
                    "frozenColumns": [], //固定在左侧的列
                    "columns": [
                        [ //列
                            { title: "优秀案例名称", field: "problemName", width: 100, tooltip: true, align: "center" },
                            { title: "优秀案例简述", field: "problemDescribe", width: 120, tooltip: true, align: "center" },
                            { title: "上报人", field: "applyUser", width: 50, tooltip: true, align: "center" },
                            {title: "上报组织", field: "belongOrgName", width: 80, tooltip: true, align: "center",
                                formatter: function (value, row, index) {
                                    var str=''
                                    if(row.belongCompanyName==row.belongDepartmentName && row.belongDepartmentName==row.belongOrgName){
                                        str = row.belongCompanyName
                                        return str
                                    }
                                    if(row.belongCompanyName==row.belongDepartmentName && row.belongDepartmentName!==row.belongOrgName){
                                        str = row.belongCompanyName+'/'+ row.belongOrgName
                                        return str
                                    }
                                    if(row.belongCompanyName!==row.belongDepartmentName && row.belongDepartmentName!==row.belongOrgName){
                                        str = row.belongCompanyName+'/'+ row.belongDepartmentName+'/'+ row.belongOrgName
                                        return str
                                    }

                                    if(row.belongCompanyName!==row.belongDepartmentName && row.belongDepartmentName==row.belongOrgName){
                                        str = row.belongCompanyName+'/'+ row.belongDepartmentName
                                        return str
                                    }

                                    if(row.belongCompanyName == row.belongOrgName && row.belongDepartmentName !==row.belongCompanyName){
                                        str = row.belongCompanyName+'/'+ row.belongDepartmentName+'/'+ row.belongOrgName
                                        return str
                                    }
                                }
                            },
                            { title: "上报时间", field: "createdTime", width: 70, tooltip: true, align: "center",
                                formatter: function (value, row, index) {
                                    return getTimeDate(value, "yyyy-MM-dd hh:mm:ss");
                                }
                            } ,
                            { field: "opt", title: "操作", width: 60, rowspan: 1, align: "center", formatter: function (value, row, index) {
                                    var g = "<a class='check' index='" + index + "'>【查看】</a>";
                                    if(web.currentUser.username=="hadmin"){
                                        g += "<a class='hadminUpdata' index='" + index + "'>【修改】</a>"
                                    }
                                    return g
                                }
                            }
                        ]
                    ]
                }
            };

            if(gps.companyCode){
                pageparam.listtable.queryParams = {
                    belongDepartmentCode: gps.companyCode
                }
            }
            loadGrid(pageparam);
            $('#belongDepartmentCode').val(gps.companyCode)

            //当前登陆人为管理员或者wupeng7可筛选 其他人一律已归档
            if(web.currentUser.username=='wupeng7'||web.currentUser.username=='hadmin'){
                $(".hadminShow").show()
            }else{
                $('#state').combobox('setValues','7')
                $('.searchtable').trigger('click')
            }

            //默认推广
            $('#questionMode').combobox('setValues','0')
            //当前登陆人为wupeng7默认推广
            // if(web.currentUser.username!='wupeng7'){
            //     $(".questionModeShow").show()
            //     $('#questionMode').combobox('setValues','0')
            //
            // }else{
            //     $('#questionMode').combobox('setValues','0')
            //     $(".questionModeShow").hide()
            // }

            //导出
            $(".exporttable").on("click", function () {
                $("#taskTableQueryForm").attr("action", web.rootdir +"action/usCaseInfo/exportParameter");
                $("#taskTableQueryForm").attr("method", "post");
                $("#taskTableQueryForm").submit();
            });

              //选择组织
              $(".chooseOrgs").on("click",function(){
                var href={"multi":"0","name":"chooseOrgsVal","pmInsId":'A'};
                if($("#taskTableQueryForm .chooseOrgs").val()!=""){
                    var datas=[];
                    var names=$("#taskTableQueryForm .chooseOrgs").val().split(",");
                    var codes=$("#taskTableQueryForm input[name=belongDepartmentCode]").val().split(",");
                    var comps=$("#taskTableQueryForm input[name=companyTypeDictValue]").val().split(",");
                    for(var i in comps){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        // datai.orgType=styles[i];
                        // datai.companyTypeDictValue=comps[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseOrgsVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseOrgsVal={"data":[]};
                }
                var url=tourl('html/choose/chooseCompanyII.html',href);
                top.dialogP(url,window.name,'选择组织','chooseOrgs',false,'800');
            });
            // if(web.currentUser.belongCompanyTypeDictValue == '01'){
                $('.c02').show()
            // }else{
            //     $('.c01').show()
            // }

            $('#taskTableQueryForm').resize()



            // $("#questionMode").combobox({
            //     onChange: function (n) {
            //         console.log(n)
            //         if(n && n=='1'){//否
            //             $('.c01').show()
            //             $('.c02').hide()
            //             $('#companyName').combobox('setValues','')
            //         }else{//组织树
            //             $('.c02').show()
            //             $('.c01').hide()
            //             $('.chooseOrgs').val('')
            //
            //         }
            //     }
            // });

        });

        //刷新页面
        function listLoad() {
            $("#taskTable").datagrid("reload");
            $('#taskTableQueryForm').resize()
        };
//hadmin修改
        $(document).on('click', 'a.hadminUpdata', function () {
            var index = $(this).attr('index');
            var item = $('#taskTable').datagrid('getRows')[index];
            var location = item.activityDefId ? item.activityDefId : 'djfupt.start'
            var param = {
                processInstId:item.processInstId,
                id: item.id,
                source: "PC",
                pmInsId: item.pmInsId,
                // initForm: 'groupCheckF',
                // type: 'check',
                location: location,
            }
            var url = tourl('html/change/changeExperience.html?type=draft&otherType=hadminUpdata', param)
            top.dialogP(url, window.name, '经验推广', 'groupCheck', true, 'maximized', 'maximized')
        })

        $(document).on('click', 'a.check', function () {
            var index = $(this).attr('index');
            var item = $('#taskTable').datagrid('getRows')[index];
            var location = item.activityDefId ? item.activityDefId : 'djfupt.start'
            var param = {
                processInstId:item.processInstId,
                id: item.id,
                source: "PC",
                pmInsId: item.pmInsId,
                // initForm: 'groupCheckF',
                // type: 'check',
                location: location,
            }
            var url = tourl('html/change/changeExperience.html?workFlag=join&type=join', param)
            top.dialogP(url, window.name, '经验推广', 'groupCheck', true, 'maximized', 'maximized')
        })
          //选择组织
          window.chooseOrgs=function(data) {
            var names = [], codes = [], styles = [], comps = [];
            for (var i in data.data) {
                names.push(data.data[0].text);
                codes.push(data.data[0].orgCode);
                // styles.push(data.data[0].styleDictValue);
                comps.push(data.data[0].companyTypeDictValue);
            }
            $("#taskTableQueryForm input[name=belongDepartmentCode]").val(codes.join(","));
            $("#taskTableQueryForm .chooseOrgs").val(names.join(","));
            $("#taskTableQueryForm input[name=pmInsId]").val('A');
            $("#taskTableQueryForm input[name=companyTypeDictValue]").val(comps.join(","));
        };  
        function companyFun(data){
            $("#taskTableQueryForm input[name=companyCode]").val(data.value);
        }
        function dialogClosed(){
            top.dialogClose("groupCheck")
        }
    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="taskTableQueryForm">
        <!-- 传给后端  -->
        <input  id="belongDepartmentCode" name="belongDepartmentCode" type="hidden"/>
        <input name="companyTypeDictValue" type="hidden"/>
        <input name="pmInsId" type="hidden"/>
        <input name="companyCode" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="90" align="right">上报时间</td>
                <td width="250">
                    <input id="startDate" name="startDate" type="text" class="easyui-datebox"
                        style="width:45%;height:32px;" validType="startDateCheck['endDate','startDate']"
                        data-options="panelHeight:'auto', editable:false" />-
                    <input id="endDate" name="endDate" type="text" class="easyui-datebox" style="width:45%;height:32px;"
                        validType="endDateCheck['startDate','endDate']"
                        data-options="panelHeight:'auto', editable:false" />
                </td>
                <td width="90" align="right">优秀案例名称</td>
                <td width="200"><input name="problemName" type="text" /></td>
                 <!-- test wdy -->
                 <td width="90" align="right">上报部门</td>
                 <td width="200" class="c01 hide">
                    <input class="chooseOrgs" name="chooseOrgs"  type="text" value=""  readonly/>
                </td>

                <td width="200" class="c02 hide">
                    <input name="companyName" class="easyui-combobox" id="companyName"
                        style="width: 100%; height: 32px;" data-options="
						valueField: 'name',
						panelHeight:'200',
						ischooseall:true,
						textField: 'name',
						editable:false,
						queryParams:{'dictType':'company'},
                        onSelect:companyFun,
                        url: web.rootdir+'action/queryDictValue/queryByType',"/>
                </td>


                <td class="hide questionModeShow" width="90" align="right">是否推广</td>
                <td class="hide questionModeShow" width="200">
                    <select class="easyui-combobox" id="questionMode" name="questionMode"
                            style="width:100%; height: 32px;" data-options="editable:false,panelHeight:'auto'"
                            noReset="true">
                        <option value="0">是</option>
                        <option value="1">否</option>
                    </select>
                </td>
                <td width="200">
                    <div class=" w200">
                        <a class="btn fr exporttable "> <font>导出</font> </a>
                        <a class="btn fr mr10 searchtable"> <font>查询</font> </a>

                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="taskTable">
        <table id="taskTable"></table>
    </div>
</body>

</html>