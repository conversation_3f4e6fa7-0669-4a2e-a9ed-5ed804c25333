package com.simbest.boot.djfupt.common.web;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.caseinfo.model.UsCaseInfo;
import com.simbest.boot.djfupt.caseinfo.service.IUsCaseInfoService;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2022/11/24 0024 10:10
 */

/**
 * 共用接口
 */
@Api(description = "共用接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/commom")
public class CommonController {

    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private OperateLogTool operateLogTool;

    /**
     * 查询附件
     *
     * @param pmInsId
     * @return
     */
    @PostMapping(value = {"/queryFile", "/api/queryFile", "/queryFile/sso"})
    public JsonResponse queryFile(String pmInsId) {
        return iCommonService.queryFile(pmInsId);
    }

    @PostMapping(value = {"/queryOrg", "/api/queryOrg", "/queryOrg/sso", "/anonymous/queryOrg"})
    public JsonResponse queryOrg() {
        return iCommonService.queryOrg();
    }


    /**
     * 网络隐患,查询自己发起的待办
     *
     * @param page            页码
     * @param rows            数量
     * @param title           标题
     * @param source          来源
     * @param currentUserCode OA账号
     * @return
     */
    @ApiOperation(value = "网络隐患查询自己发起的待办", notes = "网络隐患查询自己发起的待办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "rows", value = "rows", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "partiName", value = "处理人", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/queryMySelfCreate", "/api/queryMySelfCreate", "/queryMySelfCreate/sso", "/anonymous/queryMySelfCreate"})
    public JsonResponse queryMySelfCreate(@RequestParam Integer page,
                                          @RequestParam Integer rows,
                                          @RequestParam String source,
                                          @RequestParam(required = false) String title,
                                          @RequestParam(required = false) String currentUserCode,
                                          @RequestParam(required = false) String processType,
                                          @RequestParam(required = false) String partiName
    ) {
        return iCommonService.queryMySelfCreate(page, rows, title, source, currentUserCode, processType, partiName);
    }


    /**
     * 发送催办短信
     *
     * @param source          来源
     * @param currentUserCode 当前用户
     * @param list            待催列表
     * @return
     */
    @ApiOperation(value = "发送催办短信", notes = "发送催办短信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "是否手机端", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query"),})
    @PostMapping(value = {"/sendShortMessage", "/api/sendShortMessage", "/sendShortMessage/sso"})
    public JsonResponse sendShortMessage(@RequestParam String source,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestBody List<Map<String, Object>> list) {

        return iCommonService.sendShortMessage(source, currentUserCode, list);
    }

    /**
     * 短信催办获取流程
     *
     * @return
     */
    @PostMapping(value = {"/queryProcessType",
            "/api/queryProcessType",
            "/queryProcessType/sso",
            "/anonymous/queryProcessType"})
    public JsonResponse queryProcessType() {
        return iCommonService.queryProcessType();
    }


    @ApiOperation(value = "根据当前管理员不同，可以在查询时选择不同的组织树", notes = "根据当前管理员不同，可以在查询时选择不同的组织树")
    @PostMapping(value = {"/queryOrgTreeBySearch",
            "/api/queryOrgTreeBySearch",
            "/sso/queryOrgTreeBySearch",
            "/anonymous/queryOrgTreeBySearch"})
    public JsonResponse queryOrgTreeBySearch(@RequestParam(required = false)  String pmInsId,
                                             @RequestParam(required = false) String source,
                                             @RequestParam(required = false) String currentUserCode) {
        operateLogTool.operationSource(source, currentUserCode);
        return iCommonService.queryOrgTreeBySearch(pmInsId);
    }





    @ApiOperation(value = "根据当前管理员不同，可以在查询时选择不同的组织树", notes = "根据当前管理员不同，可以在查询时选择不同的组织树")
    @PostMapping(value = {"/queryOrgTreeBySearchTwo",
            "/api/queryOrgTreeBySearchTwo",
            "/sso/queryOrgTreeBySearchTwo",
            "/anonymous/queryOrgTreeBySearchTwo"})
    public JsonResponse queryOrgTreeBySearchTwo(@RequestParam(required = false)  String pmInsId,
                                             @RequestParam(required = false) String source,
                                             @RequestParam(required = false) String currentUserCode) {
        operateLogTool.operationSource(source, currentUserCode);
        return iCommonService.queryOrgTreeBySearchTwo(pmInsId);
    }



    @PostMapping(value = {"/visido",
            "/api/visido",
            "/visido/sso",
            "/anonymous/visido"})
    public JsonResponse visido(){
        return iCommonService.visido();
    }


    @ApiOperation(value = "跳转链接用", notes = "跳转链接用")
    @PostMapping(value = {"/getHeader", "/api/getHeader", "/getHeader/sso"})
    public JsonResponse getHeader() {
        return  iCommonService.getHeader();
    }


}