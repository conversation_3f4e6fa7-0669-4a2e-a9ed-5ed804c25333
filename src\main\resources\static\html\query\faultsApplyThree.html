<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>找茬活动上报</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/2023/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <style>
        .fs15 {
            font-size: 18px;
        }

        .ddd {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .messager-body tr td:first-child {
            width: 0px !important;
        }

        .panel-title {
            font-size: 18px !important;
        }
    </style>
    <script type="text/javascript">
        getCurrent();

        var appNameList = [];
        $(function () {
            if (gps.type == 'check' || gps.type == 'edit') {
                $('.pageInfo').hide();
                $('.body_page').attr('style', 'padding-top: 20px')
            } else {
                // $('.body_page').attr('style', 'padding-top: 85px')
            }
            if (gps.type == "draft") {
                if (gps.location) {
                    $(".reprealDraft").show();
                }
            }

            ajaxgeneral({
                url: "action/findFaults/getAllAppNameNoPage",
                data: {},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    appNameList = res.data;
                }
            });

            var param = {
                "htmlName": window.name,
                "formId": "questionForm",
                "processNextCmd": "action/findFaults/startSubmitProcess",
                "processNextBeforeSubmit": function (data) {
                    return true;
                }
            };
            loadProcess(param);

            var today = new Date();
            var year = today.getFullYear();
            var month = (today.getMonth() + 1) > 9 ? today.getMonth() + 1 : '0' + (today.getMonth() + 1);
            var day = today.getDate() > 9 ? today.getDate() : '0' + today.getDate();
            today = year + '-' + month + '-' + day;

            $('#applyUser').val(web.currentUser.truename)
            $('#applyUserPhone').val(web.currentUser.preferredMobile)
            $('#displayName').val(web.currentUser.belongCompanyName + "\\" + web.currentUser.belongDepartmentName);

            // ajaxgeneral({
            //     url: "action/findFaults/getUserGridName?username=" + web.currentUser.username,
            //     contentType: "application/json; charset=utf-8",
            //     success: function (res) {
            //         $('#gridName').val(res.data.gridName)
            //     }
            // });

            var currentDate = new Date();
            var targetDate = new Date('2026-12-15');
   




            if (!gps.location  &&  currentDate < targetDate) {

                // $("#expectedCompletionTime").datetimebox('calendar').calendar({
                //     styler: function (date) {
                //         var now = new Date();
                //         var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                //         return date < d1 ? "color:#eee" : "";
                //     },
                //     validator: function (date) {
                //         var now = new Date();
                //         var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                //         return date >= d1;
                //     }
                // });

                ajaxgeneral({
                    url: "action/findFaults/getUserGridName?username=" + web.currentUser.username,
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        $('#gridName').val(res.data.gridName)
                    }
                });

                $.messager.alert({
                    title:'温馨提示',
                    msg:'<div class="fs15"> <span class="ddd">填报注意事项：</span>'
                        +'</br>'+'1、在上报找茬问题时尽量明确问题描述、报错信息、报错截图或报错视频等信息。'
                        +'</br>'+'2、如有优化要求，请明确说明具体优化内容和期望达到的效果。'
                        +'</br>'+'3、可以备注上网格上报人员的姓名、联系电话，以方便后台与网格人员直接沟通。'
                        +'</div>',
                    width:'800px',
                    height:'260px',
                    ok:'已阅知',
                });
            }else if(!gps.location  &&  currentDate > targetDate){
                top.tabClick('processTask');
                return top.mesShow("温馨提示", "'豫起奋发 网格调度来找茬'活动(第二期)活动已结束！", 4000, "red")
            }







            //hadmin修改
            $(document).on("click", ".hadminSave", function () {
                var datas = getFormValue("questionForm")
                if (formValidate("questionForm")) {
                    ajaxgeneral({
                        url: "action/findFaults/updateProblemInfo",
                        data: datas,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            top.window.queryQuestionListDialog.window.dialogClosed()
                            top.tabClick("queryQuestionListDialog")
                        }
                    });
                }

            });


            if(gps.faultsType){
              $('#faultsType').val(gps.faultsType)
            }
        });

        function beforeSubmit(data) {
            var usFindFaultsIdea = []
            usFindFaultsIdea.push({
                "ideaDes":data.ideaDes,
                "faultsAppName":data.faultsAppName,
                "sysFiles":data.sysFiles,
            })
            data.usFindFaultsIdea = usFindFaultsIdea

            data.feedBackIdea = getFormValue('tipBackTable').feedBackIdea
            data.isOptimize = getFormValue('tipBackTable').isOptimize
            data.optimizeEndTime = getFormValue('tipBackTable').optimizeEndTime


            var x = $('.faults')
            var arr = []
            //判断有没有新增动态列表
            if (x.length == 0){
                return data
            }else {
                for (var i = 0; i < x.length; i++) {
                    arr[i] = x[i].id
                }
                var lengths = arr.length -1
                var subData = []
                var shows = false
                for (var s = 0; s < arr.length; s++) {
                    if(!formValidate(arr[s])) {
                        return
                    } else {
                        if (lengths == s){
                            shows = true
                        }
                    }
                }

                if (shows) {
                    for (var y = 0; y < arr.length; y++) {
                        usFindFaultsIdea.push(getFormValue(arr[y]))
                    }
                    data.usFindFaultsIdea = usFindFaultsIdea
                    var bol = true
                    for(var u in data.usFindFaultsIdea){
                        if(!data.usFindFaultsIdea[u].ideaDes){
                            bol = false
                        }
                    }

                    if(bol){
                        return data
                    }else{
                        return top.mesShow("温馨提示！", "请将意见建议描述填写完整！", 2000, "red")
                    }
 
                }
            }
        }

        function groupNameSelect(data) {
            if (!data) return;
            $('#groupRegistDate').val(data.groupRegistDate || '')
            $('#groupApplyDate').val(data.groupApplyDate || '')
            $('#groupPhone').val(data.groupPhone || '')
            $('#groupApplyUser').val(data.groupApplyUser || '')
            $('#groupBelongDepartmentName').val(data.groupBelongDepartmentName || '')
            $('#groupBelongCompanyName').val(data.groupBelongCompanyName || '')
            $('#groupNumber').val(data.groupNumber || '')
        }

        function submitcallback(data) {
        }

        function getcallback(data) {
            $('#id').val(data.id)
            $('#pmInsId').val(data.pmInsId)
            $('#type').val(data.type)

            // 动态列表渲染和回显
            data.faultsAppName = data.usFindFaultsIdea[0].faultsAppName
            data.ideaDes = data.usFindFaultsIdea[0].ideaDes
            data.sysFiles = data.usFindFaultsIdea[0].sysFiles
            usFindFaultsIdea  = data.usFindFaultsIdea.splice(1)
            formval(data,'questionForm')
            messageList();
            var iarr = []

            for(var i in usFindFaultsIdea){
                var faultsAppNamekey = 'faultsAppName' + i;
                var ideaDeskey = 'ideaDes' + i;
                var sysFileskey = 'sysFiles' + i;
                var obj = {}
                obj[faultsAppNamekey] = usFindFaultsIdea[i].faultsAppName
                obj[ideaDeskey] = usFindFaultsIdea[i].ideaDes
                obj[sysFileskey] = usFindFaultsIdea[i].sysFiles

                iarr.push(obj)

                // iarr.push({ [faultsAppNamekey] :usFindFaultsIdea[i].faultsAppName, [ideaDeskey] :usFindFaultsIdea[i].ideaDes, [sysFileskey] :usFindFaultsIdea[i].sysFiles})
                // iarr.push({ faultsAppNamekey :usFindFaultsIdea[i].faultsAppName, ideaDeskey :usFindFaultsIdea[i].ideaDes, sysFileskey :usFindFaultsIdea[i].sysFiles})
                if(gps.location !== 'djfupt.start' || gps.type == 'join'){
                    formReadonly('faults'+i)
                } 
            }


            for(var j in iarr){
                formval(iarr[j],'faults'+j)
            }

            if(gps.location || gps.type == 'join'){
                $('.comments').show()
                loadProcessComments()
            }
            if(gps.type == 'join'){
                formReadonly('tipBackTable')
            }

            // 判断反馈字段显隐
            if(gps.location == 'djfupt.start' || !gps.location){
                $('.tipBack').hide()
            }

            if(gps.type == 'task' && gps.location == 'djfupt.start' && gps.workItemId!==null){
                $('.tipBack').show()

                formReadonlyNo('questionForm')
                $('.reprealDraft').hide()
                $('.hadminSave').hide()
                $('.saveDraft').hide()
                idReadonly('applyUser')
                idReadonly('applyUserPhone')
                idReadonly('gridName')
                idReadonly('displayName')

                formReadonly('tipBackTable')
            }else if(gps.location == 'djfupt.startCheck' ){
                $('.tipBack').show()
                formReadonly('tipBackTable')
            }else{
                $('.tipBack').show()
                formReadonlyNo('tipBackTable')
            }
            if(gps.type == 'draft'){
                $('.tipBack').hide()
            }
            $('.tipBack').resize()
            $('#tipBackTable').resize()
             // 判断反馈字段数据回显
            formval({feedBackIdea:data.feedBackIdea,isOptimize:data.isOptimize,optimizeEndTime:data.optimizeEndTime},'tipBackTable')
        }

        window.getchoosedata = function () {
            if (!formValidate('questionForm')) {
                return false
            }
            var datas = getFormValue('questionForm');
            return {'data': datas, 'state': 1};
        };

        function nextBtnOther() {
            // return {"processType": "faults"};
            var realCondolenceDate = $('#faultsAppName').datebox('getValue');
            return {"processType": "faults3","faultsAppName":realCondolenceDate};
        }


        function loadProcess(param) {
            var processNext_flag = true;//防止接口慢时用户再次流转
            if (gps.from) {
                $("#" + param.formId).attr("cmd-select", $("#" + param.formId).attr("cmd-select") + "/sso");
            }
            if (!gps.location) {
                if (!param.startNoLoadForm) loadForm(param.formId);
                getCurrent(loadF);
                $(".nextBtn,.saveDraft").show();
            } else {
                if (gps.type) gps.workFlag = gps.type;
                gps.source = "PC";
                loadForm(param.formId, gps);
                if (!gps.modify) {
                    if (gps.location != $("#" + param.formId).attr("formLocation") || gps.type == 'task' || gps.type == 'join')
                        formReadonly(param.formId);
                } else {
                    $("#" + param.formId + " input").removeAttr("readonly");
                }
                if (!gps.modify && (gps.type == "task" || gps.type == "toRead")) $(".nextBtn,.flowTrack,.viewComments,.processImg").show();
                if (gps.type == "join" || gps.type == "doRead") $(".flowTrack,.viewComments,.processImg").show();
                if (gps.modify) $(".wfmgModifyBtn").show();
                // 草稿
                if (gps.type == "draft") {
                    $(".nextBtn,.saveDraft").show();
                }
            }

            //注销
            function cancelSure() {
                var href = {"processInstId": gps.processInstId, "source": "PC"};
                var url = tourl(param.processDeleteCmd, href);//(+gps.from?"/sso":"")
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.from) {
                                window.colse();
                            } else {
                                top.dialogClose(gps.initForm || "audit");
                            }
                        }
                    });
                }
            };

            //流程下一步
            window.processNext = function (data) {
                var href = {};
                href.outcome = data.data.outcome;
                href.outcomeName = encodeURI(data.data.outcomeName);
                href.copyLocation = data.data.copyLocation;
                if (gps.workItemId) href.workItemId = gps.workItemId;
                if (gps.processInstId) href.processInstId = gps.processInstId;
                if (gps.location) href.location = gps.location;
                if (gps.type == "toRead") href.notificationId = gps.notificationId;
                href.source = "PC";
                var url = tourl(param.processNextCmd, href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                formD.type = "B"
                var fData = {"formData": formD};
                if (data.data.nextUserName) fData.nextUserName = data.data.nextUserName;
                if (data.data.message) fData.message = data.data.message;
                fData.copyNextUserNames = data.data.copyNextUserNames;
                fData.copyMessage = data.data.copyMessage;
                fData.decisionId = data.data.outcome;
                fData.outcomeName = data.data.outcomeName;
                if (formD) {
                    /**
                     编辑者：申振楠
                     修改时间：2022/5/24 - 2022/9/15
                     修改内容：点击确定按钮置灰
                     */
                    $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                    processNext_flag = false;
                    if (param.processNextBeforeSubmit) {
                        var pnbs = eval(param.processNextBeforeSubmit(fData));
                        // if (!pnbs) return false;   这里修改了可以二次点击流转下一步
                        if (!pnbs) {
                            processNext_flag = true;
                            return false
                        }
                        ;

                    }
                    top.dialogClose("processNext");
                    ajaxgeneral({
                        url: url,
                        data: {"flowParam": fData},
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        loading: true,
                        success: function (data) {
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                            processNext_flag = true;
                            if (gps.dialogClose) {
                                top.dialogClose(gps.dialogClose);
                            }
                            if (gps.location || gps.initForm) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                }else if(gps.flushPortalUrl){
                                    // 集团单点流转
                                    var flushPortalUrl = decodeURIComponent(gps.flushPortalUrl);
                                    var params = {
                                        "appcode": gps.appcode,
                                        "uniqueId": gps.uniqueId,
                                        "itemId": gps.itemId,
                                    }
                                    var pageUrlNew = tourl(flushPortalUrl,params);
                                    window.location.replace(pageUrlNew)
                                } else if (gps.formToTab) {
                                    top.tabClose("li_" + param.htmlName);
                                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                                    var iframs = gps.initForm.split("@");
                                    setTimeout(function () {
                                        if (iframs[0].indexOf("_") > -1) {
                                            var ifi = iframs[0].split("_");
                                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                                        } else {
                                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                                            }
                                        }
                                    }, 1500);
                                } else if (gps.fromIfr) {
                                    top.dialogClose(gps.fromIfr);
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                if (gps.fromIfr) {//从列表打开的工单详情
                                    top.dialogClose(gps.fromIfr);
                                } else if (gps.newPage) {
                                    top.tabClick("li_processTask");
                                } else if (gps.openMenu) {//打开指定菜单
                                    top.tabClick(gps.openMenu);
                                } else {
                                    //window.location.href=window.location.href;
                                    setTimeout(function () {
                                        top.tabClick("processTask");
                                        top.tabClose("li_" + param.htmlName);
                                    }, 1500);
                                }
                            }
                        }, sError: function () {
                            processNext_flag = true;
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                        }, error: function () {
                            processNext_flag = true;
                            /**
                             编辑者：申振楠
                             修改时间：2022/5/24 - 2022/9/15
                             修改内容：点击确定按钮置灰
                             */
                            $(window.parent.document).find("#processNextConfirm").linkbutton({disabled: false})
                        }
                    });
                }
            };
            //废除草稿
            $(document).on("click", ".reprealDraft", function () {
                var data = getFormValue('questionForm');

                var ajaxopts = {
                    url: "action/findFaults/deleteDraft?pmInsId=" + data.pmInsId + '&currentUserCode=' + web.currentUser.username,
                    contentType: "application/json; charset=utf-8",
                    data: data,
                    success: function (data) {
                        if (gps.type == "draft") {
                            top.dialogClose("audit");
                        } else {
                            top.tabClick("processDraft");
                            top.tabClose("li_applicationFrom");
                        }
                    }
                };
                ajaxgeneral(ajaxopts);
            });
            //流程下一步
            $(document).on("click", ".nextBtn", function () {
                if (!processNext_flag) return false;
                $(".nextBtn").removeClass("activeNextBtn");
                if (!$(this).hasClass("activeNextBtn")) $(this).addClass(" activeNextBtn");//标识用
                var $form = $(this).parents("form");
                if (formValidate($form.attr("id"))) {
                    var href = {};
                    if (gps.location) {
                        href.location = gps.location;
                        if (gps.processInstId) href.processInstId = gps.processInstId;
                        if (gps.processDefName) href.processDefName = gps.processDefName;
                    } else {
                        href.location = $(this).parents("form").attr("formLocation");
                    }
                    if (gps.from) href.from = gps.from;
                    if (gps.type) href.type = gps.type;
                    var otherhref = {};
                    if ($form.attr("nextBtnOther")) {
                        if ($form.attr("nextBtnOther").indexOf("()") > -1)
                            otherhref = eval($form.attr("nextBtnOther"));
                        else
                            otherhref = eval($form.attr("nextBtnOther") + "()");
                    }
                    for (var i in otherhref) {
                        href[i] = otherhref[i];
                    }

                    href.source = "PC";
                    href.processType = 'faults3';
                    var faultsAppName = $('#faultsAppName').datebox('getValue');
                    href.faultsAppName = faultsAppName;

                    //href.questionMode = getFormValue("questionForm").questionMode
                    var url = tourl('html/process/processNext.html', href);
                    var s = getBrowserInfo();
                    var w = 1000;
                    if ((s.browser == "msie" && parseInt(s.ver) < 9) || $(window.parent.window).height() < 550) {
                        w = 1017;
                    }

                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流转下一步', 'processNext', false, w);
                }
            });

            //流程跟踪
            $(document).on("click", ".flowTrack", function () {
                var href = {"location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation")};
                var flowTrackText = $(".flowTrack font").text();
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.flowTrackBefore) {
                    var ft = eval(param.flowTrackBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processTrack.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), flowTrackText, 'processTrack', true);
            });
            //查看意见
            $(document).on("click", ".viewComments", function () {
                var href = {"location": gps.location ? gps.location : $("#" + param.formId).attr("formLocation")};
                href.processInstId = gps.processInstId;
                if (gps.from) href.from = gps.from;
                if (param.viewCommentsBefore) {
                    var ft = eval(param.viewCommentsBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }
                var url = tourl('html/process/processComments.html', href);
                top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '审批意见', 'processComments', true);
            });
            //流程图
            $(document).on("click", ".processImg", function () {
                var href = {"processinstid": gps.processInstId, "tenantId": web.appCode};
                if (param.processImgBefore) {
                    var ft = eval(param.processImgBefore());
                    for (var i in ft) {
                        href[i] = ft[i];
                    }
                }

                if (web.appCode == "dict") {
                    // dict流程图
                    var url = tourl('html/process/flowImage.html', href);
                    top.dialogP(url, gps.initForm ? gps.initForm : ((gps.location && (!gps.formToTab)) ? 'auditF' : param.htmlName), '流程图', 'flowImage', true, 'maximized', 'maximized');
                } else {
                    var url = 'http://10.92.82.44:8888/nbps/processGraphic.jsp';
                    if(window.location.href.indexOf("10.92.81.163")>-1
                        || window.location.href.indexOf("10.92.82.140")>-1
                        || window.location.href.indexOf("10.92.82.141")>-1
                        || window.location.href.indexOf("10.88.178.111")>-1
                        || window.location.href.indexOf("10.228.113.")>-1
                        || window.location.href.indexOf("ha.cmcc")>-1){
                        // url='http://10.92.81.163:8088/nbps/processGraphic.jsp';
                        url = "http://10.88.178.243:8090/nbps/processGraphic.jsp";
                    }
                    url=tourl(url,href);//正式:http://10.92.81.163:8088/nbps/processGraphic.jsp  测试:http://10.92.82.44:8888/nbps/processGraphic.jsp
                    top.dialogP(url,gps.initForm?gps.initForm:((gps.location && (!gps.formToTab))?'auditF':param.htmlName),'流程图','processImg',true,$(window).width()-50,$(window).height()-20);
                }
            });
            //修改提交用于wfmg
            $(document).on("click", ".wfmgModifyBtn", function () {
                var href = {};
                href.businessKey = gps.businessKey;
                var url = tourl("action/workOrderMg/updateWorkDetail", href);//(gps.from?"/sso?currentUser=":
                var formD = getFormValue(param.formId);
                if (formD) {
                    ajaxgeneral({
                        url: url,
                        data: formD,
                        currentUser: true,
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (gps.location) {
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                }else if(gps.flushPortalUrl){
                                    // 集团单点流转
                                window.opener=null;
                                    window.open('','_self');
                                    window.close();
                                } else {
                                    top.dialogClose("audit");
                                }
                            } else {
                                //window.location.href=window.location.href;
                                setTimeout(function () {
                                    top.tabClose("li_" + param.htmlName);
                                }, 1500);
                            }
                        }
                    });
                }
            });
        };


        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            var data = getFormValue('questionForm');
            var ajaxopts = {
                url: "action/findFaults/saveDraft?source=PC",
                contentType: "application/json; charset=utf-8",
                loading: true,
                data: data,
                success: function (data) {
                    if (gps.type == "draft") {
                        top.dialogClose("audit");
                    } else {
                        // top.dialogClose(gps.fromIfr);
                        top.tabClick("processDraft");
                        top.tabClose("faultsApply");
                    }
                }
            };
            ajaxgeneral(ajaxopts);
        });

        //关闭
        $(document).on("click", ".optClose", function () {
            var param = {htmlName: 'changeQuestion'}
            if (gps.location || gps.initForm) {
                if (gps.from) {
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                }else if(gps.flushPortalUrl){
                            // 集团单点流转
                           window.opener=null;
                            window.open('','_self');
                            window.close();
                        } else if (gps.formToTab) {
                    top.tabClose("li_" + param.htmlName);
                } else if (gps.initForm && gps.initForm.indexOf("@") > -1) {
                    var iframs = gps.initForm.split("@");
                    setTimeout(function () {
                        if (iframs[0].indexOf("_") > -1) {
                            var ifi = iframs[0].split("_");
                            if (ifi[0] && ifi[1] == 1) top[ifi[0]].location.reload();//流转成功后调用的方法
                        } else {
                            if ($("#li_" + param.htmlName, top[iframs[0]].document).length > 0) {
                                $("#li_" + param.htmlName + " i.fr", top[iframs[0]].document).trigger("click");
                            }
                        }
                    }, 1500);
                } else if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.dialogClose("audit");
                    top.dialogClose("getCheck");
                }
            } else {
                if (gps.fromIfr) {
                    top.dialogClose(gps.fromIfr);
                } else if (gps.newPage) {
                    top.tabClick("li_processTask");
                } else if (gps.blank && gps.blank == "blank") {
                    // 项目内打开新标签页办理场景
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else {
                    top.tabOpen("html/process/processTask.html", "我的待办");
                    top.tabClose("li_" + param.htmlName);
                }
            }
        });


        var usFindFaultsIdea = []

        function addMessage() {
                var x = $('.faults')
                var arr = []
                //判断有没有新增动态列表
                if (x.length == 0){
                    // return data
                    var params = {
                        "ideaDes": "",
                        "faultsAppName": "",
                        "sysFiles": [],
                    }
                    usFindFaultsIdea.push(params);
                }else {
                    for (var i = 0; i < x.length; i++) {
                        arr[i] = x[i].id
                    }
                    var lengths = arr.length -1
                    var subData = []
                    var shows = false
                    for (var s = 0; s < arr.length; s++) {
                       
                        if (lengths == s){
                            shows = true
                        }
                    }

                    if (shows) {
                        for (var y = 0; y < arr.length; y++) {
                            usFindFaultsIdea[y] = getFormValue(arr[y])
                        }
                        var params = {
                            "ideaDes": "",
                            "faultsAppName": "",
                            "sysFiles": [],
                        }
                        usFindFaultsIdea.push(params);
                    }
                }
                messageList2();
        }

        function deleteMessage(index) {
            usFindFaultsIdea.splice(index,1);
            messageList2();
        }

        function messageList() {
            $('#faultsAppNameList').html('')
            usFindFaultsIdea.forEach(function(item, index){
                var input1 = '<input id="faultsAppName'+index+'" name="faultsAppName"'+index+' class="easyui-combobox" required style="width:100%; height: 32px;"/>'
                var a1 = '<a class="btn fl ml10 addMatter" onclick="deleteMessage('+index+')"><span>删除上报内容</span></a>'
                var input2 = '<input id="sysFiles'+index+'"  name="sysFiles" type="text" file="true" mulaccept="true" class="cselectorImageUpload fl" btnmsg="<i class=\'iconfont\' title=\'添加\'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsTypePart=1" noReset="true"/>'
                var textarea = '<textarea id="ideaDes'+index+'" name="ideaDes"  required class="easyui-validatebox" validType="maxLength[1000,ideaDes'+index+']" style="min-height:120px;"></textarea><p class="ideaDes"></p>'
                var usFindFaultsIdeaTemp = '<form class="faults" id="faults'+index+'">\n' +
                    '    <table class="tabForm formTable w100" style="margin-top: 0" cellpadding="0" cellspacing="10">\n' +
                        '    <tr>\n' +
                            '   <td width="10%"></td>\n' +
                            '   <td width="23.3%"></td>\n' +
                            '   <td width="10%"></td>\n' +
                            '  <td width="23.3%"></td>\n' +
                            ' <td width="10%"></td>\n' +
                            ' <td width="23.3%"></td>\n' +
                            '</tr>\n' +
                    '        <tr>\n' +
                    '            <td class="lable"><span>意见建议归属系统</span> <font class="col_r">*</font></td>\n' +
                    '            <td colspan="4">\n' + input1 + 
                    '            </td>\n' +
                    '            <td >\n' + a1 +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '        <tr>\n' +
                    '            <td class="lable"><span>意见建议描述</span> <font class="col_r">*</font></td>\n' +
                    '            <td colspan="5" width="90%">\n' + textarea +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '        <tr style="height: 40px">\n' +
                    '            <td class="lable"><span>附件</span></td>\n' +
                    '            <td colspan="5" width="90%" style="padding-left: 7px">\n' + input2 +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '    </table>\n' +
                    '</form>'

                $('#faultsAppNameList').append(usFindFaultsIdeaTemp)

                $('#faultsAppName' + index + '' ).combobox({
                    url: web.rootdir + 'action/findFaults/getAllAppNameNoPage',
                    valueField: 'appName',
                    textField: 'appName',
                    panelHeight: '200',
                    queryParams: {},
                    editable: true, //false不可编辑状态
                    // filter: function (q, row) {
                    //     var opts = $(this).combobox('options');
                    //     //==0表示前缀批评为，>-1表示全局搜索匹配
                    //     return row[opts.textField].indexOf(q) > -1;
                    // }
                })

                $('#faults'+index+'').find("input.cselectorCheckBox,input.cselectorRadio,input.cselectorTree,input.cselectorCheckTree,input.cselectorCheckList,input.cselectorStar").each(function (i, v) {//重置form之后文件上传控件变形或者重置
                    initcselector($(v));
                });

                $('#faults'+index+'').find("input.cselectorImageUpload").each(function (i, v) {//重置form之后文件上传控件变形或者重置
                    initfileupload($(v));
                });
            })
        }

        function messageList2() {
            $('#faultsAppNameList').html('')
            usFindFaultsIdea.forEach(function(item, index){
                var input1 = '<input id="faultsAppName'+index+'" name="faultsAppName"'+index+' class="easyui-combobox" required style="width:100%; height: 32px;"/>'
                var a1 = '<a class="btn fl ml10 addMatter" onclick="deleteMessage('+index+')"><span>删除上报内容</span></a>'
                var input2 = '<input id="sysFiles'+index+'"  name="sysFiles" type="text" file="true" mulaccept="true" class="cselectorImageUpload fl" btnmsg="<i class=\'iconfont\' title=\'添加\'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsTypePart=1" noReset="true"/>'
                var textarea = '<textarea id="ideaDes'+index+'" name="ideaDes"  required class="easyui-validatebox" validType="maxLength[1000,ideaDes'+index+']" style="min-height:120px;"></textarea><p class="ideaDes"></p>'
                var usFindFaultsIdeaTemp = '<form class="faults" id="faults'+index+'">\n' +
                    '    <table class="tabForm formTable w100" style="margin-top: 0" cellpadding="0" cellspacing="10">\n' +
                        '    <tr>\n' +
                            '   <td width="10%"></td>\n' +
                            '   <td width="23.3%"></td>\n' +
                            '   <td width="10%"></td>\n' +
                            '  <td width="23.3%"></td>\n' +
                            ' <td width="10%"></td>\n' +
                            ' <td width="23.3%"></td>\n' +
                            '</tr>\n' +
                    '        <tr>\n' +
                    '            <td class="lable"><span>意见建议归属系统</span> <font class="col_r">*</font></td>\n' +
                    '            <td colspan="4">\n' + input1 + 
                    '            </td>\n' +
                    '            <td >\n' + a1 +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '        <tr>\n' +
                    '            <td class="lable"><span>意见建议描述</span> <font class="col_r">*</font></td>\n' +
                    '            <td colspan="5" width="90%">\n' + textarea +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '        <tr style="height: 40px">\n' +
                    '            <td class="lable"><span>附件</span></td>\n' +
                    '            <td colspan="5" width="90%" style="padding-left: 7px">\n' + input2 +
                    '            </td>\n' +
                    '        </tr>\n' +
                    '    </table>\n' +
                    '</form>'

                $('#faultsAppNameList').append(usFindFaultsIdeaTemp)

                $('#faultsAppName' + index + '' ).combobox({
                    url: web.rootdir + 'action/findFaults/getAllAppNameNoPage',
                    valueField: 'appName',
                    textField: 'appName',
                    panelHeight: '200',
                    queryParams: {},
                    editable: false, //false不可编辑状态
                    // filter: function (q, row) {
                    //     var opts = $(this).combobox('options');
                    //     //==0表示前缀批评为，>-1表示全局搜索匹配
                    //     return row[opts.textField].indexOf(q) > -1;
                    // }
                })

                $('#faults'+index+'').find("input.cselectorCheckBox,input.cselectorRadio,input.cselectorTree,input.cselectorCheckTree,input.cselectorCheckList,input.cselectorStar").each(function (i, v) {//重置form之后文件上传控件变形或者重置
                    initcselector($(v));
                });

                $('#faults'+index+'').find("input.cselectorImageUpload").each(function (i, v) {//重置form之后文件上传控件变形或者重置
                    initfileupload($(v));
                });
            })


            var iarr = []
            for(var i in usFindFaultsIdea){
                var faultsAppNamekey = 'faultsAppName' + i;
                var ideaDeskey = 'ideaDes' + i;
                var sysFileskey = 'sysFiles' + i;
                var obj = {}
                obj[faultsAppNamekey] = usFindFaultsIdea[i].faultsAppName
                obj[ideaDeskey] = usFindFaultsIdea[i].ideaDes
                obj[sysFileskey] = usFindFaultsIdea[i].sysFiles

                iarr.push(obj)
                // iarr.push({ [faultsAppNamekey] :usFindFaultsIdea[i].faultsAppName, [ideaDeskey] :usFindFaultsIdea[i].ideaDes, [sysFileskey] :usFindFaultsIdea[i].sysFiles})  
                // iarr.push({ faultsAppNamekey:usFindFaultsIdea[i].faultsAppName, ideaDeskey :usFindFaultsIdea[i].ideaDes, sysFileskey:usFindFaultsIdea[i].sysFiles})  
            }

            for(var j in iarr){
                formval(iarr[j],'faults'+j)
            }
        }
    </script>
    <style>
        .tabForm {
            border-top: none;
        }
        .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
        .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
        .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
        .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
        .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
        .cselectorImageUL .btn{ right: 3px; top: 3px; }
        .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
        textarea{ line-height: 20px; letter-spacing: 1px; }
        table.tabForm{ border-color: #dedede; }
    </style>
</head>

<body class="body_page">
<!--noNextUserDecisionId无下一审批人的决策id比如归档，可以为多个节点中间用|隔开；mulitNextUserDecisionId审批人多选的决策项-->
<form id="questionForm" formLocation="djfupt.start" method="post" contentType="application/json; charset=utf-8"
      cmd-select="action/findFaults/getFormDetail" beforeSubmit="beforeSubmit()" submitcallback="submitcallback()"
      getcallback="getcallback()">
    <input id="pmInsId" name="pmInsId" value="" hidden>
    <input id="id" name="id" value="" hidden>
    <input id="type" name="type" value="" hidden>
    <div class="pageInfo" style="min-width: 0">
        <div class="pageInfoD">
            <a class="btn small fl mr15 nextBtn hide"><i class="iconfont">&#xe688;</i>
                <font>流转下一步</font>
            </a>
            <a class="btn small fl mr15 saveDraft hide"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 hadminSave hide mb20"><i class="iconfont">&#xe63a;</i>
                <font>保存</font>
            </a>
            <a class="btn small fl mr15 reprealDraft hide"><i class="iconfont">&#xe6bd;</i>
                <font>废除草稿</font>
            </a>
            <a class="btn small fl mr15 flowTrack hide"><i class="iconfont">&#xe68c;</i>
                <font>流程跟踪</font>
            </a>
            <a class="btn small fl mr15 viewComments hide"><i class="iconfont">&#xe629;</i>
                <font>查看意见</font>
            </a>
            <a class="btn small fl mr15 processImg hide"><i class="iconfont">&#xe6bd;</i>
                <font>流程图</font>
            </a>
            <a class="btn small fl mr15 optClose"><i class="iconfont">&#xe690;</i>
                <font>关闭</font>
            </a>
        </div>
    </div>

    <div style="width: 100%;">
        <table class="tabForm formTable w100" cellpadding="0" cellspacing="10" style="margin-top: 80px;">
            <input type="hidden" id="faultsType" name="faultsType" noReset="true" />
            <tr>
                <td width="10%"></td>
                <td width="23.3%"></td>
                <td width="10%"></td>
                <td width="23.3%"></td>
                <td width="10%"></td>
                <td width="23.3%"></td>
            </tr>
            <tr>
                <td colspan="6" style="text-align:center;font-size: 18px;color:#3CB9FC;font-weight:700;padding: 5px;">找茬活动上报</td>
            </tr>
            <tr>
                <td class="lable">上报人姓名</td>
                <td><input id="applyUser" name="applyUser" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">联系电话</td>
                <td><input id="applyUserPhone" name="applyUserPhone" type="text" readonly="readonly"
                           noReset="true" style="width:100%; height: 32px;"/>
                </td>
                <td class="lable">发起人组织</td>
                <td><input id="displayName" name="displayName" type="text" readonly="readonly" noReset="true"
                           style="width:100%; height: 32px;"/>
                </td>
            </tr>
            <tr>
                <td class="lable">网格名称</td>
                <td colspan="5" width="90%" style="padding-left: 7px">
                    <input id="gridName" name="gridName" type="text"  readonly="readonly" noReset="true"
                       style="width:100%; height: 32px;"/>
                    
                </td>
            </tr>
            <tr>
                <td class="lable"><span>意见建议归属系统</span> <font class="col_r">*</font></td>
                <td colspan="4" width="100%" style="padding-left: 0px">
                    <input id="faultsAppName"
                           name="faultsAppName"
                           class="easyui-combobox"
                           style="width: 100%; height: 32px;"
                           data-options="
                            valueField: 'appName',
                            panelHeight:'auto',
                            ischooseall:true,
                            textField: 'appName',
                            editable:false,
                            required:true,
                            url: 'action/findFaults/getAllAppNameNoPage'
                            "/>
                   
                </td>
                <td>
<!--                <a class="btn fl ml10 addMatter" onclick="addMessage()"><span>新增上报内容</span></a>-->
                </td>
            </tr>
            <tr>
                <td class="lable"><span>意见建议描述</span> <font class="col_r">*</font></td>
                <td colspan="5" width="90%">
                        <textarea id="ideaDes" placeholder="您可以按照1、2、3...反馈多条意见建议。" name="ideaDes" class="easyui-validatebox" required validType="maxLength[1000,'talkContentTips']" style="min-height:120px;"></textarea><!--required="true"-->
                    <p class="talkContentTips"></p>
                </td>
            </tr>
            <tr style="height: 40px">
                <td class="lable"><span>附件</span></td>
                <td colspan="5" width="90%" style="padding-left: 7px">
                    <input id="sysFiles" name="sysFiles" type="text" file="true" mulaccept="true" class="cselectorImageUpload fl" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsTypePart=1" noReset="true"/>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="faultsAppNameList"></div>


<div class="tipBack hide">
    <table id="tipBackTable" class="tabForm formTable w100" cellpadding="0" cellspacing="0">
        <tr>
            <td width="10%"></td>
            <td width="23.3%"></td>
            <td width="10%"></td>
            <td width="23.3%"></td>
            <td width="10%"></td>
            <td width="23.3%"></td>
        </tr>
       
        <tr>
            <td class="lable">反馈意见</td>
            <td colspan="5" width="90%" style="padding-left: 7px">
                <textarea id="feedBackIdea" name="feedBackIdea" class="easyui-validatebox" validType="maxLength[1000,'feedBackIdeaTips']" style="min-height:120px;"></textarea>
                <p class="feedBackIdeaTips"></p>
            </td>
        </tr>
        <tr>
            <td class="lable">是否涉及系统优化</td>
            <td>
                <input id="isOptimize" name="isOptimize" style="width: 100%; height: 32px;" type="text"
                           class="easyui-combobox" data-options="
                            valueField: 'value',
                            textField: 'label',
                            panelHeight:'auto',
                            editable:false,
                            data: [{ label: '是', value: 1 },{ label: '否', value: 0 }]"/>
            </td>
            <td class="lable">计划优化完成时间</td>
            <td>
                <input id="optimizeEndTime" name="optimizeEndTime" type="text" class="easyui-datebox"  style="width:100%;height:32px;"
                     data-options="panelHeight:'auto', editable:false,requied:true " />
            </td>
            <td class="lable"></td>
            <td></td>
        </tr>
    </table>
</div>
<div class="comments hide">
    <div class="tit">审批意见</div>
    <div class="commentsTable"><table id="commentsTable"></table></div>
</div>
</body>

</html>


<style>
    .comments{
        margin-top: 20px; 
    }

    .comments .tit{
        font-size: 16px;
        padding: 5px 0 5px 10px;
        border-left: 5px solid #38a8ec;
        margin-bottom: 10px;
        
    }

</style>