package com.simbest.boot.djfupt.problem.web;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.djfupt.common.service.ICommonService;
import com.simbest.boot.djfupt.problem.model.ProblemStatisticsVo;
import com.simbest.boot.djfupt.problem.model.UsProblemInfo;
import com.simbest.boot.djfupt.problem.service.IUsProblemInfoService;
import com.simbest.boot.djfupt.record.model.UsRecordFill;
import com.simbest.boot.djfupt.util.Constants;
import com.simbest.boot.djfupt.util.OperateLogTool;
import com.simbest.boot.djfupt.util.PageTool;
import com.simbest.boot.djfupt.util.PaginationHelps;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 问题上报相关接口
 */
@Api(description = "问题上报配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/usProblemInfo")
public class UsProblemInfoController extends LogicController<UsProblemInfo, String> {

    private IUsProblemInfoService usProblemInfoService;

    @Autowired
    public UsProblemInfoController(IUsProblemInfoService usProblemInfoService) {
        super(usProblemInfoService);
        this.usProblemInfoService = usProblemInfoService;
    }

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private PaginationHelps paginationHelp;


    String param1 = "/action/usProblemInfo";

    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;

    @Autowired
    private LoginUtils loginUtils;


    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation(value = "查询决策", notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getDecisions", "/api/getDecisions", "/getDecisions/sso", "/anonymous/getDecisions"})
    public JsonResponse getDecisions(
            @RequestParam(required = false) String processInstId,
            @RequestParam(required = false) String processDefName,
            @RequestParam String location,
            @RequestParam String source,
            @RequestParam(required = false) String currentUserCode,
            @RequestParam String processType,
            @RequestParam(required = false) String questionMode) {

        return iCommonService.getDecisions(processInstId, processDefName, location, source, currentUserCode, processType, param1, questionMode);
    }

    /**
     * 获取到决策下组织人员
     *
     * @param processInstId 流程实例id
     * @param appDecision   决策对象
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getOrgAndUser", "/api/getOrgAndUser", "/getOrgAndUser/sso"})
    public JsonResponse getOrgAndUser(@RequestParam String source,
                                      @RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestBody SimpleAppDecision appDecision) {
        return iCommonService.getOrgAndUser(processInstId, source, currentUserCode, appDecision, param1,null);
    }


    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param notificationId  待阅id
     * @param bodyParam       提交参数
     * @param formId          表单id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "nextUserName", value = "审批人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "copyLocation", value = "抄送下一环节", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyMessage", value = "抄送意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "copyNextUserNames", value = "抄送人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
    })
    @PostMapping(value = {"/startSubmitProcess", "/api/startSubmitProcess", "/startSubmitProcess/sso", "/anonymous/startSubmitProcess"})
    public JsonResponse startSubmitProcess(@RequestParam String currentUserCode,
                                           @RequestParam String source,
                                           @RequestParam(required = false) String workItemId,
                                           @RequestParam(required = false) String outcome,
                                           @RequestParam(required = false) String location,
                                           @RequestParam(required = false) String copyLocation,
                                           @RequestParam(required = false) String notificationId,
                                           @RequestParam(required = false) String formId,
                                           @RequestBody Map<String, Object> bodyParam) throws Exception {
        return usProblemInfoService.startSubmitProcess(source, currentUserCode, workItemId, outcome, location, copyLocation, bodyParam, formId, notificationId);
    }


    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "userCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getFormDetail", "/api/getFormDetail", "/getFormDetail/sso", "/anonymous/getFormDetail"})
    public JsonResponse getFormDetail(@RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String workFlag,
                                      @RequestParam(required = false) String source,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String pmInsId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String type) {
        return usProblemInfoService.getFormDetail(processInstId, workFlag, source, currentUserCode, pmInsId, location,type);
    }

    @PostMapping(value = {"/getCurrentProblemInfo", "/api/getCurrentProblemInfo", "/getCurrentProblemInfo/sso", "/anonymous/getCurrentProblemInfo"})
    public JsonResponse getCurrentProblemInfo() {
        return usProblemInfoService.getCurrentProblemInfo();
    }


    /**
     * 保存草稿
     *
     * @return
     */
    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/api/saveDraft", "/saveDraft/sso"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody UsProblemInfo usProblemInfo) {
        return usProblemInfoService.saveDraft(source, currentUserCode, usProblemInfo);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/api/deleteDraft", "/deleteDraft/sso"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody UsProblemInfo usProblemInfo) {
        return usProblemInfoService.deleteDraft(source, currentUserCode, pmInsId, usProblemInfo);
    }


    /**
     * 台账查询
     */
    @PostMapping(value = {"/queryAllUsCaseInfo", "/api/queryAllUsCaseInfo", "/queryAllUsCaseInfo/sso"})
    public JsonResponse queryAllUsCaseInfo(@RequestParam(required = false) Integer page,
                                           @RequestParam(required = false) Integer rows,
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestParam(required = false) String source,
                                           @RequestBody(required = false) Map<String, Object> resultMap
    ) {
        String startTime = MapUtil.getStr(resultMap, "startDate");
        String problemName = MapUtil.getStr(resultMap, "problemName");
        String endTime = MapUtil.getStr(resultMap, "endDate");
        String belongDepartmentCode = MapUtil.getStr(resultMap, "companyCode");
        String state = MapUtil.getStr(resultMap, "state");
        return usProblemInfoService.queryApplication(page, rows, source, currentUserCode, startTime, endTime, problemName, belongDepartmentCode, state);
    }


    @PostMapping(value = {"/exportParameter", "/api/exportParameter", "/sso/exportParameter"})
    public void exportParameter(@RequestParam(required = false) String startDate,
                                @RequestParam(required = false) String endDate,
                                @RequestParam(required = false) String problemName,
                                @RequestParam(required = false) String belongDepartmentCode,
                                @RequestParam(required = false) Integer page,
                                @RequestParam(required = false) Integer rows,
                                @RequestParam(required = false) String source,
                                @RequestParam(required = false) String userCode,
                                @RequestParam(required = false) String state,
                                HttpServletResponse response,
                                HttpServletRequest request) {
        rows = 99999;
        usProblemInfoService.exportParameter(page, rows, source, userCode, startDate, endDate, problemName, belongDepartmentCode, state, response, request);
    }


    /**
     * 问题数量
     */
    @ApiOperation(value = "问题数量", notes = "问题数量")
    @PostMapping(value = {"/problemCount", "/api/problemCount", "/problemCount/sso"})
    public JsonResponse policyCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usProblemInfoService.problemCount(resultMap);
        return JsonResponse.success(resultList);
    }


    /**
     * 问题数量
     */
    @ApiOperation(value = "问题数量", notes = "问题数量")
    @PostMapping(value = {"/problemAllCount", "/api/problemAllCount", "/problemAllCount/sso"})
    public JsonResponse problemAllCount(@RequestBody(required = false) Map<String, Object> resultMap) {
        List<Map<String, Object>> resultList = usProblemInfoService.problemAllCount(resultMap);
        return JsonResponse.success(resultList);
    }

    @PostMapping(value = {"/test", "/api/test", "/test/sso", "/anonymous/test"})

    public JsonResponse test() {
        return usProblemInfoService.test();
    }


    /**
     * 问题数据统计
     *
     * @param page
     * @param rows
     * @param resultMap
     * @return
     */
    @PostMapping(value = {"/problemStatistics", "/api/problemStatistics", "/problemStatistics/sso", "/anonymous/problemStatistics"})
    public JsonResponse problemStatistics(@RequestParam(required = false, defaultValue = "1") int page, //当前页码
                                          @RequestParam(required = false, defaultValue = "10") int rows, //每页数量
                                          @RequestBody(required = false) Map<String, Object> resultMap,
                                          @RequestParam(required = false) String currentUserCode,
                                          @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        IUser user = SecurityUtils.getCurrentUser();
        List<ProblemStatisticsVo> resultList = null;
        List<SimpleRole> simpleRoles = uumsSysRoleApi.findRoleByUsername(Constants.APP_CODE);
        boolean isAdmin =  simpleRoles.stream().anyMatch(simpleRoless -> StrUtil.equals(Constants.FJFUPT_PRO, simpleRoless.getRoleCode()));
        //省公司
        if (user.getBelongCompanyTypeDictValue().equals("01")||isAdmin) {
            resultList = usProblemInfoService.problemStatistics(page, rows, resultMap);
        } else {
            resultList = usProblemInfoService.problemStatisticsOther(page, rows, resultMap);
        }

        Pageable pageable = paginationHelp.getPageable(page, rows, "", "");
        if (resultList != null) {
            long totalRecords = resultList.size();
            resultList = PageTool.pagination(resultList, page, rows);
            Page pageInfo = new PageImpl<>(resultList, pageable, totalRecords);
            return JsonResponse.success(pageInfo);
        }
        return JsonResponse.success(null, "查询数据有误");
    }


    @PostMapping(value = {"/exportProblemStatistics", "/api/exportProblemStatistics", "/sso/exportProblemStatistics"})
    public void exportProblemStatistics(UsProblemInfo problemInfo,
                                        HttpServletResponse response,
                                        HttpServletRequest request,
                                        @RequestParam(required = false) String currentUserCode,
                                        @RequestParam(required = false) String source) {
        operateLogTool.operationSource(source, currentUserCode);
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", problemInfo.getStartTime());
        map.put("endTime", problemInfo.getEndTime());
        map.put("companyName", problemInfo.getCompanyName());

        usProblemInfoService.exportProblemStatistics(map, response, request);
    }


    @PostMapping(value = {"/updateProblemInfo", "/api/updateProblemInfo", "/sso/updateProblemInfo"})
    public JsonResponse updateProblemInfo(@RequestBody UsProblemInfo problemInfo ) {
        return   usProblemInfoService.updateProblemInfo(problemInfo);
    }

    /**
     * 提交问题上报
     */
    @ApiOperation(value = "提交问题上报", notes = "提交问题上报")
    @PostMapping(value = {"/insertUsProblemInfo", "/api/insertUsProblemInfo", "/insertUsProblemInfo/sso"})
    public JsonResponse insertUsProblemInfo(@RequestParam(required = false) String source,
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestBody UsProblemInfo usRecordFill) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        UsProblemInfo newUsRecordFill = null;
        if (ObjectUtil.isNotEmpty(usRecordFill)) {
            if (StringUtils.isNotEmpty(usRecordFill.getId())) {
                newUsRecordFill = new UsProblemInfo();
                usRecordFill.setIsDraft("1");
                usProblemInfoService.updateFileByPmInsId(usRecordFill,"B");//更新附件
                usProblemInfoService.update(usRecordFill);
            } else {
                String pmInsId = "B" + IdGenerator.idWorker.nextId();//获取到pmInsId
                usRecordFill.setPmInsId(pmInsId);
                usRecordFill.setIsDraft("1");
                BelongInfoTool.setBelongCompanyAndDepartment(usRecordFill);
                newUsRecordFill = usProblemInfoService.insert(usRecordFill);
                usProblemInfoService.updateFileByPmInsId(usRecordFill,"B");//更新附件
            }
        }
        if (ObjectUtil.isNotEmpty(newUsRecordFill)) {
            return JsonResponse.success("添加成功");
        } else {
            return JsonResponse.success("添加失败");
        }
    }
    /**
     * 保存问题上报
     */
    @ApiOperation(value = "保存问题上报", notes = "保存问题上报")
    @PostMapping(value = {"/saveUsProblemInfo", "/api/saveUsProblemInfo", "/saveUsProblemInfo/sso"})
    public JsonResponse saveUsProblemInfo(@RequestParam(required = false) String source,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestBody UsProblemInfo usRecordFill) {
        if (StringUtils.isNotEmpty(source) && source.equals("MOBILE")) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        if (ObjectUtil.isNotEmpty(usRecordFill)) {
            if (StringUtils.isEmpty(usRecordFill.getId())) {
                String pmInsId = "B" + IdGenerator.idWorker.nextId();//获取到pmInsId
                usRecordFill.setPmInsId(pmInsId);
                usRecordFill.setIsDraft("2");
                BelongInfoTool.setBelongCompanyAndDepartment(usRecordFill);
                usProblemInfoService.insert(usRecordFill);
                usProblemInfoService.updateFileByPmInsId(usRecordFill,"B");//更新附件
            } else {
                usRecordFill.setIsDraft("2");
                BelongInfoTool.setBelongCompanyAndDepartment(usRecordFill);
                usProblemInfoService.updateFileByPmInsId(usRecordFill,"B");//更新附件
                usProblemInfoService.updateWithNull(usRecordFill);
            }
        }
        return JsonResponse.success("保存成功");
    }
}
