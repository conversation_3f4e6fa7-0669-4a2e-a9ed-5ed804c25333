package com.simbest.boot.djfupt.screenCount.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.security.IUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_screen_count")
@ApiModel(value = "数智大屏统计")
public class ScreenCount extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "US_SAM")         //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(value = "组织")
    private String orgName;

    @Column(length = 100)
    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @Column(length = 100)
    @ApiModelProperty(value = "部门")
    private String deptName;

    @Column(length = 100)
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    private String companyName;

    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String companyCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位编码")
    private String companyParentCode;

    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    private String companyParentName;

    @Column(length = 100)
    @ApiModelProperty(value = "公司类型")
    private String companyType;

    @Column(length = 40)
    @ApiModelProperty(value = "OA账号")
    private String username;

    @Column(length = 100)
    @ApiModelProperty(value = "姓名")
    private String truename;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "排序")
    private Integer displayOrder;


    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党建数智员工")
    private Long szyg = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "支部近期学习")
    private Long jqxx = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党委第一议题")
    private Long dyyt = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党建指导员")
    private Long djzdy = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "星火党建")
    private Long xhdj = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党建文化")
    private Long djwh = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "豫先锋")
    private Long yxf = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "建言献策")
    private Long jyxc = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "合力攻坚")
    private Long hlgj = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "员工论坛")
    private Long yglt = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党建助手")
    private Long djzs = 0L;

    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党建资源共享(浮动专栏)")
    private Long fdzl = 0L;


    @Column(columnDefinition = "int default 0")
    @ApiModelProperty(value = "党费管理")
    private Long dfglxt = 0L;

    @Transient
    @ApiModelProperty(value = "查询条件：开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate sdate;

    @Transient
    @ApiModelProperty(value = "查询条件：结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate edate;


    public ScreenCount created(IUser user) {
        username = user.getUsername();
        truename = user.getTruename();

        orgCode = user.getBelongOrgCode();
        orgName = user.getBelongOrgName();
        deptCode = user.getBelongDepartmentCode();
        deptName = user.getBelongDepartmentName();
        companyCode = user.getBelongCompanyCode();
        companyName = user.getBelongCompanyName();
        companyParentCode = user.getBelongCompanyCodeParent();
        companyParentName = user.getBelongCompanyNameParent();

        companyType = user.getBelongCompanyTypeDictValue();

        return this;
    }

}
