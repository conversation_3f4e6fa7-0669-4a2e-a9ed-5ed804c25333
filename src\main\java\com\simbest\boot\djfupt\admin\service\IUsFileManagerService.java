package com.simbest.boot.djfupt.admin.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.djfupt.admin.model.UsFileManager;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IUsFileManagerService extends ILogicService<UsFileManager, String> {

    JsonResponse updateInfo(String source, String currentUserCode, UsFileManager usFileManager);

    JsonResponse delInfo(String id);

    Page<UsFileManager> findListByPage(int page, int size, String direction, String properties, String title);

    JsonResponse insertInfo(String source, String currentUserCode, UsFileManager usFileManager);

    UsFileManager getById(String id);
}
