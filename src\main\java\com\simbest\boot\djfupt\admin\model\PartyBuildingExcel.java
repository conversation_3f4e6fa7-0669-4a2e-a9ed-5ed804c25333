package com.simbest.boot.djfupt.admin.model;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @data 2022/11/25 0025 17:52
 */
@Data
public class PartyBuildingExcel {



    @Column(length = 20)
    @ApiModelProperty(value = "OA账号")
    @ExcelVOAttribute(name = "OA账号", column = "A")
    private String userName;

    @Column(length = 20)
    @ApiModelProperty(value = "管理员姓名")
    @ExcelVOAttribute(name = "管理员姓名", column = "B")
    private String trueName;

    @ExcelVOAttribute(name ="网格名称" , column = "C")
    private String gridName;   //
}
